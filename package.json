{"name": "bookproofs", "private": true, "version": "1.0.0", "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite --no-open", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "electron:dev": "concurrently \"npm run dev\" \"electron .\"", "electron:build": "npm run build && electron .", "electron:package": "npm run build && electron-builder", "electron:package:win": "npm run build && electron-builder --win", "electron:package:mac": "npm run build && electron-builder --mac --x64", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@tanstack/react-virtual": "^3.13.11", "@types/lodash": "^4.17.16", "@types/pako": "^2.0.3", "@types/react-color": "^3.0.13", "@types/react-window": "^1.8.8", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "exifr": "^7.1.3", "framer-motion": "^12.8.0", "html2canvas-pro": "^1.5.8", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "pako": "^2.1.0", "pdf-lib": "file:icc-pdf-lib-fork-1.17.5.tgz", "react": "^18.3.1", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-window": "^1.8.11", "recharts": "^2.12.7", "sharp": "^0.33.5", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "use-resize-observer": "^9.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/resize-observer-browser": "^0.1.11", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "electron": "^29.0.0", "electron-builder": "^24.13.3", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}, "build": {"appId": "com.bookproofs.app", "productName": "BookProofs", "npmRebuild": true, "asar": false, "files": ["dist/**/*", "electron/**/*", "build/**/*"], "directories": {"buildResources": "build", "output": "release"}, "fileAssociations": [{"ext": "bp", "name": "BookProofs Project", "description": "BookProofs Designer Project File", "icon": "build/bp-file-icon.icns", "role": "Editor"}], "win": {"icon": "build/logo.ico", "target": "nsis"}, "mac": {"icon": "build/logo.icns", "target": "dmg"}, "linux": {"icon": "build/logo.png", "target": ["AppImage", "deb"]}}}