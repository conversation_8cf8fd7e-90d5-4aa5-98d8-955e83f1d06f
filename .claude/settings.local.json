{"permissions": {"allow": ["Bash(rg:*)", "Bash(grep:*)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"getContext|drawImage|canvas.*element\" src/)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n -A5 \"return\" src/components/BookSetup.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"^\\s*return\" src/components/BookSetup.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"min-h-screen\" src/components/BookSetup.tsx)", "Bash(find:*)", "Bash(awk:*)", "Bash(for file in *.ts)", "Bash(do echo \"Processing $file...\")", "<PERSON><PERSON>(sed:*)", "Bash(done)", "Bash(node:*)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"onFileUpdated|addEventListener.*fileUpdated|file.*updated\" --glob \"*.tsx\")", "<PERSON><PERSON>(true)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"onFileUpdated\" src/)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"SpreadCanvas.*images.*=\" .)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"images.*images.*SpreadCanvas\" .)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 5 -B 5 -n \"images.*=.*images\" BookEditor.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 20 \"interface.*SpreadCanvasProps\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 5 -B 5 -n \"img.*src.*=|image.*src.*=\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 10 -B 5 -n \"<img.*src\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 10 -B 5 -n \"backgroundImage.*url\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 20 -B 5 -n \"previewUrl|thumbnailUrl.*img\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 10 -n \"backgroundImage.*url.*image.*url\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"url.*image.*previewUrl|url.*image.*thumbnailUrl|backgroundImage.*\\`url\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"backgroundImage.*url\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"imageRefreshTimestamps\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 5 -B 5 -n \"setImageRefreshTimestamps\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 10 -B 5 -n \"projectBackgroundImage.*SpreadCanvas|project.*background.*SpreadCanvas\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"projectBackgroundImage\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -n \"displayableProjectBackgroundSrc\" SpreadCanvas.tsx)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg -A 5 -n \"src.*imageSrcUrl\" SpreadCanvas.tsx)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "Bash(git add:*)", "Bash(npx tsc:*)", "Bash(ls:*)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg \"focusTray\" src/components/BookEditor.tsx -A 10 -B 10)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg \"selectedTab.*templates\" src/components/BookEditor.tsx -A 5 -B 5)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg \"setSelectedTab\" src/components/BookEditor.tsx -A 3 -B 3)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg \"useEffect.*selectedTab\" src/components/BookEditor.tsx -A 10 -B 2)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg \"selectedTab\" src/components/BookEditor.tsx -A 5 -B 5)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg \"onClick.*setSelectedTab.*templates\" src/components/BookEditor.tsx -A 10 -B 5)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-darwin/rg \"Templates.*button\" src/components/BookEditor.tsx -A 5 -B 5)", "<PERSON><PERSON>(python3:*)"], "deny": []}}