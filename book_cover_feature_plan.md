# Book Cover Feature Implementation Plan

This document outlines the plan to implement a new "Book Cover" feature in the BookProofs Designer application.

## User Requirements Summary

*   Add an option in "Settings" for a "Book Cover".
*   Allow users to drag and drop an image for the cover, similar to the "Project Background" image functionality.
*   When an image is dropped, create a new "Cover" spread at the start of the book.
*   The Cover spread should be exactly 1/2 the width of the project's regular spread canvas (i.e., the width of a single page).
*   The cover image should be single, full-bleed, but allow for zoom and pan adjustments by the user.
*   The cover thumbnail in the `SpreadsTray` should represent a single page (half-width of a normal spread thumbnail).
*   Page numbering for the book should begin *after* the cover.
*   If a new image is dropped for the cover when one already exists, the new image should automatically replace the old one.

## Implementation Phases

### Phase 1: Data Structure & State Management

1.  **Modify `Spread` Interface**:
    *   **File**: [`src/components/SpreadsTray.tsx`](src/components/SpreadsTray.tsx)
    *   **Change**: Add an optional field `spreadType?: 'cover' | 'spread';` to the `Spread` interface.
        *   `'cover'`: Indicates a book cover.
        *   `'spread'` or `undefined`: Indicates a regular two-page spread.

2.  **Update `BookEditor.tsx` State**:
    *   **File**: [`src/components/BookEditor.tsx`](src/components/BookEditor.tsx)
    *   **Add State Variables**:
        *   `bookCoverImage: string | null;` (URL for displaying the cover image)
        *   `bookCoverImagePath: string | null;` (Original file path of the cover image)
    *   **Add Handler Function**:
        *   `handleSetBookCoverImage(imageUrl: string | null, imagePath: string | null)`: This function will manage updating the state and the cover spread itself.

### Phase 2: Settings Panel UI & Logic

1.  **Update `SettingsPanelProps`**:
    *   **File**: [`src/components/SettingsPanel.tsx`](src/components/SettingsPanel.tsx)
    *   **Extend Interface**: Add the following to `SettingsPanelProps`:
        *   `bookCoverImage: string | null;`
        *   `bookCoverImagePath: string | null;`
        *   `handleSetBookCoverImage: (imageUrl: string | null, imagePath: string | null) => void;`

2.  **Implement in `SettingsPanel.tsx`**:
    *   Add a new UI section titled "Book Cover".
    *   Implement a drag-and-drop area, mirroring the "Project Background Image" functionality.
    *   Create `handleCoverDragOver` and `handleCoverDrop` functions, similar to existing `handleDragOver` and `handleDrop`.
    *   `handleCoverDrop` will invoke `props.handleSetBookCoverImage`.
    *   Include a "Clear Book Cover" button.

### Phase 3: Cover Spread Creation & Management in `BookEditor.tsx`

1.  **Logic for `handleSetBookCoverImage` in `BookEditor.tsx`**:
    *   **If a new cover image is provided (`imageUrl` is not null)**:
        *   Check if a cover spread (`spreadType === 'cover'`) already exists.
        *   **If cover exists**: Update the `imageId` in the `ImagePlacement` of the existing cover spread. The `ImageTransform` should be preserved unless a decision is made to reset it (current plan: preserve).
        *   **If no cover exists**:
            *   Create a new `Spread` object:
                *   `id`: e.g., `cover-${Date.now()}`
                *   `spreadType`: `'cover'`
                *   `templateId`: `__cover_template__` (a special identifier for a fixed, single-image layout).
                *   `images`: `[{ placeholderId: 'cover-placeholder-1', imageId: newImageId, transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'cover' } }]`
            *   Insert this new cover spread at the *beginning* of the `spreads` array.
        *   Update `bookCoverImage` and `bookCoverImagePath` state.
        *   Mark project as dirty (`setIsDirty(true)`).
        *   Save state for undo (`saveStateForUndo()`).
    *   **If the cover is cleared (`imageUrl` is null)**:
        *   Remove the cover spread from the `spreads` array.
        *   Clear `bookCoverImage` and `bookCoverImagePath` state.
        *   Mark project as dirty and save state for undo.

### Phase 4: Rendering the Cover

1.  **Update `SpreadCanvas.tsx`**:
    *   If `currentSpread.spreadType === 'cover'`:
        *   The canvas width for this spread will be `props.spreadDimensionsPt.width` (single page width).
        *   The layout will be a single, full-bleed image placeholder based on `__cover_template__`.
        *   **Enable zoom and pan interactions for the cover image**, updating its `ImageTransform` via `handleUpdateImageTransform`.

2.  **Update `SpreadsTray.tsx` (Thumbnail Rendering)**:
    *   The `SpreadThumbnailPreview` component will recognize `spreadType === 'cover'`.
    *   Cover spread thumbnails will be rendered to represent a single page (e.g., half the width of regular spread thumbnails).

3.  **Update `allSpreadLayouts` Calculation in `BookEditor.tsx`**:
    *   When processing a spread with `spreadType === 'cover'`, the layout calculation (e.g., `adjustTemplateForGap` or similar logic for covers) will use a target width of `aspectRatio.widthPt` (single page width).
    *   The `__cover_template__` will define a single placeholder spanning 100% of this single-page area.

### Phase 5: PDF Export

1.  **Update `handleExportPdf` in `BookEditor.tsx`**:
    *   When iterating through `spreadsForExport`, if a spread has `spreadType === 'cover'`:
        *   Add a PDF page with dimensions `pageWidthPt` (single page width) by `pageHeightPt`.
        *   Draw the cover image onto this single-page dimension, respecting its `ImageTransform`.

## Visual Flow (Mermaid Diagram)

```mermaid
graph TD
    A[User Action: Drop Image on New Cover Setting] --> B{SettingsPanel: handleCoverDrop};
    B --> C{BookEditor: handleSetBookCoverImage};
    C --> D[Update bookCoverImage State];
    C --> E{Modify 'spreads' Array};
    E --> E1[Add/Update Cover Spread (type: 'cover') at Start];
    E1 --> F[Cover Spread Data (type, imageId, transform)];

    subgraph "Data Structures & State"
        K[Spread Interface in SpreadsTray.tsx] -- Add spreadType --> F;
        L[BookEditor.tsx State] -- Add bookCoverImage/Path state --> D;
    end

    subgraph "Rendering Logic"
        F --> G{SpreadCanvas.tsx};
        G --> G1[If spread.spreadType is 'cover'];
        G1 --> G2[Render as Single Page Width, Enable Zoom/Pan];

        F --> H{SpreadsTray.tsx Thumbnail};
        H --> H1[If spread.spreadType is 'cover'];
        H1 --> H2[Render Thumbnail as Single Page Width];

        F --> I{BookEditor.tsx: allSpreadLayouts calc};
        I --> I1[If spread.spreadType is 'cover'];
        I1 --> I2[Calculate Layout for Single Page Width using __cover_template__];
    end

    subgraph "Export Logic"
        F --> J{BookEditor.tsx: PDF Export};
        J --> J1[If spread.spreadType is 'cover'];
        J1 --> J2[Export Cover as Single PDF Page];
    end
```

## Open Questions Answered

1.  **Cover Layout**: Single, full-bleed image with zoom/pan capabilities.
2.  **Cover in Spreads Tray**: Thumbnail will be half-width, representing a single page.
3.  **Page Numbering**: Numbering starts on the first two-page spread *after* the cover.
4.  **Replacing Cover**: New image drop automatically replaces the existing cover.