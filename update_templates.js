const fs = require('fs');
const path = require('path');

const layoutsDir = './src/lib/templates/layouts';
const files = fs.readdirSync(layoutsDir).filter(file => file.endsWith('.ts'));

files.forEach(file => {
  const filePath = path.join(layoutsDir, file);
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Replace template objects that don't have classification
  content = content.replace(
    /(\s+images:\s*\[\s*(?:[^[\]]*(?:\[[^\]]*\])*[^[\]]*)*\]\s*)\n(\s*})/g,
    (match, images, closingBrace) => {
      if (!match.includes('classification:')) {
        return `${images},\n    classification: 'ALL'\n${closingBrace}`;
      }
      return match;
    }
  );
  
  fs.writeFileSync(filePath, content);
  console.log(`Updated ${file}`);
});