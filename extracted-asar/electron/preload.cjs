// electron/preload.js
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose a controlled API to the Renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // Function the Renderer can call to request opening the image dialog
  openImageDialog: () => ipc<PERSON>enderer.invoke('dialog:openImage'),

  // Function to trigger "Save As..." (always shows dialog)
  saveProjectAs: (data) => ipcRenderer.invoke('dialog:saveProjectAs', data), // Renamed handler

  // Function to trigger "Save" (overwrites current file if path exists)
  projectSaveFile: (data) => ipcRenderer.invoke('project:saveFile', data), // New handler for direct save
  // Function to trigger loading a project file
  // Load project data given a specific file path
  loadProject: (filePath) => ipcRenderer.invoke('dialog:loadProject', filePath), // Returns { success, data?, filePath?, projectDirectoryPath?, error?, message?, canceled? }
  // Function to show confirmation dialog for reusing an image
  confirmImageReuse: (imageName) => ipc<PERSON><PERSON><PERSON>.invoke('dialog:confirmImageReuse', imageName),

  // NEW: Function for generic confirmation dialog
  confirmDialog: (options) => ipcRenderer.invoke('dialog:confirm', options),

  // --- Start Page Dialog Handlers ---
  // Show dialog to create a new .bookproof project file
  createNewProjectFile: () => ipcRenderer.invoke('dialog:create-new-project-file'), // Returns { success, filePath?, projectDirectoryPath?, canceled?, error? }
  // Show dialog to select an existing .bookproof file to open
  openProjectFile: () => ipcRenderer.invoke('dialog:open-project-file'), // Returns { success, filePath?, canceled?, error? }
  // Inform main process of the active project directory (after save/load or new project setup)
  setProjectDirectory: (directoryPath) => ipcRenderer.invoke('project:set-directory', directoryPath), // Returns { success, path?, error? }

  // --- Listeners for Main Process events ---
  // Listener for Undo shortcut
  onTriggerUndo: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-undo', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-undo', listener);
    };
  },
  // Listener for Redo shortcut
  onTriggerRedo: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-redo', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-redo', listener);
    };
  }, // Correct comma after onTriggerRedo
  // Listener for Load Project menu item
  onTriggerLoad: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-load', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-load', listener);
    };
  },
  // Listener for New Project menu item
  onTriggerNewProject: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-new-project', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-new-project', listener);
    };
  },
  // Listener for consolidated import progress (metadata, thumbnails, previews)
  onImportProgress: (callback) => {
    const listener = (event, data) => callback(data); // Pass data { stage, current, total } or null
    ipcRenderer.on('import-progress', listener);
    return () => {
      ipcRenderer.removeListener('import-progress', listener);
    };
  },
  // Listener for individual preview completion (still needed to update image state)
  onPreviewGenerated: (callback) => {
    const listener = (event, data) => callback(data); // Pass data { originalPath, previewUrl }
    ipcRenderer.on('preview-generated', listener);
    return () => {
      ipcRenderer.removeListener('preview-generated', listener);
    };
  },

  // Functions for native fullscreen control
  setNativeFullScreen: (flag) => ipcRenderer.invoke('window:setFullScreen', flag),
  isNativeFullScreen: () => ipcRenderer.invoke('window:isFullScreen'),
  // Function to load and pre-process image data for PDF export
  loadImageData: (filePath, options) => ipcRenderer.invoke('load-image-data', filePath, options),
  // Function to request window resize
  setWindowSize: (size) => ipcRenderer.invoke('window:setSize', size),
  // Function to reveal a file in the OS file explorer
  showItemInFolder: (filePath) => ipcRenderer.invoke('shell:showItemInFolder', filePath),
  // Function to attempt opening a file specifically in Photoshop
  editInPhotoshop: (filePath) => ipcRenderer.invoke('app:editInPhotoshop', filePath), // Invokes the main process handler
  // Listener for file updates from the main process (after external edit)
  onFileUpdated: (callback) => {
    const listener = (event, data) => callback(data); // data: { originalPath, thumbnailUrl, previewUrl }
    ipcRenderer.on('app:file-updated', listener);
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('app:file-updated', listener);
    };
  },
   // Listener for file update errors from the main process
   onFileUpdateError: (callback) => {
    const listener = (event, data) => callback(data); // data: { originalPath, error }
    ipcRenderer.on('app:file-update-error', listener);
    return () => {
      ipcRenderer.removeListener('app:file-update-error', listener);
    };
  }, // Add comma here
  // Listener for Save shortcut
  onTriggerSave: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-save', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-save', listener);
    };
  }, // Add comma here
  // Function to get image data as Data URL
  getImageDataUrl: (filePath) => ipcRenderer.invoke('image:get-data-url', filePath)
}); // Correctly closing the exposeInMainWorld object and call

console.log('Preload script loaded.'); // For debugging