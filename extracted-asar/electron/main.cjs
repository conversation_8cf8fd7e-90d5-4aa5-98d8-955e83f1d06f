// electron/main.js
const { app, BrowserWindow, ipcMain, dialog, protocol, Menu, shell, session } = require('electron'); // Import Menu, shell, and session
const path = require('node:path');
const fs = require('node:fs');
const fsPromises = require('node:fs').promises; // Use promises for async fs operations
const crypto = require('node:crypto'); // For unique filenames
const { execFile } = require('node:child_process'); // Import execFile
const sharp = require('sharp'); // Import sharp
const exifr = require('exifr'); // Import exifr
const chokidar = require('chokidar'); // Import chokidar for file watching

// Determine if running in development or production
const isDev = process.env.NODE_ENV !== 'production';

// Top-level Maps for watcher and debounce management
const fileWatchers = new Map(); // Map to store active chokidar watchers (filePath -> watcher instance)
const debounceTimers = new Map(); // Map to store debounce timers (filePath -> timerId)

function createWindow() {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200, // Adjust width as needed
    height: 800, // Adjust height as needed
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'), // Use the preload script
      nodeIntegration: false, // Keep false for security
      contextIsolation: true, // Keep true for security
    },
  });

  // Load the index.html of the app.
  if (isDev) {
    // Load the Vite dev server URL
    // Make sure this port matches your Vite config (default is 5173)
    mainWindow.loadURL('http://localhost:8080'); // Updated port to match Vite server
    // Open the DevTools automatically if not packaged (i.e., during development)
    if (!app.isPackaged) {
      mainWindow.webContents.openDevTools();
    }
  } else {
    // Load the built index.html file
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // Optional: Maximize window on start
  // mainWindow.maximize();
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Register the custom protocol *after* app is ready, *before* creating window
  protocol.registerFileProtocol('safe-file', (request, callback) => {
    const rawUrl = request.url; // Keep original for logging
    try {
      // Use URL constructor to parse. Base URL is needed but only pathname matters.
      const parsedUrl = new URL(rawUrl);
      const filePath = parsedUrl.pathname; // Gets the path part, e.g., /path/to/cache/hash.webp

      // Decode the pathname specifically
      const decodedPath = decodeURI(filePath);

      // Normalize the decoded path
      const normalizedPath = path.normalize(decodedPath);

      // Check if the file actually exists and is readable *after* normalization
      fs.access(normalizedPath, fs.constants.R_OK, (err) => {
         if (err) {
            console.error(`safe-file: File not found or not readable: ${normalizedPath} (from URL: ${rawUrl})`);
            callback({ error: -6 }); // FILE_NOT_FOUND
         } else {
            callback({ path: normalizedPath }); // Return the valid, normalized path
         }
      });

    } catch (error) {
      console.error(`Failed to process path for safe-file protocol: ${rawUrl}`, error);
      callback({ error: -6 }); // FILE_NOT_FOUND or other processing error
    }
  });

  // --- Set Content Security Policy ---
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    // Define the CSP string
    // NOTE: 'unsafe-inline' for style-src is often needed for UI libraries, but try removing it if possible.
    // NOTE: connect-src needs the Vite dev server URL during development.
    // Define CSP parts conditionally
    const scriptSrc = isDev ? "'self' 'unsafe-inline' https://cdn.gpteng.co" : "'self' https://cdn.gpteng.co"; // Allow inline scripts in dev + gpteng script always
    const connectSrc = isDev ? "'self' http://localhost:8080 ws://localhost:8080" : "'self'"; // Allow Vite HMR in dev

    const csp = [
      "default-src 'self'",
      `script-src ${scriptSrc}`, // Use the conditional script-src
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com", // Allow Google Fonts stylesheets
      "font-src 'self' https://fonts.gstatic.com", // Allow Google Fonts font files
      "img-src 'self' data: safe-file:", // Allows images from origin, data URLs, and safe-file: protocol
      `connect-src ${connectSrc}`, // Use the conditional connect-src
    ].join('; ');

    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [csp]
      }
    });
  });
  // --- End CSP Setup ---

  let currentProjectPath = null; // Variable to store the active project directory path
  let currentProjectFilePath = null; // Variable to store the full path to the active .bp file
  // These Maps are now declared at the top level

  // Set up IPC handler for opening the image dialog
  // --- Image Processing Logic ---
  const THUMBNAIL_SIZE = 200; // px for list thumbnails
  const PREVIEW_SIZE = 2000; // px for canvas previews (long edge)
  const THUMBNAIL_CACHE_DIR = path.join(app.getPath('userData'), 'thumbnail_cache');
  const PREVIEW_CACHE_DIR = path.join(app.getPath('userData'), 'preview_cache'); // New cache dir

  // Ensure cache directories exist
  if (!fs.existsSync(THUMBNAIL_CACHE_DIR)) {
    fs.mkdirSync(THUMBNAIL_CACHE_DIR, { recursive: true });
  }
  if (!fs.existsSync(PREVIEW_CACHE_DIR)) { // Ensure preview cache dir exists
    fs.mkdirSync(PREVIEW_CACHE_DIR, { recursive: true });
  }

  // IPC Handler for Renderer to set the project path (e.g., after creating new or loading existing)
  ipcMain.handle('project:set-directory', (event, directoryPath) => {
    if (directoryPath && typeof directoryPath === 'string') {
      // Ensure it's a valid directory before setting
      try {
        const stats = fs.statSync(directoryPath);
        if (stats.isDirectory()) {
          currentProjectPath = directoryPath; // Store the actual directory path
          console.log(`[Main Process] Project directory set to: ${currentProjectPath}`);
          return { success: true, path: currentProjectPath };
        } else {
           console.error('[Main Process] Invalid path (not a directory) for project:set-directory:', directoryPath);
           currentProjectPath = null;
           return { success: false, error: 'Path is not a directory.' };
        }
      } catch (error) {
         console.error('[Main Process] Error accessing path for project:set-directory:', directoryPath, error);
         currentProjectPath = null;
         return { success: false, error: `Failed to access path: ${error.message}` };
      }
    } else {
      console.error('[Main Process] Invalid path type received for project:set-directory:', directoryPath);
      currentProjectPath = null; // Reset if invalid path received
      return { success: false, error: 'Invalid directory path provided (must be a string).' };
    }
  });

  async function generateThumbnail(originalPath) {
    try {
      // Create a unique filename based on hash of original path to avoid collisions
      const hash = crypto.createHash('sha256').update(originalPath).digest('hex');
      const thumbnailFilename = `${hash}.webp`;

      if (!currentProjectPath) {
        console.error('Error: Project directory not set. Cannot generate thumbnail.');
        return null; // Or throw an error
      }
      const projectThumbnailCacheDir = path.join(currentProjectPath, 'thumbnail_cache');
      if (!fs.existsSync(projectThumbnailCacheDir)) {
        fs.mkdirSync(projectThumbnailCacheDir, { recursive: true });
      }
      const thumbnailPath = path.join(projectThumbnailCacheDir, thumbnailFilename);

      // Check if thumbnail already exists
      if (!fs.existsSync(thumbnailPath)) {
        console.log(`Generating thumbnail for: ${originalPath}`);
        await sharp(originalPath)
          .resize(THUMBNAIL_SIZE, THUMBNAIL_SIZE, {
            fit: 'inside', // Preserve aspect ratio, fit within bounds
            withoutEnlargement: true, // Don't enlarge small images
          })
          .webp({ quality: 80 }) // Adjust quality as needed
          .toFile(thumbnailPath);
      } else {
         // console.log(`Using cached thumbnail for: ${originalPath}`);
      }
      // Return the path using the custom protocol
      return `safe-file://${thumbnailPath}`;
    } catch (error) {
      console.error(`Failed to generate thumbnail for ${originalPath}:`, error);
      return null; // Indicate failure for this specific image
    }
  }
  // --------------------------------

  // --- Preview Generation Logic ---
  // --------------------------------

  async function generatePreview(originalPath) {
    try {
      const hash = crypto.createHash('sha256').update(originalPath).digest('hex');
      const previewFilename = `${hash}.webp`;

      if (!currentProjectPath) {
        console.error('Error: Project directory not set. Cannot generate preview.');
        return null; // Or throw an error
      }
      const projectPreviewCacheDir = path.join(currentProjectPath, 'preview_cache');
       if (!fs.existsSync(projectPreviewCacheDir)) {
        fs.mkdirSync(projectPreviewCacheDir, { recursive: true });
      }
      const previewPath = path.join(projectPreviewCacheDir, previewFilename);

      // Check if preview already exists
      if (!fs.existsSync(previewPath)) {
        console.log(`[Main Process] Generating preview for: ${path.basename(originalPath)}`);
        await sharp(originalPath)
          .resize(PREVIEW_SIZE, PREVIEW_SIZE, { // Use PREVIEW_SIZE
            fit: 'inside', // Preserve aspect ratio
            withoutEnlargement: true, // Don't enlarge small images
          })
          .webp({ quality: 85 }) // Slightly higher quality for previews
          .toFile(previewPath);

        // Add extra check after writing
        if (!fs.existsSync(previewPath)) {
          console.error(`[Main Process] Failed to verify preview file existence after write: ${previewPath}`);
          return null; // Indicate failure
        }
      } else {
        // console.log(`Using cached preview for: ${originalPath}`);
      }
      return `safe-file://${previewPath}`;
    } catch (error) {
      console.error(`Failed to generate preview for ${originalPath}:`, error);
      return null;
    }
  }
  // --------------------------------

  ipcMain.handle('dialog:openImage', async (event) => { // Added event parameter for potential future use
    console.log('[Main Process] Entered dialog:openImage handler.'); // Log entry
    const { canceled, filePaths } = await dialog.showOpenDialog({
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'webp', 'heic', 'avif'] } // Added more types
      ]
    });

    if (canceled || !filePaths || filePaths.length === 0) {
      console.log('[Main Process] Image dialog canceled or no files selected.');
      return [];
    }

    console.log(`[Main Process] Selected file paths: ${JSON.stringify(filePaths)}`); // Log selected paths
    // Send initial progress update
    if (event.sender && !event.sender.isDestroyed()) {
      event.sender.send('import-progress', { stage: 'reading_metadata', current: 0, total: filePaths.length });
    }

    // Process metadata and generate thumbnails
    const results = await Promise.all(filePaths.map(async (originalPath, index) => { // Add index
      try {
        // console.log(`[Main Process] Processing image: ${path.basename(originalPath)}`); // Log start of processing

        // Get metadata (including dimensions) using sharp
        const imageMetadata = await sharp(originalPath).metadata();
        // console.log(`[Main Process] Sharp metadata for ${path.basename(originalPath)}:`, imageMetadata); // Log raw metadata
        const naturalWidth = imageMetadata.width;
        const naturalHeight = imageMetadata.height;
        // console.log(`[Main Process] Extracted dimensions for ${path.basename(originalPath)}: ${naturalWidth}x${naturalHeight}`); // Log extracted dimensions

        // Check if dimensions were successfully retrieved
        if (!naturalWidth || !naturalHeight) {
          console.error(`[Main Process] Failed to get dimensions for ${path.basename(originalPath)}`);
          return null; // Skip if dimensions couldn't be read
        }
        // Send metadata progress update (optional, maybe too frequent)
        // if (event.sender && !event.sender.isDestroyed()) {
        //   event.sender.send('import-progress', { stage: 'reading_metadata', current: index + 1, total: filePaths.length });
        // }

        const thumbnailUrl = await generateThumbnail(originalPath);
        if (!thumbnailUrl) {
           console.log(`[Main Process] Thumbnail generation failed for ${path.basename(originalPath)}`);
           return null; // Skip if thumbnail failed
        }
        // Send thumbnail progress update
        if (event.sender && !event.sender.isDestroyed()) {
          event.sender.send('import-progress', { stage: 'generating_thumbnails', current: index + 1, total: filePaths.length });
        }

        let rating = 0; // Default rating
        try {
          // Parse relevant metadata blocks (including XMP where 'Rating' was found)
          // Removed 'pick' option as it seemed unreliable for 'Rating'
          const metadata = await exifr.parse(originalPath, {
            xmp: true,    // Ensure XMP block is parsed
            iptc: true,   // Include IPTC just in case
            exif: true,   // Include EXIF just in case
            icc: false,   // Exclude ICC profile data
            // No 'pick' option - get all tags from specified segments
          });
          rating = metadata?.Rating ?? 0; // Access Rating directly from the parsed result
          // console.log(`[Main Process] Parsed Rating for ${path.basename(originalPath)}: ${rating}`); // Optional: Keep for confirmation
        } catch (parseError) {
           console.error(`[Main Process] Error parsing metadata for ${path.basename(originalPath)}:`, parseError);
           // Keep rating as 0 if parsing fails
        }

        return {
          originalPath: originalPath,
          thumbnailUrl: thumbnailUrl,
          name: path.basename(originalPath), // Keep original filename for display
          rating: rating, // Include the extracted rating
          naturalWidth: naturalWidth, // Add natural width
          naturalHeight: naturalHeight // Add natural height
        };
      } catch (error) {
         console.error(`Failed processing image ${originalPath}:`, error);
         return null; // Exclude images that failed processing
      }
    }));

    const initialResults = results.filter(result => result !== null);

    // --- Trigger background preview generation (don't await) ---
    // Use setImmediate to ensure this runs after the current event loop tick completes
    // and the initial results have been returned to the renderer.
    setImmediate(() => {
      // Pass only the necessary info (originalPath) to the background task
      const pathsToProcess = initialResults.map(res => res.originalPath);
      generatePreviewsInBackground(pathsToProcess, event.sender); // Pass sender for sending updates
    });
    // ---------------------------------------------------------

    return initialResults; // Return initial results immediately (without previewUrl)
  });

  // --- Background Preview Generation Function ---
  async function generatePreviewsInBackground(originalPaths, webContents) {
    console.log(`[Background] Starting preview generation for ${originalPaths.length} images.`);
    for (let i = 0; i < originalPaths.length; i++) {
      const originalPath = originalPaths[i];
      try {
        const previewUrl = await generatePreview(originalPath); // Await generation for this image

        // Send update back to the specific renderer window that initiated the request
        if (webContents && !webContents.isDestroyed()) {
          webContents.send('preview-generated', {
            originalPath: originalPath,
            previewUrl: previewUrl // Will be null if generation failed
          });
          // Send preview progress update (using the new channel name)
          webContents.send('import-progress', {
            stage: 'generating_previews', // Add stage info
            current: i + 1,
            total: originalPaths.length
          });
        }
      } catch (error) {
        console.error(`[Background] Error generating preview for ${originalPath}:`, error);
        // Optionally send an error status back to renderer if needed
        if (webContents && !webContents.isDestroyed()) {
           webContents.send('preview-generated', {
             originalPath: originalPath,
             previewUrl: null // Indicate failure
           });
           // Still send preview progress update (using the new channel name)
           webContents.send('import-progress', {
             stage: 'generating_previews', // Add stage info
             current: i + 1,
             total: originalPaths.length
           });
        }
      }
    }
    console.log(`[Background] Finished preview generation.`);
    // Send final completion message (using the new channel name)
    if (webContents && !webContents.isDestroyed()) {
       webContents.send('import-progress', null); // Send null to indicate completion
    }
  }
  // -----------------------------------------
  // IPC handler for "Save As..." - ALWAYS shows the dialog
  ipcMain.handle('dialog:saveProjectAs', async (event, data) => {
    const mainWindow = BrowserWindow.fromWebContents(event.sender);
    if (!mainWindow) return { success: false, error: 'No active window' };

    const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, {
      title: 'Save Project As',
      defaultPath: currentProjectFilePath || 'My Book Project.bp', // Suggest current filename or default
      filters: [
        { name: 'Book Proofs Project', extensions: ['bp'] }
      ]
    });

    if (!canceled && filePath) {
      try {
        const jsonData = JSON.stringify(data, null, 2); // Pretty print JSON
        fs.writeFileSync(filePath, jsonData, 'utf-8');
        currentProjectPath = path.dirname(filePath); // Update directory path
        currentProjectFilePath = filePath; // Update the active file path
        console.log(`Project saved AS: ${filePath}. Project directory set to: ${currentProjectPath}`);
        // Return the new file path so the renderer can update its state
        return { success: true, filePath: filePath };
      } catch (error) {
        console.error('Failed to save project (Save As):', error);
        return { success: false, error: error.message };
      }
    }
    // Return success: false if dialog was canceled, but include canceled flag
    return { success: false, canceled: true };
  });

  // IPC handler for "Save" - overwrites the current file if it exists
  ipcMain.handle('project:saveFile', async (event, data) => {
    if (!currentProjectFilePath) {
      console.error('[project:saveFile] Attempted to save without an active file path.');
      // Indicate that "Save As" is needed
      return { success: false, error: 'No active file path. Use Save As.', needsSaveAs: true };
    }

    try {
      const jsonData = JSON.stringify(data, null, 2);
      fs.writeFileSync(currentProjectFilePath, jsonData, 'utf-8');
      console.log(`Project saved (overwrite): ${currentProjectFilePath}`);
      return { success: true, filePath: currentProjectFilePath }; // Return path for consistency
    } catch (error) {
      console.error(`Failed to save project (overwrite) to ${currentProjectFilePath}:`, error);
      return { success: false, error: error.message };
    }
  });

  // --- New Dialog Handlers for StartPage ---

  // Handler to CREATE a NEW project file (.bookproof)
  ipcMain.handle('dialog:create-new-project-file', async () => {
    const mainWindow = BrowserWindow.getFocusedWindow();
    if (!mainWindow) return { success: false, error: 'No active window' };

    const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, {
      title: 'Create New Project File',
      defaultPath: 'Untitled Book.bp', // Suggest a filename with new extension
      filters: [{ name: 'Book Proofs Project', extensions: ['bp'] }]
    });

    if (canceled || !filePath) {
      return { success: false, canceled: true };
    }

    try {
      // Create a minimal initial project file (can be empty or have basic structure)
      const initialData = JSON.stringify({
        // Add any essential starting fields if needed, otherwise empty is fine
        // aspectRatio: null, // Will be set in setup step
        images: [],
        spreads: [],
        imageGap: 2, // Default gap
      }, null, 2);
      fs.writeFileSync(filePath, initialData, 'utf-8');

      // Set the project path internally
      currentProjectPath = path.dirname(filePath);
      currentProjectFilePath = filePath; // Store the full file path
      console.log(`[Main Process] New project file created: ${filePath}. Project directory set to: ${currentProjectPath}. Active file path set.`);

      // Return success, the full file path, and the directory path
      return { success: true, filePath: filePath, projectDirectoryPath: currentProjectPath };

    } catch (error) {
      console.error('Failed to create initial project file:', error);
      currentProjectPath = null; // Reset path on failure
      return { success: false, error: `Failed to create project file: ${error.message}` };
    }
  });

  // Handler to select an EXISTING project file (.bookproof)
  ipcMain.handle('dialog:open-project-file', async () => {
    const mainWindow = BrowserWindow.getFocusedWindow();
    if (!mainWindow) return { success: false, error: 'No active window' };

    const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
      title: 'Open Project File',
      properties: ['openFile'],
      filters: [{ name: 'Book Proofs Project', extensions: ['bp'] }]
    });

    if (canceled || !filePaths || filePaths.length === 0) {
      return { success: false, canceled: true };
    }

    const filePath = filePaths[0];
    // Don't load data or set currentProjectPath here. Renderer will call loadProject with this path.
    console.log(`[Main Process] Existing project file selected by user: ${filePath}`);
    return { success: true, filePath: filePath };
  });

  // --- End New Dialog Handlers ---

  // IPC handler for loading the project
  // IPC handler for loading the project DATA (requires filePath)
  ipcMain.handle('dialog:loadProject', async (event, filePath) => { // filePath argument is required
    // Removed internal dialog.showOpenDialog call

    if (!filePath || typeof filePath !== 'string') {
        console.error('[Main Process] loadProject called without a valid filePath.');
        return { success: false, error: true, message: 'No project file path provided.' };
    }

    // Proceed with loading using the provided filePath
    try {
        const jsonData = fs.readFileSync(filePath, 'utf-8');
        const data = JSON.parse(jsonData);
        currentProjectPath = path.dirname(filePath); // Set project directory path
        currentProjectFilePath = filePath; // Set the active file path
        console.log(`Project loaded from: ${filePath}. Project directory set to: ${currentProjectPath}. Active file path set.`);
        // Return data, file path, and the derived directory path
        return { success: true, data: data, filePath: filePath, projectDirectoryPath: currentProjectPath };
      } catch (error) {
        console.error('Failed to load or parse project:', error);
        // Return a structured error object
        currentProjectPath = null;
        return { success: false, error: true, message: error.message };
      }
    // Catch block remains the same (handles read/parse errors)
  }); // End of dialog:loadProject

  // IPC handler to get image data as Data URL for preview modal
  ipcMain.handle('image:get-data-url', async (event, filePath) => {
    console.log(`[Main Process] Received request for data URL: ${filePath}`); // Log request
    if (!filePath || typeof filePath !== 'string') {
      return { success: false, error: 'Invalid file path provided.' };
    }

    try {
      // Basic Security Check: Ensure file exists and is readable before proceeding.
      // WARNING: In a real application, add more robust checks here to ensure
      // only expected/allowed files are accessed (e.g., check if path is within
      // the project directory or known cache locations). Accessing arbitrary paths
      // passed from the renderer can be a security risk.
      await fsPromises.access(filePath, fs.constants.R_OK);

      const fileBuffer = await fsPromises.readFile(filePath);
      const base64Data = fileBuffer.toString('base64');

      // Determine MIME type from file extension (simple approach)
      const ext = path.extname(filePath).toLowerCase();
      let mimeType = 'application/octet-stream'; // Default
      if (ext === '.jpg' || ext === '.jpeg') {
        mimeType = 'image/jpeg';
      } else if (ext === '.png') {
        mimeType = 'image/png';
      } else if (ext === '.webp') {
        mimeType = 'image/webp';
      } else if (ext === '.gif') {
        mimeType = 'image/gif';
      } else if (ext === '.heic') {
        mimeType = 'image/heic'; // Common MIME type for HEIC
      } else if (ext === '.avif') {
         mimeType = 'image/avif';
      }
      // Add more types as needed

      const dataUrl = `data:${mimeType};base64,${base64Data}`;
      console.log(`[Main Process] Successfully generated data URL for: ${filePath} (MIME: ${mimeType})`); // Log success
      return { success: true, dataUrl: dataUrl };

    } catch (error) {
      console.error(`[Main Process] Failed to get data URL for ${filePath}:`, error);
      // Check for specific file not found error
      if (error.code === 'ENOENT') {
         return { success: false, error: `File not found: ${filePath}` };
      }
      return { success: false, error: `Failed to read file or generate data URL: ${error.message}` };
    }
  });

  // Removed incorrect project:request-location handler

  // IPC handler for confirming image reuse
  ipcMain.handle('dialog:confirmImageReuse', async (event, imageName) => {
    const mainWindow = BrowserWindow.getFocusedWindow(); // Get the window to make dialog modal
    if (!mainWindow) return false; // Should not happen if called from renderer

    const { response } = await dialog.showMessageBox(mainWindow, { // Make modal to window
      type: 'question',
      buttons: ['Cancel', 'Use Image'],
      defaultId: 1, // Index of the default button ('Use Image')
      title: 'Confirm Image Reuse',
      message: `The image "${imageName}" has already been used in this project.`,
      detail: 'Are you sure you want to use it again?',
      cancelId: 0 // Index of the cancel button
    });
    return response === 1; // Return true if 'Use Image' was clicked
  });

  // NEW: IPC handler for generic confirmation dialog
  ipcMain.handle('dialog:confirm', async (event, options) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (!window) {
      console.error("Could not find browser window for confirm dialog.");
      return options.cancelId ?? 0; // Return cancel index if window not found
    }
    try {
      const { response } = await dialog.showMessageBox(window, {
        type: options.type ?? 'question', // Default to question type
        buttons: options.buttons ?? ['Cancel', 'OK'], // Default buttons
        title: options.title ?? 'Confirm', // Default title
        message: options.message, // Required
        detail: options.detail,
        defaultId: options.defaultId,
        cancelId: options.cancelId,
        noLink: true // Often desirable for confirmation dialogs
      });
      return response; // Return the index of the clicked button
    } catch (error) {
      console.error("Error showing confirmation dialog:", error);
      return options.cancelId ?? 0; // Return cancel index on error
    }
  });

  // --- Window Control Handlers ---
  ipcMain.handle('window:setFullScreen', (event, flag) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (window) {
      window.setFullScreen(flag);
    }
  });

  ipcMain.handle('window:isFullScreen', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    return window ? window.isFullScreen() : false;
  });

  // --- Window Resizing Handler ---
  const defaultWindowSize = { width: 1200, height: 800 };
  const largeWindowSize = { width: 1600, height: 1000 }; // Define the larger size

  ipcMain.handle('window:setSize', (event, size) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (window) {
      let targetSize;
      if (size === 'large') {
        targetSize = largeWindowSize;
        console.log(`[Main Process] Resizing window to large: ${targetSize.width}x${targetSize.height}`);
      } else { // Default to 'default' size
        targetSize = defaultWindowSize;
        console.log(`[Main Process] Resizing window to default: ${targetSize.width}x${targetSize.height}`);
      }
      // Use setBounds for potentially better control, or setSize
      // setSize(width, height, animate)
      window.setSize(targetSize.width, targetSize.height, true); // Animate the resize
    } else {
      console.error("[Main Process] Could not find window to resize.");
    }
  });
  // -----------------------------

// Helper function for calculating sharp extraction region based on fit/scale/pan
function calculateExtractionRegion(options) {
  const {
    targetWidthPt: containerW,
    targetHeightPt: containerH,
    transform,
    naturalWidth: imgW,
    naturalHeight: imgH,
  } = options;
  const { scale, offsetX, offsetY, fit } = transform;

  if (!imgW || !imgH || !containerW || !containerH) {
    console.warn('Invalid dimensions for extraction calculation:', options);
    return null; // Cannot calculate
  }

  const imgRatio = imgW / imgH;
  const containerRatio = containerW / containerH;

  let interimW, interimH;

  // Calculate intermediate dimensions based on fit mode (before user scaling)
  if (fit === 'cover') {
    if (imgRatio > containerRatio) {
      interimH = containerH;
      interimW = interimH * imgRatio;
    } else {
      interimW = containerW;
      interimH = interimW / imgRatio;
    }
  } else { // 'contain'
    if (imgRatio > containerRatio) {
      interimW = containerW;
      interimH = interimW / imgRatio;
    } else {
      interimH = containerH;
      interimW = interimH * imgRatio;
    }
  }

  // Apply user scaling
  const scaledW = interimW * scale;
  const scaledH = interimH * scale;

  // Calculate offsets relative to the scaled image center vs container center
  // Assuming offsetX/Y are pixel offsets from the center in the *renderer*
  // Convert these to offsets relative to the *scaled image* size (approximate: 1pt = 1px)
  const centerShiftX = offsetX;
  const centerShiftY = offsetY;

  // Top-left corner of the scaled image relative to the container's top-left
  const scaledImgRelX = (containerW - scaledW) / 2 + centerShiftX;
  const scaledImgRelY = (containerH - scaledH) / 2 + centerShiftY;

  // Calculate the visible portion of the scaled image within the container (intersection)
  // Coordinates relative to the container's top-left
  const visibleX_relContainer = Math.max(0, scaledImgRelX);
  const visibleY_relContainer = Math.max(0, scaledImgRelY);
  const visibleEndX_relContainer = Math.min(containerW, scaledImgRelX + scaledW);
  const visibleEndY_relContainer = Math.min(containerH, scaledImgRelY + scaledH);

  const visibleW_inContainer = visibleEndX_relContainer - visibleX_relContainer;
  const visibleH_inContainer = visibleEndY_relContainer - visibleY_relContainer;

  if (visibleW_inContainer <= 0 || visibleH_inContainer <= 0) {
    console.warn('Image is entirely outside the visible container area.');
    return null; // Nothing to extract
  }

  // Map the visible portion back to the *original* image coordinates for sharp.extract
  // Top-left corner of the visible area *within the scaled image*
  const visibleX_inScaledImg = visibleX_relContainer - scaledImgRelX;
  const visibleY_inScaledImg = visibleY_relContainer - scaledImgRelY;

  // Convert this top-left corner back to original image coordinates
  const extractX = Math.round(visibleX_inScaledImg * (imgW / scaledW));
  const extractY = Math.round(visibleY_inScaledImg * (imgH / scaledH));

  // Convert the visible dimensions back to original image coordinates
  const extractW = Math.round(visibleW_inContainer * (imgW / scaledW));
  const extractH = Math.round(visibleH_inContainer * (imgH / scaledH));

  // Clamp values to ensure they are within the original image bounds
  const finalExtractX = Math.max(0, extractX);
  const finalExtractY = Math.max(0, extractY);
  const finalExtractW = Math.max(1, Math.min(imgW - finalExtractX, extractW)); // Ensure at least 1px
  const finalExtractH = Math.max(1, Math.min(imgH - finalExtractY, extractH)); // Ensure at least 1px


  if (finalExtractW <= 0 || finalExtractH <= 0) {
     console.warn('Calculated extraction width or height is zero or negative.');
     return null;
  }

  return {
    left: finalExtractX,
    top: finalExtractY,
    width: finalExtractW,
    height: finalExtractH,
  };
}


// --- PDF Export Image Loading Handler ---
ipcMain.handle('load-image-data', async (event, filePath, options) => {
  try {
    const cleanPath = filePath.startsWith('safe-file://')
      ? path.normalize(decodeURI(filePath.substring(12)))
      : path.normalize(decodeURI(filePath));

    if (!cleanPath || typeof cleanPath !== 'string') {
       throw new Error('Invalid or unsafe file path provided');
    }

    console.log(`[Main Process] Processing image for PDF: ${cleanPath} with options:`, options);

    // Calculate the region to extract from the original image
    const region = calculateExtractionRegion(options);

    if (!region) {
      throw new Error('Failed to calculate valid extraction region.');
    }

    const pointsPerInch = 72;
    let processedImageBuffer;
    let actualWidthPt;
    let actualHeightPt;

    // Check the desired resolution
    if (options.resolution === 'native') {
      // Native Resolution Export (Contain Fit Only for now)
      console.log(`[Main Process] Exporting native resolution for: ${cleanPath}`);

      // Extract the calculated region without resizing
      const { data, info } = await sharp(cleanPath)
        .extract(region)
        .jpeg({ quality: 98 }) // Use high quality JPEG for native export
        .toBuffer({ resolveWithObject: true });

      processedImageBuffer = data;
      // Calculate points based on extracted pixel dimensions
      actualWidthPt = info.width / (options.naturalWidth / region.width) * (options.targetWidthPt / region.width); // Approximation based on ratios
      actualHeightPt = info.height / (options.naturalHeight / region.height) * (options.targetHeightPt / region.height); // Approximation based on ratios

      // Simpler calculation for points: Use extracted pixel dimensions directly
      // Assuming 1 pixel in original extraction corresponds roughly to 1 point in the target layout
      // This might need refinement depending on how jsPDF handles image scaling
      const extractedPixelsToPointsScale = 72 / 300; // Or simply use the region dimensions if mapping is 1:1? Let's try direct pixel->point mapping first.
      // Let's use the extracted region's dimensions directly converted to points.
      // This assumes the PDF viewer will scale it correctly based on the placeholder size.
      // We need to calculate the size of the *contained* image within the placeholder at native res.
      const containerRatio = options.targetWidthPt / options.targetHeightPt;
      const extractedRatio = region.width / region.height;
      let containedWidthPx, containedHeightPx;
      if (extractedRatio > containerRatio) { // Image wider than container (fits width)
          containedWidthPx = region.width;
          containedHeightPx = region.width / containerRatio; // This seems wrong. Should be based on extractedRatio
          containedHeightPx = region.width / extractedRatio;
      } else { // Image taller than container (fits height)
          containedHeightPx = region.height;
          containedWidthPx = region.height * extractedRatio;
      }
      // The actual extracted region IS the native resolution data for the visible part.
      // We just need to tell jsPDF its dimensions in points.
      actualWidthPt = region.width * (pointsPerInch / 300); // Convert extracted pixels to points assuming 300dpi source? No, use 72dpi standard.
      actualHeightPt = region.height * (pointsPerInch / 72); // 1 pixel = 1 point? No.
      // Let's use the target points dimensions, as jsPDF will place it within that box.
      // The base64 data contains the *extracted* native pixels.
      // jsPDF needs the dimensions (in points) of the box it should occupy.
      // For 'contain', the image might not fill the whole box. We need the dimensions of the *contained* image in points.

      if (extractedRatio > containerRatio) { // Fits width
          actualWidthPt = options.targetWidthPt;
          actualHeightPt = options.targetWidthPt / extractedRatio;
      } else { // Fits height
          actualHeightPt = options.targetHeightPt;
          actualWidthPt = options.targetHeightPt * extractedRatio;
      }


    } else {
      // 300 DPI Resizing (Original Logic)
      console.log(`[Main Process] Exporting 300 DPI resolution for: ${cleanPath}`);
      const DPI = 300;
      const scaleFactor = DPI / pointsPerInch;

      const finalPixelWidth = Math.round(options.targetWidthPt * scaleFactor);
      const finalPixelHeight = Math.round(options.targetHeightPt * scaleFactor);

      if (finalPixelWidth <= 0 || finalPixelHeight <= 0) {
          throw new Error(`Invalid target dimensions for resize: ${finalPixelWidth}x${finalPixelHeight}`);
      }

      const { data, info } = await sharp(cleanPath)
        .extract(region)
        .resize({
          width: finalPixelWidth,
          height: finalPixelHeight,
          fit: options.transform.fit === 'contain' ? sharp.fit.inside : sharp.fit.cover,
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .jpeg()
        .toBuffer({ resolveWithObject: true });

      processedImageBuffer = data;
      actualWidthPt = info.width / scaleFactor;
      actualHeightPt = info.height / scaleFactor;
    }

    // Return base64 string and the calculated final dimensions in points
    return {
        base64Data: processedImageBuffer.toString('base64'),
        finalWidthPt: actualWidthPt,
        finalHeightPt: actualHeightPt
    };

  } catch (error) {
    console.error(`[Main Process] Error processing image data for PDF (${filePath}):`, error);
    return null; // Indicate failure
  }
});
// ------------------------------------

// --- Shell Operations ---
ipcMain.handle('shell:showItemInFolder', (event, filePath) => {
  if (!filePath || typeof filePath !== 'string') {
    console.error('[Main Process] Invalid file path received for showItemInFolder:', filePath);
    return; // Or return an error indicator
  }
  console.log(`[Main Process] Received request to show item in folder: ${filePath}`);
  shell.showItemInFolder(filePath);
});
// ----------------------

// --- Helper: Find Latest Photoshop on Windows ---
async function findLatestPhotoshopWindows() {
  const baseDirs = [
    process.env.ProgramFiles, // C:\Program Files
    process.env['ProgramFiles(x86)'] // C:\Program Files (x86)
  ].filter(Boolean); // Filter out undefined/null paths

  let latestYear = -1;
  let latestPhotoshopExePath = null;

  for (const baseDir of baseDirs) {
    const adobeDir = path.join(baseDir, 'Adobe');
    try {
      // Check if Adobe directory exists
      await fsPromises.access(adobeDir, fs.constants.F_OK);
      const entries = await fsPromises.readdir(adobeDir, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.isDirectory()) {
          const match = entry.name.match(/^Adobe Photoshop (\d{4})$/); // Match "Adobe Photoshop YYYY"
          if (match) {
            const year = parseInt(match[1], 10);
            if (year > latestYear) {
              const potentialExePath = path.join(adobeDir, entry.name, 'Photoshop.exe');
              try {
                // Verify the executable exists
                await fsPromises.access(potentialExePath, fs.constants.X_OK); // Check execute permission
                latestYear = year;
                latestPhotoshopExePath = potentialExePath;
                console.log(`[Main Process] Found potential Photoshop: ${potentialExePath}`);
              } catch (exeAccessError) {
                // Photoshop.exe not found or not executable in this version folder
                console.warn(`[Main Process] Photoshop.exe not found or accessible in ${entry.name}`);
              }
            }
          }
        }
      }
    } catch (error) {
      // Adobe directory doesn't exist or couldn't be read in this baseDir
      if (error.code !== 'ENOENT') {
         console.warn(`[Main Process] Error reading Adobe directory ${adobeDir}:`, error);
      }
    }
  }

  if (latestPhotoshopExePath) {
     console.log(`[Main Process] Determined latest Photoshop path: ${latestPhotoshopExePath}`);
  } else {
     console.warn('[Main Process] Could not find any verified Photoshop installation path on Windows.');
  }
  return latestPhotoshopExePath;
}

// --- File Change Handler (for watched files) ---
async function handleFileChange(filePath) {
  console.log(`[Main Process] Change detected for: ${filePath}`);

  // Clear existing debounce timer for this file
  if (debounceTimers.has(filePath)) {
      clearTimeout(debounceTimers.get(filePath));
  }

  // Set a new debounce timer (e.g., 1 second)
  const timerId = setTimeout(async () => {
      console.log(`[Main Process] Debounced processing for: ${filePath}`);
      try {
          // Regenerate thumbnail and preview
          // Ensure generateThumbnail/Preview handle potential errors gracefully
          // We need to invalidate the cache first by deleting the old files
          const hash = crypto.createHash('sha256').update(filePath).digest('hex');
          const thumbnailFilename = `${hash}.webp`;
          const previewFilename = `${hash}.webp`;

          if (currentProjectPath) {
             const projectThumbnailCacheDir = path.join(currentProjectPath, 'thumbnail_cache');
             const projectPreviewCacheDir = path.join(currentProjectPath, 'preview_cache');
             const oldThumbnailPath = path.join(projectThumbnailCacheDir, thumbnailFilename);
             const oldPreviewPath = path.join(projectPreviewCacheDir, previewFilename);

             try {
                if (fs.existsSync(oldThumbnailPath)) {
                   await fsPromises.unlink(oldThumbnailPath);
                   console.log(`[Main Process] Deleted old thumbnail: ${oldThumbnailPath}`);
                }
                if (fs.existsSync(oldPreviewPath)) {
                   await fsPromises.unlink(oldPreviewPath);
                   console.log(`[Main Process] Deleted old preview: ${oldPreviewPath}`);
                }
             } catch (deleteError) {
                console.error(`[Main Process] Error deleting cached files for ${filePath}:`, deleteError);
                // Continue regeneration even if deletion failed
             }
          } else {
             console.warn('[Main Process] Project path not set, cannot delete cached files.');
             // Proceed with regeneration, which might fail if project path is needed
          }


          const newThumbnailUrl = await generateThumbnail(filePath);
          const newPreviewUrl = await generatePreview(filePath);

          // Check if regeneration was successful before sending update
          if (newThumbnailUrl && newPreviewUrl) {
            // Notify the renderer of success (find the main window)
            const mainWindow = BrowserWindow.getAllWindows()[0]; // Adjust if multiple windows
            if (mainWindow && !mainWindow.isDestroyed()) {
                 console.log(`[Main Process] Sending app:file-updated for ${filePath}`);
                 mainWindow.webContents.send('app:file-updated', {
                     originalPath: filePath,
                     thumbnailUrl: newThumbnailUrl,
                     previewUrl: newPreviewUrl
                 });
            }
          } else {
             // Regeneration failed for thumbnail or preview (or both)
             console.error(`[Main Process] Failed to regenerate cache for ${filePath}. Thumbnail: ${!!newThumbnailUrl}, Preview: ${!!newPreviewUrl}`);
             throw new Error('Failed to regenerate cached images after update.'); // Throw error to trigger catch block below
          }
      } catch (error) {
          console.error(`[Main Process] Error processing changed file ${filePath}:`, error); // This will now catch regeneration errors too
          // Optionally notify renderer of the error
          const mainWindow = BrowserWindow.getAllWindows()[0];
           if (mainWindow && !mainWindow.isDestroyed()) {
               mainWindow.webContents.send('app:file-update-error', {
                   originalPath: filePath,
                   error: error.message || 'Failed to update image after external edit.'
               });
           }
      } finally {
          // Clean up the timer reference after execution
          debounceTimers.delete(filePath);
      }
  }, 1000); // 1 second debounce period

  debounceTimers.set(filePath, timerId);
}
// ---------------------------------------------
// ---------------------------------------------

// --- Edit in Photoshop Handler ---
ipcMain.handle('app:editInPhotoshop', async (event, filePath) => {
  if (!filePath || typeof filePath !== 'string') {
    console.error('[Main Process] Invalid file path received for editInPhotoshop:', filePath);
    throw new Error('Invalid file path provided.');
  }

  console.log(`[Main Process] Received request to edit in Photoshop: ${filePath}`);
  const platform = process.platform;

  try {
    if (platform === 'darwin') { // macOS
      await new Promise((resolve, reject) => {
        execFile('open', ['-b', 'com.adobe.Photoshop', filePath], (error, stdout, stderr) => {
          if (error) {
            console.error(`[Main Process] Error opening with Photoshop bundle ID: ${stderr || error.message}`);
            // Try opening with default app as a fallback? Or just fail? Let's fail clearly.
            reject(new Error('Failed to launch Photoshop. Is it installed?'));
          } else {
            console.log(`[Main Process] Launched Photoshop for ${filePath} via bundle ID.`);
            resolve(); // Resolve the promise for editInPhotoshop

            // Start watching the file if not already watched
            if (!fileWatchers.has(filePath)) {
                console.log(`[Main Process] Starting to watch: ${filePath}`);
                try {
                  const watcher = chokidar.watch(filePath, {
                      persistent: true,
                      ignoreInitial: true, // Don't trigger on initial setup
                      awaitWriteFinish: { // Try to wait for writes to finish
                          stabilityThreshold: 2000, // ms
                          pollInterval: 100 // ms
                      },
                      usePolling: platform === 'win32' // Use polling on Windows for better compatibility with some editors
                  });

                  watcher.on('change', (path) => handleFileChange(path));
                  watcher.on('error', (error) => {
                     console.error(`[Main Process] Watcher error for ${filePath}:`, error);
                     // Attempt to remove the faulty watcher
                     const faultyWatcher = fileWatchers.get(filePath);
                     if (faultyWatcher) {
                        faultyWatcher.close();
                        fileWatchers.delete(filePath);
                        console.log(`[Main Process] Closed faulty watcher for ${filePath}`);
                     }
                  });
                  // Store the watcher instance
                  fileWatchers.set(filePath, watcher);
                } catch (watchError) {
                   console.error(`[Main Process] Failed to start watcher for ${filePath}:`, watchError);
                   // Don't reject the main promise here, as launching PS might have succeeded
                }
            }
          }
        });
      });
    } else if (platform === 'win32') { // Windows
      const photoshopPath = await findLatestPhotoshopWindows();
      if (!photoshopPath) {
        throw new Error('Adobe Photoshop installation not found.');
      }
      await new Promise((resolve, reject) => {
        execFile(photoshopPath, [filePath], (error, stdout, stderr) => {
          if (error) {
            console.error(`[Main Process] Error launching Photoshop executable (${photoshopPath}): ${stderr || error.message}`);
            reject(new Error('Failed to launch Photoshop.'));
          } else {
            console.log(`[Main Process] Launched ${photoshopPath} for ${filePath}.`);
            resolve(); // Resolve the promise for editInPhotoshop

             // Start watching the file if not already watched
            if (!fileWatchers.has(filePath)) {
                console.log(`[Main Process] Starting to watch: ${filePath}`);
                 try {
                   const watcher = chokidar.watch(filePath, {
                       persistent: true,
                       ignoreInitial: true, // Don't trigger on initial setup
                       awaitWriteFinish: { // Try to wait for writes to finish
                           stabilityThreshold: 2000, // ms
                           pollInterval: 100 // ms
                       },
                       usePolling: platform === 'win32' // Use polling on Windows for better compatibility with some editors
                   });

                   watcher.on('change', (path) => handleFileChange(path));
                   watcher.on('error', (error) => {
                      console.error(`[Main Process] Watcher error for ${filePath}:`, error);
                      // Attempt to remove the faulty watcher
                      const faultyWatcher = fileWatchers.get(filePath);
                      if (faultyWatcher) {
                         faultyWatcher.close();
                         fileWatchers.delete(filePath);
                         console.log(`[Main Process] Closed faulty watcher for ${filePath}`);
                      }
                   });
                   // Store the watcher instance
                   fileWatchers.set(filePath, watcher);
                 } catch (watchError) {
                    console.error(`[Main Process] Failed to start watcher for ${filePath}:`, watchError);
                    // Don't reject the main promise here, as launching PS might have succeeded
                 }
            }
          }
        });
      });
    } else {
      console.warn(`[Main Process] Edit in Photoshop not supported on platform: ${platform}`);
      throw new Error(`Editing in Photoshop is not supported on this operating system (${platform}).`);
    }
  } catch (error) {
     // Re-throw the specific error message from the try block or a generic one
     console.error('[Main Process] Error in editInPhotoshop handler:', error);
     throw error instanceof Error ? error : new Error('An unexpected error occurred while trying to open Photoshop.');
  }
});
// -----------------------------

  // --- Create Application Menu ---
  const menuTemplate = [
    // { role: 'appMenu' } // On macOS
    ...(process.platform === 'darwin' ? [{
      label: app.name,
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    }] : []),
    // { role: 'fileMenu' }
    {
      label: 'File',
      submenu: [
        {
          label: 'New Project',
          accelerator: 'CmdOrCtrl+N', // Standard accelerator for New
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              // Send message to renderer to initiate the new project process
              browserWindow.webContents.send('trigger-new-project');
            }
          }
        },
        { type: 'separator' }, // Separator
        {
          label: 'Load Project...',
          accelerator: 'CmdOrCtrl+O', // Standard accelerator for Open
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              // Send message to renderer to initiate the load process
              browserWindow.webContents.send('trigger-load');
            }
          }
        },
        {
          label: 'Save',
          accelerator: 'CmdOrCtrl+S', // Standard save shortcut
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              // Send a message to the renderer to trigger the save action
              browserWindow.webContents.send('trigger-save');
            }
          }
        },
        { type: 'separator' }, // Separator before close/quit
        process.platform === 'darwin' ? { role: 'close' } : { role: 'quit' }
      ]
    },
    // { role: 'editMenu' }
    {
      label: 'Edit',
      submenu: [
        {
          label: 'Undo',
          accelerator: 'CmdOrCtrl+Z',
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              browserWindow.webContents.send('trigger-undo');
            }
          }
        },
        {
          label: 'Redo',
          // Common accelerators for Redo
          accelerator: process.platform === 'darwin' ? 'Cmd+Shift+Z' : 'CmdOrCtrl+Y',
           click: (menuItem, browserWindow) => {
            if (browserWindow) {
              browserWindow.webContents.send('trigger-redo');
            }
          }
        },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        ...(process.platform === 'darwin' ? [
          { role: 'pasteAndMatchStyle' },
          { role: 'delete' },
          { role: 'selectAll' },
          { type: 'separator' },
          {
            label: 'Speech',
            submenu: [
              { role: 'startSpeaking' },
              { role: 'stopSpeaking' }
            ]
          }
        ] : [
          { role: 'delete' },
          { type: 'separator' },
          { role: 'selectAll' }
        ])
      ]
    },
    // { role: 'viewMenu' }
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    // { role: 'windowMenu' }
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'zoom' },
        ...(process.platform === 'darwin' ? [
          { type: 'separator' },
          { role: 'front' },
          { type: 'separator' },
          { role: 'window' }
        ] : [
          { role: 'close' }
        ])
      ]
    },
    {
      role: 'help',
      submenu: [
        // Add help links later
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(menu);
  // --------------------------

  createWindow();
  // createWindow(); // Removed duplicate call

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// App cleanup before quitting
app.on('will-quit', () => {
  console.log('[Main Process] Closing all file watchers...');
  fileWatchers.forEach((watcher, filePath) => {
      try {
        watcher.close();
        console.log(`[Main Process] Closed watcher for: ${filePath}`);
      } catch (closeError) {
         console.error(`[Main Process] Error closing watcher for ${filePath}:`, closeError);
      }
  });
  fileWatchers.clear();

  console.log('[Main Process] Clearing all debounce timers...');
  debounceTimers.forEach(timerId => clearTimeout(timerId));
  debounceTimers.clear();
});


// In this file, you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.