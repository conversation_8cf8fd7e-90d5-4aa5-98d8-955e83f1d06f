import React, { useState, useEffect, useCallback, useRef } from 'react'; // Import useCallback and useRef
import StartPage from '@/components/StartPage'; // Import StartPage
import BookSetup from '@/components/BookSetup';
import BookEditor from '@/components/BookEditor';
import { toast } from 'sonner'; // Import toast for notifications - will keep for other errors
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"; // Import AlertDialog components
import { useUserSettings } from '@/hooks/useUserSettings'; // Import useUserSettings

// Define possible view states
type ViewState = 'start' | 'setup' | 'editor';

const Index = () => {
  // Initialize state with potential restoration from sessionStorage
  const [currentView, setCurrentView] = useState<ViewState>(() => {
    const savedState = sessionStorage.getItem('projectState');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        return parsed.currentView || 'start';
      } catch (e) {
        console.warn('Failed to parse saved project state');
      }
    }
    return 'start';
  });
  
  const [projectDirectoryPath, setProjectDirectoryPath] = useState<string | null>(() => {
    const savedState = sessionStorage.getItem('projectState');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        return parsed.projectDirectoryPath || null;
      } catch (e) {
        return null;
      }
    }
    return null;
  });
  
  const [projectFilePath, setProjectFilePath] = useState<string | null>(() => {
    const savedState = sessionStorage.getItem('projectState');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        return parsed.projectFilePath || null;
      } catch (e) {
        return null;
      }
    }
    return null;
  });
  
  const [showEmptyFolderModal, setShowEmptyFolderModal] = useState<boolean>(false); // State for modal
  const continueButtonRef = useRef<HTMLButtonElement>(null); // Ref for the continue button
  // REMOVED: State for canvas hover info (moved to context)
  const [bookAspectRatio, setBookAspectRatio] = useState<{
    id: string;
    title: string;
    ratio: string;
    dimensions: string;
    widthPt?: number; // Added
    heightPt?: number; // Added
    bleed?: string; // Added bleed
    safetyMargin?: string; // Added safety margin
  } | null>(() => {
    const savedState = sessionStorage.getItem('projectState');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        return parsed.bookAspectRatio || null;
      } catch (e) {
        return null;
      }
    }
    return null;
  });
  
  const [loadedProjectData, setLoadedProjectData] = useState<any | null>(() => {
    const savedState = sessionStorage.getItem('projectState');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        return parsed.loadedProjectData || null;
      } catch (e) {
        return null;
      }
    }
    return null;
  });

  // Get user settings
  // projectBackgroundImage is now handled per-project, so removed from here
  const {
    theme, // Destructure theme
    projectBackgroundImageOpacity, // Destructure the new opacity setting
    setProjectBackgroundImageOpacity, // Destructure the new opacity setter
    showBleedArea, // Destructure bleed area visibility
    setShowBleedArea, // Destructure bleed area visibility setter
    // other settings can be destructured here if needed by Index page directly
  } = useUserSettings();

  // Effect to save project state to sessionStorage when it changes
  useEffect(() => {
    if (currentView === 'editor' && bookAspectRatio && (projectDirectoryPath || projectFilePath)) {
      const projectState = {
        currentView,
        projectDirectoryPath,
        projectFilePath,
        bookAspectRatio,
        loadedProjectData
      };
      sessionStorage.setItem('projectState', JSON.stringify(projectState));
    }
  }, [currentView, projectDirectoryPath, projectFilePath, bookAspectRatio, loadedProjectData]);

  // Effect to clean up saved state when starting a new project or going to start
  useEffect(() => {
    if (currentView === 'start' || currentView === 'setup') {
      sessionStorage.removeItem('projectState');
    }
  }, [currentView]);

  // Effect to resize window based on view state
  useEffect(() => {
    if (window.electronAPI && typeof window.electronAPI.setWindowSize === 'function') {
      if (currentView === 'editor') {
        window.electronAPI.setWindowSize('large');
      } else {
        window.electronAPI.setWindowSize('default');
      }
    } else {
      console.warn("electronAPI.setWindowSize is not available. Window resizing disabled.");
    }
  }, [currentView]);

  const handleBookSetupComplete = (aspectRatio: {
    id: string;
    title: string;
    ratio: string;
    dimensions: string;
    bleed?: string; // Added bleed
    safetyMargin?: string; // Added safety margin
  }) => {
    setBookAspectRatio(aspectRatio);
    
    // Safety margin value will be handled by BookEditor as a per-project setting
    
    // Don't change view here directly, it depends on whether it's a new or loaded project
    // The view change will happen after load or after initial directory selection
    // For new projects, the view is already 'setup' -> 'editor' transition happens later
    // For loaded projects, the view is set after loading data
    setCurrentView('editor');
  };

  // Function to reset the app state for a new project
  const handleNewProject = () => {
    setBookAspectRatio(null); // Clear aspect ratio for new setup
    setLoadedProjectData(null);
    setProjectDirectoryPath(null); // Clear directory path
    setProjectFilePath(null); // Clear file path
    setCurrentView('start'); // Go back to start page
    // Reset other relevant states if necessary (e.g., loadedProjectData, bookAspectRatio)
    setLoadedProjectData(null);
    setBookAspectRatio(null);
  };

  // Handler for "Create New Project" button on StartPage
  const handleCreateNew = () => {
    if (!window.electronAPI?.createNewProjectFile) {
      alert("Error: Project creation functionality is unavailable.");
      return;
    }
    setShowEmptyFolderModal(true); // Show the modal instead of toast
  };

  // Actual project creation logic, called from the modal
  const confirmAndCreateProject = async () => {
    setShowEmptyFolderModal(false); // Close modal first
    if (!window.electronAPI?.createNewProjectFile) {
      // This check is redundant if handleCreateNew already did it, but good for safety
      alert("Error: Project creation functionality is unavailable.");
      return;
    }
    try {
      // 1. Ask user to select location and name for the new .bookproof file
      const fileResult = await window.electronAPI.createNewProjectFile();

      if (fileResult.success && fileResult.projectDirectoryPath) {
        // Main process created the file and set the internal project path.
        // 2. Update frontend state (paths) and navigate to setup
        setProjectDirectoryPath(fileResult.projectDirectoryPath); // Set directory path
        setProjectFilePath(fileResult.filePath); // Set the specific file path
        setLoadedProjectData(null); // Ensure no old data persists
        setBookAspectRatio(null); // Ensure no old aspect ratio
        setCurrentView('setup');
      } else if (!fileResult.canceled) {
        // Handle failure to create file
        if (fileResult.error?.includes('Folder must be empty')) {
          // This specific error might still be useful as a toast if the main process returns it
          // after the user has selected a non-empty folder.
          toast.error("The selected folder is not empty. Please choose or create an empty folder for your project. This helps keep all project-related assets organized in one location.");
        } else if (fileResult.error?.includes('No active window')) {
            // This is the error we are trying to avoid, but if it still happens, show a specific message
            toast.error("Project creation failed: The application window lost focus or was closed. Please try again.");
        }
        else {
          alert(`Error creating project file: ${fileResult.error || 'Unknown error'}`);
        }
      }
      // If user canceled the save dialog, do nothing
    } catch (error: any) {
      console.error("Error during new project setup:", error);
      setProjectDirectoryPath(null); // Reset paths on error
      setProjectFilePath(null);
      alert(`An unexpected error occurred: ${error.message}`);
    }
  };

  // Handler for "Open Recent Projects" (or "Open Project") button on StartPage
  const handleOpenRecent = useCallback(async () => {
    if (!window.electronAPI?.openProjectFile || !window.electronAPI?.loadProject) {
       alert("Error: Project loading functionality is unavailable.");
       return;
    }
    try {
      // 1. Ask user to select a .bookproof file
      const fileResult = await window.electronAPI.openProjectFile();

      if (fileResult.success && fileResult.filePath) {
        // 2. Load the project data using the selected file path
        const loadResult = await window.electronAPI.loadProject(fileResult.filePath);

        if (loadResult.success && loadResult.data && loadResult.projectDirectoryPath) {
          // 3. Update state with loaded data and path
          setLoadedProjectData(loadResult.data);

          // Ensure aspect ratio has point dimensions after loading
          const loadedAspectRatio = loadResult.data.aspectRatio;
          if (loadedAspectRatio && loadedAspectRatio.dimensions &&
              (!loadedAspectRatio.widthPt || !loadedAspectRatio.heightPt || loadedAspectRatio.widthPt <= 0 || loadedAspectRatio.heightPt <= 0)) {
            console.warn("[Index] Loaded project aspect ratio missing valid point dimensions. Attempting calculation.");
            try {
              const cleanedDimensions = loadedAspectRatio.dimensions
                .replace(/"/g, '')      // Remove inch marks (")
                .replace(/×/g, 'x')     // Replace multiplication symbol (×) with 'x'
                .replace(/\s+/g, '');   // Remove any whitespace
              const dims = cleanedDimensions.toLowerCase().split('x');
              if (dims.length === 2) {
                const widthInches = parseFloat(dims[0]);
                const heightInches = parseFloat(dims[1]);
                if (!isNaN(widthInches) && !isNaN(heightInches) && widthInches > 0 && heightInches > 0) {
                  loadedAspectRatio.widthPt = widthInches * 72;
                  loadedAspectRatio.heightPt = heightInches * 72;
                } else {
                   console.error(`[Index] Invalid number format after parsing loaded dimensions: ${loadedAspectRatio.dimensions}`);
                }
              } else {
                 console.error(`[Index] Invalid dimensions format after cleaning loaded dimensions: ${cleanedDimensions}. Expected 'WxH'.`);
              }
            } catch (error) {
              console.error(`[Index] Error calculating dimensions for loaded project from '${loadedAspectRatio.dimensions}':`, error);
              // Proceed without calculated dimensions, BookEditor might calculate later
            }
          }

          setBookAspectRatio(loadedAspectRatio); // Set the potentially updated aspect ratio
          setProjectDirectoryPath(loadResult.projectDirectoryPath); // Set directory path
          setProjectFilePath(loadResult.filePath); // Set the specific file path
          setCurrentView('editor');
        } else {
          // Handle load failure
          const errorMsg = loadResult.message || 'Unknown error loading project data';
          console.error("Failed to load project data:", errorMsg);
          alert(`Error loading project: ${errorMsg}`);
          // Reset paths/state if load fails after selection
          setProjectDirectoryPath(null);
          setProjectFilePath(null);
          setCurrentView('start');
        }
      } else if (!fileResult.canceled) {
        // Handle file selection failure
        alert(`Error selecting project file: ${fileResult.error || 'Unknown error'}`);
      }
      // If file selection was canceled, do nothing
    } catch (error: any) {
      console.error("Error during open project process:", error);
      alert(`An unexpected error occurred: ${error.message}`);
    }
  }, []); // No external state dependencies, so empty array is fine

  const navigateToStartAndThenOpenRecent = async () => {
    setCurrentView('start');
    await handleOpenRecent();
  };

  // Function to load a project file directly by path
  const loadProjectByPath = useCallback(async (filePath: string) => {
    console.log(`[Index] Loading project from path: ${filePath}`);
    
    if (!window.electronAPI?.loadProject) {
      alert("Error: Project loading functionality is unavailable.");
      return;
    }
    
    try {
      // Load the project data using the provided file path
      const loadResult = await window.electronAPI.loadProject(filePath);
      
      if (loadResult.success && loadResult.data && loadResult.projectDirectoryPath) {
        // Update state with loaded data and path
        setLoadedProjectData(loadResult.data);
        
        // Ensure aspect ratio has point dimensions after loading
        const loadedAspectRatio = loadResult.data.aspectRatio;
        if (loadedAspectRatio && loadedAspectRatio.dimensions &&
            (!loadedAspectRatio.widthPt || !loadedAspectRatio.heightPt || loadedAspectRatio.widthPt <= 0 || loadedAspectRatio.heightPt <= 0)) {
          console.warn("[Index] Loaded project aspect ratio missing valid point dimensions. Attempting calculation.");
          try {
            const cleanedDimensions = loadedAspectRatio.dimensions
              .replace(/"/g, '')      // Remove inch marks (")
              .replace(/×/g, 'x')     // Replace multiplication symbol (×) with 'x'
              .replace(/\s+/g, '');   // Remove any whitespace
            const dims = cleanedDimensions.toLowerCase().split('x');
            if (dims.length === 2) {
              const widthInches = parseFloat(dims[0]);
              const heightInches = parseFloat(dims[1]);
              if (!isNaN(widthInches) && !isNaN(heightInches) && widthInches > 0 && heightInches > 0) {
                loadedAspectRatio.widthPt = widthInches * 72;
                loadedAspectRatio.heightPt = heightInches * 72;
              } else {
                console.error(`[Index] Invalid number format after parsing loaded dimensions: ${loadedAspectRatio.dimensions}`);
              }
            } else {
              console.error(`[Index] Invalid dimensions format after cleaning loaded dimensions: ${cleanedDimensions}. Expected 'WxH'.`);
            }
          } catch (error) {
            console.error(`[Index] Error calculating dimensions for loaded project from '${loadedAspectRatio.dimensions}':`, error);
            // Proceed without calculated dimensions, BookEditor might calculate later
          }
        }
        
        setBookAspectRatio(loadedAspectRatio); // Set the potentially updated aspect ratio
        setProjectDirectoryPath(loadResult.projectDirectoryPath); // Set directory path
        setProjectFilePath(loadResult.filePath); // Set the specific file path
        setCurrentView('editor');
      } else {
        // Handle load failure
        const errorMsg = loadResult.message || 'Unknown error loading project data';
        console.error("Failed to load project data:", errorMsg);
        alert(`Error loading project: ${errorMsg}`);
        // Reset paths/state if load fails
        setProjectDirectoryPath(null);
        setProjectFilePath(null);
        setCurrentView('start');
      }
    } catch (error: any) {
      console.error("Error during project loading:", error);
      alert(`An unexpected error occurred: ${error.message}`);
      setCurrentView('start');
    }
  }, []);
  
  // Effect to listen for the load trigger from the main process menu
  useEffect(() => {
    let removeLoadListener: (() => void) | undefined;
    if (window.electronAPI?.onTriggerLoadProject) {
      // When menu item "Load Project..." is clicked, trigger the same logic as the StartPage button
      removeLoadListener = window.electronAPI.onTriggerLoadProject(handleOpenRecent);
    } else {
      console.warn("Index: Electron API or onTriggerLoad not found.");
    }
    // Cleanup
    return () => {
      if (removeLoadListener) {
        removeLoadListener();
      }
    };
  }, [handleOpenRecent]); // Dependency on the memoized handleOpenRecent

  useEffect(() => {
    let removeSaveListener: (() => void) | undefined;
    let removeSaveAsListener: (() => void) | undefined;
    let removeImportListener: (() => void) | undefined;
    let removeExportListener: (() => void) | undefined;
    let removeNewProjectListener: (() => void) | undefined;

    if (window.electronAPI) {
      if (window.electronAPI.onTriggerSave && currentView === 'editor') {
        removeSaveListener = window.electronAPI.onTriggerSave(() => {
          // This event will be caught by BookEditor.tsx
        });
      }
      if (window.electronAPI.onTriggerSaveAs && currentView === 'editor') {
        removeSaveAsListener = window.electronAPI.onTriggerSaveAs(() => {
          // This event will be caught by BookEditor.tsx
        });
      }
      if (window.electronAPI.onTriggerImportImages && currentView === 'editor') {
        removeImportListener = window.electronAPI.onTriggerImportImages(() => {
          // This event will be caught by BookEditor.tsx to trigger its import logic
        });
      }
      if (window.electronAPI.onTriggerExportDialog && currentView === 'editor') {
        removeExportListener = window.electronAPI.onTriggerExportDialog(() => {
          // This event will be caught by BookEditor.tsx
        });
      }
      if (window.electronAPI.onTriggerNewProject) {
       removeNewProjectListener = window.electronAPI.onTriggerNewProject(handleNewProject);
      }
    } else {
      console.warn("Index: Electron API not found for menu triggers.");
    }

    return () => {
      if (removeSaveListener) removeSaveListener();
      if (removeSaveAsListener) removeSaveAsListener();
      if (removeImportListener) removeImportListener();
      if (removeExportListener) removeExportListener();
      if (removeNewProjectListener) removeNewProjectListener();
    };
  }, [currentView, handleNewProject]);
  
  // Effect to listen for file-opened events (when user double-clicks a .bp file in Finder)
  useEffect(() => {
    let removeFileOpenedListener: (() => void) | undefined;
    if (window.electronAPI?.onFileOpened) {
      removeFileOpenedListener = window.electronAPI.onFileOpened((filePath: string) => {
        console.log(`[Index] Received file-opened event with path: ${filePath}`);
        if (filePath.toLowerCase().endsWith('.bp')) {
          loadProjectByPath(filePath);
        } else {
          console.warn(`[Index] Received file-opened event with non-.bp file: ${filePath}`);
        }
      });
    } else {
      console.warn("Index: Electron API or onFileOpened not found.");
    }
    // Cleanup
    return () => {
      if (removeFileOpenedListener) {
        removeFileOpenedListener();
      }
    };
  }, [loadProjectByPath]); // Dependency on the memoized loadProjectByPath

  // Handler for going back from BookSetup to StartPage
  const handleGoBackToStart = () => {
    setCurrentView('start');
  };

  // Callback for BookEditor to update paths after a successful "Save As"
  const handleProjectSaveAs = useCallback((newFilePath: string) => {
    setProjectFilePath(newFilePath);
    // Assuming main process updated its internal currentProjectPath correctly,
    // we can derive the directory path here.
    // Alternatively, the main process could return both paths. For now, derive it.
    // Note: This requires 'path' module logic if running in Node, or a simpler string split.
    // Since this is renderer code, a simple split is safer.
    const lastSeparatorIndex = Math.max(newFilePath.lastIndexOf('/'), newFilePath.lastIndexOf('\\'));
    if (lastSeparatorIndex >= 0) {
      setProjectDirectoryPath(newFilePath.substring(0, lastSeparatorIndex));
    } else {
      console.warn("Could not derive directory path from new file path:", newFilePath);
      // Keep the old directory path? Or set to null? Let's keep it for now.
    }
  }, [setProjectFilePath, setProjectDirectoryPath]);

  // Callback for BookEditor to update the calculated point dimensions
  const handleUpdateAspectRatioPoints = useCallback((points: { widthPt: number; heightPt: number }) => {
    setBookAspectRatio(prev => {
      if (!prev) return null; // Should not happen if called correctly
      // Only update if points actually changed to avoid infinite loops
      if (prev.widthPt === points.widthPt && prev.heightPt === points.heightPt) {
        return prev;
      }
      return { ...prev, widthPt: points.widthPt, heightPt: points.heightPt };
    });
  }, [setBookAspectRatio]); // Dependency on the state setter

  // REMOVED: Handler for canvas hover changes (moved to context/BookEditor)


  // Render component based on currentView state
  const renderView = () => {
    switch (currentView) {
      case 'start':
        return <StartPage theme={theme} onCreateNew={handleCreateNew} onOpenRecent={handleOpenRecent} />;
      case 'setup':
        return <BookSetup theme={theme} onComplete={handleBookSetupComplete} onBack={handleGoBackToStart} />;
      case 'editor':
        if (!bookAspectRatio) {
          setCurrentView('setup');
          return null;
        }
        
        // Add validation for point dimensions
        if (!bookAspectRatio.widthPt || !bookAspectRatio.heightPt || 
            bookAspectRatio.widthPt <= 0 || bookAspectRatio.heightPt <= 0) {
          // Calculate the dimensions before rendering
          const updatedAspectRatio = { ...bookAspectRatio };
          
          try {
            const cleanedDimensions = updatedAspectRatio.dimensions
              .replace(/"/g, '')      
              .replace(/×/g, 'x')     
              .replace(/\s+/g, '');   
            const dims = cleanedDimensions.toLowerCase().split('x');
            
            if (dims.length === 2) {
              const widthInches = parseFloat(dims[0]);
              const heightInches = parseFloat(dims[1]);
              
              if (!isNaN(widthInches) && !isNaN(heightInches) && widthInches > 0 && heightInches > 0) {
                updatedAspectRatio.widthPt = widthInches * 72;
                updatedAspectRatio.heightPt = heightInches * 72;
                
                // Update the state but don't wait for re-render
                setBookAspectRatio(updatedAspectRatio);
                
                // Return with calculated values
                return (
                  <BookEditor
                    aspectRatio={updatedAspectRatio}
                    bookPageWidthInches={updatedAspectRatio.widthPt / 72}
                    initialProjectData={loadedProjectData}
                    projectDirectoryPath={projectDirectoryPath}
                    projectFilePath={projectFilePath}
                    onLoadAspectRatio={handleBookSetupComplete}
                    onUpdateAspectRatioPoints={handleUpdateAspectRatioPoints}
                    onNewProjectRequest={handleNewProject}
                    onProjectSaveAs={handleProjectSaveAs}
                    projectBackgroundImage={loadedProjectData?.projectBackgroundImage || null}
                    projectBackgroundImageOpacity={projectBackgroundImageOpacity} // Pass opacity
                    setProjectBackgroundImageOpacity={setProjectBackgroundImageOpacity} // Pass opacity setter
                    showBleedArea={showBleedArea} // Pass bleed area visibility
                    setShowBleedArea={setShowBleedArea} // Pass bleed area visibility setter
                  />
                );
              } else {
                console.error(`[Index] Invalid dimensions format: ${updatedAspectRatio.dimensions}`);
                setCurrentView('setup');
                return null;
              }
            } else {
              console.error(`[Index] Invalid dimensions format: ${cleanedDimensions}`);
              setCurrentView('setup');
              return null;
            }
          } catch (error) {
            console.error(`[Index] Error calculating dimensions:`, error);
            setCurrentView('setup');
            return null;
          }
        }
        
        return (
          <BookEditor
            aspectRatio={bookAspectRatio}
            // Derive bookPageWidthInches from points, default if not available
            bookPageWidthInches={bookAspectRatio.widthPt ? bookAspectRatio.widthPt / 72 : 0}
            initialProjectData={loadedProjectData}
            projectDirectoryPath={projectDirectoryPath} // Pass directory path
            projectFilePath={projectFilePath} // Pass file path
            onLoadAspectRatio={handleBookSetupComplete} // Pass the updated state setter
            onUpdateAspectRatioPoints={handleUpdateAspectRatioPoints} // Pass the new callback
            onNewProjectRequest={handleNewProject}
            onProjectSaveAs={handleProjectSaveAs} // Pass callback for Save As updates
            projectBackgroundImage={loadedProjectData?.projectBackgroundImage || null}
            projectBackgroundImageOpacity={projectBackgroundImageOpacity} // Pass opacity
            setProjectBackgroundImageOpacity={setProjectBackgroundImageOpacity} // Pass opacity setter
            showBleedArea={showBleedArea} // Pass bleed area visibility
            setShowBleedArea={setShowBleedArea} // Pass bleed area visibility setter
            // REMOVED: onCanvasHoverChange prop
            onTriggerLoadAndNavigate={navigateToStartAndThenOpenRecent} // Add new prop
          />
        );
      default:
        return <StartPage theme={theme} onCreateNew={handleCreateNew} onOpenRecent={handleOpenRecent} />;
    }
  };

  // Effect to focus the continue button when modal opens
  useEffect(() => {
    if (showEmptyFolderModal && continueButtonRef.current) {
      // Timeout to ensure the button is rendered and focusable
      setTimeout(() => {
        continueButtonRef.current?.focus();
      }, 100); // Small delay might be needed
    }
  }, [showEmptyFolderModal]);

  return (
    <div className="h-screen w-screen overflow-hidden bg-app-background"> {/* Use consistent background */}
      {renderView()}

      {showEmptyFolderModal && (
        <AlertDialog open={showEmptyFolderModal} onOpenChange={setShowEmptyFolderModal}>
          <AlertDialogContent
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                // Prevent default behavior (like form submission if any)
                e.preventDefault();
                // Directly call the confirm action
                confirmAndCreateProject();
              }
            }}
          >
            <AlertDialogHeader>
              <AlertDialogTitle>Create New Project</AlertDialogTitle>
              <AlertDialogDescription>
                To ensure your project assets are neatly organized, Bookproofs requires a <strong>new, empty folder</strong> for each project.
                <br /><br />
                When you click "Continue", you will be prompted to select an empty folder and then name your project file.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setShowEmptyFolderModal(false)}>Cancel</AlertDialogCancel>
              <AlertDialogAction ref={continueButtonRef} onClick={confirmAndCreateProject}>Continue</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
};

export default Index;
