// src/electron.d.ts

// Define the structure of the API exposed by the preload script
// Define the structure of project data to be saved/loaded
// (We can refine this later if needed)

// Define ImageFile interface to match the one in ImageTray.tsx
export interface ImageFile {
  id: string;
  name: string;
  originalPath: string;
  thumbnailUrl: string;
  previewUrl?: string | null;
  rating?: number;
  dateAdded?: number;
  dateTaken?: number | null;
  dateModified?: number;
  naturalWidth?: number;
  naturalHeight?: number;
}

export interface ProjectData {
  // Define types more accurately if possible, but 'any' is okay for now if types aren't shared easily
  version?: number; // Project data version for backward compatibility
  spreads: any[]; // Replace 'any' with Spread type if imported/defined here
  images: ImageFile[]; // Now using the ImageFile type
  imageGap: number;
  aspectRatio: any; // Replace 'any' with actual type
  backgroundColor: string; // Added for background color
  projectBackgroundImage: string | null; // Original path or URL
  projectBackgroundImagePath: string | null; // Path for finding the image
  projectBackgroundImageNormalizedUrl?: string | null; // Normalized URL for consistent display
  favoriteTemplateIds?: string[]; // Optional template favorites
  textOverlays?: any[]; // Text overlay data
  // Add other relevant state here
}

// Add global window interface declaration to avoid duplicate declarations
declare global {
  interface Window {
    electronAPI?: IElectronAPI;
    bookProofsApp?: {
      getUpdatedFilePath?: (originalPath: string) => string | null;
      getAllUpdatedPaths?: () => Record<string, string>;
      updatedPaths?: Record<string, string>;
      updateImagePaths?: (images: ImageFile[]) => ImageFile[];
    };
  }
}

// Import PreviewGenerationMode if it's defined and exported in useUserSettings.ts
// For now, assuming it's available or we define it inline for simplicity if not.
// Let's assume PreviewGenerationMode is 'onImport' | 'onPlacement'
type PreviewGenerationMode = 'onPlacement';

// Define ColorSpaceOption inline for simplicity in .d.ts
export type ColorSpaceOption = 'srgb' | 'adobergb' | 'passthrough';

export interface IElectronAPI {
  // Update return type for openImageDialog - previewUrl is now generated async
  openImageDialog: (args?: { previewGenerationMode?: PreviewGenerationMode; generatePreviews?: boolean }) => Promise<{ originalPath: string; thumbnailUrl: string; name: string; rating: number; naturalWidth: number; naturalHeight: number; dateTaken?: number | null; dateModified?: number | null }[]>;
  // Regenerate thumbnails and previews for a file
  regenerateThumbnails: (filePath: string) => Promise<{ originalPath: string; thumbnailUrl: string; previewUrl: string; name: string; dateAdded: number; dateTaken: number | null; dateModified: number; naturalWidth: number; naturalHeight: number } | null>;
  // Renamed: Function to trigger "Save As..." (always shows dialog)
  saveProjectAs: (data: ProjectData) => Promise<{ success: boolean; filePath?: string; error?: string; canceled?: boolean }>;
  // New: Function to trigger "Save" (overwrites current file if path exists)
  projectSaveFile: (data: ProjectData) => Promise<{ success: boolean; filePath?: string; error?: string; needsSaveAs?: boolean }>;
  // Updated return type to match main.cjs implementation
  // Load project data given a specific file path
  loadProject: (filePath: string) => Promise<{ success: boolean; data?: ProjectData; filePath?: string; projectDirectoryPath?: string; error?: boolean; message?: string; canceled?: boolean }>;
  confirmImageReuse: (imageName: string) => Promise<boolean>; // Ask confirmation, returns true if confirmed
  // NEW: Generic confirmation dialog (returns button index)
  confirmDialog: (options: {
    title: string;
    message: string;
    detail?: string;
    buttons?: string[];
    defaultId?: number;
    cancelId?: number;
    type?: 'none' | 'info' | 'error' | 'question' | 'warning';
  }) => Promise<number>;
  // Define types for the listener functions exposed by preload
  // They accept a callback and return a function to remove the listener
  onTriggerUndo: (callback: () => void) => () => void;
  onTriggerRedo: (callback: () => void) => () => void;
  // Function to open a file path with the default application or a specified one
  openExternal: (filePath: string, appName?: string) => Promise<{ success: boolean; error?: string }>;
  // Function to show a file in the native file manager
  showItemInFolder: (filePath: string) => Promise<{ success: boolean; error?: string }>;
  // Functions for native fullscreen control
  setNativeFullScreen: (flag: boolean) => Promise<void>;
  isNativeFullScreen: () => Promise<boolean>;
  // Function to load and pre-process image data for PDF export
  loadImageData: (
    filePath: string,
    options: {
      targetWidthPt: number;
      targetHeightPt: number;
      transform: { // Define transform structure inline or import if available
        scale: number;
        offsetX: number;
        offsetY: number;
        // fit property removed, 'contain' is assumed
      };
      // Add naturalWidth/Height if needed for calculations in main process
      naturalWidth: number;
      naturalHeight: number;
      resolution?: '300dpi' | 'native'; // Add optional resolution field
    }
  ) => Promise<{
    base64Data: string;
    finalWidthPt: number; // The actual width of the processed image in points
    finalHeightPt: number; // The actual height of the processed image in points
  } | null>;
  // Listener for Load Project menu item
  onTriggerLoadProject: (callback: () => void) => () => void; // Renamed for clarity
  // Listener for New Project menu item
  onTriggerNewProject: (callback: () => void) => () => void;
  // Listener for Save shortcut
  onTriggerSave: (callback: () => void) => () => void;
  onTriggerSaveAs: (callback: () => void) => () => void; // Added for Save As
  onTriggerImportImages: (callback: () => void) => () => void; // Added for Import Images
  onTriggerExportDialog: (callback: () => void) => () => void; // Added for Export Dialog
  // Listener for file-opened events (when user double-clicks a .bp file in Finder)
  onFileOpened: (callback: (filePath: string) => void) => () => void;
  // Function to request window resize
  setWindowSize: (size: 'default' | 'large') => Promise<void>;
  // Listener for consolidated import progress
  onImportProgress: (callback: (data: { stage: 'reading_metadata' | 'generating_thumbnails' | 'generating_previews'; current: number; total: number } | null) => void) => () => void;
  // Listener for individual preview completion
  onPreviewGenerated: (callback: (data: { originalPath: string; previewUrl: string | null }) => void) => () => void;
  // Listener for preview processing status (to track progress before completion)
  onPreviewProcessing: (callback: (data: { originalPath: string; status: 'processing' }) => void) => () => void;

  // --- Start Page Dialog Handlers ---
  createNewProjectFile: () => Promise<{ success: boolean; filePath?: string; projectDirectoryPath?: string; canceled?: boolean; error?: string }>;
  openProjectFile: () => Promise<{ success: boolean; filePath?: string; canceled?: boolean; error?: string }>;
  // Inform main process of the active project directory (after save/load or new project setup)
  setProjectDirectory: (directoryPath: string) => Promise<{ success: boolean; path?: string; error?: string }>;
  // Function to attempt opening a file specifically in Photoshop
  editInPhotoshop: (filePath: string | string[]) => Promise<void>; // Returns void on success, throws error on failure
  // Listener for successful file updates after external edit
  onFileUpdated: (callback: (data: { originalPath: string; thumbnailUrl: string | null; previewUrl: string | null; dateTaken?: number | null; dateModified?: number | null }) => void) => () => void;
  // Listener for errors during file update after external edit
  onFileUpdateError: (callback: (data: { originalPath: string; error: string }) => void) => () => void;
  // Function to get image data as Data URL
  getImageDataUrl: (filePath: string) => Promise<{ success: boolean; dataUrl?: string; filename?: string; error?: string }>;
  
  // NEW: Function to get EXIF data from an image
  getExifData: (filePath: string) => Promise<{ 
    success: boolean; 
    exifData?: { 
      dimensions: { width: number; height: number };
      colorProfile: string | null;
      rating: number;
      captureTime: string | null;
      raw: any;
    }; 
    error?: string 
  }>;

  // --- Backup State Communication ---
  // Listener for main process requesting project state for backup
  // Callback should gather state and call sendProjectStateResponse
  onRequestProjectState: (callback: () => void) => () => void;
  // Function for renderer to send the state back to main process
  sendProjectStateResponse: (state: ProjectData | null) => void; // Allow sending null if state cannot be gathered
  // Listener for main process specifically requesting state for backup
  onBackupStateRequest: (callback: () => void) => () => void;
  // --- End Backup State Communication ---

  // --- PDF Export Related ---
  showSaveDialog: (options: { title?: string; defaultPath?: string; filters?: Array<{ name: string; extensions: string[] }> }) => Promise<string | null>; // Returns save path or null if cancelled
  savePdfFile: (filePath: string, data: ArrayBuffer) => Promise<boolean>; // Returns true on success
  openPath: (filePath: string) => Promise<void>; // Opens the given path (file or folder)
  
  // File availability checking
  checkFilesExist: (filePaths: string[], silent?: boolean) => Promise<boolean[]>;
  updateFilePaths: (originalPaths: string[], newBasePath: string) => Promise<{ originalPath: string; newPath: string | null }[]>;
  selectDirectory: (options?: { title?: string; defaultPath?: string }) => Promise<string | null>;
  selectFile: (options?: { title?: string; defaultPath?: string; filters?: { name: string; extensions: string[] }[] }) => Promise<string | null>;
  updateIndividualPath: (originalPath: string, newPath: string) => Promise<{ originalPath: string; newPath: string | null }>;

  // NEW: Handler for loading image data + ICC profile for PDF export
  loadImageDataForPdf: (
    filePath: string,
    options: { // Define options needed by the main process handler
      targetWidthPt: number;
      targetHeightPt: number;
      transform: {
        scale: number;
        offsetX: number; // Offset relative to container center
        offsetY: number; // Offset relative to container center
        fit: 'contain' | 'cover';
        rotation?: number; // Rotation in degrees
      };
      colorSpace?: ColorSpaceOption; // Added colorSpace
      // resolution?: 'native'; // Main process v2 ignores this, but keep for potential future use
    }
  ) => Promise<{
    success: boolean;
    imageType?: 'png' | 'jpeg'; // Added image type
    imageBuffer?: ArrayBuffer; // Use ArrayBuffer for IPC
    iccProfileBuffer?: ArrayBuffer | null; // Use ArrayBuffer for IPC (compressed for PNG, raw for JPEG)
    width?: number; // Pixel width of the returned imageBuffer
    height?: number; // Pixel height of the returned imageBuffer
    error?: string;
  }>;

  // NEW: Handler for loading image data for Canvas export (similar to PDF)
  loadImageDataForCanvas: (
    filePath: string,
    options: {
      targetWidthPt: number;
      targetHeightPt: number;
      transform: {
        scale: number;
        offsetX: number;
        offsetY: number;
        fit: 'contain' | 'cover';
        rotation?: number; // Rotation in degrees
      };
      colorSpace?: ColorSpaceOption;
    }
  ) => Promise<{
    success: boolean;
    imageType?: 'png' | 'jpeg';
    imageBuffer?: ArrayBuffer;
    iccProfileBuffer?: ArrayBuffer | null;
    width?: number; // Pixel width of the returned imageBuffer
    height?: number; // Pixel height of the returned imageBuffer
    error?: string;
  }>;

  requestPreviewGeneration: (originalPath: string) => Promise<void>; // New: Request single preview

  // For handling files dropped onto the UploadArea
  // Use the ImageFile interface directly for consistency
  processDroppedFiles: (filePaths: string[], options?: { generatePreviews?: boolean }) => Promise<ImageFile[] | null>;
  onDroppedFilesProgress: (
    callback: (data: {
      stage?: string;
      current: number;
      total: number;
      filename?: string;
      error?: string;
    } | null) => void
  ) => (() => void) | undefined; // Returns a cleanup function or undefined if not supported

  // Dialog for confirming action when too many images are dropped
  confirmExcessImagesDialog: (
    droppedCount: number,
    availableSlots: number,
    templateCapacity: number
  ) => Promise<'autobuild' | 'cancel' | 'error_or_closed'>;

  // Dialog for selecting a directory or file
  showOpenDialog: (options: {
    title?: string;
    defaultPath?: string;
    buttonLabel?: string;
    filters?: Array<{ name: string; extensions: string[] }>;
    properties?: Array<'openFile' | 'openDirectory' | 'multiSelections' | 'showHiddenFiles' | 'createDirectory' | 'promptToCreate' | 'noResolveAliases' | 'treatPackageAsDirectory' | 'dontAddToRecent'>;
    message?: string; // macOS only
    securityScopedBookmarks?: boolean; // macOS only
  }) => Promise<{ canceled: boolean; filePaths: string[]; bookmark?: string }>;

  // Function to save a file from a data URL
  saveFile: (filePath: string, dataUrl: string) => Promise<boolean>;

  // Function to check if a file exists
  checkFileExists: (filePath: string) => Promise<boolean>;

  // NEW: Handler for generating JPEG with color profile conversion
  convertAndGenerateJpeg: (
    imageData: { // Raw pixel data from canvas
      data: Uint8ClampedArray; // The pixel data
      width: number;           // Width of the pixel data
      height: number;          // Height of the pixel data
    },
    options: {
      quality: number; // JPEG quality (0-100)
      colorSpace: ColorSpaceOption; // Target color space
      // Dimensions might not be needed if imageData is already at final export resolution
      // targetWidthPx?: number;
      // targetHeightPx?: number;
    }
  ) => Promise<{
    success: boolean;
    dataUrl?: string; // JPEG as data URL
    error?: string;
  }>;

  // --- Theme Management ---
  // Function to set the title bar theme
  setTitleBarTheme: (theme: 'light' | 'dark' | 'system') => Promise<{ success: boolean; error?: string }>;
  // Listener for Toggle Theme shortcut
  onTriggerToggleTheme: (callback: () => void) => () => void;
  // --- End Theme Management ---
}

// Extend the global Window interface
declare global {
  interface Window {
    electronAPI: IElectronAPI;
  }
}

// This empty export makes the file a module
export {};