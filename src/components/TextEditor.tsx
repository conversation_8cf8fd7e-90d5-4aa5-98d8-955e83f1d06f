import React, { useState, useRef, useEffect } from 'react';
import { Trash2, Type, AlignLeft, AlignCenter, AlignRight, Bold, Italic, Underline } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'framer-motion'; // Import Framer Motion
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from '@/components/ui/input';
import { ChromePicker } from 'react-color';
import { cn } from '@/lib/utils';

export interface TextOverlay {
  id: string;
  spreadId: string;
  content: string;
  // Add a field to track edited text that hasn't been committed yet
  editingContent?: string;
  style: {
    fontFamily: string;
    fontSize: number; // Represents a percentage of the container's height (e.g., 2 for 2%)
    color: string;
    bold: boolean;
    italic: boolean;
    underline: boolean;
    textAlign: 'left' | 'center' | 'right';
    x: number; // Position as percentage of spread width (0-100)
    y: number; // Position as percentage of spread height (0-100)
    width: number; // Width as percentage of spread width (0-100)
    opacity: number; // 0-1
  };
}

export const fontFamilies = [
  // Standard PDF fonts that are guaranteed to be available in PDF export
  { name: 'Helvetica', value: 'Helvetica, Arial, sans-serif' },
  { name: 'Times Roman', value: 'Times-Roman, "Times New Roman", Times, serif' },
  { name: 'Courier', value: 'Courier, "Courier New", monospace' },
];

// Map of font family names to PDF standard fonts
// This will be used by the PDF export function
export const fontFamilyToPdfFontMap: Record<string, string> = {
  // Direct mappings for the standard PDF fonts
  'Helvetica': 'Helvetica',
  'Times Roman': 'TimesRoman',
  'Courier': 'Courier',

  // CSS value mappings (for font-family values that might be used)
  'Helvetica, Arial, sans-serif': 'Helvetica',
  'Times-Roman, "Times New Roman", Times, serif': 'TimesRoman',
  'Courier, "Courier New", monospace': 'Courier',
};


interface TextEditorProps {
  textOverlays: TextOverlay[];
  currentSpreadId: string;
  onUpdateTextOverlay: (textOverlay: TextOverlay) => void;
  // onDeleteTextOverlay will be handled by BookEditor via the controls
  containerRef: React.RefObject<HTMLDivElement>; // Reference to the SpreadCanvas container
  spreadDimensionsPt: { width: number; height: number };
  onSelectTextOverlay: (overlay: TextOverlay | null) => void; // New callback
  textControlsBarRef?: React.RefObject<HTMLDivElement>; // Ref for the text controls bar from BookEditor
  navigationDirection?: 'prev' | 'next' | null; // Direction of navigation for animations
}

export const TextEditor: React.FC<TextEditorProps> = ({
  textOverlays,
  currentSpreadId,
  onUpdateTextOverlay,
  // onDeleteTextOverlay, // Removed, will be handled by BookEditor
  containerRef,
  spreadDimensionsPt,
  onSelectTextOverlay, // New callback
  textControlsBarRef, // Destructure the new prop
  navigationDirection, // Destructure the navigation direction prop
}) => {
  const [selectedTextId, setSelectedTextId] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isDragging, setIsDragging] = useState(false); // For dragging text elements
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 }); // For dragging text elements
  const [currentContainerHeight, setCurrentContainerHeight] = useState<number>(0);
  // Removed state related to controls: colorPickerOpen, isDraggingControls, controlsPosition, controlsDragStart

  const textEditorRef = useRef<HTMLDivElement>(null);
  const textInputRef = useRef<HTMLTextAreaElement>(null);
  // Removed controlsBarRef

  // Get only the text overlays for the current spread or cover and ensure proper font family format
  const currentSpreadOverlays = textOverlays.filter(overlay =>
    overlay.spreadId === currentSpreadId ||
    (overlay.spreadId === 'cover' && currentSpreadId === 'cover')
  ).map(overlay => {
    // Check if this overlay needs font family migration
    if (overlay.style.fontFamily && !overlay.style.fontFamily.includes(',')) {
      // Find the matching font family with proper format
      const matchingFont = fontFamilies.find(f =>
        f.name === overlay.style.fontFamily ||
        f.value.startsWith(overlay.style.fontFamily)
      );

      if (matchingFont) {
        // Create a new overlay with updated font family
        return {
          ...overlay,
          style: {
            ...overlay.style,
            fontFamily: matchingFont.value
          }
        };
      }
    }
    return overlay;
  });

  // Get the selected text with proper font family format
  const selectedText = selectedTextId ?
    textOverlays.find(text => text.id === selectedTextId) : null;

  // Make sure selected text has proper font family format if found
  const normalizedSelectedText = selectedText ? {
    ...selectedText,
    style: {
      ...selectedText.style,
      fontFamily: selectedText.style.fontFamily.includes(',')
        ? selectedText.style.fontFamily
        : (fontFamilies.find(f => f.name === selectedText.style.fontFamily)?.value || fontFamilies[0].value)
    }
  } : null;

  // Update the textOverlay state with the given changes
  // This function preserves all style properties across updates
  const updateTextOverlay = (changes: Partial<TextOverlay>) => {
    if (selectedTextId) {
      // Always get the latest overlay from the textOverlays array
      // This ensures we have the most up-to-date style properties
      const overlay = textOverlays.find(o => o.id === selectedTextId);
      if (overlay) {
        // Create a complete copy of the overlay, preserving ALL style properties
        const updatedOverlay = {
          ...overlay,
          ...changes,
          // If there are style changes, make sure to properly merge them
          ...(changes.style ? { style: { ...overlay.style, ...changes.style } } : {})
        };

        // If we're explicitly setting content, clear editingContent
        if (changes.content !== undefined) {
          updatedOverlay.editingContent = undefined;
        }

        console.log('updateTextOverlay - Style properties preserved:', JSON.stringify(updatedOverlay.style, null, 2));
        onUpdateTextOverlay(updatedOverlay);
      }
    }
  };

  // Handle text selection
  const handleTextSelect = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedTextId(id);
    setIsEditing(false);

    // Get the selected overlay
    const selectedOverlay = textOverlays.find(overlay => overlay.id === id) || null;

    // If there's an editingContent, commit it before selecting a new text overlay
    if (selectedTextId && selectedTextId !== id) {
      const previousOverlay = textOverlays.find(overlay => overlay.id === selectedTextId);
      if (previousOverlay && previousOverlay.editingContent !== undefined) {
        onUpdateTextOverlay({
          ...previousOverlay,
          content: previousOverlay.editingContent,
          editingContent: undefined
        });
      }
    }

    // Ensure the text box width is correct for the selected text
    if (selectedOverlay) {
      // Use a small timeout to ensure the DOM is updated
      setTimeout(() => {
        updateTextBoxWidth(id);
      }, 0);
    }

    onSelectTextOverlay(selectedOverlay);
  };

  // Helper function to commit any pending edits - Moved OUTSIDE the effect to avoid closure issues
  const commitPendingEdits = () => {
    if (selectedTextId && isEditing && textInputRef.current) {
      // Get the current text from the textarea
      const currentText = textInputRef.current.value;

      // Find the exact overlay from the current textOverlays array
      // This ensures we have the most up-to-date style properties
      const currentOverlay = textOverlays.find(o => o.id === selectedTextId);

      if (currentOverlay) {
        console.log('CommitPendingEdits - Current overlay before update:', JSON.stringify(currentOverlay.style, null, 2));

        // Create a new overlay object with the updated text but preserving ALL style properties
        const updatedOverlay = {
          ...currentOverlay,
          content: currentText,
          editingContent: undefined
        };

        console.log('CommitPendingEdits - Updated overlay to send:', JSON.stringify(updatedOverlay.style, null, 2));

        // IMPORTANT: Update overlay BEFORE setting isEditing to false
        // This ensures the update happens with the full context still available
        onUpdateTextOverlay(updatedOverlay);

        // Now set editing to false AFTER the update
        setIsEditing(false);
      }
    }
  };

  // Effect to handle clicks outside the textbox
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const targetNode = e.target as Node;

      const clickedWithinTextEditorOverlays = textEditorRef.current && textEditorRef.current.contains(targetNode);
      const clickedOnControls = textControlsBarRef?.current && textControlsBarRef.current.contains(targetNode);
      const clickedDirectlyOnSpreadCanvasContainer = containerRef.current && containerRef.current === targetNode;

      // Determine if the click was on an actual text element (child of textEditorRef.current)
      // This is important because textEditorRef.current itself is pointer-events-none.
      // So, if textEditorRef.current.contains(targetNode) is true, targetNode must be a descendant (an overlay).
      const isClickOnActualTextOverlayContent = clickedWithinTextEditorOverlays;

      // If clicking on controls while editing, preserve the editing state
      // This prevents text from being reset when clicking on color picker or other controls
      if (clickedOnControls && isEditing && selectedTextId) {
        // Don't deselect or stop editing when clicking on controls
        return;
      }

      if (clickedDirectlyOnSpreadCanvasContainer) {
        // Click is directly on the SpreadCanvas container (background)
        // Deselect unless the click was also somehow on the controls (unlikely if target is containerRef)
        if (!clickedOnControls) {
          if (isEditing) {
            commitPendingEdits(); // Commit pending edits (this already handles setIsEditing(false) internally)
          }
          if (selectedTextId) {
            setSelectedTextId(null); // Deselect the text box
            onSelectTextOverlay(null); // Notify parent
          }
        }
      } else if (!isClickOnActualTextOverlayContent && !clickedOnControls) {
        // Click was not on an actual text overlay and not on controls
        if (isEditing) {
          commitPendingEdits(); // Commit pending edits (this already handles setIsEditing(false) internally)
        }
        if (selectedTextId) {
          setSelectedTextId(null); // Deselect the text box
          onSelectTextOverlay(null); // Notify parent
        }
      }
      // If isClickOnActualTextOverlayContent is true (and not clickedDirectlyOnSpreadCanvasContainer),
      // it means a text overlay was clicked, so no deselection should occur here.
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isEditing, selectedTextId, textOverlays, onUpdateTextOverlay, textControlsBarRef, textEditorRef, containerRef, setIsEditing, setSelectedTextId, commitPendingEdits]);

  // Helper function to start text editing and select all text
  const startTextEditing = () => {
    setIsEditing(true);
    // Use a slightly longer timeout to ensure the textarea is properly rendered
    setTimeout(() => {
      if (textInputRef.current && selectedTextId) {
        // Find the current overlay to get its content
        const overlay = textOverlays.find(o => o.id === selectedTextId);
        if (overlay) {
          // Initialize editingContent with the current content if not already set
          if (overlay.editingContent === undefined) {
            updateTextOverlay({ editingContent: overlay.content });
          }

          // Ensure the text box width is correct for the current content
          const textContainer = textInputRef.current.parentElement;
          if (textContainer) {
            const newWidth = updateTextBoxWidth(selectedTextId);
            if (newWidth) {
              textContainer.style.width = `${newWidth}%`;
              textInputRef.current.style.width = '100%';
            }
          }
        }
        textInputRef.current.focus();
        textInputRef.current.select(); // Use only the standard select method
      }
    }, 50); // Keep the timeout
  };

  // Handle double click to edit text
  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault(); // Prevent default double-click behaviors
    if (selectedTextId) {
      // Before starting to edit, ensure the text box width is correct
      updateTextBoxWidth(selectedTextId);
      startTextEditing();
    }
  };

  // Helper function to calculate text width and update text box width
  const calculateAndUpdateTextWidth = (text: string, overlay: TextOverlay, updateOverlay = true) => {
    if (!containerRef.current) return null;

    // Create a temporary span to measure text width
    const tempSpan = document.createElement('span');
    tempSpan.style.visibility = 'hidden';
    tempSpan.style.position = 'absolute';
    tempSpan.style.whiteSpace = 'nowrap';
    tempSpan.style.fontFamily = overlay.style.fontFamily;
    tempSpan.style.fontSize = `${(overlay.style.fontSize / 100) * currentContainerHeight}px`;
    tempSpan.style.fontWeight = overlay.style.bold ? 'bold' : 'normal';
    tempSpan.style.fontStyle = overlay.style.italic ? 'italic' : 'normal';
    tempSpan.style.textDecoration = overlay.style.underline ? 'underline' : 'none';
    tempSpan.innerText = text || ' '; // Use space for empty text to maintain minimum width

    document.body.appendChild(tempSpan);
    const textWidth = tempSpan.getBoundingClientRect().width;
    document.body.removeChild(tempSpan);

    // Add padding to the measured width (20px for padding)
    const paddedWidth = textWidth + 20;

    // Calculate width as percentage of container width
    const containerWidth = containerRef.current.getBoundingClientRect().width;
    const widthPercentage = (paddedWidth / containerWidth) * 100;
    const newWidth = Math.max(widthPercentage, 5); // Ensure minimum width of 5%

    if (updateOverlay) {
      // Create a new overlay with the updated width
      const updatedOverlay = {
        ...overlay,
        style: {
          ...overlay.style,
          width: newWidth
        }
      };

      // Update the overlay with new width
      onUpdateTextOverlay(updatedOverlay);
    }

    return newWidth;
  };

  // Handle text input change
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (selectedTextId && normalizedSelectedText) {
      // Get the current overlay to ensure we have the latest style properties
      const currentOverlay = textOverlays.find(overlay => overlay.id === selectedTextId);
      if (currentOverlay) {
        // Remove any newline characters to enforce single line
        const newValue = e.target.value.replace(/\n/g, '');

        // Dynamically adjust the width of the textarea to fit the content
        if (textInputRef.current && containerRef.current) {
          // Calculate new width based on text content
          const newWidth = calculateAndUpdateTextWidth(newValue, {
            ...currentOverlay,
            editingContent: newValue,
            content: newValue
          }, false); // Don't update overlay yet

          if (newWidth) {
            // Create a new overlay with the updated text and width
            const updatedOverlay = {
              ...currentOverlay,
              editingContent: newValue,
              // Update content during typing to ensure consistent behavior
              content: newValue,
              style: {
                ...currentOverlay.style,
                width: newWidth
              }
            };

            // Update the overlay with new width and content
            onUpdateTextOverlay(updatedOverlay);

            // Update both the container and textarea width for immediate visual feedback
            if (textInputRef.current) {
              // Set the parent container width
              const textContainer = textInputRef.current.parentElement;
              if (textContainer) {
                textContainer.style.width = `${newWidth}%`;
                // Also ensure the textarea is properly sized within its container
                textInputRef.current.style.width = '100%';
              }
            }
          }
        } else {
          // Fallback if refs aren't available
          const updatedOverlay = {
            ...currentOverlay,
            editingContent: newValue,
            content: newValue
          };
          onUpdateTextOverlay(updatedOverlay);
        }
      }
    }
  };

  // Function to update text box width when needed
  const updateTextBoxWidth = (textId: string) => {
    if (!containerRef.current) return;

    const overlay = textOverlays.find(o => o.id === textId);
    if (!overlay) return;

    const textContent = overlay.editingContent !== undefined
      ? overlay.editingContent
      : overlay.content;

    // Calculate new width based on text content and current style
    const newWidth = calculateAndUpdateTextWidth(textContent, overlay, false);

    if (newWidth && newWidth !== overlay.style.width) {
      // Create updated overlay with new width
      const updatedOverlay = {
        ...overlay,
        style: {
          ...overlay.style,
          width: newWidth
        }
      };

      // Update the overlay
      onUpdateTextOverlay(updatedOverlay);

      // Update the DOM element width for immediate visual feedback
      const textContainer = document.querySelector(`[data-text-id="${textId}"]`) as HTMLElement;
      if (textContainer) {
        textContainer.style.width = `${newWidth}%`;
      }

      return newWidth;
    }

    return null;
  };

  // Removed control-specific handlers:
  // getFontDisplayName, handleFontFamilyChange, handleFontSizeChange,
  // handleColorPickerOpen, handleColorChange, handleAlignmentChange,
  // toggleBold, toggleItalic, toggleUnderline,
  // handleOpacityChange, handleWidthChange

  // Handle dragging for position
  const handleMouseDown = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();

    // Don't initiate drag if color picker is open (colorPickerOpen state removed, so this check is no longer needed here)
    // if (colorPickerOpen) return;

    if (selectedTextId === id && !isEditing) {
      console.log("Text drag started");
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && selectedTextId && selectedText && containerRef.current) {
      e.stopPropagation();
      e.preventDefault();

      const containerRect = containerRef.current.getBoundingClientRect();
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;

      // Convert pixel movement to percentage movement
      const percentX = (deltaX / containerRect.width) * 100;
      const percentY = (deltaY / containerRect.height) * 100;

      console.log("Text dragging", { deltaX, deltaY, percentX, percentY });

      // Calculate new position
      const newX = Math.min(Math.max(selectedText.style.x + percentX, selectedText.style.width / 2), 100 - selectedText.style.width / 2);
      const newY = Math.min(Math.max(selectedText.style.y + percentY, 10), 90);

      // Update position
      updateTextOverlay({
        style: {
          ...selectedText.style,
          x: newX,
          y: newY
        }
      });

      // Update drag start position
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };

  const handleMouseUp = () => {
    if (isDragging) {
      console.log("Text drag ended");
    }
    setIsDragging(false);
  };

  // Set up mouse move and mouse up event listeners for dragging TEXT ELEMENTS
  useEffect(() => {
    if (isDragging) { // This is for dragging the text element itself
      const handleGlobalMouseMove = (e: MouseEvent) => {
        // Don't process mouse moves if color picker is open (colorPickerOpen state removed)
        // if (colorPickerOpen) return;

        if (isDragging && selectedTextId && selectedText && containerRef.current) {
          const containerRect = containerRef.current.getBoundingClientRect();
          const deltaX = e.clientX - dragStart.x;
          const deltaY = e.clientY - dragStart.y;

          // Convert pixel movement to percentage movement
          const percentX = (deltaX / containerRect.width) * 100;
          const percentY = (deltaY / containerRect.height) * 100;

          console.log("Global text dragging", { deltaX, deltaY, percentX, percentY });

          // Calculate new position
          const newX = Math.min(Math.max(selectedText.style.x + percentX, selectedText.style.width / 2), 100 - selectedText.style.width / 2);
          const newY = Math.min(Math.max(selectedText.style.y + percentY, 10), 90);

          // Update position
          updateTextOverlay({
            style: {
              ...selectedText.style,
              x: newX,
              y: newY
            }
          });

          // Update drag start position
          setDragStart({ x: e.clientX, y: e.clientY });
        }
      };

      const handleGlobalMouseUp = () => {
        setIsDragging(false);
      };

      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging, selectedTextId, selectedText, containerRef, dragStart, updateTextOverlay]); // Removed colorPickerOpen from dependencies

  // Effect to monitor container size changes
  useEffect(() => {
    const currentElement = containerRef.current; // Capture current value for cleanup
    if (currentElement) {
      const observer = new ResizeObserver((entries: ResizeObserverEntry[]) => {
        for (let entry of entries) {
          setCurrentContainerHeight(entry.target.getBoundingClientRect().height);
        }
      });
      observer.observe(currentElement);
      // Set initial height
      setCurrentContainerHeight(currentElement.getBoundingClientRect().height);

      return () => {
        observer.unobserve(currentElement); // More specific cleanup
        observer.disconnect();
      };
    }
  }, [containerRef]);

  // Effect to monitor text overlay changes and update text box width when needed
  useEffect(() => {
    // Find the currently selected text overlay
    const selectedOverlay = textOverlays.find(overlay => overlay.id === selectedTextId);
    if (!selectedOverlay || !containerRef.current) return;

    // Store the previous font size to detect changes
    const prevFontSize = selectedOverlay.style.fontSize;
    const prevFontFamily = selectedOverlay.style.fontFamily;
    const prevBold = selectedOverlay.style.bold;
    const prevItalic = selectedOverlay.style.italic;
    const prevUnderline = selectedOverlay.style.underline;

    // Create a function to check if text style properties have changed
    const checkAndUpdateTextWidth = () => {
      const currentOverlay = textOverlays.find(overlay => overlay.id === selectedTextId);
      if (!currentOverlay) return;

      // Check if any text style properties have changed
      if (
        currentOverlay.style.fontSize !== prevFontSize ||
        currentOverlay.style.fontFamily !== prevFontFamily ||
        currentOverlay.style.bold !== prevBold ||
        currentOverlay.style.italic !== prevItalic ||
        currentOverlay.style.underline !== prevUnderline
      ) {
        // Use the updateTextBoxWidth function to recalculate and update width
        updateTextBoxWidth(selectedTextId);
      }
    };

    // Set up a MutationObserver to watch for style changes
    const textContainer = document.querySelector(`[data-text-id="${selectedTextId}"]`);
    if (textContainer) {
      const observer = new MutationObserver((mutations) => {
        // Check if any mutations affect style attributes
        const hasStyleChanges = mutations.some(mutation =>
          mutation.type === 'attributes' &&
          (mutation.attributeName === 'style' || mutation.attributeName === 'class')
        );

        if (hasStyleChanges) {
          checkAndUpdateTextWidth();
        }
      });

      observer.observe(textContainer, {
        attributes: true,
        attributeFilter: ['style', 'class'],
        subtree: true
      });

      return () => {
        observer.disconnect();
      };
    }

    // Also check for changes in the textOverlays array
    checkAndUpdateTextWidth();

    // Update text box width whenever the selected text overlay changes
    updateTextBoxWidth(selectedTextId);

  }, [textOverlays, selectedTextId, updateTextBoxWidth, containerRef]);

  // Removed controls bar dragging logic (handleMouseDownOnControls, useEffect for controls bar dragging)
  // Removed handleDeleteText as it will be handled by BookEditor via the controls

  // Function to handle keyboard events during text editing
  const handleTextEditorKeyDown = (e: React.KeyboardEvent) => {
    if (isEditing && selectedTextId) {
      // Handle Escape key to close the text editor
      if (e.key === 'Escape') {
        e.preventDefault();
        // Find the current overlay
        const overlay = textOverlays.find(o => o.id === selectedTextId);
        if (overlay) {
          // Discard any edits by resetting to original content
          updateTextOverlay({
            editingContent: undefined
          });
        }
        setIsEditing(false);
        onSelectTextOverlay(null); // Deselect the text overlay to close the controls bar
        e.stopPropagation();
      }
      // Handle Enter key to commit changes
      if (e.key === 'Enter') {
        e.preventDefault(); // Always prevent newlines
        // Find the current overlay
        const overlay = textOverlays.find(o => o.id === selectedTextId);
        if (overlay && overlay.editingContent !== undefined) {
          // Calculate new width based on the current text
          const newWidth = calculateAndUpdateTextWidth(overlay.editingContent, overlay, false);

          // Commit the edited content with updated width
          updateTextOverlay({
            content: overlay.editingContent,
            editingContent: undefined,
            style: {
              ...overlay.style,
              width: newWidth || overlay.style.width // Use calculated width or keep current width
            }
          });

          // Update the DOM element width for immediate visual feedback
          const textContainer = document.querySelector(`[data-text-id="${selectedTextId}"]`) as HTMLElement;
          if (textContainer && newWidth) {
            textContainer.style.width = `${newWidth}%`;
          }
        }
        setIsEditing(false);
        e.stopPropagation();
      }
      // Handle Tab key to allow focus to move to text controls while preserving edit state
      if (e.key === 'Tab') {
        // Let the tab event proceed naturally to move focus to controls
        // but don't exit editing mode
        return;
      }
      // Allow default textarea behavior for other keys
    }
  };

  // Set up global keyboard event capture when in edit mode
  useEffect(() => {
    if (!isEditing) return;

    const preventKeyboardPropagation = (e: KeyboardEvent) => {
      // Prevent the event from reaching other handlers only when editing
      if (textInputRef.current && textInputRef.current.contains(e.target as Node)) {
        // Special handling for Escape key to end editing
        if (e.key === 'Escape') {
          setIsEditing(false);
          onSelectTextOverlay(null); // Deselect the text overlay to close the controls bar
          e.stopPropagation(); // Stop Escape from propagating further if handled here.
          e.preventDefault();  // Prevent any default browser action for Escape.
        }
        // Special handling for Tab key to allow focus to move to controls
        if (e.key === 'Tab') {
          // Allow tab to move focus to controls without ending editing
          return;
        }
        // For other keys, do NOT call e.stopPropagation() here.
        // This allows the textarea to receive and process them for normal input.
        // The original unconditional e.stopPropagation() was preventing typing.
      }
    };

    // Add the capture phase listener, which runs before bubbling phase handlers
    document.addEventListener('keydown', preventKeyboardPropagation, true);

    return () => {
      document.removeEventListener('keydown', preventKeyboardPropagation, true);
    };
  }, [isEditing, onSelectTextOverlay]);

  // Add keyboard shortcut for editing when text is selected
  useEffect(() => {
    if (!selectedTextId || isEditing) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Start editing on Enter or F2 when text is selected
      if ((e.key === 'Enter' || e.key === 'F2') && selectedTextId && !isEditing) {
        e.preventDefault();
        startTextEditing();
      }
      // Close text editor bar with Escape when text is selected but not editing
      if (e.key === 'Escape' && selectedTextId && !isEditing) {
        e.preventDefault();
        onSelectTextOverlay(null); // Deselect the text overlay to close the controls bar
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedTextId, isEditing]);

  return (
    <div ref={textEditorRef} className="absolute inset-0 z-40 pointer-events-none">
      {/* Text overlays */}
      <AnimatePresence>
        {currentSpreadOverlays.map(text => {
        const actualFontSizePx = currentContainerHeight > 0 && text.style.fontSize > 0
          ? (text.style.fontSize / 100) * currentContainerHeight
          : 0; // Render with 0px font size if container height is not yet known or fontSize is 0

        return (
          <motion.div
            key={text.id}
            data-text-id={text.id}
            className={cn(
              "absolute cursor-move pointer-events-auto inline-block",
              selectedTextId === text.id ? "ring-2 ring-blue-500" : "ring-0"
            )}
            style={{
              left: `${text.style.x}%`,
              top: `${text.style.y}%`,
              width: `${text.style.width}%`,
              opacity: text.style.opacity,
              userSelect: isEditing && selectedTextId === text.id ? 'text' : 'none',
            }}
            initial={{
              transform: 'translate(-50%, -50%)',
              opacity: 0,
              // x: navigationDirection === 'next' ? -300 : 300, // REMOVED: Sliding handled by parent
              scale: 0.95 // Subtle scale
            }}
            animate={{
              transform: 'translate(-50%, -50%)',
              opacity: text.style.opacity, // Use dynamic opacity from style
              // x: 0, // REMOVED: Sliding handled by parent
              scale: 1
            }}
            exit={{
              opacity: 0,
              // x: navigationDirection === 'prev' ? -500 : 500, // REMOVED: Sliding handled by parent
              scale: 0.95, // Subtle scale
              // transition: { duration: 0.4, ease: "easeOut" } // Use main transition
            }}
            transition={{ duration: 0.2, ease: "easeOut" }} // Shorter duration for subtle fade/scale
            onClick={(e) => handleTextSelect(text.id, e)}
            onDoubleClick={handleDoubleClick}
            onMouseDown={(e) => handleMouseDown(e, text.id)}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            {isEditing && selectedTextId === text.id ? (
              <textarea
                ref={textInputRef}
                rows={1}
                className="p-2 border-none outline-none bg-transparent resize-none whitespace-nowrap"
                style={{
                  fontFamily: text.style.fontFamily,
                  fontSize: `${actualFontSizePx}px`,
                  color: text.style.color,
                  fontWeight: text.style.bold ? 'bold' : 'normal',
                  fontStyle: text.style.italic ? 'italic' : 'normal',
                  textDecoration: text.style.underline ? 'underline' : 'none',
                  textAlign: text.style.textAlign,
                  width: '100%',  // Ensure textarea uses full width of container
                  boxSizing: 'border-box' // Include padding in width calculation
                }}
                value={text.editingContent !== undefined ? text.editingContent : text.content}
                onChange={handleTextChange}
                onKeyDown={handleTextEditorKeyDown}
                onBlur={(e) => {
                  // Only handle blur if we're not clicking on text controls
                  // This prevents text from being reset when clicking on controls
                  const relatedTarget = e.relatedTarget as Node | null;
                  const isClickingOnControls = relatedTarget && textControlsBarRef?.current?.contains(relatedTarget);

                  // If clicking on controls, don't handle blur - let the controls handle their own updates
                  if (isClickingOnControls) {
                    e.preventDefault();
                    return;
                  }

                  // Get the current text from the textarea
                  const currentText = e.target.value;

                  // Find the exact overlay from the current textOverlays array
                  // This ensures we have the most up-to-date style properties
                  const currentOverlay = textOverlays.find(o => o.id === text.id);

                  if (currentOverlay) {
                    // Calculate new width based on the current text
                    const newWidth = calculateAndUpdateTextWidth(currentText, {
                      ...currentOverlay,
                      content: currentText,
                      editingContent: undefined
                    }, false); // Don't update overlay yet

                    // Create a completely new overlay object with the updated text and width
                    const updatedOverlay = {
                      ...currentOverlay,
                      content: currentText,
                      editingContent: undefined,
                      style: {
                        ...currentOverlay.style,
                        width: newWidth || currentOverlay.style.width // Use calculated width or keep current width
                      }
                    };

                    // IMPORTANT: Update before setting isEditing to false
                    // This ensures we update with the full context still available
                    onUpdateTextOverlay(updatedOverlay);

                    // Update the DOM element width for immediate visual feedback
                    const textContainer = document.querySelector(`[data-text-id="${text.id}"]`) as HTMLElement;
                    if (textContainer && newWidth) {
                      textContainer.style.width = `${newWidth}%`;
                    }

                    // AFTER updating the overlay, set editing to false
                    setIsEditing(false);
                  }
                }}
                onClick={(e) => e.stopPropagation()}
              />
            ) : (
              <div
                className="whitespace-nowrap overflow-visible cursor-move w-full" // Added w-full to ensure text container uses full width
                style={{
                  fontFamily: text.style.fontFamily,
                  fontSize: `${actualFontSizePx}px`,
                  color: text.style.color,
                  fontWeight: text.style.bold ? 'bold' : 'normal',
                  fontStyle: text.style.italic ? 'italic' : 'normal',
                  textDecoration: text.style.underline ? 'underline' : 'none',
                  textAlign: text.style.textAlign,
                  width: '100%', // Ensure div uses full width of parent container
                  boxSizing: 'border-box', // Include padding in width calculation
                }}
                onMouseDown={(e) => handleMouseDown(e, text.id)}
                onClick={(e) => {
                  // If it's a double click, handleDoubleClick will be called instead
                  // Only handle single clicks here
                  if (e.detail === 1) {
                    e.stopPropagation();
                    handleTextSelect(text.id, e);
                  }
                }}
              >
                {text.content}
              </div>
            )}
          </motion.div>
        );
      })}
      </AnimatePresence>

      {/* Controls have been moved to BookEditor.tsx */}
    </div>
  );
};

export default TextEditor;