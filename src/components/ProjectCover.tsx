import React, { useState, useEffect, useCallback, useRef, forwardRef, useImperativeHandle } from 'react';
import { ChevronLeft, ChevronRight, Upload, X, Undo, Redo, Eye, Star, FolderSearch, Pencil, RefreshCcw } from 'lucide-react'; // Added Undo, Redo for potential buttons, Added Eye, Added Star, Added FolderSearch, Added Pencil, Added RefreshCcw
import { ImageFile } from '@/components/ImageTray';
import { toast } from 'sonner';
import { useUndoRedo, CoverState as GlobalCoverState } from '@/hooks/useUndoRedo'; // Import the hook and GlobalCoverState
import { normalizeBackgroundImageUrl } from '@/utils/imageUtils';
import throttle from 'lodash/throttle';
import debounce from 'lodash/debounce';
import { Slider } from '@/components/ui/slider';
import { ImageQualityInfo, ImageQualityStatus, TARGET_DPI, calculateEffectiveDpi } from '@/lib/imageQuality';
import ImageQualityWarning from './ImageQualityWarning';
import { TextEditor, TextOverlay } from './TextEditor'; // Import TextEditor component
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import {
 Dialog,
 DialogContent,
 DialogHeader,
 DialogTitle,
 DialogDescription,
} from "@/components/ui/dialog";
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { cn } from "@/lib/utils"; // Import the cn utility

// Define ImageTransform interface (similar to SpreadCanvas)
// interface ImageTransform { // This seems unused, consider removing if confirmed
//   scale: number;
//   focalX: number;
//   focalY: number;
//   fit: 'contain' | 'cover';
// }

// State structure for undo/redo history specific to ProjectCover
interface CoverPageHistoryState extends GlobalCoverState {} // Ensure local state matches global structure if needed, or use GlobalCoverState directly. For now, they are identical.

export interface ProjectCoverHandle {
  captureCurrentCoverState: () => GlobalCoverState | null;
  applyRestoredState: (state: GlobalCoverState) => void;
  resetLocalHistory: () => void;
}

interface ProjectCoverProps {
  theme: 'light' | 'dark'; // Added theme prop
  projectWidth: number;
  projectHeight: number;
  fullSpreadWidth: number;
  fullSpreadHeight: number;
  onNavigateForward: () => void;
  coverImage?: ImageFile | null;
  onUpdateCoverImage?: (image: ImageFile | null) => void;
  trackPreviewRequest?: (originalPath: string, resetCounter?: boolean) => void;
  getImageDimensions?: (imageId: string) => { width: number; height: number } | undefined;
  showQualityIndicators?: boolean;
  dpiWarningThresholdPercent?: number;
  textOverlays?: TextOverlay[];
  onUpdateTextOverlay?: (textOverlay: TextOverlay) => void;
  onSelectTextOverlay?: (overlay: TextOverlay | null) => void;
  textControlsBarRef?: React.RefObject<HTMLDivElement>;
  // Core transform props
  coverScale?: number;
  coverFocalX?: number;
  coverFocalY?: number;
  onUpdateCoverScale?: (scale: number) => void;
  onUpdateCoverFocalX?: (focalX: number) => void;
  onUpdateCoverFocalY?: (focalY: number) => void;
  isRestoringProject?: boolean;
  // Deprecated coverTranslateX and coverTranslateY props are now fully removed.
}

const ProjectCover = forwardRef<ProjectCoverHandle, ProjectCoverProps>(({
  projectWidth,
  projectHeight,
  fullSpreadWidth,
  fullSpreadHeight,
  onNavigateForward,
  coverImage,
  onUpdateCoverImage,
  trackPreviewRequest,
  getImageDimensions,
  showQualityIndicators = true,
  dpiWarningThresholdPercent = 70,
  textOverlays = [],
  onUpdateTextOverlay,
  onSelectTextOverlay,
  textControlsBarRef,
  // Destructure primary transform props
  coverScale = 1,
  coverFocalX = 0.5,
  coverFocalY = 0.5,
  onUpdateCoverScale,
  onUpdateCoverFocalX,
  onUpdateCoverFocalY,
  isRestoringProject,
  theme, // Destructure theme prop
  // Deprecated props (coverTranslateX, coverTranslateY and their updaters) are not destructured.
}, ref) => {
  // The actual cover's dimensions (like a single page)
  const coverPageWidth = projectWidth; // Use the full projectWidth (single page width)
  const coverPageHeight = projectHeight;

  // Undo/Redo hook
  const {
    saveState: saveUndoState, // Renamed to avoid conflict if a local saveState function exists
    undo: performUndo,
    redo: performRedo,
    canUndo,
    canRedo,
    isRestoringRef: localIsRestoringRef, // Renamed to avoid conflict
    resetHistory: localResetHistory,
  } = useUndoRedo<CoverPageHistoryState>();
  
  // Create a ref to store image dimensions for fallback
  const imageDimensionsCache = useRef<Record<string, { width: number; height: number }>>({});
  
  // Create a fallback getImageDimensions function
  const getImageDimensionsWithFallback = useCallback((imageId: string): { width: number; height: number } | undefined => {
    if (getImageDimensions) {
      return getImageDimensions(imageId);
    }
    if (imageDimensionsCache.current[imageId]) {
      return imageDimensionsCache.current[imageId];
    }
    return { width: 1000, height: 1000 };
  }, [getImageDimensions]);
  
  // State for drag and drop
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const dragCounter = React.useRef(0);
  
  // State to track if the cover image is being hovered over
  const [isHovered, setIsHovered] = useState(false);

  // Use props for transform values
  const scale = coverScale;
  const focalX = coverFocalX;
  const focalY = coverFocalY;
  
  const [isPanning, setIsPanning] = useState(false);
  const panStartCoordsRef = useRef<{ x: number; y: number; startFocalX: number; startFocalY: number } | null>(null);
  const [cursor, setCursor] = useState<'grab' | 'grabbing' | 'default'>('default');
  const [imageNaturalDimsLoadedKey, setImageNaturalDimsLoadedKey] = useState(0); // To force re-render after image dimensions are loaded
  const [coverImageRefreshTimestamp, setCoverImageRefreshTimestamp] = useState<number>(0); // For Photoshop roundtrip refresh
  
  const coverContainerRef = useRef<HTMLDivElement | null>(null);
  const prevContainerDimensionsRef = useRef<{ width: number; height: number } | null>(null);
  const isInitialMountRef = useRef(true); // To track the very first mount cycle
  const isInitializingProjectRef = useRef(false);
  const initialLoadSettledRef = useRef(false);
  const isForcingPersistedTransformsRef = useRef(false);

  // Logging system for shift detection
  const logCountRef = useRef(0);
  const MAX_LOGS_PER_CYCLE = 10;
  const expectedTransformRef = useRef<{ scale: number; focalX: number; focalY: number } | null>(null);
  
  // State for image preview modal
  const [showFullImageModal, setShowFullImageModal] = useState(false);
  const [modalImageSrc, setModalImageSrc] = useState<string | null>(null);
  const [modalImageFilename, setModalImageFilename] = useState<string | null>(null);
  const [imageExifData, setImageExifData] = useState<any | null>(null);
  const [loadingExif, setLoadingExif] = useState(false);

  // Helper function to detect and log shifts
  const detectAndLogShift = useCallback((reason: string) => {
    if (!expectedTransformRef.current || logCountRef.current >= MAX_LOGS_PER_CYCLE) return;
    
    const current = { scale, focalX, focalY };
    const expected = expectedTransformRef.current;
    
    const scaleDelta = Math.abs(current.scale - expected.scale);
    const focalXDelta = Math.abs(current.focalX - expected.focalX);
    const focalYDelta = Math.abs(current.focalY - expected.focalY);
    
    // Log shifts (significant focal change or scale change)
    if (focalXDelta > 0.01 || focalYDelta > 0.01 || scaleDelta > 0.001) {
      logCountRef.current++;
    }
  }, [scale, focalX, focalY]);
  
  const [containerDimensions, setContainerDimensions] = useState<{ width: number; height: number }>({ width: 0, height: 0 });
  const [imageQualityInfo, setImageQualityInfo] = useState<ImageQualityInfo | null>(null);

  const THROTTLE_INTERVAL = 50;

  const localCaptureCurrentCoverState = useCallback((): CoverPageHistoryState => {
    let pixelTranslateX = 0;
    let pixelTranslateY = 0;
    let currentEditorMaxTranslateX = 0;
    let currentEditorMaxTranslateY = 0;

    if (coverContainerRef.current && coverImage && containerDimensions.width > 0 && containerDimensions.height > 0) {
      const imgNativeDims = getImageDimensionsWithFallback(coverImage.id);
      if (imgNativeDims && imgNativeDims.width > 0 && imgNativeDims.height > 0) {
        const containerW = containerDimensions.width;
        const containerH = containerDimensions.height;
        const imageW = imgNativeDims.width;
        const imageH = imgNativeDims.height;
        const imageAspectRatio = imageW / imageH;
        const containerAspectRatio = containerW / containerH;
        let baseContainedW, baseContainedH;
        if (imageAspectRatio > containerAspectRatio) {
          baseContainedW = containerW;
          baseContainedH = containerW / imageAspectRatio;
        } else {
          baseContainedH = containerH;
          baseContainedW = containerH * imageAspectRatio;
        }
        const finalScaledW = baseContainedW * scale;
        const finalScaledH = baseContainedH * scale;

        // Calculate the 'left' and 'top' based on focal points
        const offsetX = (containerW / 2) - (finalScaledW * focalX);
        const offsetY = (containerH / 2) - (finalScaledH * focalY);
        
        // Convert 'left'/'top' (which are absolute positions of the image's top-left corner)
        // to 'translateX'/'translateY' (which are offsets from the *centered* position of the scaled image)
        const centeredLeft = (containerW - finalScaledW) / 2;
        const centeredTop = (containerH - finalScaledH) / 2;
        pixelTranslateX = offsetX - centeredLeft;
        pixelTranslateY = offsetY - centeredTop;
        
        currentEditorMaxTranslateX = Math.max(0, (finalScaledW - containerW) / 2);
        currentEditorMaxTranslateY = Math.max(0, (finalScaledH - containerH) / 2);
      }
    }

    // This object structure MUST match GlobalCoverState in useUndoRedo.ts
    const undoState: GlobalCoverState = {
      coverImage: coverImage,
      scale: scale,
      translateX: pixelTranslateX, // Kept for potential compatibility if GlobalCoverState isn't immediately updated everywhere
      translateY: pixelTranslateY, // Kept for potential compatibility
      focalX: focalX, // Save current focalX
      focalY: focalY, // Save current focalY
      editorMaxTranslateX: currentEditorMaxTranslateX,
      editorMaxTranslateY: currentEditorMaxTranslateY,
    };
    return undoState as CoverPageHistoryState;
  }, [coverImage, scale, focalX, focalY, containerDimensions, getImageDimensionsWithFallback]);

  // Helper to apply a restored state
  const localApplyRestoredState = useCallback((stateToRestore: CoverPageHistoryState | GlobalCoverState) => {
    if (!stateToRestore) return;

    localIsRestoringRef.current = true;

    // Update transform state using callback props
    onUpdateCoverScale?.(stateToRestore.scale);

    // If the restored state has focalX/Y, use them directly.
    // Otherwise, convert from translateX/Y if those are present (for backward compatibility).
    if (stateToRestore.focalX !== undefined && stateToRestore.focalY !== undefined) {
      onUpdateCoverFocalX?.(stateToRestore.focalX);
      onUpdateCoverFocalY?.(stateToRestore.focalY);
    } else if (coverContainerRef.current && stateToRestore.coverImage && containerDimensions.width > 0 && containerDimensions.height > 0) {
      // Fallback to converting translateX/Y if focalX/Y are not in the restored state
      const imgNativeDims = getImageDimensionsWithFallback(stateToRestore.coverImage.id);
      if (imgNativeDims && imgNativeDims.width > 0 && imgNativeDims.height > 0) {
        const containerW = containerDimensions.width;
        const containerH = containerDimensions.height;
        const imageW = imgNativeDims.width;
        const imageH = imgNativeDims.height;
        const imageAspectRatio = imageW / imageH;
        const containerAspectRatio = containerW / containerH;
        let baseContainedW, baseContainedH;

        if (imageAspectRatio > containerAspectRatio) {
          baseContainedW = containerW; baseContainedH = containerW / imageAspectRatio;
        } else {
          baseContainedH = containerH; baseContainedW = containerH * imageAspectRatio;
        }
        const finalScaledW = baseContainedW * stateToRestore.scale;
        const finalScaledH = baseContainedH * stateToRestore.scale;

        if (finalScaledW > 0.01 && finalScaledH > 0.01) {
          let newFocalX = 0.5 - (stateToRestore.translateX / finalScaledW);
          let newFocalY = 0.5 - (stateToRestore.translateY / finalScaledH);
          onUpdateCoverFocalX?.(Math.max(0, Math.min(1, newFocalX)));
          onUpdateCoverFocalY?.(Math.max(0, Math.min(1, newFocalY)));
        } else {
           onUpdateCoverFocalX?.(0.5);
           onUpdateCoverFocalY?.(0.5);
        }
      } else {
         onUpdateCoverFocalX?.(0.5);
         onUpdateCoverFocalY?.(0.5);
      }
    } else {
      onUpdateCoverFocalX?.(0.5);
      onUpdateCoverFocalY?.(0.5);
    }

    // Update state managed by props (coverImage)
    // Check if the image actually changed to avoid unnecessary updates
    if (onUpdateCoverImage &&
        ( (coverImage?.id !== stateToRestore.coverImage?.id) ||
          (!coverImage && stateToRestore.coverImage) ||
          (coverImage && !stateToRestore.coverImage) )
       ) {
      onUpdateCoverImage(stateToRestore.coverImage);
    }
    
    // Delay setting localIsRestoringRef back to false to allow React to process state updates
    setTimeout(() => {
      localIsRestoringRef.current = false;
    }, 0);
  }, [onUpdateCoverImage, onUpdateCoverScale, onUpdateCoverFocalX, onUpdateCoverFocalY, localIsRestoringRef, coverImage, scale, containerDimensions, getImageDimensionsWithFallback]);

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    captureCurrentCoverState: (): GlobalCoverState | null => {
      // For useProjectPersistence: save scale, focalX, focalY.
      // GlobalCoverState might need an update to officially support focalX/Y.
      // For now, we'll cast or add them, assuming persistence can handle it.
      const persistenceState = {
        coverImage: coverImage,
        scale: scale,
        focalX: focalX,
        focalY: focalY,
        // For persistence, we save focalX, focalY.
        // The translateX/Y fields are for GlobalCoverState compatibility if it's not updated.
        // If GlobalCoverState is updated to use focalX/Y, these can be removed.
        // For now, calculate them as localCaptureCurrentCoverState does.
        translateX: 0, // Placeholder, will be filled by a proper calculation if needed
        translateY: 0, // Placeholder
      };
      // Calculate actual translateX/Y for GlobalCoverState compatibility if needed
      if (coverContainerRef.current && containerDimensions.width > 0 && containerDimensions.height > 0 && coverImage) {
        const tempState = localCaptureCurrentCoverState(); // This calculates pixel translateX/Y
        persistenceState.translateX = tempState.translateX;
        persistenceState.translateY = tempState.translateY;
      }

      return persistenceState as GlobalCoverState;
    },
    applyRestoredState: (stateFromPersistence: GlobalCoverState) => {
      if (!stateFromPersistence) return;
      
      const restoredScale = stateFromPersistence.scale;
      // Persistence should primarily use focalX/Y if available in the persisted state.
      const stateWithFocal = stateFromPersistence as any;
      const restoredFocalX = stateWithFocal.focalX ?? 0.5;
      const restoredFocalY = stateWithFocal.focalY ?? 0.5;
      const restoredCoverImage = stateFromPersistence.coverImage;


      localIsRestoringRef.current = true;
      onUpdateCoverScale?.(restoredScale);
      onUpdateCoverFocalX?.(restoredFocalX);
      onUpdateCoverFocalY?.(restoredFocalY);
      
      if (onUpdateCoverImage &&
          ((coverImage?.id !== restoredCoverImage?.id) ||
           (!coverImage && restoredCoverImage) ||
           (coverImage && !restoredCoverImage))) {
        onUpdateCoverImage(restoredCoverImage);
      }
      setTimeout(() => { localIsRestoringRef.current = false; }, 0);
    },
    resetLocalHistory: () => {
      localResetHistory();
    }
  }));

  // Undo/Redo Handlers
  const handleUndo = useCallback(() => {
    if (canUndo) {
      const prevState = performUndo(localCaptureCurrentCoverState());
      if (prevState) {
        localApplyRestoredState(prevState);
        toast.info("Undo applied");
      }
    } else {
      toast.info("Nothing to undo");
    }
  }, [canUndo, performUndo, localCaptureCurrentCoverState, localApplyRestoredState]);

  const handleRedo = useCallback(() => {
    if (canRedo) {
      const nextState = performRedo(localCaptureCurrentCoverState());
      if (nextState) {
        localApplyRestoredState(nextState);
        toast.info("Redo applied");
      }
    } else {
      toast.info("Nothing to redo");
    }
  }, [canRedo, performRedo, localCaptureCurrentCoverState, localApplyRestoredState]);

  // Keyboard shortcuts for Undo/Redo
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
      const metaKey = isMac ? event.metaKey : event.ctrlKey;

      if (metaKey && event.key.toLowerCase() === 'z') {
        event.preventDefault();
        if (event.shiftKey) {
          handleRedo();
        } else {
          handleUndo();
        }
      }
      // Some systems use Ctrl+Y for redo
      if (!isMac && event.ctrlKey && event.key.toLowerCase() === 'y') {
        event.preventDefault();
        handleRedo();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleUndo, handleRedo]);


  // Wrapped state setters
  const setScale = useCallback((newScale: number | ((prevState: number) => number)) => {
    // Don't save undo state during project initialization
    if (!isInitializingProjectRef.current) {
      saveUndoState(localCaptureCurrentCoverState());
    }
    const finalScale = typeof newScale === 'function' ? newScale(scale) : newScale;
    onUpdateCoverScale?.(finalScale);
  }, [saveUndoState, localCaptureCurrentCoverState, scale, onUpdateCoverScale]);

  const setFocalX = useCallback((newFocalX: number | ((prevState: number) => number)) => {
    if (!isInitializingProjectRef.current) {
      // Consider saveUndoState(localCaptureCurrentCoverState()); if focal changes are undoable
    }
    const finalFocalX = typeof newFocalX === 'function' ? newFocalX(focalX) : newFocalX;
    onUpdateCoverFocalX?.(finalFocalX);
  }, [focalX, onUpdateCoverFocalX, saveUndoState, localCaptureCurrentCoverState]);

  const setFocalY = useCallback((newFocalY: number | ((prevState: number) => number)) => {
    if (!isInitializingProjectRef.current) {
      // Consider saveUndoState(localCaptureCurrentCoverState());
    }
    const finalFocalY = typeof newFocalY === 'function' ? newFocalY(focalY) : newFocalY;
    onUpdateCoverFocalY?.(finalFocalY);
  }, [focalY, onUpdateCoverFocalY, saveUndoState, localCaptureCurrentCoverState]);


  // The spread container style - exactly like SpreadCanvas
  const spreadContainerStyle: React.CSSProperties = {
    aspectRatio: `${fullSpreadWidth} / ${fullSpreadHeight}`,
    width: 'calc(100% - 2rem)',
    maxWidth: '100%',
    maxHeight: 'calc(100% - 2rem)',
    overflow: 'hidden',
    position: 'relative',
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
    willChange: 'transform', // Improve performance during scaling
    contain: 'layout paint', // Optimize rendering performance
  };

  // The cover content is positioned on the right half of the spread, representing the front cover
  const coverContentStyle: React.CSSProperties = {
    width: '50%', // Take up exactly half of the spread container
    height: '100%', // Take full height
    backgroundColor: coverImage ? 'transparent' : (theme === 'dark' ? '#171717' : '#f0f0f0'), // Use neutral-900 for dark theme
    border: `1px solid ${theme === 'dark' ? '#404040' : '#ccc'}`, // Use neutral-700 for dark theme border
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
    position: 'absolute',
    top: '0',
    right: '0', // Position it on the right side of the spread
    overflow: 'hidden', // Ensure image doesn't overflow
  };

  // Handle drag events
  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current++;
    
    if (dragCounter.current === 1) {
      setIsDraggingOver(true);
    }
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current--;
    
    if (dragCounter.current === 0) {
      setIsDraggingOver(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Set the visual feedback for the drop operation
    if (e.dataTransfer.types.includes('application/json')) {
      e.dataTransfer.dropEffect = 'copy';
    } else {
      e.dataTransfer.dropEffect = 'none';
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);
    dragCounter.current = 0;
    
    try {
      const imageData = e.dataTransfer.getData('application/json');
      if (!imageData) return;
      
      const parsedData = JSON.parse(imageData);
      const image = Array.isArray(parsedData) ? parsedData[0] : parsedData;
      
      if (!image || !image.id || !image.originalPath) {
        toast.error("Invalid image data");
        return;
      }
      
      if (onUpdateCoverImage) {
        saveUndoState(localCaptureCurrentCoverState()); // Save state before updating
        onUpdateCoverImage(image);
        // Reset transforms when a new image is dropped
        onUpdateCoverScale?.(1);
        onUpdateCoverFocalX?.(0.5); // Reset focal to center
        onUpdateCoverFocalY?.(0.5); // Reset focal to center
        
        if (!image.previewUrl && trackPreviewRequest) {
          // Get the potentially updated path
          let pathForPreview = image.originalPath;
          if (window.bookProofsApp?.getUpdatedFilePath) {
            const updatedPath = window.bookProofsApp.getUpdatedFilePath(image.originalPath);
            if (updatedPath) {
              pathForPreview = updatedPath;
            }
          }
          
          // Track the preview request
          trackPreviewRequest(image.originalPath, true);
          
          // Request preview generation
          if (window.electronAPI?.regenerateThumbnails) {
            window.electronAPI.regenerateThumbnails(pathForPreview)
              .then(result => {
                if (result) {
                  // The onPreviewGenerated listener in useImageImport will handle updating the image state
                } else {
                }
              })
              .catch(error => {
                toast.error(`Failed to generate preview for ${image.name}.`);
              });
          }
        }
        
        toast.success(`Cover image set to ${image.name}`);
      }
    } catch (error) {
      toast.error('Could not set cover image');
    }
  }, [onUpdateCoverImage, trackPreviewRequest]);

  // Effect for ResizeObserver setup
  useEffect(() => {
    if (!coverContainerRef.current) return;

    const observer = new ResizeObserver(entries => {
      if (initialLoadSettledRef.current) { // Only update if settled
        for (const entry of entries) {
          const { width, height } = entry.contentRect;
          if (width > 0 && height > 0) {
            setContainerDimensions({ width, height });
          }
        }
      } else {
      }
    });
    observer.observe(coverContainerRef.current);

    return () => {
      observer.disconnect();
    };
  }, []); // Empty dependency array: runs once on mount for observer setup.

  // Function to animate panning - REMOVED
  // const animatePanning = useCallback(() => { ... }, [isPanning]);

  // Function to animate zooming - REMOVED
  // const animateZooming = useCallback(() => { ... }, []);

  // Throttled function to update image transform - REMOVED
  // const throttledUpdateTransform = useCallback( ... , [isPanning, animatePanning]);

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    // Block all interactions during restoration to preserve loaded state
    if (isRestoringProject || isInitializingProjectRef.current || !initialLoadSettledRef.current) {
      return;
    }
    
    if (e.button !== 0 || !coverImage || scale <= 1) return;
    e.preventDefault();
    
    // Save state for local undo before starting pan
    saveUndoState(localCaptureCurrentCoverState());
    
    setIsPanning(true);
    setCursor('grabbing');
    panStartCoordsRef.current = {
      x: e.clientX,
      y: e.clientY,
      startFocalX: focalX,
      startFocalY: focalY,
    };
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isRestoringProject || isInitializingProjectRef.current || !initialLoadSettledRef.current) return;
    if (!isPanning || !panStartCoordsRef.current || !coverContainerRef.current || !coverImage) return;

    const imgNativeDims = getImageDimensionsWithFallback(coverImage.id);
    const containerRect = coverContainerRef.current.getBoundingClientRect();

    if (!imgNativeDims || !containerRect.width || !containerRect.height || imgNativeDims.width <= 0 || imgNativeDims.height <= 0) return;

    const imageW = imgNativeDims.width;
    const imageH = imgNativeDims.height;
    const containerW = containerRect.width;
    const containerH = containerRect.height;

    const imageAspectRatio = imageW / imageH;
    const containerAspectRatio = containerW / containerH;
    let containedW, containedH;
    if (imageAspectRatio > containerAspectRatio) {
      containedW = containerW;
      containedH = containerW / imageAspectRatio;
    } else {
      containedH = containerH;
      containedW = containerH * imageAspectRatio;
    }
    
    const finalScaledW = containedW * scale;
    const finalScaledH = containedH * scale;

    if (finalScaledW <= 0 || finalScaledH <= 0) return;

    const dx = e.clientX - panStartCoordsRef.current.x;
    const dy = e.clientY - panStartCoordsRef.current.y;

    const focalDeltaX = -(dx / finalScaledW);
    const focalDeltaY = -(dy / finalScaledH);

    let newFocalX = panStartCoordsRef.current.startFocalX + focalDeltaX;
    let newFocalY = panStartCoordsRef.current.startFocalY + focalDeltaY;

    const maxDeviationX = (finalScaledW > containerW) ? (1 - (containerW / finalScaledW)) / 2 : 0;
    const maxDeviationY = (finalScaledH > containerH) ? (1 - (containerH / finalScaledH)) / 2 : 0;

    newFocalX = Math.max(0.5 - maxDeviationX, Math.min(0.5 + maxDeviationX, newFocalX));
    newFocalY = Math.max(0.5 - maxDeviationY, Math.min(0.5 + maxDeviationY, newFocalY));
    
    newFocalX = Math.max(0, Math.min(1, newFocalX)); // Clamp to 0-1
    newFocalY = Math.max(0, Math.min(1, newFocalY));

    onUpdateCoverFocalX?.(newFocalX);
    onUpdateCoverFocalY?.(newFocalY);
  };

  const handleMouseUpOrLeave = () => {
    // Block all interactions during restoration to preserve loaded state
    if (isRestoringProject || isInitializingProjectRef.current || !initialLoadSettledRef.current) {
      return;
    }
    
    if (isPanning) {
      setIsPanning(false);
      setCursor(scale > 1 ? 'grab' : 'default');
      panStartCoordsRef.current = null;
    }
  };

  useEffect(() => {
    // Skip during project restoration or if explicitly forcing persisted transforms
    if (isRestoringProject || isInitializingProjectRef.current || isForcingPersistedTransformsRef.current) {
      return;
    }
    
    if (scale <= 1) {
      // Check if translations are already 0 to prevent unnecessary state save if called from wrapped setter
      if (focalX !== 0.5 || focalY !== 0.5) {
        onUpdateCoverFocalX?.(0.5);
        onUpdateCoverFocalY?.(0.5);
      }
      setCursor('default');
    } else {
       if (!isPanning) setCursor('grab');
    }
  }, [scale, isPanning, focalX, focalY, isRestoringProject, onUpdateCoverFocalX, onUpdateCoverFocalY, isForcingPersistedTransformsRef]);

  // Removed the ResizeAdjust useEffect block that was here.
  // The principle is that if the container (#coverContainerRef styled by coverContentStyle)
  // maintains its aspect ratio due to its parent (#spreadRenderContainerRef styled by spreadContainerStyle),
  // then the CSS transform: translate(X,Y) scale(S) applied in calculateImageStyle
  // should visually scale correctly without needing JavaScript to further adjust translateX/Y.

  // Ref to store initial transforms, now correctly typed for focal points.
  const persistedInitialTransformsRef = useRef<{ scale: number; focalX: number; focalY: number } | null>(null);

  // Effect for delayed re-application of persisted transforms
  useEffect(() => {
    let reapplyTimerId: NodeJS.Timeout | undefined;

    const effectInstanceIsInitialMount = isInitialMountRef.current;
    const effectInstanceIsRestoringProject = isRestoringProject;

    if (effectInstanceIsInitialMount || effectInstanceIsRestoringProject) {
      persistedInitialTransformsRef.current = {
        scale: coverScale,
        focalX: coverFocalX, // Store initial coverFocalX prop
        focalY: coverFocalY, // Store initial coverFocalY prop
      };
      
      initialLoadSettledRef.current = false;
      if (effectInstanceIsRestoringProject) { // Use captured value
        isInitializingProjectRef.current = true;
        expectedTransformRef.current = { scale: coverScale, focalX: coverFocalX, focalY: coverFocalY };
      }

      reapplyTimerId = setTimeout(() => {

        isForcingPersistedTransformsRef.current = true; // Set flag before re-applying

        if (persistedInitialTransformsRef.current) {
          const { scale: persistedScale, focalX: persistedFocalX, focalY: persistedFocalY } = persistedInitialTransformsRef.current;
          const skipReapplyDueToInitialMount = effectInstanceIsInitialMount && !effectInstanceIsRestoringProject;

          if (!skipReapplyDueToInitialMount) {
            onUpdateCoverScale?.(persistedScale);
            onUpdateCoverFocalX?.(persistedFocalX);
            onUpdateCoverFocalY?.(persistedFocalY);
          } else {
          }

          // Short delay to allow transform props to propagate before syncing dimensions
          setTimeout(() => {
            if (coverContainerRef.current) {
              const currentRect = coverContainerRef.current.getBoundingClientRect();
              if (currentRect.width > 0 && currentRect.height > 0) {
                const stableDims = { width: currentRect.width, height: currentRect.height };
                prevContainerDimensionsRef.current = stableDims;
                setContainerDimensions(stableDims);
              }
            }
            initialLoadSettledRef.current = true;
            if (isInitializingProjectRef.current) {
              isInitializingProjectRef.current = false;
            }
            if (isInitialMountRef.current) {
                isInitialMountRef.current = false;
            }
            isForcingPersistedTransformsRef.current = false; // Clear flag after everything is done

          }, 0); // Minimal delay for state propagation
        } else {
          // If no persisted transforms, still need to settle
          initialLoadSettledRef.current = true;
          if (isInitializingProjectRef.current) isInitializingProjectRef.current = false;
          if (isInitialMountRef.current) isInitialMountRef.current = false;
          isForcingPersistedTransformsRef.current = false; // Clear flag
        }
      }, 50); // Reduced delay from 3000ms to 50ms
    }
    // No 'else' branch needed here; if not initial mount or restoring, existing state should persist or be handled by user interaction.

    return () => {
      if (reapplyTimerId) {
        clearTimeout(reapplyTimerId);
      }
    };
  }, [isRestoringProject, coverScale, coverFocalX, coverFocalY, onUpdateCoverScale, onUpdateCoverFocalX, onUpdateCoverFocalY]);
  
  // Effect to monitor for shifts after restoration
  useEffect(() => {
    if (!isRestoringProject && expectedTransformRef.current) {
      // Add a small delay to allow React to process the state updates
      const shiftCheckTimer = setTimeout(() => {
        detectAndLogShift('post-restoration'); // detectAndLogShift now uses focalX/Y
      }, 100);
      
      return () => clearTimeout(shiftCheckTimer);
    }
  }, [scale, focalX, focalY, isRestoringProject, detectAndLogShift]);
  
  // Effect to calculate image quality when relevant data changes
  useEffect(() => {
    // Only calculate quality when quality indicators are enabled and we have a cover image
    if (!showQualityIndicators || !coverImage) {
      setImageQualityInfo(null);
      return;
    }
    
    // Skip if we don't have all the necessary data
    if (!containerDimensions.width || !containerDimensions.height) {
      setImageQualityInfo(null);
      return;
    }
    
    // Get image dimensions
    const imgNativeDims = getImageDimensionsWithFallback(coverImage.id);
    if (!imgNativeDims || imgNativeDims.width <= 0 || imgNativeDims.height <= 0) {
      setImageQualityInfo(null);
      return;
    }
    
    // Convert container dimensions from pixels to points (72 points = 1 inch)
    // For this calculation, we'll use the actual cover dimensions in points
    const coverWidthPt = projectWidth; // Full project width (single page width)
    const coverHeightPt = projectHeight;
    
    // Create mock placement with the cover transform data
    const mockPlacement = {
      placeholderId: 'cover',
      imageId: coverImage.id,
      transform: {
        scale: scale,
        fit: 'contain' as const, // Cover uses contain fit mode
        focalX: 0.5, // Center focal point
        focalY: 0.5  // Center focal point
      }
    };
    
    // Calculate effective DPI using the same function used for regular images
    const effectiveDpi = calculateEffectiveDpi(coverImage, mockPlacement, coverWidthPt, coverHeightPt);
    
    if (effectiveDpi === null) {
      setImageQualityInfo(null);
      return;
    }
    
    // Create quality info object
    const qualityInfo: ImageQualityInfo = {
      status: ImageQualityStatus.GOOD, // Default status, will be updated below
      actualDpi: effectiveDpi,
      message: '',
      placeholderId: 'cover',
      imageId: coverImage.id
    };
    
    // Calculate thresholds based on the same logic used for regular images
    const warningThresholdDpi = TARGET_DPI * (dpiWarningThresholdPercent / 100);
    const poorThresholdDpi = TARGET_DPI * 0.5; // 150 DPI
    
    // Determine status based on thresholds
    if (effectiveDpi >= warningThresholdDpi) {
      // Image meets or exceeds the user-defined warning threshold
      qualityInfo.status = ImageQualityStatus.GOOD;
      
      // Customize message based on whether it meets the absolute target
      if (effectiveDpi >= TARGET_DPI) {
        qualityInfo.message = `Good quality: ${Math.round(effectiveDpi)} Effective DPI (meets target ${TARGET_DPI} DPI)`;
      } else {
        qualityInfo.message = `Acceptable quality: ${Math.round(effectiveDpi)} Effective DPI (meets threshold ${Math.round(warningThresholdDpi)} DPI)`;
      }
    } else if (effectiveDpi >= poorThresholdDpi) {
      // Image is below warning threshold but above poor threshold
      qualityInfo.status = ImageQualityStatus.WARNING;
      qualityInfo.message = `Warning: ${Math.round(effectiveDpi)} Effective DPI (below threshold ${Math.round(warningThresholdDpi)} DPI)`;
    } else {
      // Image is below poor threshold - this is a severe quality issue
      qualityInfo.status = ImageQualityStatus.POOR;
      qualityInfo.message = `Poor quality: ${Math.round(effectiveDpi)} Effective DPI (below minimum ${Math.round(poorThresholdDpi)} DPI)`;
    }
    
    // Only update state if the status is not GOOD (to avoid unnecessary renders)
    if (qualityInfo.status !== ImageQualityStatus.GOOD) {
      setImageQualityInfo(qualityInfo);
    } else {
      setImageQualityInfo(null);
    }
  }, [coverImage, containerDimensions, scale, showQualityIndicators, dpiWarningThresholdPercent, projectWidth, projectHeight, getImageDimensionsWithFallback]);

  // Calculate image style based on transform (focal point system)
  const calculateImageStyle = (): React.CSSProperties => {
    if (!coverImage || !containerDimensions.width || !containerDimensions.height) {
      return { position: 'absolute', cursor: cursor, visibility: 'hidden' }; // Hide if no data
    }

    const imgNativeDims = getImageDimensionsWithFallback(coverImage.id);
    if (!imgNativeDims || imgNativeDims.width <= 0 || imgNativeDims.height <= 0) {
      return { position: 'absolute', cursor: cursor, visibility: 'hidden' };
    }

    const containerW = containerDimensions.width;
    const containerH = containerDimensions.height;
    const imageW = imgNativeDims.width;
    const imageH = imgNativeDims.height;

    const imageAspectRatio = imageW / imageH;
    const containerAspectRatio = containerW / containerH;

    // Determine base 'contain' dimensions
    let baseW, baseH;
    if (imageAspectRatio > containerAspectRatio) {
      baseW = containerW;
      baseH = containerW / imageAspectRatio;
    } else {
      baseH = containerH;
      baseW = containerH * imageAspectRatio;
    }

    const finalScaledW = baseW * scale;
    const finalScaledH = baseH * scale;

    // Calculate offsets to center the focal point of the scaled image within the container
    const offsetX = (containerW / 2) - (finalScaledW * focalX);
    const offsetY = (containerH / 2) - (finalScaledH * focalY);

    return {
      position: 'absolute',
      width: `${finalScaledW}px`,
      height: `${finalScaledH}px`,
      left: `${offsetX}px`,
      top: `${offsetY}px`,
      cursor: cursor,
      willChange: 'width, height, top, left',
      maxWidth: 'none', // Prevent base img styles (like max-width: 100%) from squishing the image
    };
  };

  // Reusable function to show the image preview modal
  const showImagePreviewModal = useCallback(async (imageToView: ImageFile) => {
    if (!imageToView) return;

    // Reset modal state before fetching
    setModalImageSrc(null);
    setModalImageFilename(null);
    setImageExifData(null);
    setLoadingExif(true);
    setShowFullImageModal(false); // Ensure modal is closed before trying to reopen

    try {
      if (!window.electronAPI?.getImageDataUrl || !window.electronAPI?.getExifData) {
        toast.error("Preview error: API not available. Please restart the app.");
        setLoadingExif(false);
        return;
      }

      let imagePath = imageToView.originalPath;
      if (window.bookProofsApp?.getUpdatedFilePath) {
        const updatedPath = window.bookProofsApp.getUpdatedFilePath(imageToView.originalPath);
        if (updatedPath) {
          imagePath = updatedPath;
        }
      }
      
      if (window.electronAPI?.checkFilesExist) {
        const fileExists = await window.electronAPI.checkFilesExist([imagePath], true);
        if (!fileExists[0]) {
          toast.error(`File not found at ${imagePath}. The file may have been moved or deleted.`);
          setLoadingExif(false);
          return;
        }
      }

      const [imageResult, exifResult] = await Promise.all([
        window.electronAPI.getImageDataUrl(imagePath),
        window.electronAPI.getExifData(imagePath)
      ]);

      if (imageResult.success && imageResult.dataUrl) {
        setModalImageSrc(imageResult.dataUrl);
        setModalImageFilename(imageResult.filename || null);

        if (exifResult.success && exifResult.exifData) {
          setImageExifData(exifResult.exifData);
        } else {
          setImageExifData(null);
        }
        setShowFullImageModal(true);
      } else {
        toast.error(`Could not load cover image preview: ${imageResult.error || 'Unknown error'}`);
        setModalImageSrc(null);
        setModalImageFilename(null);
        setImageExifData(null);
        setShowFullImageModal(false);
      }
    } catch (error: any) {
      toast.error(`Error loading cover image preview: ${error.message}`);
      setModalImageSrc(null);
      setModalImageFilename(null);
      setImageExifData(null);
      setShowFullImageModal(false);
    } finally {
      setLoadingExif(false);
    }
  }, [setModalImageSrc, setModalImageFilename, setImageExifData, setLoadingExif, setShowFullImageModal]);


  // Handle keyboard events for delete key with high priority
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check for isHovered and coverImage *inside* the handler
      if (!isHovered || !coverImage) {
        return;
      }

      if (event.key === 'Delete' || event.key === 'Backspace') {
        // Prevent default browser behavior
        event.preventDefault();
        // Stop propagation to prevent other listeners from handling this event
        event.stopPropagation && event.stopPropagation();
        
        const targetElement = event.target as HTMLElement;
        const isInputFocused = targetElement.tagName === 'INPUT' ||
                             targetElement.tagName === 'TEXTAREA' ||
                             targetElement.isContentEditable;
        
        if (!isInputFocused) {
          if (onUpdateCoverImage) {
            onUpdateCoverImage(null);
            toast.success('Cover image removed');
          }
        }
      } else if (event.code === 'Space' && coverImage && isHovered) { // Spacebar for preview
        event.preventDefault();
        if (showFullImageModal) {
          setShowFullImageModal(false);
          setImageExifData(null);
        } else {
          showImagePreviewModal(coverImage);
        }
      }
    };
    
    // Add the event listener if there's a cover image and a handler
    // The listener itself will check for isHovered
    if (coverImage && onUpdateCoverImage) {
      window.addEventListener('keydown', handleKeyDown, { capture: true });
    }
    
    // Cleanup function
    return () => {
      window.removeEventListener('keydown', handleKeyDown, { capture: true });
    };
  }, [coverImage, onUpdateCoverImage, isHovered, showFullImageModal, showImagePreviewModal]);

  // Effect to handle file updates from Photoshop (for cover image)
  useEffect(() => {
    if (!window.electronAPI?.onFileUpdated || !coverImage || !onUpdateCoverImage) {
      return;
    }

    const removeListener = window.electronAPI.onFileUpdated(async (data) => {
      let currentCoverImagePath = coverImage.originalPath;
      if (window.bookProofsApp?.getUpdatedFilePath) {
        const updatedPath = window.bookProofsApp.getUpdatedFilePath(coverImage.originalPath);
        if (updatedPath) {
          currentCoverImagePath = updatedPath;
        }
      }

      if (data.originalPath === currentCoverImagePath) {
        toast.info(`Cover image "${coverImage.name}" was updated. Regenerating preview...`);
        try {
          const refreshedImage = await window.electronAPI.regenerateThumbnails(data.originalPath);
          if (refreshedImage) {
            // Update the cover image in BookEditor's state
            onUpdateCoverImage({
              ...coverImage, // Spread existing cover image data
              thumbnailUrl: refreshedImage.thumbnailUrl,
              previewUrl: refreshedImage.previewUrl,
              // Update other relevant fields if regenerateThumbnails provides them
              naturalWidth: refreshedImage.naturalWidth,
              naturalHeight: refreshedImage.naturalHeight,
              dateModified: refreshedImage.dateModified,
            });
            setCoverImageRefreshTimestamp(Date.now()); // Force refresh of the img tag
            toast.success(`Cover image preview updated for "${coverImage.name}".`);
          } else {
            toast.error(`Failed to regenerate preview for updated cover image "${coverImage.name}".`);
          }
        } catch (error: any) {
          toast.error(`Error regenerating preview for "${coverImage.name}": ${error.message}`);
        }
      }
    });

    return () => {
      removeListener();
    };
  }, [coverImage, onUpdateCoverImage, setCoverImageRefreshTimestamp]);
  
  // Clean up animation frames on unmount - REMOVED as animations are gone
  // useEffect(() => {
  //   return () => {
  //     if (animationFrameRef.current !== null) {
  //       cancelAnimationFrame(animationFrameRef.current);
  //     }
  //     if (zoomAnimationFrameRef.current !== null) {
  //       cancelAnimationFrame(zoomAnimationFrameRef.current);
  //     }
  //   };
  // }, []);

  // Effect for detailed logging - REMOVED as zoom/pan are gone
  // useEffect(() => { ... }, [imageTransform, ...]); // imageTransform is not defined

  return (
    <div
      className={cn(
        "flex-1 flex items-center justify-center relative p-4 outline-none",
        theme === 'dark' ? 'bg-neutral-800 text-neutral-100' : 'bg-gray-100 text-black'
      )}
      data-testid="project-cover-outer-container"
    >
      {/* Navigation Chevrons - Exactly like SpreadCanvas */}
      <>
        <button
          className={cn(
            "absolute left-0 top-1/2 transform -translate-y-1/2 transition-colors disabled:opacity-30 disabled:cursor-not-allowed z-10",
            theme === 'dark' ? 'text-gray-400 hover:text-gray-100' : 'text-gray-600 hover:text-gray-900'
          )}
          onClick={() => { /* No action */ }}
          disabled={true}
          aria-label="Previous (disabled)"
        >
          <ChevronLeft className="w-8 h-8" />
        </button>
        
        <button
          className={cn(
            "absolute right-0 top-1/2 transform -translate-y-1/2 transition-colors z-10",
            theme === 'dark' ? 'text-gray-400 hover:text-gray-100' : 'text-gray-600 hover:text-gray-900'
          )}
          onClick={onNavigateForward}
          aria-label="Next spread"
        >
          <ChevronRight className="w-8 h-8" />
        </button>
      </>

      {/* Spread container - Exactly like SpreadCanvas */}
      <div
        className="spread-container relative shadow-lg overflow-hidden"
        style={spreadContainerStyle}
      >
        {/* Cover content positioned on the right side */}
        <div
          ref={coverContainerRef}
          style={coverContentStyle}
          data-testid="project-cover-content-container"
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={`${isDraggingOver ? 'ring-2 ring-primary ring-inset' : ''} ${
            scale > 1 ? (isPanning ? 'cursor-grabbing' : 'cursor-grab') : 'cursor-default'
          }`}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUpOrLeave}
          onMouseLeave={handleMouseUpOrLeave}
        >
          {coverImage ? (
            // Show the cover image if available
            <ContextMenu>
              <ContextMenuTrigger asChild>
                <div className="absolute inset-0 w-full h-full group"
                   onMouseEnter={() => setIsHovered(true)}
                   onMouseLeave={() => setIsHovered(false)}
                > {/* Re-add group for hover controls */}
                  <img
                    src={`${coverImage.previewUrl || coverImage.thumbnailUrl || normalizeBackgroundImageUrl(coverImage.originalPath)}?t=${coverImageRefreshTimestamp}`}
                    alt="Project Cover"
                    // className="w-full h-full" // Removed: Dimensions are now explicit from style
                    style={calculateImageStyle()} // Applies calculated width, height, left, top
                    draggable={false}
                    onLoad={(e) => {
                      const img = e.target as HTMLImageElement;
                      if (coverImage && img.naturalWidth > 0 && img.naturalHeight > 0) {
                        const newDims = {
                          width: img.naturalWidth,
                          height: img.naturalHeight
                        };
                        const currentCachedDims = imageDimensionsCache.current[coverImage.id];
                        if (!currentCachedDims || currentCachedDims.width !== newDims.width || currentCachedDims.height !== newDims.height) {
                          imageDimensionsCache.current[coverImage.id] = newDims;
                          setImageNaturalDimsLoadedKey(prevKey => prevKey + 1); // Force re-render
                        }
                      }
                    }}
                  />
                  
                  {/* Display quality warning */}
                  {showQualityIndicators && imageQualityInfo && imageQualityInfo.status !== ImageQualityStatus.GOOD && (
                    <ImageQualityWarning qualityInfo={imageQualityInfo} />
                  )}
                  
                  {/* Controls */}
                  <div className="absolute inset-0 pointer-events-none">
                    {/* Remove Button */}
                    <button
                      className="absolute top-1 right-1 p-0.5 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-opacity opacity-0 group-hover:opacity-100 z-20 pointer-events-auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onUpdateCoverImage) {
                          onUpdateCoverImage(null);
                          toast.success("Cover image removed");
                        }
                      }}
                      title="Remove image"
                    >
                      <X className="w-3 h-3" />
                    </button>
                    
                    {/* Basic Zoom Slider */}
                    <div className="absolute bottom-2 left-1 right-1 z-20 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-auto flex items-center justify-between space-x-1 bg-black/30 p-1 rounded">
                      <Slider
                        min={1}
                        max={3}
                        step={0.02}
                        value={[scale]}
                        onValueChange={([newScale]) => {
                          // Block zoom changes during restoration to preserve loaded state
                          if (isRestoringProject || isInitializingProjectRef.current || !initialLoadSettledRef.current) {
                            return;
                          }
                          setScale(newScale);
                        }}
                        className="flex-1 h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-white/70 [&>span:first-child>span]:h-2"
                        aria-label="Zoom cover image"
                      />
                    </div>
                  </div>
                </div>
              </ContextMenuTrigger>
              <ContextMenuContent className="w-48">
                <ContextMenuItem onSelect={() => coverImage && showImagePreviewModal(coverImage)} disabled={!coverImage}>
                  <Eye className="mr-2 h-4 w-4" />
                  Image Preview
                </ContextMenuItem>
                <ContextMenuSeparator />
                <ContextMenuItem
                  onSelect={() => {
                    if (coverImage && typeof window.electronAPI?.showItemInFolder === 'function') {
                      let imagePath = coverImage.originalPath;
                      if (window.bookProofsApp?.getUpdatedFilePath) {
                        const updatedPath = window.bookProofsApp.getUpdatedFilePath(coverImage.originalPath);
                        if (updatedPath) {
                          imagePath = updatedPath;
                        }
                      }
                      window.electronAPI.showItemInFolder(imagePath);
                    } else {
                      toast.error('Could not reveal file: Feature not available or no image set.');
                    }
                  }}
                  disabled={!coverImage}
                >
                  <FolderSearch className="mr-2 h-4 w-4" />
                  Reveal in Finder
                </ContextMenuItem>
                <ContextMenuSeparator />
                <ContextMenuItem
                  onSelect={() => {
                    if (coverImage && typeof window.electronAPI?.editInPhotoshop === 'function') {
                      let imagePath = coverImage.originalPath;
                      if (window.bookProofsApp?.getUpdatedFilePath) {
                        const updatedPath = window.bookProofsApp.getUpdatedFilePath(coverImage.originalPath);
                        if (updatedPath) {
                          imagePath = updatedPath;
                        }
                      }
                      toast.info("Opening image in Photoshop...", {
                        description: "Save the file in Photoshop to update it in the app"
                      });
                      window.electronAPI.editInPhotoshop(imagePath)
                        .catch((err: Error) => {
                            toast.error(`Could not open in Photoshop: ${err.message}`);
                        });
                    } else {
                      toast.error('Could not open in Photoshop: Feature not available or no image set.');
                    }
                  }}
                  disabled={!coverImage}
                >
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit in Photoshop
                </ContextMenuItem>
                <ContextMenuSeparator />
                <ContextMenuItem
                  onSelect={async () => {
                    if (coverImage && typeof window.electronAPI?.regenerateThumbnails === 'function' && onUpdateCoverImage) {
                      let imagePath = coverImage.originalPath;
                      if (window.bookProofsApp?.getUpdatedFilePath) {
                        const updatedPath = window.bookProofsApp.getUpdatedFilePath(coverImage.originalPath);
                        if (updatedPath) {
                          imagePath = updatedPath;
                        }
                      }
                      toast.info("Regenerating cover preview...");
                      try {
                        const refreshedImage = await window.electronAPI.regenerateThumbnails(imagePath);
                        if (refreshedImage) {
                          onUpdateCoverImage({
                            ...coverImage,
                            thumbnailUrl: refreshedImage.thumbnailUrl,
                            previewUrl: refreshedImage.previewUrl,
                            naturalWidth: refreshedImage.naturalWidth,
                            naturalHeight: refreshedImage.naturalHeight,
                            dateModified: refreshedImage.dateModified,
                          });
                          setCoverImageRefreshTimestamp(Date.now());
                          toast.success("Cover preview regenerated.");
                        } else {
                          toast.error("Failed to regenerate cover preview.");
                        }
                      } catch (error: any) {
                        toast.error(`Error regenerating cover preview: ${error.message}`);
                      }
                    } else {
                      toast.error('Could not regenerate preview: Feature not available or no image set.');
                    }
                  }}
                  disabled={!coverImage}
                >
                  <RefreshCcw className="mr-2 h-4 w-4" />
                  Regenerate Preview
                </ContextMenuItem>
                {/* Add other context menu items here if needed in the future */}
              </ContextMenuContent>
            </ContextMenu>
          ) : (
            // Show placeholder content when no image is set
            <div className={`flex flex-col items-center justify-center ${isDraggingOver ? 'opacity-50' : ''}`}>
              <Upload className="w-10 h-10 text-gray-400 mb-2" />
              <h2 style={{ textAlign: 'center' }}>Project Cover</h2>
              <p style={{ textAlign: 'center', maxWidth: '100%' }}>
                <span className="text-sm">{isDraggingOver ? 'Drop image here' : 'Drag & drop an image here'}</span>
              </p>
            </div>
          )}
        </div>
      </div>
      
      {/* Text Editor for Cover - Constrained to right half of the canvas */}
      {onUpdateTextOverlay && (
        <div
          className="absolute inset-0 overflow-hidden pointer-events-none"
          style={{
            clipPath: 'polygon(50% 0, 100% 0, 100% 100%, 50% 100%)', // Only show right half
          }}
        >
          <TextEditor
            textOverlays={textOverlays}
            currentSpreadId="cover"
            onUpdateTextOverlay={onUpdateTextOverlay}
            containerRef={coverContainerRef}
            spreadDimensionsPt={{ width: fullSpreadWidth, height: fullSpreadHeight }}
            onSelectTextOverlay={onSelectTextOverlay}
            textControlsBarRef={textControlsBarRef}
          />
        </div>
      )}

      {/* Full Image Preview Modal */}
      <Dialog open={showFullImageModal} onOpenChange={(open) => {
        if (!open) {
          setImageExifData(null); // Clear EXIF data when closing
        }
        setShowFullImageModal(open);
      }}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] flex flex-col p-2 sm:p-4">
          <VisuallyHidden>
            <DialogTitle>Image Preview</DialogTitle>
            <DialogDescription>Full resolution preview of the selected image.</DialogDescription>
          </VisuallyHidden>
          <div className="relative w-full flex-1 flex items-center justify-center overflow-hidden">
            {modalImageSrc ? (
              <img
                src={modalImageSrc}
                alt="Full resolution preview"
                className="max-w-full max-h-[80vh] object-contain"
              />
            ) : (
              <p>Loading image...</p>
            )}
          </div>
          {(imageExifData || modalImageFilename) && (
            <div className="w-full mt-3 px-2 py-1.5 bg-gray-100 rounded text-xs text-gray-700 flex flex-wrap gap-x-3 gap-y-1 justify-center">
              {modalImageFilename && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">File:</span>
                  <span className="truncate" title={modalImageFilename}>{modalImageFilename}</span>
                </div>
              )}
              {imageExifData && imageExifData.dimensions && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Dimensions:</span>
                  <span>{imageExifData.dimensions.width} × {imageExifData.dimensions.height}px</span>
                </div>
              )}
              {imageExifData && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Color Profile:</span>
                  <span>
                    {imageExifData.colorProfile === "ICC Profile (Unknown)" && imageExifData.raw?.ICC
                      ? `ICC Profile (${imageExifData.raw.ICC.ProfileClass || "Unknown Type"})`
                      : imageExifData.colorProfile?.replace(" (default)", "") || "sRGB"}
                    {imageExifData.raw?.ICC?.ColorSpaceData &&
                     imageExifData.colorProfile && !imageExifData.colorProfile.includes(imageExifData.raw.ICC.ColorSpaceData) &&
                     ` • ${imageExifData.raw.ICC.ColorSpaceData}`}
                    {imageExifData.raw?.ColorSpace === 1 &&
                     imageExifData.colorProfile && !imageExifData.colorProfile.toLowerCase().includes('srgb') &&
                     ` • sRGB`}
                    {imageExifData.raw?.ColorSpace === 2 &&
                     imageExifData.colorProfile && !imageExifData.colorProfile.toLowerCase().includes('adobe') &&
                     ` • Adobe RGB`}
                  </span>
                </div>
              )}
              {imageExifData && imageExifData.rating > 0 && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Rating:</span>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star // Corrected to Star icon
                        key={star}
                        className={`w-3 h-3 ${star <= imageExifData.rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
                      />
                    ))}
                  </div>
                </div>
              )}
              {imageExifData && imageExifData.captureTime && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Captured:</span>
                  <span>{imageExifData.captureTime}</span>
                </div>
              )}
            </div>
          )}
          {loadingExif && !imageExifData && (
            <div className="w-full mt-3 px-2 py-1.5 bg-gray-100 rounded text-xs text-gray-500 text-center">
              Loading image information...
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
});

export default ProjectCover;