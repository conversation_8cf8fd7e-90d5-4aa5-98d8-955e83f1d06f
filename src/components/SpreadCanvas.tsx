import React, { useCallback, useEffect, useImperativeHandle, useRef, useState, forwardRef, useMemo, useContext } from 'react';
import { createPortal } from 'react-dom'; // Added for Portal
import { RgbaStringColorPicker } from 'react-colorful'; // Changed to RgbaStringColorPicker
import { normalizeBackgroundImageUrl, getOriginalPathFromUrl } from '@/utils/imageUtils';
import { getCachedDataUrl, setCachedDataUrl, getNormalizedPathKey, fetchDataUrl, isTimestampProcessed, markTimestampProcessed, clearCachedDataUrl } from '@/utils/dataUrlCache';
import throttle from 'lodash/throttle';
import debounce from 'lodash/debounce'; // Import debounce
import { useImageImport } from '../hooks/useImageImport';
import { useUserSettings } from '@/hooks/useUserSettings';
import { motion, AnimatePresence } from 'framer-motion'; // Import Framer Motion
import { ImagePlus, ChevronLeft, ChevronRight, X, Maximize2, Minimize2, Maximize, Expand, Shrink, FolderSearch, Pencil, Star, Eye, AlertTriangle, Layers, Edit2, RefreshCcw, PanelsLeftBottom, RotateCw, Plus, Replace, GripHorizontal, Trash2 } from 'lucide-react';
import VisualDropChoice from './VisualDropChoice'; // Added RefreshCcw icon for regenerate preview, Added PanelsLeftBottom, Added RotateCw for rotation handle
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { ImageNumberOverlay, ImageNumberOverlayProps } from '@/components/ProofingComponents'; // Import the ImageNumberOverlay component and props
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
} from "@/components/ui/context-menu"; // Added Context Menu imports
import {
 Dialog,
 DialogContent,
 DialogHeader,
 DialogTitle,
 DialogDescription, // Import DialogDescription
 DialogTrigger, // Keep trigger if needed elsewhere, otherwise remove
 DialogClose, // Keep close if needed elsewhere, otherwise remove
} from "@/components/ui/dialog"; // Import Dialog components
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"; // Import AlertDialog components
import { Spread, ImageTransform, ImagePlacement } from './SpreadsTray'; // Import ImageTransform AND ImagePlacement
import { ImageFile } from './ImageTray';
// import { TemplateImage } from './Template'; // Removed old import
import { TemplateImage, TemplateData } from '@/lib/templates/interfaces'; // Import from new location, add TemplateData
import { toast } from 'sonner';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'; // Import for Dialog accessibility
import SpreadBackgroundManager, { SpreadBackgroundData } from './SpreadBackgroundManager'; // Import for per-spread background
import { AdjustedPlaceholderPt, adjustTemplateForGap, ImageGap } from '@/lib/templates'; // Import the new type and gap adjustment function
import { cn } from "@/lib/utils"; // Import the cn utility
import { checkImageQuality, checkSpreadImagesQuality, ImageQualityInfo, ImageQualityStatus } from '@/lib/imageQuality'; // Import image quality utilities (ADDED checkImageQuality)
import ImageQualityWarning from './ImageQualityWarning'; // Import image quality warning component
import { TextEditor, TextOverlay } from './TextEditor'; // Import TextEditor and TextOverlay
import OnboardingTooltip from './OnboardingTooltip'; // Import the OnboardingTooltip component

// Color conversion utility functions
const parseRgbaString = (rgbaString: string | undefined): { r: number; g: number; b: number; a: number } => {
  if (!rgbaString) return { r: 0, g: 0, b: 0, a: 1 };
  const match = rgbaString.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)$/i);
  if (match) {
    return {
      r: parseInt(match[1], 10),
      g: parseInt(match[2], 10),
      b: parseInt(match[3], 10),
      a: match[4] !== undefined ? parseFloat(match[4]) : 1,
    };
  }
  return { r: 0, g: 0, b: 0, a: 1 }; // Fallback for parse error
};

// Convert RGBA string to hex format for color input
const rgbaToHex = (rgbaString: string): string => {
  const { r, g, b } = parseRgbaString(rgbaString);
  const toHex = (n: number) => n.toString(16).padStart(2, '0');
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
};

// Convert hex to RGBA string
const hexToRgba = (hex: string): string => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r},${g},${b},1)`;
};

interface SpreadCanvasProps {
  theme: 'light' | 'dark'; // Added theme prop
  // Windowing props
  renderedSpreads: Spread[]; // Still needed for finding current spread data
  currentSpreadId: string;
  currentRenderedIndex: number; // Index within renderedSpreads

  // New props for calculated layout
  allSpreadLayouts: Record<string, AdjustedPlaceholderPt[]>; // Map of spreadId to its layout
  spreadDimensionsPt: { width: number; height: number }; // Spread dimensions in points

  // Data props
  images: ImageFile[]; // Still needed for image data lookup
  textOverlays: TextOverlay[]; // For TextEditor integration
  imageGap: number | ImageGap; // Image gap value for custom template adjustment
  
  // Quality props
  onlyShowPoorQuality?: boolean; // Whether to only show poor quality indicators
  dpiWarningThresholdPercent?: number; // DPI warning threshold percentage
  showQualityIndicators?: boolean; // Whether to show quality indicators

  // Preview generation tracking
  trackPreviewRequest?: (originalPath: string, resetCounter?: boolean) => void;

  // Callback props
  onUpdateSpreadImages: (
    spreadId: string,
    placeholderId: string,
    placement: ImagePlacement | null | undefined
  ) => void;
  onUpdateImageTransform?: (
    spreadId: string,
    placeholderId: string,
    transformUpdates: Partial<ImageTransform>
  ) => void;
  onRemoveImageAndSwitchTemplate?: (spreadId: string, placeholderIdToRemove: string) => void;
  onRemoveMultipleImagesAndSwitchTemplate?: (spreadId: string, placeholderIdsToRemove: string[]) => void;
  // Renamed and updated prop type
  onHoverChange?: (hoverInfo: { placeholderId: string | null, isZoomed: boolean }) => void;
  onFocusChange?: (isFocused: boolean) => void;
  onNavigateSpread: (direction: 'prev' | 'next') => void;
  onAddSpread?: () => void;
  onSelectTemplate?: (templateId: string) => void; // Keep if context menu uses it
  onActivateTemplateFilter?: (count: number, selectFirst?: boolean, currentTemplateId?: string | null, arrowKey?: 'ArrowUp' | 'ArrowDown') => void; // Keep if context menu uses it
  onToggleFocusMode?: () => void;
  // TextEditor Callbacks
  onUpdateTextOverlay: (textOverlay: TextOverlay) => void;
  // onDeleteTextOverlay: (textOverlayId: string) => void; // Removed, will be handled by BookEditor
  onSelectTextOverlay: (overlay: TextOverlay | null) => void; // New callback from TextEditor
  
  // Template editing props
  customTemplates?: Record<string, TemplateImage[]>; // Custom template modifications by spreadId
  onUpdateCustomTemplate?: (spreadId: string, customLayout: TemplateImage[]) => void; // Callback to save custom templates
 
  // UI State / Config props
  currentSpreadNumber: number; // For display/navigation logic
  totalSpreads: number; // For display/navigation logic
  allTemplates?: TemplateData[]; // Keep if context menu uses it
  usedImageIds?: string[]; // Keep if context menu uses it
  defaultDropModeIsCover?: boolean;
  isTemplatesTrayFocused?: boolean; // Keep if context menu uses it
  addSpreadOnArrow?: boolean;
  isFocusMode?: boolean;
  showVisualDropChoice?: boolean;
  showDragIcon?: boolean;
  autoSwitchTemplateOnRemove?: boolean;
  findExactTemplateForImageCount?: (imageCount: number) => TemplateData | null; // Keep if context menu uses it
  getImageDimensions?: (imageId: string) => { width: number; height: number } | undefined; // Now required for smart zoom
  containerWidthPx?: number; // Keep for calculating spread pixel size for display scaling? Or remove? Let's keep for now.
  containerHeightPx?: number; // Keep for calculating spread pixel size for display scaling? Or remove? Let's keep for now.

  // Callback for snapshot updates
  onUpdateThumbnailSnapshot?: (spreadId: string, dataUrl: string) => void;
  onUpdateImageThumbnails?: (imageId: string, updates: { thumbnailUrl?: string, previewUrl?: string }) => void;
  onClearImageSelection?: (clearFunction: () => void) => void; // Callback to provide clear selection function
  onSelectionChange?: (selectedCount: number) => void; // Callback to report selection changes
  // Book gutter visibility
  showBookGutter?: boolean; // Whether to show the book gutter
  // Auto-cover setting
  autoCoverNearEdges?: boolean; // Whether to auto-switch contain to cover
  // Image quality indicators and DPI warning settings are now defined above
  backgroundColor?: string; // Added for spread background color
  projectBackgroundImage?: string | null; // Added for project background image
  backgroundImageZoom?: number; // Added for background image zoom
  backgroundImagePanX?: number; // Added for background image pan X
  backgroundImagePanY?: number; // Added for background image pan Y
  projectBackgroundImageOpacity?: number; // Added for background image opacity
  projectBackgroundImagePath?: string | null; // Actual file path for the project background
 
  // Callback for individual spread background updates
  onUpdateSpreadBackground: (
    spreadId: string,
    updates: Partial<SpreadBackgroundData>
  ) => void;
  onSetSpreadBackgroundFromImageFile: (spreadId: string, imageFile: ImageFile) => void; // Or adjust based on how BookEditor handles it
  onApplyBackgroundToAll?: (spreadId: string) => void; // Prop for applying background to all spreads
  onClearAllBackgrounds?: () => void; // Prop for clearing all spread backgrounds


    onAutoBuildFromDrop?: (images: ImageFile[], currentSpreadId: string) => void; // For handling auto-build on excess image drop
    onOfferAutobuildRequest?: (data: { droppedImages: ImageFile[]; currentSpreadId: string; existingImageCount: number; maxTemplateSize: number; }) => void; // NEW: Callback to request autobuild offer dialog
    textControlsBarRef?: React.RefObject<HTMLDivElement>; // Added for text controls
    isTextEditorActive?: boolean; // New prop: Is the text editor currently active?
    isEditingBackgroundForSpreadId?: string | null; // ID of the spread whose background is being edited
    toggleBackgroundEditMode?: (spreadId: string) => void; // Function to toggle background edit mode
    onToggleBackgroundEditMode?: (spreadId: string) => void; // Prop for toggling background edit mode (passed to SpreadBackgroundManager)
    // Removed props: templateMap, previewTemplateId, aspectRatio, imageGap, bookPageWidthInches, onlyShowPoorQuality, previewGenerationMode
    hasCover?: boolean; // ADDED: Is there a project cover
    onNavigateToCover?: () => void; // ADDED: Function to navigate to the cover view
    bleedValue?: number; // Added bleed value in points
    showBleedArea?: boolean; // Added to control bleed area visualization
    bleedAreaMode?: 'hide' | 'indicate' | 'ignore'; // Added to control bleed area mode
    displayTrimLines?: boolean; // Added to control trim line visibility independently
    showSafetyMargin?: boolean; // Added to control safety margin visibility
    safetyMarginValuePt?: number; // Added safety margin value in points
    imageBorderSize?: number; // Project-level image border size in points
    imageBorderColor?: string; // Project-level image border color
  }
// Define the handle type that will be exposed via the ref
// Interface for image quality warnings export data
export interface ImageQualityWarnings {
  hasWarnings: boolean;
  qualityIssues: Record<string, Record<string, ImageQualityInfo>>;
}

export interface SpreadCanvasHandle {
  focusCanvas: () => void;
  deleteHoveredImage: () => void;
  toggleFitModeAll: () => void; // NEW: Toggle fit mode for all images
  saveTransformsBeforeNavigation: () => void; // Ensure transforms are saved before navigating
  checkImageQuality: () => ImageQualityWarnings; // Check image quality for all spreads
}

// Wrap component with forwardRef
const SpreadCanvas = forwardRef<SpreadCanvasHandle, SpreadCanvasProps>(({ 
  // Destructure new props
  allSpreadLayouts, // Use the new prop name
  spreadDimensionsPt,
  // Keep existing needed props
  renderedSpreads, currentSpreadId, currentRenderedIndex, images, imageGap, textOverlays, onUpdateSpreadImages, currentSpreadNumber, totalSpreads, onNavigateSpread, onAddSpread, onSelectTemplate, allTemplates = [], usedImageIds = [], onUpdateImageTransform, defaultDropModeIsCover = false, isTemplatesTrayFocused = false, onActivateTemplateFilter, addSpreadOnArrow = true, isFocusMode = false, onToggleFocusMode, showVisualDropChoice = true, showDragIcon = true, autoSwitchTemplateOnRemove = false, onRemoveImageAndSwitchTemplate, onRemoveMultipleImagesAndSwitchTemplate, onHoverChange, onFocusChange, findExactTemplateForImageCount, getImageDimensions, containerWidthPx = 0, containerHeightPx = 0, onUpdateThumbnailSnapshot, onUpdateImageThumbnails, onClearImageSelection, onSelectionChange, showBookGutter = true, autoCoverNearEdges = true, showQualityIndicators = true, dpiWarningThresholdPercent = 70, backgroundColor = '#FFFFFF', projectBackgroundImage = null, backgroundImageZoom = 1, backgroundImagePanX = 50, backgroundImagePanY = 50, projectBackgroundImageOpacity, projectBackgroundImagePath, onAutoBuildFromDrop, onOfferAutobuildRequest, onUpdateTextOverlay, onSelectTextOverlay, textControlsBarRef, isTextEditorActive = false, // Destructure new prop with default
  onUpdateSpreadBackground, onSetSpreadBackgroundFromImageFile, onApplyBackgroundToAll, onClearAllBackgrounds, // Destructure new background props
  isEditingBackgroundForSpreadId, toggleBackgroundEditMode, onToggleBackgroundEditMode, // Destructure background editing props
  trackPreviewRequest, // Destructure trackPreviewRequest prop
  hasCover, onNavigateToCover, // ADDED: Destructure cover props
  bleedValue = 0, // Added bleedValue prop with default
  showBleedArea = true, // Added showBleedArea prop with default
  bleedAreaMode = 'indicate', // Added bleedAreaMode prop with default
  displayTrimLines = true, // Added displayTrimLines prop with default
  showSafetyMargin = true, // Added showSafetyMargin prop with default
  safetyMarginValuePt = 0, // Added safetyMarginValuePt prop with default (e.g., 0.25 inches * 72 points/inch = 18 points)
  imageBorderSize = 0, // Project-level image border size with default
  imageBorderColor = '#000000', // Project-level image border color with default
  // Template editing props
  customTemplates,
  onUpdateCustomTemplate,
  // Removed: templateMap, previewTemplateId, aspectRatio, bookPageWidthInches, onlyShowPoorQuality
  theme, // Destructure theme prop
 }, ref) => {
 // Helper function to ensure color is in RGBA string format
 const ensureRgbaString = (colorInput: string | undefined | null): string => { // Added undefined/null check
   if (typeof colorInput !== 'string' || colorInput === null || colorInput === undefined) { // Added null/undefined check
     // console.warn(`[SpreadCanvas] Invalid color input type: ${typeof colorInput}. Defaulting to black.`);
     return 'rgba(0,0,0,1)';
   }
   if (colorInput.startsWith('rgba')) {
     return colorInput;
   }
   if (colorInput.startsWith('#')) {
     let hex = colorInput.replace(/^#/, '');
     if (hex.length === 3) {
       hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
     }
     if (hex.length === 6) {
       const r = parseInt(hex.substring(0, 2), 16);
       const g = parseInt(hex.substring(2, 4), 16);
       const b = parseInt(hex.substring(4, 6), 16);
       return `rgba(${r},${g},${b},1)`; // Default alpha to 1 for hex
     }
   }
   // console.warn(`[SpreadCanvas] Could not convert color '${colorInput}' to RGBA string. Defaulting to black.`);
   return 'rgba(0,0,0,1)'; // Fallback for unrecognized formats
 };

   const parseRgbaString = (rgbaString: string | undefined): { r: number; g: number; b: number; a: number } => {
    if (!rgbaString) return { r: 0, g: 0, b: 0, a: 1 };
    const match = rgbaString.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)$/i);
    if (match) {
      return {
        r: parseInt(match[1], 10),
        g: parseInt(match[2], 10),
        b: parseInt(match[3], 10),
        a: match[4] !== undefined ? parseFloat(match[4]) : 1,
      };
    }
    return { r: 0, g: 0, b: 0, a: 1 }; // Fallback for parse error
  };

  // Helper function for rotation
  const normalizeRotation = (rotation: number): number => {
    const normalized = rotation % 360;
    return normalized < 0 ? normalized + 360 : normalized;
  };

  // trackPreviewRequest is now received as a prop

  const userSettings = useUserSettings(); // Hook returns an object with settings and their setters
  const THROTTLE_INTERVAL = 50;

  // Throttled function to commit color changes to parent state
  const throttledCommitColorChange = useCallback(
    throttle((placeholderId: string, newColor: string) => {
      const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
      const targetPlacement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
      if (activeSpread && targetPlacement && onUpdateSpreadImages) {
        const updatedSettings = { ...targetPlacement.individualBorder, color: newColor };
        onUpdateSpreadImages(currentSpreadId, placeholderId, { ...targetPlacement, individualBorder: updatedSettings });
      }
    }, THROTTLE_INTERVAL),
    [currentSpreadId, renderedSpreads, onUpdateSpreadImages] // Dependencies for useCallback
  );
  
  // State to track real-time border updates from settings panel
  const [localImageBorderSize, setLocalImageBorderSize] = useState(imageBorderSize);
  const [localImageBorderColor, setLocalImageBorderColor] = useState(ensureRgbaString(imageBorderColor));
  
  // Listen for settings changes from the settings panel
  useEffect(() => {
    const handleSettingsChange = (event: CustomEvent) => {
      const { setting, value } = event.detail;
      
      if (setting === 'imageBorderSize') {
        setLocalImageBorderSize(value);
      } else if (setting === 'imageBorderColor') {
        setLocalImageBorderColor(value);
      }
    };
    
    // Add event listener with type assertion
    window.addEventListener('bookproofsSettingsChange', handleSettingsChange as EventListener);
    
    return () => {
      // Remove event listener with type assertion
      window.removeEventListener('bookproofsSettingsChange', handleSettingsChange as EventListener);
    };
  }, []);
  
  // Keep local state in sync with props - run immediately and when props change
  useEffect(() => {
    setLocalImageBorderSize(imageBorderSize);
  }, [imageBorderSize]);
  
  useEffect(() => {
    setLocalImageBorderColor(ensureRgbaString(imageBorderColor));
  }, [imageBorderColor]);

  // Calculate the maximum number of images any template supports
  const maxTemplateSize = useMemo(() => {
    if (!allTemplates || allTemplates.length === 0) return 0;
    return Math.max(...allTemplates.map(t => t.images.length));
  }, [allTemplates]);

  // --- Define Zoom Constants Once ---
  const ZOOM_BASE_FACTOR = 1.075;
  // Define thresholds (Aspect Ratio = Width / Height)
  // Thresholds and factors removed as per request. Only ZOOM_BASE_FACTOR is used now.
  // --- End Zoom Constants ---

  // Removed old aspectRatio parsing and calculations for spreadPixelWidth, spreadPixelHeight, gapInPixels

  const [dragOverId, setDragOverId] = useState<string | null>(null);
  const [isDragFromCanvas, setIsDragFromCanvas] = useState<boolean>(false);
  const [rejectionMessage, setRejectionMessage] = useState<string | null>(null);
  // Visual drop choice state
  const [visualDropChoice, setVisualDropChoice] = useState<{
    isVisible: boolean;
    placeholderId: string;
  }>({ isVisible: false, placeholderId: '' });
  const visualDropChoiceRef = useRef<'replace' | 'add' | null>(null);
  const currentDragDataRef = useRef<{ isSingleImage: boolean; data: any } | null>(null);
  const lastVisualDropPlaceholderRef = useRef<string>('');
  const dragOverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const contextMenuSelectionRef = useRef<{ placeholderIds: string[]; clickedPlaceholderId: string } | null>(null);
  // Removed isAnimating state, previousTemplateIdRef, animationTimerRef, and associated useEffect
  // Animation will rely solely on CSS transitions applied permanently

  // Memoize the current template ID (still needed for key generation)
  const currentTemplateId = useMemo(() => {
    return renderedSpreads.find(s => s.id === currentSpreadId)?.templateId ?? null;
  }, [renderedSpreads, currentSpreadId]);

  // Custom gap adjustment function for custom templates that preserves positioning
  const adjustCustomTemplateForGap = useCallback((
    customLayout: TemplateImage[],
    imageGapInput: number | ImageGap,
    spreadWidthPt: number,
    spreadHeightPt: number
  ): AdjustedPlaceholderPt[] => {
    // Handle both single number and object-based imageGapInput
    const gapHorizontal = typeof imageGapInput === 'number' ? imageGapInput : imageGapInput.horizontal;
    const gapVertical = typeof imageGapInput === 'number' ? imageGapInput : imageGapInput.vertical;

    // Check for zero gap case - apply small overlap to prevent hairlines
    if (gapHorizontal <= 0 && gapVertical <= 0) {
      const overlap = 0.5;
      return customLayout.map(p => ({
        id: p.id,
        adjustedX_pt: p.x * spreadWidthPt,
        adjustedY_pt: p.y * spreadHeightPt,
        adjustedWidth_pt: (p.width * spreadWidthPt) + overlap,
        adjustedHeight_pt: (p.height * spreadHeightPt) + overlap,
      }));
    }

    // For custom templates, preserve the original positioning relationships
    // Apply gaps by detecting neighbor relationships and adjusting accordingly
    const adjustedLayout: AdjustedPlaceholderPt[] = [];
    const FRAC_EPSILON = 1e-6;
    
    for (const placeholder of customLayout) {
      // Initialize adjustment values
      let leftAdjustment = 0;
      let rightAdjustment = 0;
      let topAdjustment = 0;
      let bottomAdjustment = 0;
      
      // Check if there are neighbors to the left
      const hasLeftNeighbor = customLayout.some(p => 
        p !== placeholder && 
        Math.abs(p.x + p.width - placeholder.x) < FRAC_EPSILON &&
        (p.y < placeholder.y + placeholder.height && p.y + p.height > placeholder.y)
      );
      
      // Check if there are neighbors to the right
      const hasRightNeighbor = customLayout.some(p => 
        p !== placeholder && 
        Math.abs(p.x - (placeholder.x + placeholder.width)) < FRAC_EPSILON &&
        (p.y < placeholder.y + placeholder.height && p.y + p.height > placeholder.y)
      );
      
      // Check if there are neighbors above
      const hasTopNeighbor = customLayout.some(p => 
        p !== placeholder && 
        Math.abs(p.y + p.height - placeholder.y) < FRAC_EPSILON &&
        (p.x < placeholder.x + placeholder.width && p.x + p.width > placeholder.x)
      );
      
      // Check if there are neighbors below
      const hasBottomNeighbor = customLayout.some(p => 
        p !== placeholder && 
        Math.abs(p.y - (placeholder.y + placeholder.height)) < FRAC_EPSILON &&
        (p.x < placeholder.x + placeholder.width && p.x + p.width > placeholder.x)
      );
      
      // Apply adjustments based on neighboring placeholders
      if (hasLeftNeighbor) leftAdjustment = gapHorizontal / 2;
      if (hasRightNeighbor) rightAdjustment = gapHorizontal / 2;
      if (hasTopNeighbor) topAdjustment = gapVertical / 2;
      if (hasBottomNeighbor) bottomAdjustment = gapVertical / 2;
      
      // Calculate final dimensions with adjustments
      const adjustedX_pt = placeholder.x * spreadWidthPt + leftAdjustment;
      const adjustedY_pt = placeholder.y * spreadHeightPt + topAdjustment;
      const adjustedWidth_pt = Math.max(0, placeholder.width * spreadWidthPt - leftAdjustment - rightAdjustment);
      const adjustedHeight_pt = Math.max(0, placeholder.height * spreadHeightPt - topAdjustment - bottomAdjustment);
      
      adjustedLayout.push({
        id: placeholder.id,
        adjustedX_pt,
        adjustedY_pt,
        adjustedWidth_pt,
        adjustedHeight_pt,
      });
    }
    
    return adjustedLayout;
  }, []);

  // Removed handleTransitionEnd callback as animation state is no longer used

  const [isWheeling, setIsWheeling] = useState(false);
  const wheelTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  // Add state to track navigation direction
  const [navigationDirection, setNavigationDirection] = useState<'prev' | 'next' | null>(null);
  // State to store measured pixel dimensions of placeholder containers
  const [placeholderPixelDims, setPlaceholderPixelDims] = useState<Record<string, { width: number; height: number }>>({});
  // Refs for placeholder containers
  const placeholderContainerRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const [hoveredImage, setHoveredImage] = useState<ImageFile | null>(null); // DEPRECATED - Use hoveredPlaceholderId instead for context
  const [hoveredPlaceholderId, setHoveredPlaceholderId] = useState<string | null>(null); // State for hovered placeholder ID
  // Panning State
  const [isPanning, setIsPanning] = useState(false);
  // Rotation State
  const [isRotating, setIsRotating] = useState(false);
  const [rotationStartAngle, setRotationStartAngle] = useState(0);
  const [rotationStartValue, setRotationStartValue] = useState(0);
  const [rotatingPlaceholderId, setRotatingPlaceholderId] = useState<string | null>(null);
  // Store starting mouse position and the focal point at the start of the pan
  const [panStartCoords, setPanStartCoords] = useState<{ x: number; y: number; startFocalX: number; startFocalY: number } | null>(null); // Corrected type syntax
  const panningPlaceholderIdRef = useRef<string | null>(null); // Ref to track which placeholder is being panned
  
  // Animation state for smooth panning
  const animationFrameRef = useRef<number | null>(null);
  const currentFocalPointsRef = useRef<{[placeholderId: string]: {x: number, y: number}}>({});
  const targetFocalPointsRef = useRef<{[placeholderId: string]: {x: number, y: number}}>({}); // Target position to animate to
  const [placeholderCursor, setPlaceholderCursor] = useState<Record<string, 'move' | 'grab' | 'default'>>({}); // State for cursor per placeholder
  // panningStartDims is no longer needed as focal points are relative

  // For smooth zoom animation
  const zoomAnimationFrameRef = useRef<{[placeholderId: string]: number | null}>({});
  const currentZoomLevelsRef = useRef<{[placeholderId: string]: number}>({});
  const targetZoomLevelsRef = useRef<{[placeholderId: string]: number}>({});

  const [showFullImageModal, setShowFullImageModal] = useState(false); // State for modal visibility
  const [modalImageSrc, setModalImageSrc] = useState<string | null>(null); // State for modal image source
  const [modalImageFilename, setModalImageFilename] = useState<string | null>(null); // State for modal image filename
  const [imageExifData, setImageExifData] = useState<any | null>(null); // New state for EXIF data
  const [loadingExif, setLoadingExif] = useState(false); // Track loading state
  
  // State for duplicate image warning dialog
  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);
  const [duplicateWarningData, setDuplicateWarningData] = useState<{
    image: ImageFile;
    usageCount: number;
    placeholderId: string;
    initialFit: 'cover' | 'contain';
  } | null>(null);
  const [qualityIssues, setQualityIssues] = useState<Record<string, ImageQualityInfo>>({}); // State for image quality issues
  const [allSpreadsQualityIssues, setAllSpreadsQualityIssues] = useState<Record<string, Record<string, ImageQualityInfo>>>({});
  // Add state for tracking image refresh timestamps to force re-rendering
  const [imageRefreshTimestamps, setImageRefreshTimestamps] = useState<Record<string, number>>({});
  // Track updated image paths to force re-renders when paths change
  const [updatedImagePaths, setUpdatedImagePaths] = useState<Record<string, string>>({});
  const canvasContainerRef = useRef<HTMLDivElement | null>(null); // Ref for the main container
  const spreadRenderContainerRef = useRef<HTMLDivElement | null>(null); // <<< ADDED REF for the rendering container
  const [bleedInPixels, setBleedInPixels] = useState(0); // State for bleed value in pixels
  const [safetyMarginInPixels, setSafetyMarginInPixels] = useState(0); // State for safety margin value in pixels
  const [pixelsPerPoint, setPixelsPerPoint] = useState(1); // State for pixels per point scaling factor
  const [isFocused, setIsFocused] = useState(false); // Track focus state
  const spreadContainerRefs = useRef<Record<string, HTMLDivElement | null>>({}); // Refs for individual spread containers
  const lastClickTimeRef = useRef<number>(0); // For tracking double-click time
  const lastClickedPlaceholderRef = useRef<string | null>(null); // For tracking which placeholder was clicked
  const prevSpreadIdForResizeRef = useRef<string>(currentSpreadId); // Ref to track spread ID for resize logic
  const requestedPreviewPathsRef = useRef(new Set<string>()); // <<< Ref to track requested previews
  const [isCanvasHovered, setIsCanvasHovered] = useState(false); // State for canvas hover
  const [isDraggingImageOverCanvas, setIsDraggingImageOverCanvas] = useState(false); // State for drag hover
  const [isCornerDropZoneDraggedOver, setIsCornerDropZoneDraggedOver] = useState<boolean>(false); // State for corner drop zone drag feedback
  const [hoveredPlaceholderIdForTooltip, setHoveredPlaceholderIdForTooltip] = useState<string | null>(null); // State for onboarding tooltip trigger (based on hover)
  const [displayableProjectBackgroundSrc, setDisplayableProjectBackgroundSrc] = useState<string | null>(null); // For Data URL of project background
  // projectSessionTooltipDismissed state is removed, will use sessionStorage
  
  const lastLayoutChangeTimestampRef = useRef<number>(0); // Ref to track recent layout changes
  // State for individual border settings per placeholder
  const [individualBorderSettings, setIndividualBorderSettings] = useState<Record<string, { enabled: boolean; size: number; color: string }>>({});
  const [activeColorPickerId, setActiveColorPickerId] = useState<string | null>(null); // State for active color picker
  const colorPickerRef = useRef<HTMLDivElement | null>(null); // Ref for the color picker popover
  const swatchRefs = useRef<Record<string, HTMLDivElement | null>>({}); // Refs for color swatches
  const [pickerPosition, setPickerPosition] = useState<{ top: number; left: number } | null>(null); // Position for portal
  const [activePickerRgbaInputStrings, setActivePickerRgbaInputStrings] = useState({ r: '0', g: '0', b: '0', a: '1.00' });
  
  // Multi-image selection state
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set()); // Set of placeholderIds with selected images

  // State for placeholder positions (for portal popout positioning)
  const [placeholderPositions, setPlaceholderPositions] = useState<Record<string, DOMRect>>({});
  
  // State to track which placeholders should use popout overlay
  const [smallPlaceholders, setSmallPlaceholders] = useState<Set<string>>(new Set());
  
  // State to track popout hover to prevent premature closing
  const [isPopoutHovered, setIsPopoutHovered] = useState(false);
  
  // Ref for hover delay timeout
  const hoverDelayTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Threshold for determining if a placeholder is too small for overlays
  const SMALL_PLACEHOLDER_THRESHOLD = 120; // pixels

  // Template editor resize state
  const [isResizing, setIsResizing] = useState(false);
  const [resizeData, setResizeData] = useState<{
    placeholderId: string;
    edge: string;
    startX: number;
    startY: number;
    initialLayout: TemplateImage[];
  } | null>(null);
  const resizeDataRef = useRef<{
    placeholderId: string;
    edge: string;
    startX: number;
    startY: number;
    initialLayout: TemplateImage[];
  } | null>(null);
  
  // Template editor move state
  const [isMoving, setIsMoving] = useState(false);
  const [moveData, setMoveData] = useState<{
    placeholderId: string;
    startX: number;
    startY: number;
    initialLayout: TemplateImage[];
    originalPlaceholder: TemplateImage;
    placeholdersToMove?: Array<{ id: string; original: TemplateImage }>;
  } | null>(null);
  const moveDataRef = useRef<{
    placeholderId: string;
    startX: number;
    startY: number;
    initialLayout: TemplateImage[];
    originalPlaceholder: TemplateImage;
    placeholdersToMove?: Array<{ id: string; original: TemplateImage }>;
  } | null>(null);
  const [customTemplatesState, setCustomTemplatesState] = useState<Record<string, TemplateImage[]>>(customTemplates || {});
  const customTemplatesStateRef = useRef<Record<string, TemplateImage[]>>(customTemplates || {});
  
  // Track Option key for move mode visual feedback
  const [isOptionPressed, setIsOptionPressed] = useState(false);
  
  // Snap guides state for visual feedback during resize
  const [snapGuides, setSnapGuides] = useState<{
    vertical: number[];   // X positions (normalized 0-1)
    horizontal: number[]; // Y positions (normalized 0-1)
    gridVisible: boolean;
    marginVertical: number[];   // X positions for margin snapping (normalized 0-1)
    marginHorizontal: number[]; // Y positions for margin snapping (normalized 0-1)
    gutterVertical: number[];   // X positions for gutter snapping (normalized 0-1)
    gutterHorizontal: number[]; // Y positions for gutter snapping (normalized 0-1)
  }>({
    vertical: [],
    horizontal: [],
    gridVisible: false,
    marginVertical: [],
    marginHorizontal: [],
    gutterVertical: [],
    gutterHorizontal: []
  });
  
  // Add keyboard listeners for Option key detection
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey && !isOptionPressed) {
        setIsOptionPressed(true);
      }
    };
    
    const handleKeyUp = (e: KeyboardEvent) => {
      if (!e.altKey && isOptionPressed) {
        setIsOptionPressed(false);
      }
    };
    
    // Add focus/blur handlers to ensure state is reset when window loses focus
    const handleWindowBlur = () => {
      setIsOptionPressed(false);
    };
    
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    window.addEventListener('blur', handleWindowBlur);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('blur', handleWindowBlur);
    };
  }, [isOptionPressed]);
  
  // Sync state with props
  useEffect(() => {
    setCustomTemplatesState(customTemplates || {});
  }, [customTemplates]);
  
  // Sync ref with state
  useEffect(() => {
    customTemplatesStateRef.current = customTemplatesState;
  }, [customTemplatesState]);
  
  // Clear selection function
  const clearImageSelection = useCallback(() => {
    setSelectedImages(new Set());
  }, []);

  // Add cleanup when mouse leaves the SpreadCanvas container entirely
  useEffect(() => {
    const canvasContainer = canvasContainerRef.current;
    if (!canvasContainer || !hoveredPlaceholderId || !smallPlaceholders.has(hoveredPlaceholderId)) {
      return;
    }

    const handleCanvasMouseLeave = (e: MouseEvent) => {
      // Check if the mouse is truly leaving the canvas container
      const rect = canvasContainer.getBoundingClientRect();
      const isLeavingCanvas = (
        e.clientX < rect.left ||
        e.clientX > rect.right ||
        e.clientY < rect.top ||
        e.clientY > rect.bottom
      );

      if (isLeavingCanvas) {
        // Clear any existing timeout
        if (hoverDelayTimeoutRef.current) {
          clearTimeout(hoverDelayTimeoutRef.current);
        }
        
        // Small delay to prevent race conditions, but clean up when truly leaving canvas
        hoverDelayTimeoutRef.current = setTimeout(() => {
          if (hoveredPlaceholderId && smallPlaceholders.has(hoveredPlaceholderId)) {
            setHoveredPlaceholderId(null);
            setHoveredImage(null);
            onHoverChange?.({ placeholderId: null, isZoomed: false });
          }
        }, 100);
      }
    };

    canvasContainer.addEventListener('mouseleave', handleCanvasMouseLeave);
    
    return () => {
      canvasContainer.removeEventListener('mouseleave', handleCanvasMouseLeave);
    };
  }, [hoveredPlaceholderId, smallPlaceholders, onHoverChange]);

  // Call the parent callback when selection changes or component mounts to provide the clear function
  useEffect(() => {
    if (onClearImageSelection) {
      onClearImageSelection(clearImageSelection);
    }
  }, [onClearImageSelection, clearImageSelection]);

  // Effect to update placeholder positions and detect small placeholders when hovered
  useEffect(() => {
    if (hoveredPlaceholderId) {
      const placeholderElement = document.querySelector(`[data-placeholder-id="${hoveredPlaceholderId}"]`);
      if (placeholderElement) {
        const rect = placeholderElement.getBoundingClientRect();
        setPlaceholderPositions(prev => ({
          ...prev,
          [hoveredPlaceholderId]: rect
        }));
        
        // Check if placeholder is small and should use popout
        const isSmall = rect.width < SMALL_PLACEHOLDER_THRESHOLD || rect.height < SMALL_PLACEHOLDER_THRESHOLD;
        setSmallPlaceholders(prev => {
          const newSet = new Set(prev);
          if (isSmall) {
            newSet.add(hoveredPlaceholderId);
          } else {
            newSet.delete(hoveredPlaceholderId);
          }
          return newSet;
        });
      }
    }
     }, [hoveredPlaceholderId, placeholderPixelDims, SMALL_PLACEHOLDER_THRESHOLD]);

  // Pre-calculate all placeholder sizes when spread loads to prevent flash
  useEffect(() => {
    if (!placeholderPixelDims || Object.keys(placeholderPixelDims).length === 0) return;
    
    const newSmallPlaceholders = new Set<string>();
    
    Object.entries(placeholderPixelDims).forEach(([placeholderId, dims]) => {
      if (dims.width < SMALL_PLACEHOLDER_THRESHOLD || dims.height < SMALL_PLACEHOLDER_THRESHOLD) {
        newSmallPlaceholders.add(placeholderId);
      }
    });
    
    setSmallPlaceholders(newSmallPlaceholders);
  }, [placeholderPixelDims, SMALL_PLACEHOLDER_THRESHOLD, currentSpreadId]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverDelayTimeoutRef.current) {
        clearTimeout(hoverDelayTimeoutRef.current);
      }
    };
  }, []);

  // Report selection changes to parent
  useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange(selectedImages.size);
    }
  }, [selectedImages, onSelectionChange]);

  // Effect to sync RGBA input strings when picker opens or its color changes
  useEffect(() => {
    if (activeColorPickerId && individualBorderSettings[activeColorPickerId]) {
      const parsed = parseRgbaString(individualBorderSettings[activeColorPickerId].color);
      setActivePickerRgbaInputStrings({
        r: String(parsed.r),
        g: String(parsed.g),
        b: String(parsed.b),
        a: parsed.a.toFixed(2),
      });
    }
  }, [activeColorPickerId, individualBorderSettings]);

  // Effect to handle global drag events for better drag leave detection of placeholder overlay
  useEffect(() => {
    if (!dragOverId || showVisualDropChoice) return;

    // Global dragend listener to ensure cleanup
    const handleGlobalDragEnd = () => {
      setDragOverId(null);
      setIsDragFromCanvas(false);
    };

    // Global dragover listener to detect when drag is outside SpreadCanvas
    const handleGlobalDragOver = (e: DragEvent) => {
      // Find the main SpreadCanvas container
      const spreadCanvasElement = document.querySelector('[data-spread-canvas="true"]');
      if (spreadCanvasElement && !spreadCanvasElement.contains(e.target as Node)) {
        // We're dragging outside SpreadCanvas, start cleanup timeout
        if (dragOverTimeoutRef.current) {
          clearTimeout(dragOverTimeoutRef.current);
        }
        dragOverTimeoutRef.current = setTimeout(() => {
          setDragOverId(null);
          setIsDragFromCanvas(false);
        }, 150);
      } else {
        // We're back over SpreadCanvas, cancel cleanup
        if (dragOverTimeoutRef.current) {
          clearTimeout(dragOverTimeoutRef.current);
          dragOverTimeoutRef.current = null;
        }
      }
    };

    document.addEventListener('dragend', handleGlobalDragEnd);
    document.addEventListener('dragover', handleGlobalDragOver);

    return () => {
      document.removeEventListener('dragend', handleGlobalDragEnd);
      document.removeEventListener('dragover', handleGlobalDragOver);
      if (dragOverTimeoutRef.current) {
        clearTimeout(dragOverTimeoutRef.current);
      }
    };
  }, [dragOverId, showVisualDropChoice]);

  // Effect to clear dragOverId when showVisualDropChoice is enabled
  useEffect(() => {
    if (showVisualDropChoice && dragOverId) {
      setDragOverId(null);
      setIsDragFromCanvas(false);
    }
  }, [showVisualDropChoice, dragOverId]);

  // Effect to handle global drag events for Visual Drop Choice cleanup
  useEffect(() => {
    if (!showVisualDropChoice || !visualDropChoice.isVisible) return;

    // Global dragend listener to ensure Visual Drop Choice cleanup
    const handleGlobalDragEnd = () => {
      if (visualDropChoice.isVisible) {
        lastVisualDropPlaceholderRef.current = '';
        setVisualDropChoice({ isVisible: false, placeholderId: '' });
      }
    };

    // Global dragover listener to detect when drag is outside SpreadCanvas
    const handleGlobalDragOver = (e: DragEvent) => {
      // Find the main SpreadCanvas container
      const spreadCanvasElement = document.querySelector('[data-spread-canvas="true"]');
      if (spreadCanvasElement && !spreadCanvasElement.contains(e.target as Node)) {
        // We're dragging outside SpreadCanvas, start cleanup timeout
        if (dragOverTimeoutRef.current) {
          clearTimeout(dragOverTimeoutRef.current);
        }
        dragOverTimeoutRef.current = setTimeout(() => {
          if (visualDropChoice.isVisible) {
            lastVisualDropPlaceholderRef.current = '';
            setVisualDropChoice({ isVisible: false, placeholderId: '' });
          }
        }, 150);
      } else {
        // We're back over SpreadCanvas, cancel cleanup
        if (dragOverTimeoutRef.current) {
          clearTimeout(dragOverTimeoutRef.current);
          dragOverTimeoutRef.current = null;
        }
      }
    };

    document.addEventListener('dragend', handleGlobalDragEnd);
    document.addEventListener('dragover', handleGlobalDragOver);

    return () => {
      document.removeEventListener('dragend', handleGlobalDragEnd);
      document.removeEventListener('dragover', handleGlobalDragOver);
      if (dragOverTimeoutRef.current) {
        clearTimeout(dragOverTimeoutRef.current);
      }
    };
  }, [showVisualDropChoice, visualDropChoice.isVisible]);

  // Helper function to calculate which edges of a placeholder are outside edges
  const calculatePlaceholderOutsideEdges = useCallback((placeholderId: string) => {
    if (!placeholderId) {
      return { top: false, bottom: false, left: false, right: false };
    }

    // Get the layout for the current spread
    const layoutForCurrentSpread = allSpreadLayouts[currentSpreadId] || [];
    
    // Find the adjusted placeholder by ID
    const adjustedPlaceholder = layoutForCurrentSpread.find(placeholder => placeholder.id === placeholderId);
    if (!adjustedPlaceholder) {
      return { top: false, bottom: false, left: false, right: false };
    }

    // Use the adjusted placeholder coordinates (already in points)
    const placeholderX_pt = adjustedPlaceholder.adjustedX_pt;
    const placeholderY_pt = adjustedPlaceholder.adjustedY_pt;
    const placeholderWidth_pt = adjustedPlaceholder.adjustedWidth_pt;
    const placeholderHeight_pt = adjustedPlaceholder.adjustedHeight_pt;

    const spreadTotalWidthPt = spreadDimensionsPt.width;
    const spreadTotalHeightPt = spreadDimensionsPt.height;
    const edgeTolerancePt = 2.0; // Same tolerance as used in border calculations

    // Determine if placeholder edges are near page edges (same logic as border calculations)
    const isTopEdgeOutside = placeholderY_pt < edgeTolerancePt;
    const isBottomEdgeOutside = (placeholderY_pt + placeholderHeight_pt) >= (spreadTotalHeightPt - edgeTolerancePt);
    const isLeftEdgeOutside = placeholderX_pt < edgeTolerancePt;
    const isRightEdgeOutside = (placeholderX_pt + placeholderWidth_pt) >= (spreadTotalWidthPt - edgeTolerancePt);

    return {
      top: isTopEdgeOutside,
      bottom: isBottomEdgeOutside,
      left: isLeftEdgeOutside,
      right: isRightEdgeOutside,
    };
  }, [currentSpreadId, allSpreadLayouts, spreadDimensionsPt]);
 
  // Effect to sync updated paths from global cache
  // Removed useEffect that reset projectSessionTooltipDismissed based on currentSpreadId

  useEffect(() => {
    if (window.bookProofsApp?.getAllUpdatedPaths) {
      const allPaths = window.bookProofsApp.getAllUpdatedPaths();
      if (Object.keys(allPaths).length > 0) {
        setUpdatedImagePaths(allPaths);
      }
    }
  }, []);

  // Effect to load projectBackgroundImage as a Data URL - OPTIMIZED for performance
  useEffect(() => {
    if (!projectBackgroundImage) {
      setDisplayableProjectBackgroundSrc(null);
      return;
    }

    if (!window.electronAPI?.getImageDataUrl) {
      return;
    }

    // PERFORMANCE OPTIMIZATION: Defer expensive IPC call to avoid blocking navigation
    const timeoutId = setTimeout(() => {
      
      fetchDataUrl(projectBackgroundImage)
        .then(dataUrl => {
          if (dataUrl) {
            setDisplayableProjectBackgroundSrc(dataUrl);
          } else {
            setDisplayableProjectBackgroundSrc(null);
          }
        })
        .catch(error => {
          setDisplayableProjectBackgroundSrc(null);
        });
    }, 100); // 100ms delay to allow navigation to complete first

    return () => {
      clearTimeout(timeoutId); // Cleanup timeout on unmount/change
    };
  }, [projectBackgroundImage]); // Re-run when the main prop changes

  // Effect to detect project background file updates (photoshop round trips) and refresh Data URL - OPTIMIZED
  useEffect(() => {
    if (!projectBackgroundImage) return;
    
    // Extract timestamp from cache-busted URL (e.g., "?t=1748452216307")
    const timestampMatch = projectBackgroundImage.match(/[?&]t=(\d+)/);
    const timestamp = timestampMatch ? timestampMatch[1] : null;
    
    // Only refresh Data URL if this is a photoshop round trip (has timestamp) and we haven't processed this timestamp yet
    if (timestamp && displayableProjectBackgroundSrc && !isTimestampProcessed(timestamp)) {
      
      // Mark this timestamp as processed globally
      markTimestampProcessed(timestamp);
      
      // Clear cache for this file since it was updated
      clearCachedDataUrl(projectBackgroundImage);
      
      // PERFORMANCE OPTIMIZATION: Also defer round-trip refreshes to avoid blocking
      const timeoutId = setTimeout(() => {
        
        fetchDataUrl(projectBackgroundImage)
          .then(dataUrl => {
            if (dataUrl) {
              setDisplayableProjectBackgroundSrc(dataUrl);
            } else {
            }
          })
          .catch(error => {
          });
      }, 50); // Shorter delay for round-trips since they're user-initiated

      return () => clearTimeout(timeoutId);
    } else if (timestamp && isTimestampProcessed(timestamp)) {
    }
  }, [projectBackgroundImage, displayableProjectBackgroundSrc]); // Trigger when projectBackgroundImage changes and we have existing Data URL

  // Effect to initialize/update individualBorderSettings when spread changes
  useEffect(() => {
    const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (activeSpread) {
      const newSettings: Record<string, { enabled: boolean; size: number; color: string }> = {};
      activeSpread.images.forEach(placement => {
        if (placement.imageId) { // Only consider placements with images
          newSettings[placement.placeholderId] = {
            enabled: placement.individualBorder?.enabled ?? false,
            size: placement.individualBorder?.size ?? localImageBorderSize, // Default to project or 0 if not set
            color: ensureRgbaString(placement.individualBorder?.color ?? localImageBorderColor), // Ensure RGBA string
          };
        }
      });
      setIndividualBorderSettings(newSettings);
    } else {
      setIndividualBorderSettings({}); // Clear settings if no active spread
    }
  }, [currentSpreadId, renderedSpreads, localImageBorderSize, localImageBorderColor]); // Add dependencies

  // Effect to handle clicks outside the color picker to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (activeColorPickerId && colorPickerRef.current && !colorPickerRef.current.contains(event.target as Node)) {
        // Flush any pending throttled color updates before closing
        throttledCommitColorChange.flush();
        setActiveColorPickerId(null);
        setPickerPosition(null); // Reset position when closing
      }
    };

    if (activeColorPickerId) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeColorPickerId, throttledCommitColorChange]); // Added throttledCommitColorChange to dependencies

  // Rotation event handlers with vertical slider
  const handleRotationMouseDown = useCallback((e: React.MouseEvent, placeholderId: string) => {
    e.stopPropagation();
    e.preventDefault();


    const activeSpread = renderedSpreads[currentRenderedIndex];
    const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
    if (!placement || !placement.imageId) {
      return;
    }

    const currentRotation = placement.transform?.rotation || 0;

    setIsRotating(true);
    setRotationStartAngle(e.clientY); // Use Y position as start reference
    setRotationStartValue(currentRotation);
    setRotatingPlaceholderId(placeholderId);

    document.body.style.cursor = 'ns-resize'; // North-south resize cursor
  }, [renderedSpreads, currentRenderedIndex]);

  const handleRotationMouseMove = useCallback((e: MouseEvent) => {
    if (!isRotating || !rotatingPlaceholderId) return;

    e.preventDefault();
    e.stopPropagation();

    // Calculate rotation based on vertical mouse movement
    // Moving up = counter-clockwise, moving down = clockwise
    const deltaY = rotationStartAngle - e.clientY; // Inverted so up is positive
    const rotationSensitivity = 0.5; // Degrees per pixel moved
    const rotationDelta = deltaY * rotationSensitivity;
    const newRotation = normalizeRotation(rotationStartValue + rotationDelta);

    if (onUpdateImageTransform) {
      
      // Get current transform to preserve other properties
      const activeSpread = renderedSpreads[currentRenderedIndex];
      const placement = activeSpread?.images.find(p => p.placeholderId === rotatingPlaceholderId);
      const currentTransform = placement?.transform || {};
      
      // Send complete transform to ensure rotation isn't lost
      onUpdateImageTransform(currentSpreadId, rotatingPlaceholderId, {
        ...currentTransform,
        rotation: newRotation
      });
    }
  }, [isRotating, rotatingPlaceholderId, rotationStartAngle, rotationStartValue, onUpdateImageTransform, currentSpreadId, renderedSpreads, currentRenderedIndex]);

  const handleRotationMouseUp = useCallback(() => {
    if (isRotating && rotatingPlaceholderId && onUpdateImageTransform) {
      // Mark the end of rotation for undo state
      const activeSpread = renderedSpreads[currentRenderedIndex];
      const placement = activeSpread?.images.find(p => p.placeholderId === rotatingPlaceholderId);
      if (placement && placement.transform) {
        onUpdateImageTransform(currentSpreadId, rotatingPlaceholderId, {
          ...placement.transform,
          // @ts-ignore: Add special flag for undo handling
          _isTransformEnd: true
        });
      }
    }

    setIsRotating(false);
    setRotatingPlaceholderId(null);
    document.body.style.cursor = 'default';
  }, [isRotating, rotatingPlaceholderId, onUpdateImageTransform, renderedSpreads, currentRenderedIndex, currentSpreadId]);

  // Add global event listeners for rotation
  useEffect(() => {
    if (isRotating) {
      document.addEventListener('mousemove', handleRotationMouseMove);
      document.addEventListener('mouseup', handleRotationMouseUp);
      document.addEventListener('mouseleave', handleRotationMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleRotationMouseMove);
      document.removeEventListener('mouseup', handleRotationMouseUp);
      document.removeEventListener('mouseleave', handleRotationMouseUp);
    };
  }, [isRotating, handleRotationMouseMove, handleRotationMouseUp]);

  // --- Set up listener for file updates from Photoshop edits ---
  useEffect(() => {
    if (window.electronAPI?.onFileUpdated) {
      const removeListener = window.electronAPI.onFileUpdated((data) => {
        const { originalPath, previewUrl } = data;
        
        // Find the image that matches this originalPath
        const matchedImage = images.find(img => img.originalPath === originalPath);
        
        if (matchedImage && typeof window.electronAPI?.regenerateThumbnails === 'function') {
          
          // Get the most up-to-date path for this image
          let imagePath = matchedImage.originalPath;
          
          // Check if we have an updated path in our cache
          if (window.bookProofsApp?.getUpdatedFilePath) {
            const updatedPath = window.bookProofsApp.getUpdatedFilePath(matchedImage.originalPath);
            if (updatedPath) {
              imagePath = updatedPath;
            }
          }
          
          // Use the exact same method as manual regenerate preview
          window.electronAPI.regenerateThumbnails(imagePath)
            .then((refreshedImage) => {
              if (refreshedImage) {
                // Store the regenerated image data in our local state to ensure it persists
                setUpdatedImagePaths(prev => ({
                  ...prev,
                  [matchedImage.originalPath]: imagePath
                }));
                
                // Create a direct cache entry for this specific image
                if (!window.bookProofsApp) window.bookProofsApp = {};
                if (!(window.bookProofsApp as any).imageCache) {
                  (window.bookProofsApp as any).imageCache = {};
                }
                (window.bookProofsApp as any).imageCache[matchedImage.id] = {
                  thumbnailUrl: refreshedImage.thumbnailUrl,
                  previewUrl: refreshedImage.previewUrl,
                  originalPath: imagePath,
                  timestamp: Date.now()
                };
                
                // Update the global image state with the new thumbnails
                if (onUpdateImageThumbnails) {
                  onUpdateImageThumbnails(matchedImage.id, {
                    thumbnailUrl: refreshedImage.thumbnailUrl,
                    previewUrl: refreshedImage.previewUrl
                  });
                }
                
                // Force refresh the image by updating its timestamp
                setImageRefreshTimestamps(prev => ({
                  ...prev,
                  [matchedImage.id]: Date.now()
                }));
                
              }
            })
            .catch((err: Error) => {
              console.error(`[SpreadCanvas] Auto-regenerate preview failed for ${originalPath}:`, err);
            });
        }
      });

      return () => {
        removeListener();
      };
    }
  }, [images, onUpdateImageThumbnails]);

  // --- Auto-Cover Logic ---
  const checkAndApplyAutoCover = useCallback((spreadId: string, placeholderId: string, triggerSource: string) => {
    // console.log(`[AutoZoom Check START] Source: ${triggerSource}, Spread: ${spreadId}, Placeholder: ${placeholderId}`); // VERBOSE LOG

    // Check if we're currently in the middle of an undo/redo operation
    if (window.bookProofsApp?.isRestoringState) {
      // Skip autozoom during undo/redo operations
      // console.log(`[AutoZoom Check SKIP] Undo/redo operation in progress`);
      return;
    }

    if (!autoCoverNearEdges || !onUpdateImageTransform || !getImageDimensions) {
      // Setting is off or required functions/data are missing
      // console.log(`[AutoZoom Check SKIP] Setting disabled or required functions/data missing. autoCoverNearEdges: ${autoCoverNearEdges}, onUpdateImageTransform: ${!!onUpdateImageTransform}, getImageDimensions: ${!!getImageDimensions}`); // VERBOSE LOG
      return;
    }

    const activeSpread = renderedSpreads.find(s => s.id === spreadId);
    const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
    const containerDims = placeholderPixelDims[placeholderId];

    // Check if we have a placement FIRST
    if (!placement) {
      // console.log(`[AutoZoom Check SKIP] Placement not found for placeholderId: ${placeholderId} on spreadId: ${spreadId}`); // VERBOSE LOG
      return;
    }
    // Now check image, container dims, and fit mode
    const currentFitMode = placement.transform?.fit ?? 'contain';
    if (!placement.imageId || !containerDims || containerDims.width <= 0 || containerDims.height <= 0 || currentFitMode !== 'contain') {
       // console.log(`[AutoZoom Check SKIP] No image, invalid container dims, or fit mode is not 'contain'. ImageId: ${placement.imageId}, ContainerDims: ${JSON.stringify(containerDims)}, FitMode: ${currentFitMode}`); // VERBOSE LOG
      return; // Need valid image dimensions
    }

    const imageNativeDims = getImageDimensions(placement.imageId);
    if (!imageNativeDims || imageNativeDims.width <= 0 || imageNativeDims.height <= 0) {
      // console.log(`[AutoZoom Check SKIP] Invalid native image dimensions. ImageId: ${placement.imageId}, Dims: ${JSON.stringify(imageNativeDims)}`); // VERBOSE LOG
      return; // Need valid image dimensions
    }

    // --- Calculate Rendered Size for 'contain' ---
    const containerW = containerDims.width;
    const containerH = containerDims.height;
    const imageW = imageNativeDims.width;
    const imageH = imageNativeDims.height;
    const imageRatio = imageW / imageH;
    const containerRatio = containerW / containerH;

    let renderedW: number;
    let renderedH: number;

    if (imageRatio > containerRatio) { // Image is wider than container (relative to height) -> fit width
      renderedW = containerW;
      renderedH = containerW / imageRatio;
    } else { // Image is taller or same aspect ratio -> fit height
      renderedH = containerH;
      renderedW = containerH * imageRatio;
    }

    // --- NEW LOGIC: Check if IMAGE edges are close to SPREAD edges ---
    const templateId = activeSpread?.templateId;
    const templateData = templateId ? allTemplates?.find(t => t.id === templateId) : undefined;
    const placeholder = templateData?.images.find(p => p.id === placeholderId);

    // Proceed only if we have placeholder data and it's not a blank spread
    if (placeholder && templateId !== '__blank__') {
        const frameX = placeholder.x;
        const frameY = placeholder.y;
        const frameW = placeholder.width;
        const frameH = placeholder.height;

        // Calculate relative gaps *within the placeholder frame*
        // Avoid division by zero if container dimensions are invalid
        const gapRelX = containerW > 0 ? ((containerW - renderedW) / 2) / containerW : 0;
        const gapRelY = containerH > 0 ? ((containerH - renderedH) / 2) / containerH : 0;

        // Calculate the image's visible edges relative to the spread (0-1)
        const imageEdgeLeftRel = frameX + (gapRelX * frameW);
        const imageEdgeRightRel = frameX + frameW - (gapRelX * frameW);
        const imageEdgeTopRel = frameY + (gapRelY * frameH);
        const imageEdgeBottomRel = frameY + frameH - (gapRelY * frameH);

        // Define the threshold for closeness to the spread edge
        const spreadEdgeThresholdPercent = 0.015; // 1.5% (User adjustable target)
        const calculationTolerance = 2.5e-3; // Increased tolerance to 1.25% (0.0125) for floating point inaccuracies

        // Check if any image edge is close to the corresponding spread edge,
        // BUT *outside* the calculation tolerance zone.
        const isNearLeftSpreadEdge = imageEdgeLeftRel > calculationTolerance && imageEdgeLeftRel < spreadEdgeThresholdPercent;
        const isNearRightSpreadEdge = imageEdgeRightRel < (1 - calculationTolerance) && imageEdgeRightRel > (1 - spreadEdgeThresholdPercent);
        const isNearTopSpreadEdge = imageEdgeTopRel > calculationTolerance && imageEdgeTopRel < spreadEdgeThresholdPercent;
        const isNearBottomSpreadEdge = imageEdgeBottomRel < (1 - calculationTolerance) && imageEdgeBottomRel > (1 - spreadEdgeThresholdPercent);

        const isImageNearSpreadEdge = isNearLeftSpreadEdge || isNearRightSpreadEdge || isNearTopSpreadEdge || isNearBottomSpreadEdge;

        // Apply zoom ONLY if the image edge is measurably near (but not exactly on) the spread edge
        if (isImageNearSpreadEdge) {
            const currentTransform = placement.transform ?? { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'contain' };
            const currentScale = currentTransform.scale ?? 1;

            // Only apply zoom if the current scale is very close to 1 and fit is 'contain'
            const currentFitModeCheck = currentTransform.fit ?? 'contain'; // Re-check fit mode just before applying
            if (currentScale < 1.01 && currentFitModeCheck === 'contain') {
                // --- VERBOSE Logging ---
                const logReason = [
                    isNearLeftSpreadEdge ? `Left (${imageEdgeLeftRel.toFixed(6)})` : null,
                    isNearRightSpreadEdge ? `Right (${imageEdgeRightRel.toFixed(6)})` : null,
                    isNearTopSpreadEdge ? `Top (${imageEdgeTopRel.toFixed(6)})` : null,
                    isNearBottomSpreadEdge ? `Bottom (${imageEdgeBottomRel.toFixed(6)})` : null,
                ].filter(Boolean).join(', ');

                // console.groupCollapsed(
                //     `%c[AutoZoom APPLYING] Source: ${triggerSource}, Placeholder: ${placeholderId}, Image: ${placement.imageId}`,
                //     'color: blue; font-weight: bold;'
                // );
                // console.log(`Reason: Near Spread Edge(s): ${logReason}`);
                // console.log(`Threshold: ${spreadEdgeThresholdPercent}, Tolerance: ${calculationTolerance}`);
                // console.log(`Current State: Scale=${currentScale.toFixed(4)}, Fit=${currentFitModeCheck}`);
                // console.log(`Container Dims (px): w=${containerW.toFixed(2)}, h=${containerH.toFixed(2)}`);
                // console.log(`Image Native Dims (px): w=${imageW}, h=${imageH}`);
                // console.log(`Rendered Dims (px): w=${renderedW.toFixed(2)}, h=${renderedH.toFixed(2)}`);
                // console.log(`Frame Rel Pos: x=${frameX.toFixed(4)}, y=${frameY.toFixed(4)}, w=${frameW.toFixed(4)}, h=${frameH.toFixed(4)}`);
                // console.log(`Gaps Rel: x=${gapRelX.toFixed(6)}, y=${gapRelY.toFixed(6)}`);
                // console.log(`Image Edges Rel: L=${imageEdgeLeftRel.toFixed(6)}, R=${imageEdgeRightRel.toFixed(6)}, T=${imageEdgeTopRel.toFixed(6)}, B=${imageEdgeBottomRel.toFixed(6)}`);
                // console.log(`Edge Checks: nearL=${isNearLeftSpreadEdge}, nearR=${isNearRightSpreadEdge}, nearT=${isNearTopSpreadEdge}, nearB=${isNearBottomSpreadEdge}`);
                // console.groupEnd();
                // --- End VERBOSE Logging ---

                const newScale = currentScale * 1.02; // Apply 2% zoom

                onUpdateImageTransform(spreadId, placeholderId, {
                    ...currentTransform,
                    scale: newScale,
                    fit: 'contain', // Ensure fit remains 'contain'
                    // @ts-ignore: Mark this as a change that should trigger undo state save
                    _shouldSaveUndo: true
                });
                toast.info(`Image near spread edge detected, applying 2% zoom.`, { id: `autozoom-${placeholderId}` });
            }
        }
    } else {
       // console.log(`[AutoZoom Check SKIP] Placeholder/Template invalid. Placeholder: ${!!placeholder}, TemplateID: ${templateId}`); // VERBOSE LOG
    }
    // --- END NEW LOGIC ---

  }, [
    autoCoverNearEdges,
    renderedSpreads, // Need access to current spread data
    placeholderPixelDims, // Need container dimensions
    getImageDimensions, // Need image dimensions
    onUpdateImageTransform, // Need to call the update function
    allTemplates // Need access to template data for placeholder info
    // currentSpreadId is implicitly available in the scope where this will be called
    // triggerSource is passed directly, not a dependency
  ]);
  // --- End Auto-Cover Logic ---

  const handleRegenerateBackgroundPreview = useCallback(async (spreadIdToUpdate: string, currentBgImagePathForSpread: string) => {
    if (!window.electronAPI?.regenerateThumbnails) {
      toast.error("Preview regeneration feature is not available.");
      return;
    }

    const osPath = getOriginalPathFromUrl(currentBgImagePathForSpread);
    if (!osPath) {
      toast.error("Could not determine the original path for the spread's background image.");
      return;
    }

    try {
      const refreshedImage = await window.electronAPI.regenerateThumbnails(osPath);

      if (refreshedImage) {

        // For manual regeneration, update both the visual (imagePath) and dimensions
        const updates: Partial<SpreadBackgroundData> = {};
        
        // Ensure originalPath is preserved
        updates.originalPath = osPath;
        
        // Update the image path to trigger visual refresh (this was missing in my previous fix)
        if (refreshedImage.previewUrl) {
          updates.imagePath = refreshedImage.previewUrl;
        }
        
        // Update natural dimensions from original image, not thumbnail dimensions
        const originalImage = images.find(img => img.originalPath === osPath);
        if (originalImage && originalImage.naturalWidth && originalImage.naturalHeight) {
          updates.naturalWidth = originalImage.naturalWidth;
          updates.naturalHeight = originalImage.naturalHeight;
        } else if (refreshedImage.naturalWidth && refreshedImage.naturalHeight) {
          // Fallback to refreshed image dimensions if original not found
          updates.naturalWidth = refreshedImage.naturalWidth;
          updates.naturalHeight = refreshedImage.naturalHeight;
        }

        // Apply updates if we have any
        if (Object.keys(updates).length > 0) {
          onUpdateSpreadBackground(spreadIdToUpdate, updates);
        }
        
        toast.success("Spread background preview regenerated.");
      } else {
        toast.error("Failed to regenerate spread background preview.");
      }
    } catch (error) {
      toast.error(`Error regenerating spread background preview: ${(error as Error).message}`);
    }
  }, [onUpdateSpreadBackground]);

  // Expose the focusCanvas function via the ref
  // --- Imperative Handle ---
  
  // Update quality issues when spread changes or images are modified
  useEffect(() => {
    // console.log(`[Quality Check RUN] Spread: ${currentSpreadId}, onlyShowPoorQuality: ${onlyShowPoorQuality}, showQualityIndicators: ${showQualityIndicators}, dpiThresholdPercent: ${dpiWarningThresholdPercent}`); // DEBUG LOG REMOVED
    
    // Inline the quality check logic directly in the effect to avoid stale closures
    const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (!activeSpread) {
      setQualityIssues({});
      return;
    }

    const spreadContainerEl = spreadRenderContainerRef.current;
    if (!spreadContainerEl) {
      setQualityIssues({});
      return;
    }

    const spreadContainerPxWidth = spreadContainerEl.offsetWidth;
    const spreadPtWidth = spreadDimensionsPt.width * 2; // Double page width

    if (spreadContainerPxWidth <= 0 || spreadPtWidth <= 0) {
      setQualityIssues({});
      return;
    }

    const scaleFactor = spreadContainerPxWidth / spreadPtWidth; // px per pt

    const currentQualityIssues: Record<string, ImageQualityInfo> = {};

    activeSpread.images.forEach(placement => {
      if (!placement.imageId) return; // Skip empty placements

      const image = images.find(img => img.id === placement.imageId);
      if (!image) return; // Skip if image data not found

      const placeholderId = placement.placeholderId;
      const pixelDims = placeholderPixelDims[placeholderId];

      if (!pixelDims || pixelDims.width <= 0 || pixelDims.height <= 0) {
        // console.warn(`[Quality Check] Missing or invalid pixel dimensions for placeholder ${placeholderId}. Skipping.`);
        return; // Skip if pixel dimensions aren't measured yet or invalid
      }

      // Calculate physical dimensions in points based on measured pixels and scale factor
      const physicalWidthPt = pixelDims.width / scaleFactor;
      const physicalHeightPt = pixelDims.height / scaleFactor;

      // Check quality using the calculated physical dimensions and the user threshold
      const qualityInfo = checkImageQuality(
        image,
        placement,
        physicalWidthPt,
        physicalHeightPt,
        dpiWarningThresholdPercent // <<< Pass threshold prop
      );

      // Add to issues if quality is not good (revert filtering here)
      if (qualityInfo && qualityInfo.status !== ImageQualityStatus.GOOD) {
         // console.log(`[Quality Check EVAL] Placeholder: ${placeholderId}, Status: ${qualityInfo.status}, DPI: ${qualityInfo.actualDpi.toFixed(1)}, Threshold%: ${dpiWarningThresholdPercent}, onlyShowPoor: ${onlyShowPoorQuality} (Adding to state)`); // DEBUG LOG REMOVED
         currentQualityIssues[placeholderId] = qualityInfo;
      }
      // else if (qualityInfo) { // DEBUG LOG REMOVED
      //    console.log(`[Quality Check EVAL] Placeholder: ${placeholderId}, Status: ${qualityInfo.status}, DPI: ${qualityInfo.actualDpi.toFixed(1)}, Threshold%: ${dpiWarningThresholdPercent} (Status is GOOD, removing from state)`); // DEBUG LOG REMOVED
      // } // DEBUG LOG REMOVED
    });

    // Update the current spread's quality issues
    setQualityIssues(currentQualityIssues);
    
    // Save the quality issues for this spread in our all-spreads state
    setAllSpreadsQualityIssues(prevState => ({
      ...prevState,
      [currentSpreadId]: currentQualityIssues
    }));
  }, [
      currentSpreadId,
      renderedSpreads,
      images,
      placeholderPixelDims,
      spreadDimensionsPt,
      dpiWarningThresholdPercent,
      // Removed onlyShowPoorQuality dependency
      showQualityIndicators
  ]);

  // Function to use saved quality issues instead of recalculating
  const getSavedQualityIssues = () => {
    // Check if we have any saved quality issues
    const hasAnyIssues = Object.values(allSpreadsQualityIssues).some(
      spreadIssues => Object.keys(spreadIssues).length > 0
    );
    
    return { 
      hasWarnings: hasAnyIssues, 
      qualityIssues: allSpreadsQualityIssues 
    };
  };
    
      useImperativeHandle(ref, () => ({
    focusCanvas: () => {
      canvasContainerRef.current?.focus();
    },
    // Implement the delete logic here - now handles both single and multiple selected images
    deleteHoveredImage: () => {
      const activeSpread = renderedSpreads[currentRenderedIndex];
      if (!activeSpread) return;

      // Determine which images to remove
      let placeholdersToRemove: string[] = [];
      
      // Priority 1: If we have selected images and the hovered image is among them, remove all selected
      if (selectedImages.size > 0 && hoveredPlaceholderId && selectedImages.has(hoveredPlaceholderId)) {
        placeholdersToRemove = Array.from(selectedImages);
      }
      // Priority 2: If we have selected images but no hovered image (or hovered is not selected), remove all selected
      else if (selectedImages.size > 0 && (!hoveredPlaceholderId || !selectedImages.has(hoveredPlaceholderId))) {
        placeholdersToRemove = Array.from(selectedImages);
      }
      // Priority 3: Fallback to single hovered image (original behavior)
      else if (hoveredPlaceholderId) {
        placeholdersToRemove = [hoveredPlaceholderId];
      } else {
        // No valid target for deletion
        return;
      }

      // Filter out empty placeholders
      const validPlaceholdersToRemove = placeholdersToRemove.filter(placeholderId => {
        const placement = activeSpread.images.find(p => p.placeholderId === placeholderId);
        return placement && placement.imageId;
      });

      if (validPlaceholdersToRemove.length === 0) {
        return; // No valid images to remove
      }

      // Ensure findExactTemplateForImageCount is available and callable
      const findExactTemplate = typeof findExactTemplateForImageCount === 'function' ? findExactTemplateForImageCount : () => null;

      const currentImageCount = activeSpread.images.filter(p => p.imageId).length;
      const targetImageCount = currentImageCount - validPlaceholdersToRemove.length;

      // Handle template switching logic for multiple image removal
      if (autoSwitchTemplateOnRemove && onRemoveImageAndSwitchTemplate && validPlaceholdersToRemove.length === 1) {
        // For single image removal, use existing template switching logic
        const placeholderToRemove = validPlaceholdersToRemove[0];
        
        if (targetImageCount > 0) {
          const newTemplate = findExactTemplate(targetImageCount);
          if (newTemplate) {
            onRemoveImageAndSwitchTemplate(currentSpreadId, placeholderToRemove);
            toast.info(`Image removed and template switched to '${newTemplate.name}'.`);
          } else {
            onUpdateSpreadImages(currentSpreadId, placeholderToRemove, null);
            toast.info(`Image removed. No template found for ${targetImageCount} images.`);
          }
        } else {
          onUpdateSpreadImages(currentSpreadId, placeholderToRemove, null);
          toast.info("Last image removed.");
        }
      } else {
        // For multiple image removal or when auto-switch is disabled
        if (validPlaceholdersToRemove.length > 1 && onRemoveMultipleImagesAndSwitchTemplate) {
          // Use the new multi-image removal function that handles proper image mapping
          // (The function itself will check autoSwitchTemplateOnRemove internally)
          onRemoveMultipleImagesAndSwitchTemplate(currentSpreadId, validPlaceholdersToRemove);
          // Toast is handled by the function itself
        } else {
          // Fallback to sequential removal for single images or when auto-switch is disabled
          validPlaceholdersToRemove.forEach(placeholderId => {
            onUpdateSpreadImages(currentSpreadId, placeholderId, null);
          });

          // Try to switch template after removing all images if auto-switch is enabled
          if (autoSwitchTemplateOnRemove && targetImageCount > 0) {
            const newTemplate = findExactTemplate(targetImageCount);
            if (newTemplate && onSelectTemplate) {
              onSelectTemplate(newTemplate.id);
              if (validPlaceholdersToRemove.length === 1) {
                toast.info(`Image removed and template switched to '${newTemplate.name}'.`);
              } else {
                toast.info(`${validPlaceholdersToRemove.length} images removed and template switched to '${newTemplate.name}'.`);
              }
            } else {
              if (validPlaceholdersToRemove.length === 1) {
                toast.info(`Image removed. No template found for ${targetImageCount} images.`);
              } else {
                toast.info(`${validPlaceholdersToRemove.length} images removed. No template found for ${targetImageCount} images.`);
              }
            }
          } else {
            // No auto-switch or target count is 0
            if (targetImageCount === 0) {
              if (validPlaceholdersToRemove.length === 1) {
                toast.info("Last image removed.");
              } else {
                toast.info("All images removed.");
              }
            } else {
              if (validPlaceholdersToRemove.length === 1) {
                toast.info("Image removed.");
              } else {
                toast.info(`${validPlaceholdersToRemove.length} images removed.`);
              }
            }
          }
        }
      }

      // Clear states
      setHoveredPlaceholderId(null);
      setSelectedImages(new Set());
    },
    // Function to check image quality for all spreads
    // Now uses saved quality issues instead of recalculating
    checkImageQuality: () => {
      // Return the saved quality issues for all spreads
      return getSavedQualityIssues();
    },
    // --- FUNCTION: Toggle Fit Mode All (Ensuring Reset) ---
    toggleFitModeAll: () => {
      const activeSpread = renderedSpreads[currentRenderedIndex];
      if (!activeSpread || !onUpdateImageTransform) {
        return;
      }

      // Determine target mode based on the first image's current mode
      const firstImagePlacement = activeSpread.images.find(p => p.imageId);
      const currentModeOfFirst = firstImagePlacement?.transform?.fit ?? 'contain';
      const targetFitMode: 'cover' | 'contain' = currentModeOfFirst === 'contain' ? 'cover' : 'contain';

      let updatedCount = 0;

      activeSpread.images.forEach(placement => {
        if (placement.imageId) {
          const placeholderId = placement.placeholderId;
          // Apply the target mode and reset transform
          onUpdateImageTransform(currentSpreadId, placeholderId, {
            fit: targetFitMode,
            scale: 1,
            focalX: 0.5, // Ensure focal points are reset
            focalY: 0.5,  // Ensure focal points are reset
            rotation: 0, // Reset rotation
            // @ts-ignore: Mark this as a change that should trigger undo state save
            _shouldSaveUndo: true
          });
          updatedCount++;
        }
      });

      if (updatedCount > 0) {
        toast.info(`Fit mode set to '${targetFitMode}' & zoom reset for ${updatedCount} image(s).`);
        // If we just set everything to 'contain', the useEffect watching renderedSpreads will handle auto-cover
        // No direct call needed here.
      } else {
        toast.info("No images found on spread to toggle fit mode.");
      }
    },
    // --- END NEW FUNCTION ---
    // --- FUNCTION: Save Transforms Before Navigation ---
    saveTransformsBeforeNavigation: () => {
      // Flush any pending throttled updates to ensure the latest transform state is saved
      throttledUpdateTransform.flush();
      
      // Ensure the current spread's quality issues are saved in our all-spreads state
      if (currentSpreadId) {
        setAllSpreadsQualityIssues(prevState => ({
          ...prevState,
          [currentSpreadId]: qualityIssues
        }));
      }
    }
    // --- END FUNCTION ---
  }), [
    // Dependencies for deleteHoveredImage function
    hoveredPlaceholderId,
    selectedImages,
    renderedSpreads,
    currentRenderedIndex,
    currentSpreadId,
    autoSwitchTemplateOnRemove,
    findExactTemplateForImageCount,
    onRemoveImageAndSwitchTemplate,
    onUpdateSpreadImages,
    onSelectTemplate,
    // Dependencies for other functions
    onUpdateImageTransform,
    qualityIssues,
    onSelectionChange
  ]);

  // Effect for keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Comment out the check for TemplatesTray focus.
      // TemplatesTray now handles its own keys (Up/Down/Enter/Escape) or ignores others (Left/Right),
      // so this global listener should always handle Left/Right for navigation/add.
      // if (isTemplatesTrayFocused && (event.key === 'ArrowLeft' || event.key === 'ArrowRight')) {
      //   return;
      // }
      if (event.key === 'ArrowLeft') {
        if (currentSpreadNumber === 1 && hasCover && onNavigateToCover) {
          onNavigateToCover();
        } else if (currentSpreadNumber > 1) {
          onNavigateSpread('prev');
        }
      } else if (event.key === 'ArrowRight') {
        // Restore original logic: Navigate if not last, add if last
        if (currentSpreadNumber < totalSpreads) {
          onNavigateSpread('next');
        } else if (addSpreadOnArrow && onAddSpread) { // Check the setting before adding
          onAddSpread();
        }
        // Comment out the "always add" logic from previous step:
        // // Always add a new spread on Right Arrow, regardless of current position or focus
        // if (onAddSpread) {
        //   onAddSpread();
        // }
      } else if (event.key === 'Escape' && isFocusMode) {
        // Exit focus mode when Escape is pressed
        event.preventDefault();
        onToggleFocusMode?.();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentSpreadNumber, totalSpreads, onNavigateSpread, onAddSpread, isTemplatesTrayFocused, isFocusMode, onToggleFocusMode, currentSpreadId, renderedSpreads, hasCover, onNavigateToCover]); // Added dependencies from commit
 
  // Effect for Up/Down arrow key template filter activation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only trigger if ArrowUp or ArrowDown is pressed AND the template tray is NOT focused
      // Also check if any element with class containing "templates" has focus (more robust check)
      const activeEl = document.activeElement;
      const isTemplatesTrayActuallyFocused = activeEl?.closest('.h-full.p-6.outline-none.overflow-auto') !== null;
      
      if ((event.key === 'ArrowUp' || event.key === 'ArrowDown') && !isTemplatesTrayFocused && !isTemplatesTrayActuallyFocused) {
        event.preventDefault(); // Prevent default scroll behavior

        // Calculate the number of images currently placed on the *active* spread
        const activeSpread = renderedSpreads[currentRenderedIndex];
        const imageCount = activeSpread ? activeSpread.images.filter(p => p.imageId).length : 0;

        // Call the activation handler if it exists
        if (onActivateTemplateFilter) {
          const activeSpread = renderedSpreads[currentRenderedIndex]; // Get active spread again to access templateId
          const currentTemplateId = activeSpread?.templateId; // Get the current template ID
          // Let's activate even with 0 images. Pass true to select first, current template ID, and the arrow key
          onActivateTemplateFilter(imageCount, true, currentTemplateId, event.key as 'ArrowUp' | 'ArrowDown');
        }
      }
    };

    // Add listener to the window
    window.addEventListener('keydown', handleKeyDown);

    // Cleanup function
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
    // Dependencies: spread.images to recalculate count, isTemplatesTrayFocused to check focus, and the handler itself
  }, [renderedSpreads, currentRenderedIndex, isTemplatesTrayFocused, onActivateTemplateFilter]); // Depend on renderedSpreads and index

  // --- Reusable function to show the image preview modal ---
  const showImagePreviewModal = useCallback(async (imageFile: ImageFile) => {
    setIsCanvasHovered(false); // Explicitly set hover to false when modal opens
    // Reset states
    setModalImageSrc(null);
    setModalImageFilename(null); // Reset filename
    setImageExifData(null);
    setLoadingExif(true);
    // Don't show modal immediately - wait until image is loaded
    
    try {
      // Check if API is available
      if (!window.electronAPI?.getImageDataUrl || !window.electronAPI?.getExifData) {
        toast.error("Preview error: API not available. Please restart the app.");
        setLoadingExif(false);
        return;
      }
      
      // Get the most up-to-date path for this image
      let imagePath = imageFile.originalPath;
      
      // Check if we have an updated path in our cache
      if (window.bookProofsApp?.getUpdatedFilePath) {
        const updatedPath = window.bookProofsApp.getUpdatedFilePath(imageFile.originalPath);
        if (updatedPath) {
          imagePath = updatedPath;
        }
      }
      
      try {
        const [imageResult, exifResult] = await Promise.all([
          window.electronAPI.getImageDataUrl(imagePath),
          window.electronAPI.getExifData(imagePath)
        ]);

        if (imageResult.success && imageResult.dataUrl) {
          setModalImageSrc(imageResult.dataUrl); // Set the source to the received Data URL
          setModalImageFilename(imageResult.filename || null); // Set the filename
          
          // Store EXIF data if successful
          if (exifResult.success && exifResult.exifData) {
            // console.log("SpreadCanvas EXIF data received:", exifResult.exifData); // <<< REMOVED
            // Look specifically for ICC or color space info in the raw data
            if (exifResult.exifData.raw) {
              // console.log("Raw EXIF data color info:", { // <<< REMOVED
              //   ColorSpace: exifResult.exifData.raw.ColorSpace,
              //   ICCProfile: exifResult.exifData.raw.ICCProfile,
              //   ICC: exifResult.exifData.raw.ICC,
              //   ProfileDescription: exifResult.exifData.raw.ICC?.ProfileDescription,
              //   DeviceModelDesc: exifResult.exifData.raw.ICC?.DeviceModelDesc,
              // });
            }
            setImageExifData(exifResult.exifData);
          } else {
            setImageExifData(null);
          }
          
          // Show the modal AFTER getting the data and setting the image source
          setShowFullImageModal(true);
        } else {
          toast.error(`Could not load image preview: ${imageResult.error || 'Unknown error'}`);
          setModalImageSrc(null); // Ensure src is null on error
          setModalImageFilename(null); // Ensure filename is null on error
          setImageExifData(null);
          setShowFullImageModal(false);
        }
      } catch (error) {
        toast.error('Failed to load image preview. Please try again.');
        setModalImageSrc(null);
        setModalImageFilename(null); // Ensure filename is null on error
        setImageExifData(null);
        setShowFullImageModal(false);
      }
    } catch (error: any) {
      toast.error(`Error loading image preview: ${error.message}`);
      setModalImageSrc(null);
      setModalImageFilename(null); // Ensure filename is null on error
      setImageExifData(null);
      setShowFullImageModal(false);
    } finally {
      setLoadingExif(false);
    }
  }, [setModalImageSrc, setModalImageFilename, setImageExifData, setLoadingExif, setShowFullImageModal, setIsCanvasHovered]); // Dependencies
 
  // Effect for Spacebar image preview (using hoveredPlaceholderId)
  useEffect(() => {
    const handleKeyDown = async (event: KeyboardEvent) => {
      // Check if spacebar is pressed and a placeholder is currently hovered
      if (event.code === 'Space' && hoveredPlaceholderId) {
        event.preventDefault(); // Prevent default spacebar action

        // If modal is already open, just close it and don't reopen
        if (showFullImageModal) {
          setShowFullImageModal(false);
          setImageExifData(null); // Clear EXIF data when closing
          return;
        }

        // Find the image associated with the hovered placeholder on the current spread
        const activeSpread = renderedSpreads[currentRenderedIndex];
        const placement = activeSpread?.images.find(p => p.placeholderId === hoveredPlaceholderId);
        const imageId = placement?.imageId;
        const imageFile = imageId ? images.find(img => img.id === imageId) : null;

        if (imageFile) {
          // Use the reusable function
          showImagePreviewModal(imageFile);
        } else {
          // Handle case where placeholder is hovered but has no image
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
    // Update dependencies
  }, [hoveredPlaceholderId, renderedSpreads, currentRenderedIndex, images, showFullImageModal, showImagePreviewModal]);

  // Effect for "S" key to toggle spread background edit mode
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle S key if not in an input field and background edit mode is available
      if (event.key === 's' || event.key === 'S') {
        // Skip if any modifier keys are pressed (to allow Cmd+S for save)
        if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) {
          return;
        }
        
        const target = event.target as HTMLElement;
        const isInInputField = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;
        
        if (!isInInputField && onToggleBackgroundEditMode) {
          event.preventDefault();
          onToggleBackgroundEditMode(currentSpreadId);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentSpreadId, onToggleBackgroundEditMode]);

  // Add a wrapper function to log transform updates before they're applied
  const loggedUpdateTransform = (spreadId: string, placeholderId: string, transform: Partial<ImageTransform>, fitMode?: 'contain' | 'cover') => {
    // console.log(`[loggedUpdateTransform] Updating ${placeholderId} on spread ${spreadId}`, { transform, fitMode }); // Commented out for less noise

    // Call the original transform update function
    if (onUpdateImageTransform) {
      // Ensure fit mode is included if provided, otherwise use the one from transform or default
      const finalTransform = {
        ...transform,
        fit: fitMode || transform.fit || 'contain',
      };
      onUpdateImageTransform(spreadId, placeholderId, finalTransform);
    }
  };

  // Function to smoothly animate between focal points
  const animatePanning = useCallback((spreadId: string, placeholderId: string, scale: number, fitMode: 'contain' | 'cover') => {
    if (!currentFocalPointsRef.current[placeholderId] || !targetFocalPointsRef.current[placeholderId]) return;
    
    const current = currentFocalPointsRef.current[placeholderId];
    const target = targetFocalPointsRef.current[placeholderId];
    
    // Calculate the distance to move this frame
    // Using an easing factor between 0 and 1 (lower = smoother but slower)
    const easingFactor = 0.25;
    
    // Calculate new position with easing
    const newX = current.x + (target.x - current.x) * easingFactor;
    const newY = current.y + (target.y - current.y) * easingFactor;
    
    // Update the current position
    currentFocalPointsRef.current[placeholderId] = { x: newX, y: newY };
    
    // Check if we're close enough to the target to stop animating
    const isCloseEnough = 
      Math.abs(newX - target.x) < 0.001 && 
      Math.abs(newY - target.y) < 0.001;
    
    if (isCloseEnough) {
      // We've reached the target, stop animating
      currentFocalPointsRef.current[placeholderId] = { x: target.x, y: target.y };
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    }
    
    // Apply the new transform while preserving rotation
    if (onUpdateImageTransform) {
      // Get current placement to preserve rotation
      const activeSpread = renderedSpreads.find(s => s.id === spreadId);
      const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
      const currentRotation = placement?.transform?.rotation || 0;
      
      onUpdateImageTransform(spreadId, placeholderId, {
        scale,
        focalX: newX,
        focalY: newY,
        fit: fitMode,
        rotation: currentRotation
      });
    }
    
    // Continue animation if we haven't reached the target
    if (!isCloseEnough && isPanning) {
      animationFrameRef.current = requestAnimationFrame(() => 
        animatePanning(spreadId, placeholderId, scale, fitMode)
      );
    }
  }, [onUpdateImageTransform, isPanning, renderedSpreads]);

  const animateZooming = useCallback((
    spreadId: string,
    placeholderId: string,
    fitMode: 'contain' | 'cover',
    focalXToMaintain: number,
    focalYToMaintain: number
  ) => {
    if (!currentZoomLevelsRef.current[placeholderId] || targetZoomLevelsRef.current[placeholderId] === undefined) {
      if (zoomAnimationFrameRef.current[placeholderId]) {
        cancelAnimationFrame(zoomAnimationFrameRef.current[placeholderId]!);
        zoomAnimationFrameRef.current[placeholderId] = null;
      }
      return;
    }

    const currentZoom = currentZoomLevelsRef.current[placeholderId];
    const targetZoom = targetZoomLevelsRef.current[placeholderId];

    const easingFactor = 0.2; // Easing factor for zoom
    const newZoom = currentZoom + (targetZoom - currentZoom) * easingFactor;

    currentZoomLevelsRef.current[placeholderId] = newZoom;

    const isCloseEnough = Math.abs(newZoom - targetZoom) < 0.001;

    if (isCloseEnough) {
      currentZoomLevelsRef.current[placeholderId] = targetZoom; // Snap to target
      if (zoomAnimationFrameRef.current[placeholderId]) {
        cancelAnimationFrame(zoomAnimationFrameRef.current[placeholderId]!);
        zoomAnimationFrameRef.current[placeholderId] = null;
      }
      if (onUpdateImageTransform) {
        // Get current placement to preserve rotation
        const activeSpread = renderedSpreads.find(s => s.id === spreadId);
        const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
        const currentRotation = placement?.transform?.rotation || 0;
        
        onUpdateImageTransform(spreadId, placeholderId, {
          scale: targetZoom,
          focalX: focalXToMaintain,
          focalY: focalYToMaintain,
          fit: fitMode,
          rotation: currentRotation,
          // @ts-ignore: Mark this as a change that should trigger undo state save
          _shouldSaveUndo: true
        });
      }
    } else {
      if (onUpdateImageTransform) {
        // Get current placement to preserve rotation
        const activeSpread = renderedSpreads.find(s => s.id === spreadId);
        const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
        const currentRotation = placement?.transform?.rotation || 0;
        
        onUpdateImageTransform(spreadId, placeholderId, {
          scale: newZoom,
          focalX: focalXToMaintain,
          focalY: focalYToMaintain,
          fit: fitMode,
          rotation: currentRotation
        });
      }
      zoomAnimationFrameRef.current[placeholderId] = requestAnimationFrame(() =>
        animateZooming(spreadId, placeholderId, fitMode, focalXToMaintain, focalYToMaintain)
      );
    }
  }, [onUpdateImageTransform, renderedSpreads]);
 
  // Throttled function to update image transform with limits
  const throttledUpdateTransform = useCallback(
    throttle((spreadId: string, placeholderId: string, transform: Partial<ImageTransform>, fitMode?: 'contain' | 'cover') => {
      // For panning, we want to use animation
      if (isPanning && transform.focalX !== undefined && transform.focalY !== undefined) {
        // Update the target focal points
        targetFocalPointsRef.current[placeholderId] = { 
          x: transform.focalX, 
          y: transform.focalY 
        };
        
        // Initialize current focal points if not set
        if (!currentFocalPointsRef.current[placeholderId]) {
          currentFocalPointsRef.current[placeholderId] = { 
            x: transform.focalX, 
            y: transform.focalY 
          };
        }
        
        // Start animation if not already running
        if (!animationFrameRef.current) {
          animationFrameRef.current = requestAnimationFrame(() => 
            animatePanning(spreadId, placeholderId, transform.scale || 1, fitMode || 'contain')
          );
        }
      } else {
        // For non-panning updates, use the regular update function
        loggedUpdateTransform(spreadId, placeholderId, transform, fitMode);
      }
    }, THROTTLE_INTERVAL),
    [onUpdateImageTransform, placeholderPixelDims, isPanning, animatePanning] // Added dependencies
  );
 
  // Removed outdated global event listener useEffect for panning, as local handlers are now used.

  // Effect to measure placeholder container dimensions and trigger debounced checks
  useEffect(() => {
    const observerSpreadId = currentSpreadId; // Capture spreadId for the observer scope

    // Debounced function to run checks after resize events settle
    const debouncedResizeCheck = debounce((placeholderIdToCheck: string) => {
        // console.log(`[ResizeObserver Debounced] Running check for ${placeholderIdToCheck} on spread ${observerSpreadId}`);
        checkAndApplyAutoCover(observerSpreadId, placeholderIdToCheck, 'ResizeObserver (Debounced)');
    }, 250); // 250ms debounce interval

    const observer = new ResizeObserver(entries => {
      // console.log(`[ResizeObserver] Callback triggered for ${entries.length} entries. Spread: ${observerSpreadId}`);
      const newDims: Record<string, { width: number; height: number }> = {};
      let changed = false;
      const changedIds: string[] = []; // Keep track of which IDs changed

      const isNavigationResize = observerSpreadId !== prevSpreadIdForResizeRef.current;
      // console.log(`[ResizeObserver] isNavigationResize: ${isNavigationResize} (observerSpreadId: ${observerSpreadId}, prevSpreadIdRef: ${prevSpreadIdForResizeRef.current})`);

      for (const entry of entries) {
        const id = Object.keys(placeholderContainerRefs.current).find(
          key => placeholderContainerRefs.current[key] === entry.target
        );
        if (id) {
          const { width, height } = entry.contentRect;
          if (width > 0 && height > 0 &&
              (placeholderPixelDims[id]?.width !== width || placeholderPixelDims[id]?.height !== height)) {
            newDims[id] = { width, height };
            changed = true;
            changedIds.push(id); // Track changed ID
          } else if (placeholderPixelDims[id]) {
            newDims[id] = placeholderPixelDims[id];
          }
        }
      }

      if (changed) {
        setPlaceholderPixelDims(prevDims => ({ ...prevDims, ...newDims }));

        const timeSinceLastLayoutChange = Date.now() - lastLayoutChangeTimestampRef.current;
        const recentlyChangedLayout = timeSinceLastLayoutChange < 700;

        if (!isNavigationResize && !recentlyChangedLayout) {
          // Call the debounced check for each changed placeholder
          changedIds.forEach(id => {
             // console.log(`[ResizeObserver] Dimensions changed for ${id}. Triggering debounced check.`);
             debouncedResizeCheck(id); // Trigger debounce for this specific ID
          });
        } else {
           if (isNavigationResize) {
              // console.log(`[ResizeObserver] Dimensions changed during navigation resize. Skipping debounced check.`);
           }
           if (recentlyChangedLayout) {
              // console.log(`[ResizeObserver] Layout changed recently (${timeSinceLastLayoutChange}ms ago). Skipping debounced check, deferring to useEffect.`);
           }
        }
      }
      prevSpreadIdForResizeRef.current = observerSpreadId;
    });

    Object.values(placeholderContainerRefs.current).forEach(el => {
      if (el) observer.observe(el);
    });

    return () => {
      observer.disconnect();
      debouncedResizeCheck.cancel(); // Cancel any pending debounced calls on cleanup
      // console.log(`[ResizeObserver Cleanup] Disconnected observer and cancelled debounced checks for spread ${observerSpreadId}`);
    };
  // Dependencies need to include functions/values used inside the effect and debounce setup
  }, [
      currentSpreadId, // Needed for observerSpreadId capture
      placeholderPixelDims, // Needed for comparison inside observer
      checkAndApplyAutoCover // Needed by the debounced function
      // renderedSpreads, currentRenderedIndex, onUpdateImageTransform are likely not direct dependencies here anymore
      // but checkAndApplyAutoCover depends on them, so they are implicitly included via that.
  ]);
  // Removed unused getImageForPlaceholder function

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    // Clear previous drag data when starting a new drag
    currentDragDataRef.current = null;
    
    // Use the custom markers to determine if it's a single image
    if (e.dataTransfer.types.includes('application/x-bookproofs-single-image')) {
      currentDragDataRef.current = {
        isSingleImage: true,
        data: null
      };
    } else if (e.dataTransfer.types.includes('application/x-bookproofs-multiple-images')) {
      currentDragDataRef.current = {
        isSingleImage: false,
        data: null
      };
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, placeholderId: string) => {
    // Ensure preventDefault is called first to allow dropping
    e.preventDefault();
    e.stopPropagation(); // Add stopPropagation just in case

    // Set the visual feedback for the drop operation
    if (e.dataTransfer.types.includes('application/x-bookproofs-swap')) {
      e.dataTransfer.dropEffect = 'move';
      setIsDragFromCanvas(true);
    } else if (e.dataTransfer.types.includes('application/json')) { // Image from tray
      e.dataTransfer.dropEffect = 'copy';
      setIsDragFromCanvas(false);
      
      // Check if we should show visual drop choice overlay
      if (showVisualDropChoice) {
        // Use stored drag data to determine if it's a single image
        const hasMultipleMarker = e.dataTransfer.types.includes('application/x-bookproofs-multiple-images');
        const hasSingleMarker = e.dataTransfer.types.includes('application/x-bookproofs-single-image');
        const isSingleImage = !hasMultipleMarker && (currentDragDataRef.current?.isSingleImage || hasSingleMarker);
        
        if (isSingleImage) {
          // Check if the placeholder is occupied
          const activeSpread = renderedSpreads[currentRenderedIndex];
          const isOccupied = activeSpread?.images.some(
            img => img.placeholderId === placeholderId && img.imageId
          );
          
          if (isOccupied) {
            // Clear any pending timeout
            if (dragOverTimeoutRef.current) {
              clearTimeout(dragOverTimeoutRef.current);
            }
            
            // Only update if different from current state to prevent flickering
            if (lastVisualDropPlaceholderRef.current !== placeholderId || !visualDropChoice.isVisible) {
              lastVisualDropPlaceholderRef.current = placeholderId;
              setVisualDropChoice({
                isVisible: true,
                placeholderId
              });
            }
          } else {
            // Hide overlay if placeholder is empty - clear regardless of which placeholder was showing it
            if (visualDropChoice.isVisible) {
              if (dragOverTimeoutRef.current) {
                clearTimeout(dragOverTimeoutRef.current);
              }
              lastVisualDropPlaceholderRef.current = '';
              setVisualDropChoice({ isVisible: false, placeholderId: '' });
            }
          }
        } else {
          // Hide overlay for multiple images
          if (visualDropChoice.isVisible) {
            lastVisualDropPlaceholderRef.current = '';
            setVisualDropChoice({ isVisible: false, placeholderId: '' });
                  }
        }
      }
    } else if (e.dataTransfer.types.includes('application/x-bookproofs-template')) { // Template from tray
      e.dataTransfer.dropEffect = 'copy'; // Allow template drop
    } else if (e.dataTransfer.types.includes('Files')) { // OS file drag
      e.dataTransfer.dropEffect = 'copy'; // Allow drop so we can show rejection message
      // Show rejection message immediately on dragover
      if (!rejectionMessage) {
        setRejectionMessage("Please import images to the Photo Library first");
        // Clear message after 3 seconds
        setTimeout(() => setRejectionMessage(null), 3000);
      }
    } else {
      e.dataTransfer.dropEffect = 'none'; // Disallow drop for other types
    }

    // Highlight the placeholder being dragged over (only when visual drop choice is disabled)
    if (!showVisualDropChoice) {
      setDragOverId(placeholderId);
    }
  };

  const handleDragLeave = (e?: React.DragEvent<HTMLDivElement>) => {
    // Clear any pending timeouts
    if (dragOverTimeoutRef.current) {
      clearTimeout(dragOverTimeoutRef.current);
    }
    
    // Increase debounce time and improve logic to prevent flickering
    dragOverTimeoutRef.current = setTimeout(() => {
      // Only clear if we're truly leaving the current target and not moving to a child element
      if (e && e.currentTarget && e.relatedTarget) {
        const isLeavingPlaceholder = !e.currentTarget.contains(e.relatedTarget as Node);
        if (isLeavingPlaceholder) {
          setDragOverId(null);
          // Clear rejection message when dragging away
          if (rejectionMessage) {
            setRejectionMessage(null);
          }
          setIsDragFromCanvas(false);
          // Only clear visual drop choice if we're actually leaving
          if (lastVisualDropPlaceholderRef.current) {
            lastVisualDropPlaceholderRef.current = '';
            setVisualDropChoice({ isVisible: false, placeholderId: '' });
                  }
        }
      } else {
        // More conservative fallback: only clear if no related target
        if (!e || !e.relatedTarget) {
          setDragOverId(null);
          setIsDragFromCanvas(false);
          lastVisualDropPlaceholderRef.current = '';
          setVisualDropChoice({ isVisible: false, placeholderId: '' });
              }
      }
    }, 100); // Increased debounce time from 50ms to 100ms
  };

  const handleDragEnd = () => {
    // Clear any pending timeouts
    if (dragOverTimeoutRef.current) {
      clearTimeout(dragOverTimeoutRef.current);
      dragOverTimeoutRef.current = null;
    }
    
    // Clear drag data when drag ends
    currentDragDataRef.current = null;
    lastVisualDropPlaceholderRef.current = '';
    setVisualDropChoice({ isVisible: false, placeholderId: '' });
    setIsCornerDropZoneDraggedOver(false);
    setDragOverId(null); // Clear drag over highlight
    setIsDragFromCanvas(false); // Clear drag source state
  };

  const handleVisualDropChoice = (choice: 'replace' | 'add') => {
    // Clear any pending timeouts
    if (dragOverTimeoutRef.current) {
      clearTimeout(dragOverTimeoutRef.current);
      dragOverTimeoutRef.current = null;
    }
    
    // Hide the overlay first
    lastVisualDropPlaceholderRef.current = '';
    setVisualDropChoice({ isVisible: false, placeholderId: '' });
    
    // Store the choice for use in handleDrop
    // We'll add this logic to handleDrop to check for this stored choice
    visualDropChoiceRef.current = choice;
  };

  const findTemplateForImageCount = (imageCount: number) => {
    if (!allTemplates || allTemplates.length === 0) return null;

    // Find a template with exactly the same number of image placeholders
    const exactMatch = allTemplates.find(t => t.images.length === imageCount);
    if (exactMatch) return exactMatch;

    // If no exact match, find the closest template with more placeholders
    const closestTemplates = allTemplates.filter(t => t.images.length >= imageCount);
    closestTemplates.sort((a, b) => a.images.length - b.images.length);

    return closestTemplates[0] || null;
  };

  // Function to find a template with an EXACT image count
  // Removed internal findExactTemplateForImageCount function (now passed as prop)
  // --- REMOVED Delete Key useEffect ---

  // --- Focus Handling ---
  const handleFocus = useCallback(() => {
    setIsFocused(true);
    onFocusChange?.(true);
  }, [onFocusChange]);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    onFocusChange?.(false);
  }, [onFocusChange]);


// Refactored drop logic into a reusable async function
// Updated to pass a full ImagePlacement object and initialFit
// Corrected function signature and placement of initialFit declaration
const processDroppedImage = async (image: ImageFile, targetPlaceholderId: string, initialFit: 'cover' | 'contain' = 'contain') => {
    if (!window.electronAPI) {
      toast.error("Confirmation dialog requires the desktop app.");
      return;
    }

    // Check if image is already used
    const isUsed = usedImageIds.includes(image.id);
    
    if (isUsed) {
      // Show duplicate warning dialog and wait for user response
      return new Promise<void>((resolve) => {
        setDuplicateWarningData({
          image,
          usageCount: 1, // We'll just indicate it's been used, exact count isn't critical
          placeholderId: targetPlaceholderId,
          initialFit
        });
        setShowDuplicateWarning(true);
        
        // The dialog will handle calling the actual placement logic
        // and resolving this promise via handleDuplicateConfirm/Cancel
        (window as any).pendingDuplicateResolve = resolve;
      });
    }

    // If not used, proceed directly
    performImagePlacement(image, targetPlaceholderId, initialFit);
  };

  // Extracted placement logic for reuse
  const performImagePlacement = (image: ImageFile, targetPlaceholderId: string, initialFit: 'cover' | 'contain') => {
    // Construct the new placement object with default transform and initialFit
    const newPlacement: ImagePlacement = {
      placeholderId: targetPlaceholderId,
      imageId: image.id,
      // Ensure the full default transform is always set on initial placement, including the passed initialFit and default focal points
      transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: initialFit } // Already correct
    };
    onUpdateSpreadImages(currentSpreadId, targetPlaceholderId, newPlacement); // Use currentSpreadId

    // --- Trigger On-Demand Preview Generation (now always onPlacement) ---
    if (!image.previewUrl && !requestedPreviewPathsRef.current.has(image.originalPath)) {
      // Get the potentially updated path
      let pathForPreview = image.originalPath;
      if (window.bookProofsApp?.getUpdatedFilePath) {
        const updatedPath = window.bookProofsApp.getUpdatedFilePath(image.originalPath);
        if (updatedPath) {
          pathForPreview = updatedPath;
        }
      }

      requestedPreviewPathsRef.current.add(image.originalPath); // Track request by original path
      
      // Track the preview request in the useImageImport hook if prop is provided
      if (trackPreviewRequest) {
        // Reset the counter when processing a new image drop
        trackPreviewRequest(image.originalPath, true);
      }

      window.electronAPI.regenerateThumbnails(pathForPreview)
        .then(result => {
          if (result) {
            // The onPreviewGenerated listener in useImageImport will handle updating the image state.
          } else {
            // Remove from ref if generation failed or returned nothing, allowing retry?
            requestedPreviewPathsRef.current.delete(image.originalPath);
          }
        })
        .catch(error => {
          toast.error(`Failed to generate preview for ${image.name}.`);
          requestedPreviewPathsRef.current.delete(image.originalPath); // Remove from ref on error
        });
    }
    // --- End On-Demand Preview ---

    // Check auto-cover after initial placement
    // console.log(`[processDroppedImage] Calling checkAndApplyAutoCover after initial placement. Placeholder: ${targetPlaceholderId}`); // VERBOSE LOG
    checkAndApplyAutoCover(currentSpreadId, targetPlaceholderId, 'processDroppedImage');
    toast.success(`Image added to template (${initialFit} mode)`); // Use the passed initialFit in the toast
  };

  // Dialog handlers for duplicate warning
  const handleDuplicateConfirm = () => {
    if (duplicateWarningData) {
      performImagePlacement(duplicateWarningData.image, duplicateWarningData.placeholderId, duplicateWarningData.initialFit);
      
      // Resolve the pending promise
      if ((window as any).pendingDuplicateResolve) {
        (window as any).pendingDuplicateResolve();
        delete (window as any).pendingDuplicateResolve;
      }
    }
    
    setShowDuplicateWarning(false);
    setDuplicateWarningData(null);
  };

  const handleDuplicateCancel = () => {
    // Resolve the pending promise without placing the image
    if ((window as any).pendingDuplicateResolve) {
      (window as any).pendingDuplicateResolve();
      delete (window as any).pendingDuplicateResolve;
    }
    
    setShowDuplicateWarning(false);
    setDuplicateWarningData(null);
    toast.info("Image placement cancelled.");
  };

  // Variable to hold the current drag preview element
  let currentDragPreview: HTMLImageElement | null = null;
  
  // Track the current placeholder being dragged - to help debug
  let currentDraggedPlaceholderId: string | null = null;

  // Cache for preloaded thumbnail images
  const preloadedThumbnails = useRef<Record<string, HTMLImageElement>>({});
  
  // Preload thumbnails for the current spread
  useEffect(() => {
    if (!renderedSpreads || renderedSpreads.length === 0) return;
    
    const currentSpread = renderedSpreads[currentRenderedIndex];
    if (!currentSpread) return;
    
    // Collect all image IDs used in the current spread
    const imageIds = currentSpread.images
      .filter(placement => placement.imageId)
      .map(placement => placement.imageId as string);
    
    // Clear cache of any images not in the current spread to avoid memory bloat
    const newCache: Record<string, HTMLImageElement> = {};
    imageIds.forEach(id => {
      if (preloadedThumbnails.current[id]) {
        newCache[id] = preloadedThumbnails.current[id];
      }
    });
    preloadedThumbnails.current = newCache;
    
    // Preload all thumbnails for the current spread
    imageIds.forEach(imageId => {
      // Skip if already preloaded
      if (preloadedThumbnails.current[imageId]) return;
      
      const imageFile = images.find(img => img.id === imageId);
      if (!imageFile || !imageFile.thumbnailUrl) return;
      
      // Create and preload the image
      const img = new Image();
      img.src = imageFile.thumbnailUrl;
      
      // Add to cache when loaded
      img.onload = () => {
        preloadedThumbnails.current[imageId] = img;
        // console.log(`Preloaded thumbnail for image ${imageId}`); // Commented out
      };
    });
  }, [renderedSpreads, currentRenderedIndex, images]);
  
  // Click-outside deselection for multi-selected images
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Only clear selection if clicking outside the canvas container
      if (canvasContainerRef.current && !canvasContainerRef.current.contains(event.target as Node)) {
        if (selectedImages.size > 0) {
          setSelectedImages(new Set());
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [selectedImages]);

  // Clear selected images when placeholders no longer have images assigned
  useEffect(() => {
    if (selectedImages.size === 0) return;
    
    const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (!currentSpread) return;
    
    // Get set of placeholder IDs that currently have images
    const placeholdersWithImages = new Set(
      currentSpread.images
        .filter(placement => placement.imageId)
        .map(placement => placement.placeholderId)
    );
    
    // Check if any selected placeholders no longer have images
    const shouldClearSelection = Array.from(selectedImages).some(
      placeholderId => !placeholdersWithImages.has(placeholderId)
    );
    
    if (shouldClearSelection) {
      // Clear all selections when images are moved away
      setSelectedImages(new Set());
    }
  }, [renderedSpreads, currentSpreadId, selectedImages]);

  // Template Editor Resize Handlers
  const handleResizeStart = useCallback((e: React.MouseEvent, placeholderId: string, edge: string) => {
    e.preventDefault();
    e.stopPropagation();


    const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (!currentSpread) {
      return;
    }

    // Get current template layout
    const currentTemplate = allTemplates?.find(t => t.id === currentSpread.templateId);
    if (!currentTemplate) {
      return;
    }

    // Use custom template if it exists, otherwise use original
    // Use the prop directly to avoid stale state issues
    const currentLayout = (customTemplates && customTemplates[currentSpread.id]) || currentTemplate.images;
    const targetPlaceholder = currentLayout.find(p => p.id === placeholderId);
    

    const newResizeData = {
      placeholderId,
      edge,
      startX: e.clientX,
      startY: e.clientY,
      initialLayout: [...currentLayout]
    };

    // Set both state and ref immediately
    setIsResizing(true);
    setResizeData(newResizeData);
    resizeDataRef.current = newResizeData;


    // Add global mouse event listeners
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);
  }, [renderedSpreads, currentSpreadId, allTemplates, customTemplates]);

  const handleResizeMove = useCallback((e: MouseEvent) => {
    // Use ref for immediate access to current data
    if (!resizeDataRef.current) {
      return;
    }

    e.preventDefault();

    const { placeholderId, edge, startX, startY, initialLayout } = resizeDataRef.current;
    
    
    // Get the canvas container for proper coordinate calculation
    const canvasContainer = canvasContainerRef.current;
    if (!canvasContainer) {
      return;
    }
    
    const rect = canvasContainer.getBoundingClientRect();
    const scaleX = spreadDimensionsPt.width / rect.width;
    const scaleY = spreadDimensionsPt.height / rect.height;
    
    const deltaX = (e.clientX - startX) * scaleX / spreadDimensionsPt.width;
    const deltaY = (e.clientY - startY) * scaleY / spreadDimensionsPt.height;
    

    // Get current spread and current layout state (not initial layout)
    const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (!currentSpread) {
      return;
    }
    
    // Use the current layout state from ref to avoid dependency issues
    const currentLayout = customTemplatesStateRef.current[currentSpread.id] || initialLayout;
    
    // Find the placeholder being resized in current layout
    const placeholderIndex = currentLayout.findIndex(p => p.id === placeholderId);
    if (placeholderIndex === -1) {
      return;
    }

    // Use the placeholder from INITIAL layout for delta calculations, but apply to current layout
    const originalPlaceholder = initialLayout.find(p => p.id === placeholderId);
    if (!originalPlaceholder) {
      return;
    }
    
    const otherPlaceholders = currentLayout.filter(p => p.id !== placeholderId);
    const newLayout = [...currentLayout];
    

    // Enhanced snapping configuration
    const snapDistance = 0.015; // Reduced from 0.025 to 0.015 (1.5% snap distance - less aggressive)
    const minSize = 0.05; // Minimum size (5% of canvas)
    const gridSize = 0.125; // 12.5% grid (8x8 grid)
    const enableGridSnap = true; // Could be user preference later
    const enableEdgeSnap = true; // Could be user preference later
    
    // Helper function to find snap points with grid and edge options and update snap guides
    const findSnapPoints = (edge: string, proposedValue: number, enableSnapping: boolean = true) => {
      if (!enableSnapping) {
        // Clear snap guides when snapping is disabled
        setSnapGuides({ vertical: [], horizontal: [], gridVisible: false, marginVertical: [], marginHorizontal: [], gutterVertical: [], gutterHorizontal: [] });
        return proposedValue;
      }
      
      const snapPoints: number[] = [];
      const gridPoints: number[] = [];
      const canvasEdgePoints: number[] = [];
      const placeholderEdgePoints: number[] = [];
      const marginSnapPoints: number[] = [];
      const gutterSnapPoints: number[] = [];
      
      // Add grid points if enabled
      if (enableGridSnap) {
        for (let i = 0; i <= 1; i += gridSize) {
          const point = Math.round(i * 1000) / 1000;
          snapPoints.push(point);
          gridPoints.push(point);
        }
      }
      
      // Add canvas edges if enabled
      if (enableEdgeSnap) {
        const canvasEdges = [0, 0.5, 1]; // Left/Top, Center, Right/Bottom
        snapPoints.push(...canvasEdges);
        canvasEdgePoints.push(...canvasEdges);
      }
      
      // Add adjacent placeholder edges if enabled
      if (enableEdgeSnap) {
        otherPlaceholders.forEach(placeholder => {
          if (edge.includes('x') || edge.includes('left') || edge.includes('right')) {
            // Horizontal snapping
            const leftEdge = placeholder.x;
            const rightEdge = placeholder.x + placeholder.width;
            snapPoints.push(leftEdge, rightEdge);
            placeholderEdgePoints.push(leftEdge, rightEdge);
          }
          if (edge.includes('y') || edge.includes('top') || edge.includes('bottom')) {
            // Vertical snapping
            const topEdge = placeholder.y;
            const bottomEdge = placeholder.y + placeholder.height;
            snapPoints.push(topEdge, bottomEdge);
            placeholderEdgePoints.push(topEdge, bottomEdge);
          }
        });
      }
      
      // Add outside edge margin snapping for equal margins on opposite sides
      if (enableEdgeSnap) {
        const marginSnapDistance = 0.025; // Slightly larger snap distance for margin snapping
        
        if (edge.includes('x') || edge.includes('left') || edge.includes('right')) {
          // Horizontal margin snapping - check for equal left/right margins
          otherPlaceholders.forEach(placeholder => {
            // Calculate what the opposite margin would be if we snap to create equal margins
            const placeholderLeft = placeholder.x;
            const placeholderRight = placeholder.x + placeholder.width;
            
            // For left edge snapping to create equal margins
            if (edge.includes('left') || edge.includes('x')) {
              const rightMargin = 1 - placeholderRight; // margin from placeholder right to canvas right
              const equalLeftEdge = rightMargin; // position that would create equal left margin
              if (Math.abs(proposedValue - equalLeftEdge) < marginSnapDistance) {
                marginSnapPoints.push(equalLeftEdge);
              }
            }
            
            // For right edge snapping to create equal margins
            if (edge.includes('right') || edge.includes('x')) {
              const leftMargin = placeholderLeft; // margin from canvas left to placeholder left  
              const equalRightEdge = 1 - leftMargin; // position that would create equal right margin
              if (Math.abs(proposedValue - equalRightEdge) < marginSnapDistance) {
                marginSnapPoints.push(equalRightEdge);
              }
            }
          });
        }
        
        if (edge.includes('y') || edge.includes('top') || edge.includes('bottom')) {
          // Vertical margin snapping - check for equal top/bottom margins
          otherPlaceholders.forEach(placeholder => {
            // Calculate what the opposite margin would be if we snap to create equal margins
            const placeholderTop = placeholder.y;
            const placeholderBottom = placeholder.y + placeholder.height;
            
            // For top edge snapping to create equal margins
            if (edge.includes('top') || edge.includes('y')) {
              const bottomMargin = 1 - placeholderBottom; // margin from placeholder bottom to canvas bottom
              const equalTopEdge = bottomMargin; // position that would create equal top margin
              if (Math.abs(proposedValue - equalTopEdge) < marginSnapDistance) {
                marginSnapPoints.push(equalTopEdge);
              }
            }
            
            // For bottom edge snapping to create equal margins  
            if (edge.includes('bottom') || edge.includes('y')) {
              const topMargin = placeholderTop; // margin from canvas top to placeholder top
              const equalBottomEdge = 1 - topMargin; // position that would create equal bottom margin
              if (Math.abs(proposedValue - equalBottomEdge) < marginSnapDistance) {
                marginSnapPoints.push(equalBottomEdge);
              }
            }
          });
        }
        
        // Add margin snap points to main snap points with light snapping priority
        snapPoints.push(...marginSnapPoints);
      }
      
      // Add gutter (center spine) snapping for equal distances from the center
      if (enableEdgeSnap) {
        const gutterSnapDistance = 0.025; // Slightly larger snap distance for gutter snapping
        const gutterCenter = 0.5; // Center of the spread (gutter/spine position)
        
        if (edge.includes('x') || edge.includes('left') || edge.includes('right')) {
          // Horizontal gutter snapping - check for equal distances from center spine
          otherPlaceholders.forEach(placeholder => {
            // Calculate distance from placeholder edges to gutter center
            const placeholderLeftDistance = Math.abs(placeholder.x - gutterCenter);
            const placeholderRightDistance = Math.abs((placeholder.x + placeholder.width) - gutterCenter);
            
            // For left edge snapping to match distance from gutter
            if (edge.includes('left') || edge.includes('x')) {
              // Match left edge of other placeholder's distance from gutter
              const equalLeftEdge = gutterCenter - placeholderLeftDistance;
              if (equalLeftEdge >= 0 && Math.abs(proposedValue - equalLeftEdge) < gutterSnapDistance) {
                gutterSnapPoints.push(equalLeftEdge);
              }
              
              // Match right edge of other placeholder's distance from gutter  
              const equalLeftEdgeFromRight = gutterCenter - placeholderRightDistance;
              if (equalLeftEdgeFromRight >= 0 && Math.abs(proposedValue - equalLeftEdgeFromRight) < gutterSnapDistance) {
                gutterSnapPoints.push(equalLeftEdgeFromRight);
              }
            }
            
            // For right edge snapping to match distance from gutter
            if (edge.includes('right') || edge.includes('x')) {
              // Match left edge of other placeholder's distance from gutter
              const equalRightEdge = gutterCenter + placeholderLeftDistance;
              if (equalRightEdge <= 1 && Math.abs(proposedValue - equalRightEdge) < gutterSnapDistance) {
                gutterSnapPoints.push(equalRightEdge);
              }
              
              // Match right edge of other placeholder's distance from gutter
              const equalRightEdgeFromRight = gutterCenter + placeholderRightDistance;
              if (equalRightEdgeFromRight <= 1 && Math.abs(proposedValue - equalRightEdgeFromRight) < gutterSnapDistance) {
                gutterSnapPoints.push(equalRightEdgeFromRight);
              }
            }
          });
        }
        
        
        // Add gutter snap points to main snap points
        snapPoints.push(...gutterSnapPoints);
      }
      
      // Find closest snap point with improved logic
      let closestSnap = proposedValue;
      let minDistance = snapDistance;
      let snappedToPoint: number | null = null;
      let snapType: 'grid' | 'canvas' | 'placeholder' | 'margin' | 'gutter' | null = null;
      
      // Prioritize grid snaps over edge snaps when both are close
      const gridSnaps: { point: number; distance: number }[] = [];
      const edgeSnaps: { point: number; distance: number }[] = [];
      const marginSnaps: { point: number; distance: number }[] = [];
      const gutterSnaps: { point: number; distance: number }[] = [];
      
      snapPoints.forEach(snapPoint => {
        const distance = Math.abs(proposedValue - snapPoint);
        if (distance < snapDistance) {
          const isGridPoint = gridPoints.includes(snapPoint);
          const isMarginPoint = marginSnapPoints.includes(snapPoint);
          const isGutterPoint = gutterSnapPoints.includes(snapPoint);
          if (isGridPoint) {
            gridSnaps.push({ point: snapPoint, distance });
          } else if (isMarginPoint) {
            marginSnaps.push({ point: snapPoint, distance });
          } else if (isGutterPoint) {
            gutterSnaps.push({ point: snapPoint, distance });
          } else {
            edgeSnaps.push({ point: snapPoint, distance });
          }
        }
      });
      
      // Prefer grid snaps, then edge snaps, then gutter snaps, then margin snaps (light snapping)
      const allSnaps = [...gridSnaps, ...edgeSnaps, ...gutterSnaps, ...marginSnaps].sort((a, b) => a.distance - b.distance);
      
      if (allSnaps.length > 0) {
        closestSnap = allSnaps[0].point;
        snappedToPoint = closestSnap;
        
        // Determine snap type
        if (gridPoints.includes(closestSnap)) {
          snapType = 'grid';
        } else if (canvasEdgePoints.includes(closestSnap)) {
          snapType = 'canvas';
        } else if (gutterSnapPoints.includes(closestSnap)) {
          snapType = 'gutter';
        } else if (marginSnapPoints.includes(closestSnap)) {
          snapType = 'margin';
        } else {
          snapType = 'placeholder';
        }
      }
      
      // Update snap guides based on what we're snapping to
      const isHorizontalEdge = edge.includes('y') || edge.includes('top') || edge.includes('bottom');
      const isVerticalEdge = edge.includes('x') || edge.includes('left') || edge.includes('right');
      
      setSnapGuides(prev => {
        const newGuides = { ...prev };
        
        if (snappedToPoint !== null) {
          if (snapType === 'gutter') {
            // Show gutter snap guides as red vertical lines from center spine
            if (isVerticalEdge) {
              if (!newGuides.gutterVertical.includes(snappedToPoint)) {
                newGuides.gutterVertical = [...newGuides.gutterVertical, snappedToPoint];
              }
              
              // Always show the center gutter line (spine)
              const gutterCenter = 0.5;
              if (!newGuides.gutterVertical.includes(gutterCenter)) {
                newGuides.gutterVertical = [...newGuides.gutterVertical, gutterCenter];
              }
              
              // Show reference placeholder edges that we're matching distance from
              otherPlaceholders.forEach(placeholder => {
                const placeholderLeftDistance = Math.abs(placeholder.x - gutterCenter);
                const placeholderRightDistance = Math.abs((placeholder.x + placeholder.width) - gutterCenter);
                
                // Check if we're snapping to equal distance and show reference edges
                if (edge.includes('left') || edge.includes('x')) {
                  if (Math.abs(snappedToPoint - (gutterCenter - placeholderLeftDistance)) < 0.01) {
                    if (!newGuides.gutterVertical.includes(placeholder.x)) {
                      newGuides.gutterVertical = [...newGuides.gutterVertical, placeholder.x];
                    }
                  }
                  if (Math.abs(snappedToPoint - (gutterCenter - placeholderRightDistance)) < 0.01) {
                    if (!newGuides.gutterVertical.includes(placeholder.x + placeholder.width)) {
                      newGuides.gutterVertical = [...newGuides.gutterVertical, placeholder.x + placeholder.width];
                    }
                  }
                }
                if (edge.includes('right') || edge.includes('x')) {
                  if (Math.abs(snappedToPoint - (gutterCenter + placeholderLeftDistance)) < 0.01) {
                    if (!newGuides.gutterVertical.includes(placeholder.x)) {
                      newGuides.gutterVertical = [...newGuides.gutterVertical, placeholder.x];
                    }
                  }
                  if (Math.abs(snappedToPoint - (gutterCenter + placeholderRightDistance)) < 0.01) {
                    if (!newGuides.gutterVertical.includes(placeholder.x + placeholder.width)) {
                      newGuides.gutterVertical = [...newGuides.gutterVertical, placeholder.x + placeholder.width];
                    }
                  }
                }
              });
            }
          } else if (snapType === 'margin') {
            // Show margin snap guides in different arrays for different colors
            if (isHorizontalEdge) {
              if (!newGuides.marginHorizontal.includes(snappedToPoint)) {
                newGuides.marginHorizontal = [...newGuides.marginHorizontal, snappedToPoint];
              }
              
              // Show reference lines for equal margins
              otherPlaceholders.forEach(placeholder => {
                if (edge.includes('top') || edge.includes('y')) {
                  const bottomMargin = 1 - (placeholder.y + placeholder.height);
                  const equalTopEdge = bottomMargin;
                  if (Math.abs(snappedToPoint - equalTopEdge) < 0.01) {
                    // Show the reference bottom edge that we're equalizing to
                    const referenceLine = placeholder.y + placeholder.height;
                    if (!newGuides.marginHorizontal.includes(referenceLine)) {
                      newGuides.marginHorizontal = [...newGuides.marginHorizontal, referenceLine];
                    }
                  }
                }
                if (edge.includes('bottom') || edge.includes('y')) {
                  const topMargin = placeholder.y;
                  const equalBottomEdge = 1 - topMargin;
                  if (Math.abs(snappedToPoint - equalBottomEdge) < 0.01) {
                    // Show the reference top edge that we're equalizing to
                    const referenceLine = placeholder.y;
                    if (!newGuides.marginHorizontal.includes(referenceLine)) {
                      newGuides.marginHorizontal = [...newGuides.marginHorizontal, referenceLine];
                    }
                  }
                }
              });
            }
            if (isVerticalEdge) {
              if (!newGuides.marginVertical.includes(snappedToPoint)) {
                newGuides.marginVertical = [...newGuides.marginVertical, snappedToPoint];
              }
              
              // Show reference lines for equal margins
              otherPlaceholders.forEach(placeholder => {
                if (edge.includes('left') || edge.includes('x')) {
                  const rightMargin = 1 - (placeholder.x + placeholder.width);
                  const equalLeftEdge = rightMargin;
                  if (Math.abs(snappedToPoint - equalLeftEdge) < 0.01) {
                    // Show the reference right edge that we're equalizing to
                    const referenceLine = placeholder.x + placeholder.width;
                    if (!newGuides.marginVertical.includes(referenceLine)) {
                      newGuides.marginVertical = [...newGuides.marginVertical, referenceLine];
                    }
                  }
                }
                if (edge.includes('right') || edge.includes('x')) {
                  const leftMargin = placeholder.x;
                  const equalRightEdge = 1 - leftMargin;
                  if (Math.abs(snappedToPoint - equalRightEdge) < 0.01) {
                    // Show the reference left edge that we're equalizing to
                    const referenceLine = placeholder.x;
                    if (!newGuides.marginVertical.includes(referenceLine)) {
                      newGuides.marginVertical = [...newGuides.marginVertical, referenceLine];
                    }
                  }
                }
              });
            }
          } else {
            // Regular snap guides
            if (isHorizontalEdge) {
              if (!newGuides.horizontal.includes(snappedToPoint)) {
                newGuides.horizontal = [...newGuides.horizontal, snappedToPoint];
              }
            }
            if (isVerticalEdge) {
              if (!newGuides.vertical.includes(snappedToPoint)) {
                newGuides.vertical = [...newGuides.vertical, snappedToPoint];
              }
            }
          }
        }
        
        // Show grid if snapping to grid
        newGuides.gridVisible = snapType === 'grid';
        
        return newGuides;
      });
      
      return closestSnap;
    };

    // Helper function to check collision with other placeholders
    const checkCollision = (testPlaceholder: TemplateImage) => {
      return otherPlaceholders.some(other => {
        const overlap = !(
          testPlaceholder.x >= other.x + other.width ||
          testPlaceholder.x + testPlaceholder.width <= other.x ||
          testPlaceholder.y >= other.y + other.height ||
          testPlaceholder.y + testPlaceholder.height <= other.y
        );
        return overlap;
      });
    };

    // Helper function to find the closest valid position when collision occurs
    const findClosestValidPosition = (testPlaceholder: TemplateImage, edge: string, proposedValue: number) => {
      if (!checkCollision(testPlaceholder)) {
        return proposedValue; // No collision, use proposed value
      }

      // Find the closest adjacent edge position
      let closestValidPosition = proposedValue;
      let minDistance = Infinity;

      otherPlaceholders.forEach(other => {
        let adjacentPosition: number;
        let distance: number;

        switch (edge) {
          case 'top':
            // For top edge, check if we can position just below the other placeholder
            adjacentPosition = other.y + other.height;
            distance = Math.abs(proposedValue - adjacentPosition);
            if (distance < minDistance && adjacentPosition <= originalPlaceholder.y + originalPlaceholder.height - minSize) {
              // Ensure the resulting placeholder wouldn't collide
              const testAdjacent = {
                ...testPlaceholder,
                y: adjacentPosition,
                height: originalPlaceholder.height - (adjacentPosition - originalPlaceholder.y)
              };
              if (!checkCollision(testAdjacent) && testAdjacent.height >= minSize) {
                closestValidPosition = adjacentPosition;
                minDistance = distance;
              }
            }
            break;

          case 'bottom':
            // For bottom edge, check if we can position just above the other placeholder
            adjacentPosition = other.y;
            const newHeight = adjacentPosition - originalPlaceholder.y;
            distance = Math.abs(originalPlaceholder.y + proposedValue - originalPlaceholder.y - adjacentPosition);
            if (distance < minDistance && newHeight >= minSize) {
              closestValidPosition = newHeight;
              minDistance = distance;
            }
            break;

          case 'left':
            // For left edge, check if we can position just to the right of the other placeholder
            adjacentPosition = other.x + other.width;
            distance = Math.abs(proposedValue - adjacentPosition);
            if (distance < minDistance && adjacentPosition <= originalPlaceholder.x + originalPlaceholder.width - minSize) {
              const testAdjacent = {
                ...testPlaceholder,
                x: adjacentPosition,
                width: originalPlaceholder.width - (adjacentPosition - originalPlaceholder.x)
              };
              if (!checkCollision(testAdjacent) && testAdjacent.width >= minSize) {
                closestValidPosition = adjacentPosition;
                minDistance = distance;
              }
            }
            break;

          case 'right':
            // For right edge, check if we can position just to the left of the other placeholder
            adjacentPosition = other.x;
            const newWidth = adjacentPosition - originalPlaceholder.x;
            distance = Math.abs(originalPlaceholder.x + proposedValue - originalPlaceholder.x - adjacentPosition);
            if (distance < minDistance && newWidth >= minSize) {
              closestValidPosition = newWidth;
              minDistance = distance;
            }
            break;
        }
      });

      return closestValidPosition;
    };

    // Check for modifier keys to disable snapping temporarily
    const disableSnapping = e.shiftKey; // Hold Shift to disable snapping
    
    // Apply resize based on edge with enhanced snapping and collision detection
    let newPlaceholder = { ...originalPlaceholder };

    switch (edge) {
      case 'top':
        let proposedTop = originalPlaceholder.y + deltaY;
        proposedTop = findSnapPoints('y', proposedTop, !disableSnapping);
        
        // Ensure the new height would be at least minSize and top doesn't go below 0 or above bottom edge
        const maxTop = originalPlaceholder.y + originalPlaceholder.height - minSize;
        proposedTop = Math.max(0, Math.min(proposedTop, maxTop));
        
        const newHeight = originalPlaceholder.height - (proposedTop - originalPlaceholder.y);
        
        // Only proceed if the new height is valid
        if (newHeight >= minSize) {
          const testTopPlaceholder = {
            ...originalPlaceholder,
            y: proposedTop,
            height: newHeight
          };
          
          
          // Find the closest valid position if collision occurs
          const validTop = findClosestValidPosition(testTopPlaceholder, 'top', proposedTop);
          const validHeight = originalPlaceholder.height - (validTop - originalPlaceholder.y);
          
          if (validHeight >= minSize) {
            newPlaceholder.y = validTop;
            newPlaceholder.height = validHeight;
          } else {
          }
        } else {
        }
        break;
        
      case 'bottom':
        let proposedHeight = originalPlaceholder.height + deltaY;
        let proposedBottom = originalPlaceholder.y + proposedHeight;
        proposedBottom = findSnapPoints('y', proposedBottom, !disableSnapping);
        proposedHeight = proposedBottom - originalPlaceholder.y;
        proposedHeight = Math.max(minSize, Math.min(1 - originalPlaceholder.y, proposedHeight));
        
        const testBottomPlaceholder = {
          ...originalPlaceholder,
          height: proposedHeight
        };
        
        // Find the closest valid position if collision occurs
        const validHeight = findClosestValidPosition(testBottomPlaceholder, 'bottom', proposedHeight);
        
        if (validHeight >= minSize) {
          newPlaceholder.height = validHeight;
        }
        break;
        
      case 'left':
        let proposedLeft = originalPlaceholder.x + deltaX;
        proposedLeft = findSnapPoints('x', proposedLeft, !disableSnapping);
        
        // Ensure the new width would be at least minSize and left doesn't go below 0 or beyond right edge
        const maxLeft = originalPlaceholder.x + originalPlaceholder.width - minSize;
        proposedLeft = Math.max(0, Math.min(proposedLeft, maxLeft));
        
        const newWidth = originalPlaceholder.width - (proposedLeft - originalPlaceholder.x);
        
        // Only proceed if the new width is valid
        if (newWidth >= minSize) {
          const testLeftPlaceholder = {
            ...originalPlaceholder,
            x: proposedLeft,
            width: newWidth
          };
          
          // Find the closest valid position if collision occurs
          const validLeft = findClosestValidPosition(testLeftPlaceholder, 'left', proposedLeft);
          const validWidth = originalPlaceholder.width - (validLeft - originalPlaceholder.x);
          
          if (validWidth >= minSize) {
            newPlaceholder.x = validLeft;
            newPlaceholder.width = validWidth;
          }
        }
        break;
        
      case 'right':
        let proposedWidth = originalPlaceholder.width + deltaX;
        let proposedRight = originalPlaceholder.x + proposedWidth;
        proposedRight = findSnapPoints('x', proposedRight, !disableSnapping);
        proposedWidth = proposedRight - originalPlaceholder.x;
        proposedWidth = Math.max(minSize, Math.min(1 - originalPlaceholder.x, proposedWidth));
        
        const testRightPlaceholder = {
          ...originalPlaceholder,
          width: proposedWidth
        };
        
        // Find the closest valid position if collision occurs
        const validWidth = findClosestValidPosition(testRightPlaceholder, 'right', proposedWidth);
        
        if (validWidth >= minSize) {
          newPlaceholder.width = validWidth;
        }
        break;
        
      // Corner handles - enhanced with proper snapping
      case 'top-left':
        let newTopLeftX = originalPlaceholder.x + deltaX;
        let newTopLeftY = originalPlaceholder.y + deltaY;
        
        newTopLeftX = findSnapPoints('x', newTopLeftX, !disableSnapping);
        newTopLeftY = findSnapPoints('y', newTopLeftY, !disableSnapping);
        
        // Ensure minimum size constraints
        const maxTopLeftX = originalPlaceholder.x + originalPlaceholder.width - minSize;
        const maxTopLeftY = originalPlaceholder.y + originalPlaceholder.height - minSize;
        newTopLeftX = Math.max(0, Math.min(newTopLeftX, maxTopLeftX));
        newTopLeftY = Math.max(0, Math.min(newTopLeftY, maxTopLeftY));
        
        const newTopLeftWidth = originalPlaceholder.width - (newTopLeftX - originalPlaceholder.x);
        const newTopLeftHeight = originalPlaceholder.height - (newTopLeftY - originalPlaceholder.y);
        
        // Only proceed if both dimensions are valid
        if (newTopLeftWidth >= minSize && newTopLeftHeight >= minSize) {
          const testTopLeftPlaceholder = {
            ...originalPlaceholder,
            x: newTopLeftX,
            y: newTopLeftY,
            width: newTopLeftWidth,
            height: newTopLeftHeight
          };
          
          if (!checkCollision(testTopLeftPlaceholder)) {
            newPlaceholder = testTopLeftPlaceholder;
          }
        }
        break;
        
      case 'top-right':
        let newTopRightY = originalPlaceholder.y + deltaY;
        let newTopRightWidth = originalPlaceholder.width + deltaX;
        let newTopRightRight = originalPlaceholder.x + newTopRightWidth;
        
        newTopRightY = findSnapPoints('y', newTopRightY, !disableSnapping);
        newTopRightRight = findSnapPoints('x', newTopRightRight, !disableSnapping);
        newTopRightWidth = newTopRightRight - originalPlaceholder.x;
        
        newTopRightY = Math.max(0, Math.min(newTopRightY, originalPlaceholder.y + originalPlaceholder.height - minSize));
        newTopRightWidth = Math.max(minSize, Math.min(1 - originalPlaceholder.x, newTopRightWidth));
        
        const testTopRightPlaceholder = {
          ...originalPlaceholder,
          y: newTopRightY,
          width: newTopRightWidth,
          height: originalPlaceholder.height - (newTopRightY - originalPlaceholder.y)
        };
        
        if (!checkCollision(testTopRightPlaceholder)) {
          newPlaceholder = testTopRightPlaceholder;
        }
        break;
        
      case 'bottom-left':
        let newBottomLeftX = originalPlaceholder.x + deltaX;
        let newBottomLeftHeight = originalPlaceholder.height + deltaY;
        let newBottomLeftBottom = originalPlaceholder.y + newBottomLeftHeight;
        
        newBottomLeftX = findSnapPoints('x', newBottomLeftX, !disableSnapping);
        newBottomLeftBottom = findSnapPoints('y', newBottomLeftBottom, !disableSnapping);
        newBottomLeftHeight = newBottomLeftBottom - originalPlaceholder.y;
        
        newBottomLeftX = Math.max(0, Math.min(newBottomLeftX, originalPlaceholder.x + originalPlaceholder.width - minSize));
        newBottomLeftHeight = Math.max(minSize, Math.min(1 - originalPlaceholder.y, newBottomLeftHeight));
        
        const testBottomLeftPlaceholder = {
          ...originalPlaceholder,
          x: newBottomLeftX,
          width: originalPlaceholder.width - (newBottomLeftX - originalPlaceholder.x),
          height: newBottomLeftHeight
        };
        
        if (!checkCollision(testBottomLeftPlaceholder)) {
          newPlaceholder = testBottomLeftPlaceholder;
        }
        break;
        
      case 'bottom-right':
        let newBottomRightWidth = originalPlaceholder.width + deltaX;
        let newBottomRightHeight = originalPlaceholder.height + deltaY;
        let newBottomRightRight = originalPlaceholder.x + newBottomRightWidth;
        let newBottomRightBottom = originalPlaceholder.y + newBottomRightHeight;
        
        newBottomRightRight = findSnapPoints('x', newBottomRightRight, !disableSnapping);
        newBottomRightBottom = findSnapPoints('y', newBottomRightBottom, !disableSnapping);
        newBottomRightWidth = newBottomRightRight - originalPlaceholder.x;
        newBottomRightHeight = newBottomRightBottom - originalPlaceholder.y;
        
        newBottomRightWidth = Math.max(minSize, Math.min(1 - originalPlaceholder.x, newBottomRightWidth));
        newBottomRightHeight = Math.max(minSize, Math.min(1 - originalPlaceholder.y, newBottomRightHeight));
        
        const testBottomRightPlaceholder = {
          ...originalPlaceholder,
          width: newBottomRightWidth,
          height: newBottomRightHeight
        };
        
        if (!checkCollision(testBottomRightPlaceholder)) {
          newPlaceholder = testBottomRightPlaceholder;
        }
        break;
    }

    newLayout[placeholderIndex] = newPlaceholder;
    

    // Update custom templates state
    setCustomTemplatesState(prev => {
      const newState = {
        ...prev,
        [currentSpread.id]: newLayout
      };
      return newState;
    });
  }, [spreadDimensionsPt, currentSpreadId, renderedSpreads, canvasContainerRef]);

  const handleResizeEnd = useCallback(() => {
    
    if (!resizeDataRef.current) {
      return;
    }


    // Clear resize data but keep isResizing true temporarily to prevent prop sync
    setResizeData(null);
    resizeDataRef.current = null;

    // Remove global mouse event listeners
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);

    // Save to project settings via callback using ref for current state
    const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (currentSpread && onUpdateCustomTemplate) {
      const finalLayout = customTemplatesStateRef.current[currentSpread.id];
      if (finalLayout) {
        onUpdateCustomTemplate(currentSpread.id, finalLayout);
        
        // Log custom template code for development
        logCustomTemplateCode(currentSpread, finalLayout);
        
        // Note: Dirty state will be triggered by the callback in BookEditor
      }
    } else {
    }
    
    // Clear snap guides immediately
    setSnapGuides({ vertical: [], horizontal: [], gridVisible: false, marginVertical: [], marginHorizontal: [], gutterVertical: [], gutterHorizontal: [] });
    
    // Clear resizing state after save is complete to prevent prop sync conflicts
    setTimeout(() => {
      setIsResizing(false);
    }, 10);
    
    toast.success('Template layout updated');
  }, [isResizing, resizeData, handleResizeMove, renderedSpreads, currentSpreadId, onUpdateCustomTemplate]);

  // Template logging function for development
  const logCustomTemplateCode = useCallback((currentSpread: Spread, finalLayout: TemplateImage[]) => {
    // Find original template data
    const originalTemplate = allTemplates?.find(t => t.id === currentSpread.templateId);
    if (!originalTemplate) return;

    // Generate template code
    const templateCode = finalLayout.map((placeholder, index) => ({
      id: placeholder.id,
      x: Math.round(placeholder.x * 100000) / 100000, // Round to 5 decimal places
      y: Math.round(placeholder.y * 100000) / 100000,
      width: Math.round(placeholder.width * 100000) / 100000,
      height: Math.round(placeholder.height * 100000) / 100000
    }));

    console.log('=== CUSTOM TEMPLATE CODE ===');
    console.log('Original Template ID:', originalTemplate.id);
    console.log('Original Template Name:', originalTemplate.name);
    console.log('Original Template Classification:', originalTemplate.classification.join(', '));
    console.log('Modified Template Code:');
    console.log(JSON.stringify(templateCode, null, 2));
    console.log('=== END CUSTOM TEMPLATE CODE ===');
  }, [allTemplates]);

  // Template Editor Move Handlers
  const handleMoveStart = useCallback((e: React.MouseEvent, placeholderId: string) => {
    // Only start move if Option/Alt key is held
    if (!e.altKey) return;
    
    e.preventDefault();
    e.stopPropagation();

    console.log(`[MOVE START] Placeholder: ${placeholderId}, MousePos: (${e.clientX}, ${e.clientY})`);

    const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (!currentSpread) {
      console.log('[MOVE START] No current spread found');
      return;
    }

    // Get current template layout
    const currentTemplate = allTemplates?.find(t => t.id === currentSpread.templateId);
    if (!currentTemplate) {
      console.log(`[MOVE START] No template found for ID: ${currentSpread.templateId}`);
      return;
    }

    // Use custom template if it exists, otherwise use original
    const currentLayout = customTemplatesStateRef.current[currentSpread.id] || currentTemplate.images;
    
    // Determine which placeholders to move
    let placeholdersToMove: string[];
    if (selectedImages.has(placeholderId) && selectedImages.size > 1) {
      // Multi-selection: move all selected placeholders
      placeholdersToMove = Array.from(selectedImages);
      console.log(`[MOVE START] Multi-selection move: ${placeholdersToMove.length} placeholders`);
    } else {
      // Single placeholder move
      placeholdersToMove = [placeholderId];
      console.log(`[MOVE START] Single placeholder move`);
    }
    
    // Get all placeholders that will be moved
    const placeholdersToMoveData = placeholdersToMove.map(id => {
      const placeholder = currentLayout.find(p => p.id === id);
      if (!placeholder) {
        console.warn(`[MOVE START] Placeholder ${id} not found in layout`);
        return null;
      }
      return { id, original: { ...placeholder } };
    }).filter(Boolean) as Array<{ id: string; original: TemplateImage }>;
    
    if (placeholdersToMoveData.length === 0) {
      console.log('[MOVE START] No valid placeholders to move');
      return;
    }

    console.log(`[MOVE START] Template: ${currentSpread.templateId}, Moving ${placeholdersToMoveData.length} placeholders`);
    console.log(`[MOVE START] Placeholders to move:`, placeholdersToMoveData.map(p => p.id));

    const newMoveData = {
      placeholderId, // Primary placeholder (the one clicked)
      startX: e.clientX,
      startY: e.clientY,
      initialLayout: [...currentLayout],
      originalPlaceholder: placeholdersToMoveData.find(p => p.id === placeholderId)!.original,
      // New: multi-selection data
      placeholdersToMove: placeholdersToMoveData
    };

    // Set both state and ref immediately
    setIsMoving(true);
    setMoveData(newMoveData);
    moveDataRef.current = newMoveData;

    console.log(`[MOVE START] Move data set for ${placeholdersToMoveData.length} placeholders, adding event listeners`);

    // Add global mouse event listeners
    document.addEventListener('mousemove', handleMoveMove);
    document.addEventListener('mouseup', handleMoveEnd);
  }, [renderedSpreads, currentSpreadId, allTemplates, selectedImages]);

  const handleMoveMove = useCallback((e: MouseEvent) => {
    // Use ref for immediate access to current data
    if (!moveDataRef.current) {
      console.log('[MOVE MOVE] No move data in ref, ignoring move');
      return;
    }

    e.preventDefault();

    const { placeholderId, startX, startY, initialLayout, originalPlaceholder, placeholdersToMove } = moveDataRef.current;
    
    // Determine if this is a multi-selection move
    const isMultiMove = placeholdersToMove && placeholdersToMove.length > 1;
    const moveCount = isMultiMove ? placeholdersToMove.length : 1;
    
    console.log(`[MOVE MOVE] Moving ${moveCount} placeholder(s), Primary: ${placeholderId}, MousePos: (${e.clientX}, ${e.clientY})`);
    
    // Get the canvas container for proper coordinate calculation
    const canvasContainer = canvasContainerRef.current;
    if (!canvasContainer) {
      console.log('[MOVE MOVE] No canvas container found');
      return;
    }
    
    const rect = canvasContainer.getBoundingClientRect();
    const scaleX = spreadDimensionsPt.width / rect.width;
    const scaleY = spreadDimensionsPt.height / rect.height;
    
    const deltaX = (e.clientX - startX) * scaleX / spreadDimensionsPt.width;
    const deltaY = (e.clientY - startY) * scaleY / spreadDimensionsPt.height;
    
    console.log(`[MOVE MOVE] Mouse delta: (${e.clientX - startX}, ${e.clientY - startY})`);
    console.log(`[MOVE MOVE] Normalized delta: (${deltaX}, ${deltaY})`);

    // Get current spread and current layout state
    const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (!currentSpread) {
      console.log('[MOVE MOVE] No current spread found');
      return;
    }
    
    // Use the current layout state from ref
    const currentLayout = customTemplatesStateRef.current[currentSpread.id] || initialLayout;
    
    // Get IDs of placeholders being moved
    const movingPlaceholderIds = isMultiMove 
      ? placeholdersToMove.map(p => p.id)
      : [placeholderId];
    
    // Get placeholders that are NOT being moved (for collision detection)
    const otherPlaceholders = currentLayout.filter(p => !movingPlaceholderIds.includes(p.id));
    const newLayout = [...currentLayout];
    
    console.log(`[MOVE MOVE] Original placeholder:`, originalPlaceholder);
    console.log(`[MOVE MOVE] Other placeholders count: ${otherPlaceholders.length}`);

    // Enhanced snapping configuration for move
    const snapDistance = 0.025; // 2.5% snap distance
    const gridSize = 0.125; // 12.5% grid (8x8 grid)
    const enableGridSnap = true;
    const enableEdgeSnap = true;
    const disableSnapping = e.shiftKey; // Hold Shift to disable snapping
    
    // Helper function to find snap points for move with snap guide updates
    const findSnapPointsForMove = (proposedValue: number, axis: 'x' | 'y', enableSnapping: boolean = true) => {
      if (!enableSnapping) {
        // Clear snap guides when snapping is disabled
        setSnapGuides({ vertical: [], horizontal: [], gridVisible: false, marginVertical: [], marginHorizontal: [], gutterVertical: [], gutterHorizontal: [] });
        return proposedValue;
      }
      
      const snapPoints: number[] = [];
      const gridPoints: number[] = [];
      const canvasEdgePoints: number[] = [];
      const marginSnapPoints: number[] = [];
      const gutterSnapPoints: number[] = [];
      
      // Add grid points if enabled
      if (enableGridSnap) {
        for (let i = 0; i <= 1; i += gridSize) {
          const point = Math.round(i * 1000) / 1000;
          snapPoints.push(point);
          gridPoints.push(point);
        }
      }
      
      // Add canvas edges if enabled
      if (enableEdgeSnap) {
        const canvasEdges = [0, 0.5, 1];
        snapPoints.push(...canvasEdges);
        canvasEdgePoints.push(...canvasEdges);
      }
      
      // Add adjacent placeholder edges if enabled
      if (enableEdgeSnap) {
        otherPlaceholders.forEach(placeholder => {
          if (axis === 'x') {
            snapPoints.push(placeholder.x, placeholder.x + placeholder.width);
          } else {
            snapPoints.push(placeholder.y, placeholder.y + placeholder.height);
          }
        });
      }
      
      // Add outside edge margin snapping for equal margins on opposite sides (for move operations)
      if (enableEdgeSnap) {
        const marginSnapDistance = 0.025; // Slightly larger snap distance for margin snapping
        const currentPlaceholder = originalPlaceholder;
        
        if (axis === 'x') {
          // Horizontal margin snapping - check for equal left/right margins
          otherPlaceholders.forEach(placeholder => {
            // Calculate what the position would be if we center between placeholder and canvas edge
            const placeholderLeft = placeholder.x;
            const placeholderRight = placeholder.x + placeholder.width;
            
            // Position to create equal margins on opposite sides  
            const rightMargin = 1 - placeholderRight;
            const leftMargin = placeholderLeft;
            
            // Center between placeholder and canvas edges
            const centerWithLeftEdge = (placeholderLeft + 0) / 2 - currentPlaceholder.width / 2;
            const centerWithRightEdge = (placeholderRight + 1) / 2 - currentPlaceholder.width / 2;
            
            if (Math.abs(proposedValue - centerWithLeftEdge) < marginSnapDistance) {
              marginSnapPoints.push(centerWithLeftEdge);
            }
            if (Math.abs(proposedValue - centerWithRightEdge) < marginSnapDistance) {
              marginSnapPoints.push(centerWithRightEdge);
            }
          });
        } else {
          // Vertical margin snapping - check for equal top/bottom margins
          otherPlaceholders.forEach(placeholder => {
            // Calculate what the position would be if we center between placeholder and canvas edge
            const placeholderTop = placeholder.y;
            const placeholderBottom = placeholder.y + placeholder.height;
            
            // Center between placeholder and canvas edges
            const centerWithTopEdge = (placeholderTop + 0) / 2 - currentPlaceholder.height / 2;
            const centerWithBottomEdge = (placeholderBottom + 1) / 2 - currentPlaceholder.height / 2;
            
            if (Math.abs(proposedValue - centerWithTopEdge) < marginSnapDistance) {
              marginSnapPoints.push(centerWithTopEdge);
            }
            if (Math.abs(proposedValue - centerWithBottomEdge) < marginSnapDistance) {
              marginSnapPoints.push(centerWithBottomEdge);
            }
          });
        }
        
        // Add margin snap points to main snap points
        snapPoints.push(...marginSnapPoints);
      }
      
      // Add gutter (center spine) snapping for equal distances from the center during move
      if (enableEdgeSnap) {
        const gutterSnapDistance = 0.025; // Slightly larger snap distance for gutter snapping
        const gutterCenter = 0.5; // Center of the spread (gutter/spine position)
        const currentPlaceholder = originalPlaceholder;
        
        if (axis === 'x') {
          // Horizontal gutter snapping for move operations
          otherPlaceholders.forEach(placeholder => {
            // Calculate distance from placeholder edges to gutter center
            const placeholderLeftDistance = Math.abs(placeholder.x - gutterCenter);
            const placeholderRightDistance = Math.abs((placeholder.x + placeholder.width) - gutterCenter);
            
            // Position to match left edge distance from gutter
            const equalPositionFromLeft = gutterCenter - placeholderLeftDistance - currentPlaceholder.width / 2;
            if (equalPositionFromLeft >= 0 && Math.abs(proposedValue - equalPositionFromLeft) < gutterSnapDistance) {
              gutterSnapPoints.push(equalPositionFromLeft);
            }
            
            // Position to match right edge distance from gutter  
            const equalPositionFromRight = gutterCenter - placeholderRightDistance - currentPlaceholder.width / 2;
            if (equalPositionFromRight >= 0 && Math.abs(proposedValue - equalPositionFromRight) < gutterSnapDistance) {
              gutterSnapPoints.push(equalPositionFromRight);
            }
            
            // Position on right side to match left edge distance
            const equalPositionRightFromLeft = gutterCenter + placeholderLeftDistance - currentPlaceholder.width / 2;
            if (equalPositionRightFromLeft <= 1 - currentPlaceholder.width && Math.abs(proposedValue - equalPositionRightFromLeft) < gutterSnapDistance) {
              gutterSnapPoints.push(equalPositionRightFromLeft);
            }
            
            // Position on right side to match right edge distance
            const equalPositionRightFromRight = gutterCenter + placeholderRightDistance - currentPlaceholder.width / 2;
            if (equalPositionRightFromRight <= 1 - currentPlaceholder.width && Math.abs(proposedValue - equalPositionRightFromRight) < gutterSnapDistance) {
              gutterSnapPoints.push(equalPositionRightFromRight);
            }
          });
        }
        // Note: Gutter snapping only applies to X-axis (horizontal positioning from center spine)
        
        // Add gutter snap points to main snap points
        snapPoints.push(...gutterSnapPoints);
      }
      
      // Find closest snap point
      let closestSnap = proposedValue;
      let minDistance = snapDistance;
      let snappedToPoint: number | null = null;
      let snapType: 'grid' | 'canvas' | 'placeholder' | 'margin' | 'gutter' | null = null;
      
      snapPoints.forEach(snapPoint => {
        const distance = Math.abs(proposedValue - snapPoint);
        if (distance < minDistance) {
          minDistance = distance;
          closestSnap = snapPoint;
          snappedToPoint = snapPoint;
          
          // Determine snap type
          if (gridPoints.includes(snapPoint)) {
            snapType = 'grid';
          } else if (canvasEdgePoints.includes(snapPoint)) {
            snapType = 'canvas';
          } else if (gutterSnapPoints.includes(snapPoint)) {
            snapType = 'gutter';
          } else if (marginSnapPoints.includes(snapPoint)) {
            snapType = 'margin';
          } else {
            snapType = 'placeholder';
          }
        }
      });
      
      // Update snap guides for move operations
      if (snappedToPoint !== null) {
        setSnapGuides(prev => {
          const newGuides = { ...prev };
          
          if (snapType === 'gutter') {
            // Show gutter snap guides as red vertical lines from center spine only
            if (axis === 'x') {
              // Always show the center gutter line (spine)
              const gutterCenter = 0.5;
              if (!newGuides.gutterVertical.includes(gutterCenter)) {
                newGuides.gutterVertical = [...newGuides.gutterVertical, gutterCenter];
              }
              
              // Show reference lines for equal gutter distances during move
              const currentPlaceholder = originalPlaceholder;
              otherPlaceholders.forEach(placeholder => {
                const placeholderLeftDistance = Math.abs(placeholder.x - gutterCenter);
                const placeholderRightDistance = Math.abs((placeholder.x + placeholder.width) - gutterCenter);
                
                // Check which position we're snapping to and show reference placeholder edges
                const equalPositionFromLeft = gutterCenter - placeholderLeftDistance - currentPlaceholder.width / 2;
                const equalPositionFromRight = gutterCenter - placeholderRightDistance - currentPlaceholder.width / 2;
                const equalPositionRightFromLeft = gutterCenter + placeholderLeftDistance - currentPlaceholder.width / 2;
                const equalPositionRightFromRight = gutterCenter + placeholderRightDistance - currentPlaceholder.width / 2;
                
                if (Math.abs(snappedToPoint - equalPositionFromLeft) < 0.01) {
                  if (!newGuides.gutterVertical.includes(placeholder.x)) {
                    newGuides.gutterVertical = [...newGuides.gutterVertical, placeholder.x];
                  }
                }
                if (Math.abs(snappedToPoint - equalPositionFromRight) < 0.01) {
                  if (!newGuides.gutterVertical.includes(placeholder.x + placeholder.width)) {
                    newGuides.gutterVertical = [...newGuides.gutterVertical, placeholder.x + placeholder.width];
                  }
                }
                if (Math.abs(snappedToPoint - equalPositionRightFromLeft) < 0.01) {
                  if (!newGuides.gutterVertical.includes(placeholder.x)) {
                    newGuides.gutterVertical = [...newGuides.gutterVertical, placeholder.x];
                  }
                }
                if (Math.abs(snappedToPoint - equalPositionRightFromRight) < 0.01) {
                  if (!newGuides.gutterVertical.includes(placeholder.x + placeholder.width)) {
                    newGuides.gutterVertical = [...newGuides.gutterVertical, placeholder.x + placeholder.width];
                  }
                }
              });
            }
            // Note: No gutter snapping for Y-axis moves since gutter is vertical
          } else if (snapType === 'margin') {
            // Show margin snap guides in different arrays for different colors
            if (axis === 'x') {
              // Show vertical margin guide line
              if (!newGuides.marginVertical.includes(snappedToPoint)) {
                newGuides.marginVertical = [...newGuides.marginVertical, snappedToPoint];
              }
              
              // Show reference lines for equal margins during move
              const currentPlaceholder = originalPlaceholder;
              otherPlaceholders.forEach(placeholder => {
                const placeholderLeft = placeholder.x;
                const placeholderRight = placeholder.x + placeholder.width;
                
                // Check if we're snapping to center between placeholder and left edge
                const centerWithLeftEdge = (placeholderLeft + 0) / 2 - currentPlaceholder.width / 2;
                if (Math.abs(snappedToPoint - centerWithLeftEdge) < 0.01) {
                  // Show reference line at the right edge of canvas (distance being equalized)
                  if (!newGuides.marginVertical.includes(1)) {
                    newGuides.marginVertical = [...newGuides.marginVertical, 1];
                  }
                  // Also show the placeholder edge we're referencing
                  if (!newGuides.marginVertical.includes(placeholderLeft)) {
                    newGuides.marginVertical = [...newGuides.marginVertical, placeholderLeft];
                  }
                }
                
                // Check if we're snapping to center between placeholder and right edge
                const centerWithRightEdge = (placeholderRight + 1) / 2 - currentPlaceholder.width / 2;
                if (Math.abs(snappedToPoint - centerWithRightEdge) < 0.01) {
                  // Show reference line at the left edge of canvas (distance being equalized)
                  if (!newGuides.marginVertical.includes(0)) {
                    newGuides.marginVertical = [...newGuides.marginVertical, 0];
                  }
                  // Also show the placeholder edge we're referencing
                  if (!newGuides.marginVertical.includes(placeholderRight)) {
                    newGuides.marginVertical = [...newGuides.marginVertical, placeholderRight];
                  }
                }
              });
            } else {
              // Show horizontal margin guide line
              if (!newGuides.marginHorizontal.includes(snappedToPoint)) {
                newGuides.marginHorizontal = [...newGuides.marginHorizontal, snappedToPoint];
              }
              
              // Show reference lines for equal margins during move
              const currentPlaceholder = originalPlaceholder;
              otherPlaceholders.forEach(placeholder => {
                const placeholderTop = placeholder.y;
                const placeholderBottom = placeholder.y + placeholder.height;
                
                // Check if we're snapping to center between placeholder and top edge
                const centerWithTopEdge = (placeholderTop + 0) / 2 - currentPlaceholder.height / 2;
                if (Math.abs(snappedToPoint - centerWithTopEdge) < 0.01) {
                  // Show reference line at the bottom edge of canvas (distance being equalized)
                  if (!newGuides.marginHorizontal.includes(1)) {
                    newGuides.marginHorizontal = [...newGuides.marginHorizontal, 1];
                  }
                  // Also show the placeholder edge we're referencing
                  if (!newGuides.marginHorizontal.includes(placeholderTop)) {
                    newGuides.marginHorizontal = [...newGuides.marginHorizontal, placeholderTop];
                  }
                }
                
                // Check if we're snapping to center between placeholder and bottom edge
                const centerWithBottomEdge = (placeholderBottom + 1) / 2 - currentPlaceholder.height / 2;
                if (Math.abs(snappedToPoint - centerWithBottomEdge) < 0.01) {
                  // Show reference line at the top edge of canvas (distance being equalized)
                  if (!newGuides.marginHorizontal.includes(0)) {
                    newGuides.marginHorizontal = [...newGuides.marginHorizontal, 0];
                  }
                  // Also show the placeholder edge we're referencing
                  if (!newGuides.marginHorizontal.includes(placeholderBottom)) {
                    newGuides.marginHorizontal = [...newGuides.marginHorizontal, placeholderBottom];
                  }
                }
              });
            }
          } else {
            // Regular snap guides
            if (axis === 'x') {
              // Show vertical guide line
              if (!newGuides.vertical.includes(snappedToPoint)) {
                newGuides.vertical = [...newGuides.vertical, snappedToPoint];
              }
            } else {
              // Show horizontal guide line
              if (!newGuides.horizontal.includes(snappedToPoint)) {
                newGuides.horizontal = [...newGuides.horizontal, snappedToPoint];
              }
            }
          }
          
          // Show grid if snapping to grid
          newGuides.gridVisible = snapType === 'grid';
          
          return newGuides;
        });
      }
      
      return closestSnap;
    };

    // Helper function to check collision with other placeholders
    const checkCollisionForMove = (testPlaceholder: TemplateImage) => {
      return otherPlaceholders.some(other => {
        const overlap = !(
          testPlaceholder.x >= other.x + other.width ||
          testPlaceholder.x + testPlaceholder.width <= other.x ||
          testPlaceholder.y >= other.y + other.height ||
          testPlaceholder.y + testPlaceholder.height <= other.y
        );
        return overlap;
      });
    };

    // Calculate new positions for all placeholders being moved
    const movingPlaceholders = isMultiMove ? placeholdersToMove : [{ id: placeholderId, original: originalPlaceholder }];
    const updatedPlaceholders: Array<{ id: string; newPlaceholder: TemplateImage }> = [];
    let hasCollision = false;
    
    // Calculate snapped position for the primary placeholder (the one being dragged)
    let primaryProposedX = originalPlaceholder.x + deltaX;
    let primaryProposedY = originalPlaceholder.y + deltaY;
    
    // Apply snapping to primary placeholder
    primaryProposedX = findSnapPointsForMove(primaryProposedX, 'x', !disableSnapping);
    primaryProposedY = findSnapPointsForMove(primaryProposedY, 'y', !disableSnapping);
    
    // Calculate the actual delta based on snapped position
    const snappedDeltaX = primaryProposedX - originalPlaceholder.x;
    const snappedDeltaY = primaryProposedY - originalPlaceholder.y;
    
    console.log(`[MOVE MOVE] Snapped delta: (${snappedDeltaX}, ${snappedDeltaY})`);
    
    // Apply the same delta to all moving placeholders
    for (const { id, original } of movingPlaceholders) {
      let proposedX = original.x + snappedDeltaX;
      let proposedY = original.y + snappedDeltaY;
      
      // Ensure each placeholder stays within canvas bounds
      proposedX = Math.max(0, Math.min(proposedX, 1 - original.width));
      proposedY = Math.max(0, Math.min(proposedY, 1 - original.height));
      
      const testPlaceholder = {
        ...original,
        x: proposedX,
        y: proposedY
      };
      
      // Check for collision with non-moving placeholders
      if (checkCollisionForMove(testPlaceholder)) {
        hasCollision = true;
        console.log(`[MOVE MOVE] Collision detected for placeholder ${id}`);
        break;
      }
      
      updatedPlaceholders.push({ id, newPlaceholder: testPlaceholder });
    }
    
    // Only apply moves if no collisions detected
    if (!hasCollision && updatedPlaceholders.length > 0) {
      // Update all moved placeholders in the layout
      for (const { id, newPlaceholder } of updatedPlaceholders) {
        const index = newLayout.findIndex(p => p.id === id);
        if (index !== -1) {
          newLayout[index] = newPlaceholder;
        }
      }
      
      console.log(`[MOVE MOVE] Applied move to ${updatedPlaceholders.length} placeholder(s)`);
      
      // Update custom templates state
      setCustomTemplatesState(prev => {
        const newState = {
          ...prev,
          [currentSpread.id]: newLayout
        };
        return newState;
      });
    } else {
      console.log(`[MOVE MOVE] Move blocked by collision or no valid placeholders`);
    }
  }, [spreadDimensionsPt, currentSpreadId, renderedSpreads, canvasContainerRef]);

  const handleMoveEnd = useCallback(() => {
    console.log(`[MOVE END] Starting cleanup, isMoving: ${isMoving}, hasMoveData: ${!!moveData}, hasRefData: ${!!moveDataRef.current}`);
    
    if (!moveDataRef.current) {
      console.log('[MOVE END] Early return - no move data in ref');
      return;
    }

    // Clear snap guides immediately
    setSnapGuides({ vertical: [], horizontal: [], gridVisible: false, marginVertical: [], marginHorizontal: [], gutterVertical: [], gutterHorizontal: [] });
    
    // Save to project first, then clear move state after a delay to prevent prop sync override
    // Clear move state
    setMoveData(null);
    moveDataRef.current = null;

    // Remove global event listeners
    document.removeEventListener('mousemove', handleMoveMove);
    document.removeEventListener('mouseup', handleMoveEnd);

    console.log(`[MOVE END] Event listeners removed`);

    // Save to project settings via callback using ref for current state
    const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (currentSpread && onUpdateCustomTemplate) {
      const finalLayout = customTemplatesStateRef.current[currentSpread.id];
      console.log(`[MOVE END] Saving layout for spread ${currentSpread.id}:`, finalLayout);
      if (finalLayout) {
        onUpdateCustomTemplate(currentSpread.id, finalLayout);
        
        // Log custom template code for development
        logCustomTemplateCode(currentSpread, finalLayout);
        
        console.log(`[MOVE END] onUpdateCustomTemplate called`);
      }
    } else {
      console.log(`[MOVE END] Not saving - currentSpread: ${!!currentSpread}, onUpdateCustomTemplate: ${!!onUpdateCustomTemplate}`);
    }
    
    // Clear moving state after save is complete to prevent prop sync conflicts
    setTimeout(() => {
      setIsMoving(false);
    }, 10);
    
    toast.success('Placeholder moved');
  }, [isMoving, moveData, handleMoveMove, renderedSpreads, currentSpreadId, onUpdateCustomTemplate]);

  // Sync custom templates from props (but not during active operations)
  useEffect(() => {
    if (customTemplates && !isMoving && !isResizing) {
      setCustomTemplatesState(customTemplates);
    } else if (customTemplates && (isMoving || isResizing)) {
    }
  }, [customTemplates, isMoving, isResizing]);

  // Log state changes
  useEffect(() => {
  }, [isResizing]);

  useEffect(() => {
  }, [resizeData]);

  useEffect(() => {
  }, [customTemplatesState]);

  // Cleanup resize listeners on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [handleResizeMove, handleResizeEnd]);
  
  // Drag start handler for images already on the canvas
  const handleImageDragStart = (
    e: React.DragEvent<HTMLDivElement>,
    sourcePlaceholderId: string,
    sourceImageId: string
  ) => {
    if (isTextEditorActive) {
        e.preventDefault(); // Prevent drag if text editor is active
        e.stopPropagation();
        return;
    }
    // console.log(`Drag start: placeholderId=${sourcePlaceholderId}, imageId=${sourceImageId}`); // Commented out
    
    // --- Cleanup of any previous preview ---
    if (currentDragPreview) {
      if (document.body.contains(currentDragPreview)) {
        try {
          document.body.removeChild(currentDragPreview);
        } catch (removeError) {
        }
      }
      currentDragPreview = null;
    }
    
    // Track which placeholder is being dragged
    currentDraggedPlaceholderId = sourcePlaceholderId;

    // Clear hover state to dismiss overlay during drag
    setHoveredPlaceholderId(null);

    // Set up drag data
    e.stopPropagation();
    e.dataTransfer.effectAllowed = 'move'; // Default for internal swaps

    // Look up the image directly from our data store
    const imageFile = images.find(img => img.id === sourceImageId);
    
    // Check if we're dragging multiple selected images
    const isMultiDrag = selectedImages.has(sourcePlaceholderId) && selectedImages.size > 1;
    
    if (isMultiDrag) {
      // Prepare data for multiple selected images
      const selectedImageData: any[] = [];
      const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
      
      selectedImages.forEach(placeholderId => {
        const placement = currentSpread?.images.find(p => p.placeholderId === placeholderId);
        if (placement) {
          const selectedImageFile = images.find(img => img.id === placement.imageId);
          if (selectedImageFile) {
            selectedImageData.push({
              // ImageFile fields
              id: selectedImageFile.id,
              name: selectedImageFile.name,
              originalPath: selectedImageFile.originalPath,
              thumbnailUrl: selectedImageFile.thumbnailUrl,
              previewUrl: selectedImageFile.previewUrl,
              naturalWidth: selectedImageFile.naturalWidth,
              naturalHeight: selectedImageFile.naturalHeight,
              dateModified: selectedImageFile.dateModified,
              // Source information
              sourceSpreadId: currentSpreadId,
              sourcePlaceholderId: placeholderId,
            });
          }
        }
      });
      
      // Set multi-image data for SpreadsTray
      e.dataTransfer.setData('application/x-bookproofs-canvas-images', JSON.stringify(selectedImageData));
      e.dataTransfer.effectAllowed = 'copyMove';
    } else {
      // Single image drag (existing logic)
      if (imageFile) {
        // Data for swapping within SpreadCanvas
        const swapData = JSON.stringify({ sourcePlaceholderId, sourceImageId });
        e.dataTransfer.setData('application/x-bookproofs-swap', swapData);

        // Data for dragging to SpreadsTray or other components
        const canvasImageData = { // No need to explicitly type ImageFile here, just ensure all fields are present
          // ImageFile fields (ensure these match the ImageFile type in ImageTray.tsx)
          id: imageFile.id,
          name: imageFile.name,
          originalPath: imageFile.originalPath,
          // path: imageFile.path, // Property 'path' does not exist on type 'ImageFile'.
          thumbnailUrl: imageFile.thumbnailUrl,
          previewUrl: imageFile.previewUrl,
          naturalWidth: imageFile.naturalWidth,
          naturalHeight: imageFile.naturalHeight,
          dateModified: imageFile.dateModified,
          // size: imageFile.size, // Property 'size' does not exist on type 'ImageFile'.
          // Source information
          sourceSpreadId: currentSpreadId, // currentSpreadId from SpreadCanvas context
          sourcePlaceholderId: sourcePlaceholderId,
        };
        e.dataTransfer.setData('application/x-bookproofs-canvas-image', JSON.stringify(canvasImageData));
        // Also set text/plain for compatibility with SettingsPanel and other external drops
        const imageSrcForDrop = imageFile.previewUrl || imageFile.thumbnailUrl || imageFile.originalPath;
        e.dataTransfer.setData('text/plain', imageSrcForDrop);
        e.dataTransfer.effectAllowed = 'copyMove'; // Allow both copy (to tray) and move (internal)
      } else {
        // Fallback if imageFile is not found, though this shouldn't happen
        const swapData = JSON.stringify({ sourcePlaceholderId, sourceImageId });
        e.dataTransfer.setData('application/x-bookproofs-swap', swapData);
      }
    }

    // Create a custom drag preview that shows the number of images being dragged
    if (isMultiDrag && selectedImages.size > 1) {
      // Multi-image drag preview (matching ImageTray style)
      try {
        const dragPreview = document.createElement('div');
        dragPreview.style.position = 'absolute';
        dragPreview.style.top = '-9999px'; // Position off-screen initially
        dragPreview.style.left = '-9999px';
        dragPreview.style.display = 'flex';
        dragPreview.style.alignItems = 'center';
        dragPreview.style.padding = '6px';
        dragPreview.style.background = 'white';
        dragPreview.style.border = '1px solid #e2e8f0';
        dragPreview.style.borderRadius = '8px';
        dragPreview.style.boxShadow = '0 3px 15px rgba(0, 0, 0, 0.1)';
        
        // Create thumbnail (60px x 60px to match ImageTray)
        const thumbnail = document.createElement('img');
        thumbnail.style.width = '60px';
        thumbnail.style.height = '60px';
        thumbnail.style.objectFit = 'contain';
        thumbnail.style.marginRight = '12px';
        thumbnail.src = imageFile.thumbnailUrl;
        
        // Create counter badge (matching ImageTray style)
        const counter = document.createElement('div');
        counter.textContent = `${selectedImages.size} images`;
        counter.style.background = '#0ea5e9'; // Sky-500 to match ImageTray
        counter.style.color = 'white';
        counter.style.borderRadius = '999px';
        counter.style.padding = '3px 12px';
        counter.style.fontSize = '18px';
        counter.style.fontWeight = 'bold';
        
        // Assemble the preview
        dragPreview.appendChild(thumbnail);
        dragPreview.appendChild(counter);
        document.body.appendChild(dragPreview);
        currentDragPreview = dragPreview as unknown as HTMLImageElement;
        
        // Set custom drag image
        e.dataTransfer.setDragImage(dragPreview, 30, 30);
        
        // Clean up after drag
        setTimeout(() => {
          if (document.body.contains(dragPreview)) {
            document.body.removeChild(dragPreview);
          }
        }, 100);
      } catch (err) {
        console.warn('Error creating custom multi-image drag preview:', err);
      }
    } else {
      // Single image drag preview
      if (!imageFile || !imageFile.thumbnailUrl) {
        return; // Let the browser use its default preview
      }
      
      try {
        // Create custom drag preview for single image (matching ImageTray style)
        const dragPreview = document.createElement('div');
        dragPreview.style.position = 'absolute';
        dragPreview.style.top = '-9999px';
        dragPreview.style.left = '-9999px';
        dragPreview.style.display = 'flex';
        dragPreview.style.alignItems = 'center';
        dragPreview.style.padding = '0';
        dragPreview.style.background = 'transparent';
        dragPreview.style.border = 'none';
        dragPreview.style.borderRadius = '0';
        dragPreview.style.boxShadow = 'none';
        dragPreview.style.overflow = 'hidden';
        dragPreview.style.opacity = '1';
        dragPreview.style.pointerEvents = 'none';
        
        // Create thumbnail (120px x 120px to match ImageTray single image)
        const thumbnail = document.createElement('img');
        thumbnail.style.width = '120px';
        thumbnail.style.height = '120px';
        thumbnail.style.objectFit = 'contain';
        thumbnail.src = imageFile.thumbnailUrl;
        
        dragPreview.appendChild(thumbnail);
        document.body.appendChild(dragPreview);
        currentDragPreview = dragPreview as unknown as HTMLImageElement;
        
        // Set custom drag image
        e.dataTransfer.setDragImage(dragPreview, 60, 60);
        
        // Clean up after drag
        setTimeout(() => {
          if (document.body.contains(dragPreview)) {
            document.body.removeChild(dragPreview);
          }
        }, 100);
      } catch (err) {
        console.warn('Error creating custom single-image drag preview:', err);
      }
    }
  };

  const handleImageDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    // console.log(`Drag ended for placeholder: ${currentDraggedPlaceholderId}`); // Commented out
    
    // Robust cleanup on drag end
    if (currentDragPreview) {
      if (document.body.contains(currentDragPreview)) {
        try {
          document.body.removeChild(currentDragPreview);
          // console.log("Successfully removed drag preview element"); // Commented out
        } catch (removeError) {
        }
      // } else {
        // console.log("Drag preview element was not in document.body"); // Commented out
      }
      currentDragPreview = null;
    // } else {
      // console.log("No currentDragPreview to clean up"); // Commented out
    }
    
    // Reset tracking
    currentDraggedPlaceholderId = null;
  };

  // Drop handler for placeholders
  const handleDrop = async (e: React.DragEvent<HTMLDivElement>, placeholderId: string) => {
    e.preventDefault(); e.stopPropagation(); setDragOverId(null); setIsDragFromCanvas(false);
    
    // Debug logging for custom templates
    const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    if (currentSpread && customTemplatesState[currentSpreadId]) {
      console.log(`[DROP DEBUG] Custom template active for spread ${currentSpreadId}`);
      console.log(`  - Drop target placeholderId: ${placeholderId}`);
      console.log(`  - Available spread.images placeholderIds: ${currentSpread.images.map(img => img.placeholderId).join(', ')}`);
      console.log(`  - Custom template placeholder IDs: ${customTemplatesState[currentSpreadId].map(p => p.id).join(', ')}`);
    }
    
    const templateId = e.dataTransfer.getData('application/x-bookproofs-template');
    if (templateId && onSelectTemplate) { onSelectTemplate(templateId); toast.success(`Template applied via drag & drop.`); return; }

    // Get the layout for the currently active spread for logic within this handler
    const currentLayout = allSpreadLayouts[currentSpreadId] || [];

    // Determine initial fit based on default setting and the Shift key
    const modifierPressed = e.shiftKey; // Check for Shift key press
    const initialFit: 'cover' | 'contain' = defaultDropModeIsCover
      ? (modifierPressed ? 'contain' : 'cover') // Default is cover, Shift reverses
      : (modifierPressed ? 'cover' : 'contain'); // Default is contain, Shift reverses

    try {
      const swapData = e.dataTransfer.getData('application/x-bookproofs-swap');
      if (swapData) {
        try {
          const { sourcePlaceholderId } = JSON.parse(swapData);
          if (sourcePlaceholderId === placeholderId) return;
          const activeSpread = renderedSpreads[currentRenderedIndex];
          const sourcePlacement = activeSpread?.images.find(p => p.placeholderId === sourcePlaceholderId);
          const targetPlacement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
          
          
          // Handle swap - pass null/undefined properly for empty placeholders
          const sourceImagePlacement = sourcePlacement?.imageId ? sourcePlacement : null;
          const targetImagePlacement = targetPlacement?.imageId ? targetPlacement : null;
          
          
          onUpdateSpreadImages(currentSpreadId, placeholderId, sourceImagePlacement);
          onUpdateSpreadImages(currentSpreadId, sourcePlaceholderId, targetImagePlacement);
          
          toast.success("Images swapped");
        } catch (error) { 
          toast.error("Could not swap images."); 
        }
      } else {
        const imageData = e.dataTransfer.getData('application/json');
        if (imageData) {
          const parsedData = JSON.parse(imageData);
          if (Array.isArray(parsedData)) { // Multiple images dropped
            const droppedImages = parsedData as ImageFile[];
            if (droppedImages.length === 0) return;

            const activeSpread = renderedSpreads[currentRenderedIndex];
            const currentFilledCount = activeSpread ? activeSpread.images.filter(img => img.imageId).length : 0;
            const totalRequiredImages = currentFilledCount + droppedImages.length;
            const currentTemplateCapacity = currentLayout.length;

            // --- NEW LOGIC ---
            if (activeSpread?.templateId === '__blank__') {
              // Drop multiple on blank spread
              if (totalRequiredImages > maxTemplateSize) {
                // Exceeds largest template -> Offer autobuild
                if (onOfferAutobuildRequest) {
                  onOfferAutobuildRequest({ droppedImages, currentSpreadId, existingImageCount: 0, maxTemplateSize });
                } else {
                  toast.error(`Cannot fit ${totalRequiredImages} images. Largest template holds ${maxTemplateSize}.`);
                }
              } else {
                // Find suitable template and place
                if (onSelectTemplate && allTemplates) {
                  const matchingTemplate = findTemplateForImageCount(totalRequiredImages); // Use totalRequiredImages
                  if (matchingTemplate) {
                    onSelectTemplate(matchingTemplate.id);
                    setTimeout(async () => {
                      let placedCount = 0;
                      for (const [index, image] of droppedImages.entries()) {
                        if (index < matchingTemplate.images.length) {
                          await processDroppedImage(image, matchingTemplate.images[index].id, initialFit);
                          placedCount++;
                        } else break;
                      }
                      toast.success(`Applied template and placed ${placedCount} images`);
                    }, 50);
                  } else {
                    toast.error(`No template available for ${totalRequiredImages} images.`);
                  }
                } else {
                  toast.error("Cannot automatically select template.");
                }
              }
            } else {
              // Drop multiple on existing template placeholder
              if (totalRequiredImages <= currentTemplateCapacity) {
                // Fits in current template -> Place in empty slots
                const emptyPlaceholders = currentLayout.filter(adjP => !activeSpread?.images.some(img => img.placeholderId === adjP.id && img.imageId));
                let emptyStartIndex = emptyPlaceholders.findIndex(p => p.id === placeholderId);
                if (emptyStartIndex === -1) emptyStartIndex = 0; // Start from first empty if target wasn't empty
                let imagesPlacedCount = 0;
                for (const image of droppedImages) {
                  // Wrap around to use all available empty slots
                  const targetEmptyIndex = (emptyStartIndex + imagesPlacedCount) % emptyPlaceholders.length;
                  
                  // Stop if we've placed as many images as there are empty slots
                  if (imagesPlacedCount >= emptyPlaceholders.length) {
                    toast.info(`Placed ${imagesPlacedCount} images. No more empty slots.`);
                    break;
                  }
                  
                  const targetPlaceholder = emptyPlaceholders[targetEmptyIndex];
                  if (!targetPlaceholder) break;
                  await processDroppedImage(image, targetPlaceholder.id, initialFit);
                  imagesPlacedCount++;
                }
                if (imagesPlacedCount > 0 && imagesPlacedCount === droppedImages.length) {
                  toast.success(`Placed ${imagesPlacedCount} images.`);
                }
              } else if (totalRequiredImages <= maxTemplateSize) {
                // Needs larger template (but fits within max) -> Switch template and place
                if (onSelectTemplate && allTemplates) {
                  const newTemplate = findTemplateForImageCount(totalRequiredImages);
                  if (newTemplate) {
                    toast.info(`Switching to template '${newTemplate.name}' to fit ${totalRequiredImages} images.`);
                    onSelectTemplate(newTemplate.id);
                    setTimeout(async () => {
                      const activeSpreadNow = renderedSpreads[currentRenderedIndex]; // Re-fetch spread data after template change
                      const existingPlacements = activeSpreadNow ? activeSpreadNow.images.filter(p => p.imageId) : [];
                      const existingImagesData = existingPlacements.map(placement => {
                        const file = images.find(f => f.id === placement.imageId);
                        return file ? { file, originalPlacement: placement } : null;
                      }).filter((item): item is { file: ImageFile; originalPlacement: ImagePlacement } => item !== null);
                      const itemsToPlace = [...existingImagesData, ...droppedImages.map(file => ({ file, originalPlacement: null }))];
                      let placedCount = 0;
                      for (let i = 0; i < newTemplate.images.length; i++) {
                        if (i >= itemsToPlace.length) break;
                        const targetPlaceholder = newTemplate.images[i];
                        const { file: imageFile, originalPlacement } = itemsToPlace[i];
                        if (originalPlacement === null) { // Newly dropped image
                          await processDroppedImage(imageFile, targetPlaceholder.id, initialFit);
                        } else { // Existing image being re-placed
                          const transformToApply = originalPlacement.transform ?? { scale: 1, focalX: 0.5, focalY: 0.5, fit: initialFit };
                          const newPlacement: ImagePlacement = {
                            placeholderId: targetPlaceholder.id, imageId: imageFile.id,
                            transform: {
                              scale: transformToApply.scale ?? 1,
                              focalX: transformToApply.focalX ?? 0.5,
                              focalY: transformToApply.focalY ?? 0.5,
                              fit: transformToApply.fit ?? initialFit
                            }
                          };
                          onUpdateSpreadImages(currentSpreadId, targetPlaceholder.id, newPlacement);
                        }
                        placedCount++;
                      }
                      toast.success(`Applied template '${newTemplate.name}' and placed ${placedCount} images`);
                    }, 100); // Delay to allow template change state update
                  } else {
                    toast.error(`No template available for ${totalRequiredImages} images.`);
                  }
                } else {
                  toast.error("Cannot automatically select template.");
                }
              } else {
                // Exceeds max template size -> Offer autobuild
                if (onOfferAutobuildRequest) {
                  onOfferAutobuildRequest({ droppedImages, currentSpreadId, existingImageCount: currentFilledCount, maxTemplateSize });
                } else {
                  toast.error(`Cannot fit ${totalRequiredImages} images. Largest template holds ${maxTemplateSize}.`);
                }
              }
            }
            // --- END NEW LOGIC ---

          } else { // Single image dropped on placeholder
            const image = parsedData as ImageFile;

            // Determine the effective action using simplified approach:
            // 1. Visual drop choice (if available)
            // 2. Default to 'replace' (most common action)
            let effectiveAction: 'replace' | 'add';
            
            if (visualDropChoiceRef.current) {
              // Use visual drop choice if available
              effectiveAction = visualDropChoiceRef.current;
              visualDropChoiceRef.current = null; // Clear after use
            } else {
              // Default to replace (most common action)
              effectiveAction = 'replace';
            }

            // --- Execute Action ---
            if (effectiveAction === 'replace') {
              // --- Replace Image Logic ---
              await processDroppedImage(image, placeholderId, initialFit); // Already passing initialFit from previous fix
              // Optional: Add toast message specific to replace action if needed
              // toast.success("Image replaced.");
            } else {
              // --- Add Image Logic ---
              const activeSpread = renderedSpreads[currentRenderedIndex];
              if (!activeSpread || currentLayout.length === 0) {
                toast.error("Cannot add image: No valid template selected.");
                return;
              }

              // Check if the specific placeholder the user dropped onto (placeholderId) is empty
              const isTargetPlaceholderEmpty = !activeSpread.images.some(
                (imgPlacement) => imgPlacement.placeholderId === placeholderId && imgPlacement.imageId
              );

              if (isTargetPlaceholderEmpty) {
                // If the specific placeholder dropped on is empty, fill it first.
                await processDroppedImage(image, placeholderId, initialFit);
              } else {
                // The target placeholder is already occupied.
                // Fall back to the original "add" logic:
                // Try to find any other empty slot in the current template, or upgrade template.
                const currentFilledCount = activeSpread.images.filter(img => img.imageId).length;
                const requiredCapacity = currentFilledCount + 1; // One more image to add
                const currentTemplateCapacity = currentLayout.length;

                if (requiredCapacity <= currentTemplateCapacity) {
                  // Enough space in the current template.
                  // Find the first empty slot (it won't be placeholderId since that one is occupied).
                  const emptySlots = currentLayout.filter(
                    adjP => !activeSpread.images.some(imgP => imgP.placeholderId === adjP.id && imgP.imageId)
                  );

                  if (emptySlots.length > 0) {
                    await processDroppedImage(image, emptySlots[0].id, initialFit);
                  } else {
                    // This means the template is full.
                    toast.info("No empty slots available in the current template.");
                  }
                } else {
                  // Need a larger template
                  if (onSelectTemplate && allTemplates) {
                    const newTemplate = findTemplateForImageCount(requiredCapacity);
                    if (newTemplate) {
                      toast.info(`Switching to template '${newTemplate.name}' to fit ${requiredCapacity} images.`);
                      onSelectTemplate(newTemplate.id); // Apply the new template

                      setTimeout(async () => {
                        const newlyAppliedTemplate = allTemplates.find(t => t.id === newTemplate.id);
                        if (newlyAppliedTemplate) {
                          const oldPlaceholderIds = new Set(currentLayout.map(p => p.id));
                          const newEmptyPlaceholders = newlyAppliedTemplate.images.filter(p => !oldPlaceholderIds.has(p.id));
                          if (newEmptyPlaceholders.length > 0) {
                            await processDroppedImage(image, newEmptyPlaceholders[0].id, initialFit);
                          } else {
                            // If after template switch, still no clear new slot, try first available on new template
                            const firstAvailableOnNewTemplate = newlyAppliedTemplate.images.find(p =>
                              !activeSpread.images.some(img => img.placeholderId === p.id && img.imageId) // Check against current spread's images
                            );
                            if (firstAvailableOnNewTemplate) {
                               await processDroppedImage(image, firstAvailableOnNewTemplate.id, initialFit);
                            } else {
                               toast.info(`Applied template '${newTemplate.name}'. Please place the image in an empty slot.`);
                            }
                          }
                        } else {
                          toast.error("Failed to find newly applied template definition after upgrade.");
                        }
                      }, 150);
                    } else {
                      toast.error(`No template available to fit ${requiredCapacity} images.`);
                    }
                  } else {
                    toast.error("Cannot automatically select a new template.");
                  }
                }
              }
            }
          }
        }
      }
    } catch (error) { toast.error('Could not add image(s) to template'); }
  };

  // Handler for the remove button click
  const handleRemoveButtonClick = useCallback((placeholderIdToRemove: string) => {
    const activeSpread = renderedSpreads[currentRenderedIndex];
    if (!activeSpread) return;

    const placementToRemove = activeSpread.images.find(p => p.placeholderId === placeholderIdToRemove);
    if (!placementToRemove || !placementToRemove.imageId) {
      return; // Placeholder is empty, do nothing
    }


    // Ensure findExactTemplateForImageCount is available and callable
    const findExactTemplate = typeof findExactTemplateForImageCount === 'function' ? findExactTemplateForImageCount : () => null;

    if (autoSwitchTemplateOnRemove && onRemoveImageAndSwitchTemplate) {
      const currentImageCount = activeSpread.images.filter(p => p.imageId).length;
      const targetImageCount = currentImageCount - 1;

      if (targetImageCount > 0) {
        const newTemplate = findExactTemplate(targetImageCount);
        if (newTemplate) {
          onRemoveImageAndSwitchTemplate(currentSpreadId, placeholderIdToRemove);
          toast.info(`Image removed and template switched to '${newTemplate.name}'.`);
        } else {
          // Fallback to just removing the image if no suitable template found
          onUpdateSpreadImages(currentSpreadId, placeholderIdToRemove, null);
          toast.info(`Image removed. No template found for ${targetImageCount} images.`);
        }
      } else {
        // Removing the last image
        onUpdateSpreadImages(currentSpreadId, placeholderIdToRemove, null);
        toast.info("Last image removed.");
      }
    } else {
      // Default behavior: just remove the image
      onUpdateSpreadImages(currentSpreadId, placeholderIdToRemove, null);
      toast.info("Image removed.");
    }
    // Clear hover state just in case it was set
    setHoveredPlaceholderId(null);
  }, [
    renderedSpreads,
    currentRenderedIndex,
    currentSpreadId,
    autoSwitchTemplateOnRemove,
    findExactTemplateForImageCount,
    onRemoveImageAndSwitchTemplate,
    onUpdateSpreadImages,
    setHoveredPlaceholderId // Added setHoveredPlaceholderId dependency
  ]);

  // Drop handler for the main canvas area (outside placeholders)
  const handleCanvasDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault(); e.stopPropagation();
    setIsDraggingImageOverCanvas(false); // Reset drag hover state on drop
    const templateId = e.dataTransfer.getData('application/x-bookproofs-template');
    if (templateId && onSelectTemplate) { onSelectTemplate(templateId); toast.success(`Template applied via drag & drop.`); return; }

    // Get the layout for the currently active spread for logic within this handler
    const currentLayout = allSpreadLayouts[currentSpreadId] || [];

    // Determine initial fit based on default setting and the Shift key for canvas drop
    const modifierPressed = e.shiftKey; // Check for Shift key press
    const initialFit: 'cover' | 'contain' = defaultDropModeIsCover
      ? (modifierPressed ? 'contain' : 'cover') // Default is cover, Shift reverses
      : (modifierPressed ? 'cover' : 'contain'); // Default is contain, Shift reverses

    try {
      const imageData = e.dataTransfer.getData('application/json');
      if (imageData) {
        const parsedData = JSON.parse(imageData);
        const activeSpread = renderedSpreads[currentRenderedIndex];
        // Removed templateMap lookup, activeTemplate not needed here anymore
        const currentFilledCount = activeSpread ? activeSpread.images.filter(img => img.imageId).length : 0;

        // --- Helper function for adding a single image (used for both single and multiple drops on canvas) ---
        const addSingleImageToSpread = async (imageToAdd: ImageFile) => {
          const requiredCapacity = currentFilledCount + 1;
          // Use currentLayout length for capacity
          const currentTemplateCapacity = currentLayout.length;

          if (!activeSpread || activeSpread.templateId === '__blank__' || requiredCapacity > currentTemplateCapacity) {
            // Need a new or larger template
            if (onSelectTemplate && allTemplates) {
              const newTemplate = findTemplateForImageCount(requiredCapacity);
              if (newTemplate) {
                toast.info(`Switching to template '${newTemplate.name}' to fit ${requiredCapacity} image(s).`);
                onSelectTemplate(newTemplate.id);
                setTimeout(async () => {
                  // Logic to place existing images + the new one after template change
                  // initialFit is already determined at the start of handleCanvasDrop

                  const activeSpreadNow = renderedSpreads[currentRenderedIndex]; // Re-fetch (might still be stale)
                  const existingPlacements = activeSpreadNow ? activeSpreadNow.images.filter(p => p.imageId) : [];
                  const existingImagesData = existingPlacements.map(placement => {
                    const file = images.find(f => f.id === placement.imageId);
                    return file ? { file, originalPlacement: placement } : null;
                  }).filter((item): item is { file: ImageFile; originalPlacement: ImagePlacement } => item !== null);
                  const itemsToPlace = [...existingImagesData, { file: imageToAdd, originalPlacement: null }];
                  let placedCount = 0;
                  // Use allTemplates prop to find the template definition
                  const newlyAppliedTemplateDef = allTemplates.find(t => t.id === newTemplate.id);
                  if (!newlyAppliedTemplateDef) {
                     toast.error("Error applying new template."); return;
                  }
                  for (let i = 0; i < newlyAppliedTemplateDef.images.length; i++) {
                    if (i >= itemsToPlace.length) break;
                    const targetPlaceholder = newlyAppliedTemplateDef.images[i];
                    const { file: imageFile, originalPlacement } = itemsToPlace[i];
                    if (originalPlacement === null) { // Newly dropped image
                      await processDroppedImage(imageFile, targetPlaceholder.id, initialFit); // Already passing initialFit from previous fix
                    } else { // Existing image being re-placed
                      // Use the existing transform or fall back to a complete default object
                      // Ensure 'fit' and focal points are preserved or defaulted if missing
                      const transformToApply = originalPlacement.transform ?? { scale: 1, focalX: 0.5, focalY: 0.5, fit: initialFit };
                      const newPlacement: ImagePlacement = {
                        placeholderId: targetPlaceholder.id, imageId: imageFile.id,
                        transform: { // Ensure all properties are present, including fit and focal points
                           scale: transformToApply.scale ?? 1,
                           focalX: transformToApply.focalX ?? 0.5, // Preserve existing focal point or default
                           focalY: transformToApply.focalY ?? 0.5, // Preserve existing focal point or default
                           fit: transformToApply.fit ?? initialFit // Preserve existing fit or use initialFit
                        }
                      };
                      onUpdateSpreadImages(currentSpreadId, targetPlaceholder.id, newPlacement);
                    }
                    placedCount++;
                  }
                  toast.success(`Applied template '${newTemplate.name}' and placed ${placedCount} image(s)`);
                }, 150);
              } else {
                toast.error(`No template available for ${requiredCapacity} image(s).`);
              }
            } else {
              toast.error("Cannot automatically select template.");
            }
          } else if (currentLayout.length > 0) { // Check currentLayout
            // Current template has space, find first empty slot based on currentLayout
            const emptyPlaceholders = currentLayout.filter(adjP => !activeSpread.images.some(img => img.placeholderId === adjP.id && img.imageId));
            if (emptyPlaceholders.length > 0) {
              // initialFit is already determined at the start of handleCanvasDrop
              await processDroppedImage(imageToAdd, emptyPlaceholders[0].id, initialFit); // Pass initialFit determined at start
              // toast.success("Image added to the first empty slot."); // Redundant with processDroppedImage toast
            } else {
              toast.info("No empty slots available in the current template.");
            }
          } else {
            toast.error("Could not determine placement logic.");
          }
        };
        // --- End Helper Function ---

        if (Array.isArray(parsedData)) { // Multiple images dropped on canvas
          const droppedImages = parsedData as ImageFile[];
          if (droppedImages.length === 0) return;

          const totalRequiredImages = currentFilledCount + droppedImages.length;
          const currentTemplateCapacity = currentLayout.length;

          // --- NEW LOGIC for Canvas Drop (Multiple Images) ---
          if (activeSpread?.templateId === '__blank__') {
            // Drop multiple on blank spread
            if (totalRequiredImages > maxTemplateSize) {
              // Exceeds largest template -> Offer autobuild
              if (onOfferAutobuildRequest) {
                onOfferAutobuildRequest({ droppedImages, currentSpreadId, existingImageCount: 0, maxTemplateSize });
              } else {
                toast.error(`Cannot fit ${totalRequiredImages} images. Largest template holds ${maxTemplateSize}.`);
              }
            } else {
              // Find suitable template and place
              if (onSelectTemplate && allTemplates) {
                const matchingTemplate = findTemplateForImageCount(totalRequiredImages);
                if (matchingTemplate) {
                  onSelectTemplate(matchingTemplate.id);
                  setTimeout(async () => {
                    let placedCount = 0;
                    for (const [index, image] of droppedImages.entries()) {
                      if (index < matchingTemplate.images.length) {
                        await processDroppedImage(image, matchingTemplate.images[index].id, initialFit);
                        placedCount++;
                      } else break;
                    }
                    toast.success(`Applied template and placed ${placedCount} images`);
                  }, 50);
                } else {
                  toast.error(`No template available for ${totalRequiredImages} images.`);
                }
              } else {
                toast.error("Cannot automatically select template.");
              }
            }
          } else {
            // Drop multiple on existing template
            if (totalRequiredImages <= currentTemplateCapacity) {
              // Fits in current template -> Place in empty slots
              const emptyPlaceholders = currentLayout.filter(adjP => !activeSpread?.images.some(img => img.placeholderId === adjP.id && img.imageId));
              if (emptyPlaceholders.length < droppedImages.length) {
                toast.info(`Not enough empty slots (${emptyPlaceholders.length}) for ${droppedImages.length} images.`);
              }
              let imagesPlacedCount = 0;
              for (const image of droppedImages) {
                if (imagesPlacedCount >= emptyPlaceholders.length) break;
                const targetPlaceholder = emptyPlaceholders[imagesPlacedCount];
                await processDroppedImage(image, targetPlaceholder.id, initialFit);
                imagesPlacedCount++;
              }
              if (imagesPlacedCount > 0) toast.success(`Placed ${imagesPlacedCount} images.`);

            } else if (totalRequiredImages <= maxTemplateSize) {
              // Needs larger template (but fits within max) -> Switch template and place
              if (onSelectTemplate && allTemplates) {
                const newTemplate = findTemplateForImageCount(totalRequiredImages);
                if (newTemplate) {
                  toast.info(`Switching to template '${newTemplate.name}' to fit ${totalRequiredImages} images.`);
                  onSelectTemplate(newTemplate.id);
                  setTimeout(async () => {
                    const activeSpreadNow = renderedSpreads[currentRenderedIndex]; // Re-fetch spread data after template change
                    const existingPlacements = activeSpreadNow ? activeSpreadNow.images.filter(p => p.imageId) : [];
                    const existingImagesData = existingPlacements.map(placement => {
                      const file = images.find(f => f.id === placement.imageId);
                      return file ? { file, originalPlacement: placement } : null;
                    }).filter((item): item is { file: ImageFile; originalPlacement: ImagePlacement } => item !== null);
                    const itemsToPlace = [...existingImagesData, ...droppedImages.map(file => ({ file, originalPlacement: null }))];
                    let placedCount = 0;
                    const newlyAppliedTemplateDef = allTemplates.find(t => t.id === newTemplate.id);
                    if (!newlyAppliedTemplateDef) { toast.error("Error applying new template."); return; }
                    for (let i = 0; i < newlyAppliedTemplateDef.images.length; i++) {
                      if (i >= itemsToPlace.length) break;
                      const targetPlaceholder = newlyAppliedTemplateDef.images[i];
                      const { file: imageFile, originalPlacement } = itemsToPlace[i];
                      if (originalPlacement === null) { // Newly dropped image
                        await processDroppedImage(imageFile, targetPlaceholder.id, initialFit);
                      } else { // Existing image being re-placed
                        const transformToApply = originalPlacement.transform ?? { scale: 1, focalX: 0.5, focalY: 0.5, fit: initialFit };
                        const newPlacement: ImagePlacement = {
                          placeholderId: targetPlaceholder.id, imageId: imageFile.id,
                          transform: {
                            scale: transformToApply.scale ?? 1,
                            focalX: transformToApply.focalX ?? 0.5,
                            focalY: transformToApply.focalY ?? 0.5,
                            fit: transformToApply.fit ?? initialFit
                          }
                        };
                        onUpdateSpreadImages(currentSpreadId, targetPlaceholder.id, newPlacement);
                      }
                      placedCount++;
                    }
                    toast.success(`Applied template '${newTemplate.name}' and placed ${placedCount} images`);
                  }, 150); // Delay to allow template change state update
                } else {
                  toast.error(`No template available for ${totalRequiredImages} images.`);
                }
              } else {
                toast.error("Cannot automatically select template.");
              }
            } else {
              // Exceeds max template size -> Offer autobuild
              if (onOfferAutobuildRequest) {
                onOfferAutobuildRequest({ droppedImages, currentSpreadId, existingImageCount: currentFilledCount, maxTemplateSize });
              } else {
                toast.error(`Cannot fit ${totalRequiredImages} images. Largest template holds ${maxTemplateSize}.`);
              }
            }
          }
          // --- END NEW LOGIC ---

        } else { // Single image dropped on canvas - ALWAYS ADD
          const droppedImage = parsedData as ImageFile;
          await addSingleImageToSpread(droppedImage); // Use the helper function
        }
      } else {
        // Check if this is an OS file drop (not from ImageTray)
        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
          // Show rejection message
          setRejectionMessage("Please import images to the Photo Library first");
          // Clear message after 3 seconds
          setTimeout(() => setRejectionMessage(null), 3000);
          return;
        }
      } // End of if(imageData)
    } catch (error) { // <<< CORRECTLY PLACED CATCH BLOCK
      toast.error('Could not add image(s) to template');
// --- Auto-Cover Logic ---
  const checkAndApplyAutoCover = useCallback((spreadId: string, placeholderId: string) => {
    if (!autoCoverNearEdges || !onUpdateImageTransform || !getImageDimensions) {
      // Setting is off or required functions/data are missing
      return;
    }

    const activeSpread = renderedSpreads.find(s => s.id === spreadId);
    const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
    const containerDims = placeholderPixelDims[placeholderId];

    // Check if we have a placement, an image, it's in 'contain' mode, and we have container dimensions
    if (!placement?.imageId || !containerDims || containerDims.width <= 0 || containerDims.height <= 0 || (placement.transform?.fit ?? 'contain') !== 'contain') {
      return;
    }

    const imageNativeDims = getImageDimensions(placement.imageId);
    if (!imageNativeDims || imageNativeDims.width <= 0 || imageNativeDims.height <= 0) {
      return; // Need valid image dimensions
    }

    // --- Calculate Rendered Size for 'contain' ---
    const containerW = containerDims.width;
    const containerH = containerDims.height;
    const imageW = imageNativeDims.width;
    const imageH = imageNativeDims.height;
    const imageRatio = imageW / imageH;
    const containerRatio = containerW / containerH;

    let renderedW: number;
    let renderedH: number;

    if (imageRatio > containerRatio) { // Image is wider than container (relative to height) -> fit width
      renderedW = containerW;
      renderedH = containerW / imageRatio;
    } else { // Image is taller or same aspect ratio -> fit height
      renderedH = containerH;
      renderedW = containerH * imageRatio;
    }

    // --- Check Closeness ---
    const thresholdPercent = 0.015; // 1.5% threshold
    const thresholdPxW = containerW * thresholdPercent;
    const thresholdPxH = containerH * thresholdPercent;

    const gapLeft = (containerW - renderedW) / 2;
    const gapRight = gapLeft;
    const gapTop = (containerH - renderedH) / 2;
    const gapBottom = gapTop;

    // Use a slightly smaller threshold check to avoid floating point issues near equality
    const isClose = gapLeft < thresholdPxW || gapRight < thresholdPxW || gapTop < thresholdPxH || gapBottom < thresholdPxH;


    if (isClose) { // Image edge is close to FRAME edge
        // --- NEW CHECK: Is the FRAME edge close to the SPREAD edge? ---
        const templateId = activeSpread?.templateId; // Get template ID first
        const templateData = templateId ? allTemplates?.find(t => t.id === templateId) : undefined; // Find template data from props
        const placeholder = templateData?.images.find(p => p.id === placeholderId); // Find placeholder within template data's images array
        const spreadEdgeThresholdPercent = 0.015; // 1.5% threshold for spread edge proximity

        let isFrameNearSpreadEdge = false;
        if (placeholder && templateId !== '__blank__') { // Check using templateId and if placeholder was found
            const frameX = placeholder.x; // Assumes placeholder coords are relative (0-1)
            const frameY = placeholder.y;
            const frameW = placeholder.width;
            const frameH = placeholder.height;

            // Check proximity to each spread edge
            const isNearLeft = frameX < spreadEdgeThresholdPercent;
            const isNearRight = (frameX + frameW) > (1 - spreadEdgeThresholdPercent);
            const isNearTop = frameY < spreadEdgeThresholdPercent;
            const isNearBottom = (frameY + frameH) > (1 - spreadEdgeThresholdPercent);

            isFrameNearSpreadEdge = isNearLeft || isNearRight || isNearTop || isNearBottom;
        }
        // --- END NEW CHECK ---

        // Only apply zoom if BOTH conditions are met: image is close to frame AND frame is close to spread edge
        if (isFrameNearSpreadEdge) {
            // console.log(`[AutoZoom] Image ${placement.imageId} in ${placeholderId} is close to FRAME edge AND frame is close to SPREAD edge. Applying zoom.`);
            // Correct logic: Apply 2% zoom, keep 'contain'
            const currentTransform = placement.transform ?? { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'contain' }; // Ensure fit is 'contain' for calculation
            const currentScale = currentTransform.scale ?? 1;

            // Only apply zoom if the current scale is very close to 1 (prevents repeated zooming)
            // And ensure we are actually in contain mode (double check)
            if (currentScale < 1.01 && (currentTransform.fit ?? 'contain') === 'contain') {
                const newScale = currentScale * 1.02; // Apply 2% zoom

                onUpdateImageTransform(spreadId, placeholderId, {
                    ...currentTransform,
                    scale: newScale, // Update the scale
                    fit: 'contain', // Ensure fit remains 'contain'
                    // @ts-ignore: Mark this as a change that should trigger undo state save
                    _shouldSaveUndo: true // Ensure this change is undoable
                });
                // Use a specific toast ID to prevent duplicates if called rapidly
                toast.info(`Image near spread edge detected, applying 2% zoom.`, { id: `autozoom-${placeholderId}` }); // Updated toast message and ID prefix
            }
        }
    }

  }, [
    autoCoverNearEdges,
    renderedSpreads, // Need access to current spread data
    placeholderPixelDims, // Need container dimensions
    getImageDimensions, // Need image dimensions
    onUpdateImageTransform // Need to call the update function
    // currentSpreadId is implicitly available in the scope where this will be called
  ]);
  // --- End Auto-Cover Logic ---
    } // <<< END OF CATCH BLOCK
  }; // <<< END OF handleCanvasDrop

  // *** ADDED: useEffect to log incoming spread images prop ***
  useEffect(() => {
    const activeSpread = renderedSpreads[currentRenderedIndex];
    if (activeSpread) {
      // Use JSON.stringify for a comparable snapshot if needed, or log directly
      // console.log(`[SpreadCanvas Prop Check - ${Date.now()}] Incoming spread images for ${currentSpreadId}:`, // Commented out
      //   activeSpread.images.map(p => ({ placeholderId: p.placeholderId, imageId: p.imageId, transform: p.transform })) // Commented out
      // ); // Commented out
    }
  }, [renderedSpreads, currentRenderedIndex, currentSpreadId]); // Depend on the data and the identifiers

  // --- Snapshot Generation Logic ---
  // Reverted to use spreadContainerRefs and original delay logic
  const generateSnapshotLogic = useCallback((spreadId: string, reason: string) => {
    const activeSpread = renderedSpreads.find(s => s.id === spreadId);
    if (!activeSpread) {
      // Silently return if spread doesn't exist yet
      return;
    }

    if (!onUpdateThumbnailSnapshot) {
      return; // Silently return if callback not provided
    }

    // !!! REVERTED: Use ref map instead of getElementById
    const elementToCapture = spreadContainerRefs.current[spreadId];
    if (!elementToCapture) {
      // Re-introduce warning for debugging, but keep silent return
      return;
    }

    // !!! REVERTED TO 7b863a3: Original simpler delay logic
    const delay = reason === 'Navigation (No Snapshot)' ? 250 : 0; // 250ms > animation duration (200ms)

    // Calculate dimensions *outside* the idle callback first (keep this check)
    const initialWidth = elementToCapture.offsetWidth;
    const initialHeight = elementToCapture.offsetHeight;

    if (initialWidth <= 0 || initialHeight <= 0) {
      // Silently return if dimensions are invalid initially
      // It might become valid later, so proceed to setTimeout/requestIdleCallback
    }

    // Use setTimeout to delay the execution of html2canvas
    setTimeout(() => {
      requestIdleCallback(() => {
        // Re-check element existence and dimensions *inside* idle callback
        const elementNow = spreadContainerRefs.current[spreadId]; // Re-check ref
        if (!elementNow) return; // Element might have been removed

        const width = elementNow.offsetWidth;
        const height = elementNow.offsetHeight;

        if (width <= 0 || height <= 0) {
          // Add back warning if dimensions are still zero inside idle callback
          return; // Don't attempt snapshot if element has no size
        }

      }, { timeout: 1500 });
    }, delay);
  }, [onUpdateThumbnailSnapshot]); // !!! REVERTED TO 7b863a3: Dependencies - ONLY onUpdateThumbnailSnapshot

  // Debounced function for general content changes (shorter delay)
  const debouncedGenerateSnapshotForContent = useMemo(() => {
    // !!! REVERTED: Original signature (no needsLongerDelay)
    return debounce((spreadId: string) => generateSnapshotLogic(spreadId, 'Content Change'), 200);
  }, [generateSnapshotLogic]);

  // Debounced function specifically for template changes (longer delay)
  const debouncedGenerateSnapshotForTemplate = useMemo(() => {
    // !!! REVERTED: Original signature and original delay (1000ms)
    return debounce((spreadId: string) => generateSnapshotLogic(spreadId, 'Template Change'), 1000); // Longer delay (1000ms)
  }, [generateSnapshotLogic]);

  // Refs to store previous state for comparison within the effect
  const prevSpreadIdRef = useRef(currentSpreadId);
  const prevImagesRef = useRef<string | null>(null);
  const prevTemplateIdRef = useRef<string | null | undefined>(null);

  // Effect to trigger snapshot on relevant data changes (images, template)
  useEffect(() => {
    const currentSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    const currentStringifiedImages = JSON.stringify(currentSpread?.images ?? []);
    const currentTemplateId = currentSpread?.templateId;

    // Trigger only if the spread ID hasn't changed within this effect cycle
    if (prevSpreadIdRef.current === currentSpreadId) {
      // Compare with previous values *after* confirming spread ID is the same
      const imagesChanged = currentStringifiedImages !== prevImagesRef.current;
      const templateChanged = currentTemplateId !== prevTemplateIdRef.current;

      // !!! REVERTED: Removed needsLongerDelay logic
      // Original logic: prioritize content changes
      if (imagesChanged) {
        // If images changed, always use the faster debounce
        debouncedGenerateSnapshotForContent(currentSpreadId);
        // Cancel the template change debounce if it was pending
        debouncedGenerateSnapshotForTemplate.cancel();
      } else if (templateChanged) {
        // If only the template changed, use the slower debounce
        debouncedGenerateSnapshotForTemplate(currentSpreadId);
        // Cancel the content change debounce if it was pending (less likely but possible)
        debouncedGenerateSnapshotForContent.cancel();
      }
    }

    // Update refs for the next render *after* comparisons
    prevSpreadIdRef.current = currentSpreadId;
    prevImagesRef.current = currentStringifiedImages;
    prevTemplateIdRef.current = currentTemplateId;

  }, [currentSpreadId, renderedSpreads, generateSnapshotLogic, debouncedGenerateSnapshotForContent, debouncedGenerateSnapshotForTemplate]); // Add debounced functions to dependencies

  // Effect to trigger snapshot specifically on navigation *if* no snapshot exists
  useEffect(() => {
    const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    // Trigger only if navigated to a spread without a thumbnail URL
    if (activeSpread && !activeSpread.thumbnailDataUrl && onUpdateThumbnailSnapshot) {
      // !!! REVERTED: Call generateSnapshotLogic directly without the extra delay param
      generateSnapshotLogic(currentSpreadId, 'Navigation (No Snapshot)');
    }
    // No cleanup needed here specifically, as the main effect handles cancellation
    // Dependencies: currentSpreadId to detect navigation, renderedSpreads to check existing URL, and the logic function
  }, [currentSpreadId, renderedSpreads, onUpdateThumbnailSnapshot, generateSnapshotLogic]); // Added generateSnapshotLogic

  // --- Auto-Cover Logic ---
  const prevSpreadCheckDataRef = useRef<{ spreadId: string; imagesJson: string; layoutJson: string } | null>(null);

  useEffect(() => {
    // Skip autozoom during undo/redo operations
    if (window.bookProofsApp?.isRestoringState) {
      // console.log(`[AutoZoom useEffect] Skipping due to undo/redo operation in progress`);
      return;
    }
    
    const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
    const layoutForThisSpread = allSpreadLayouts[currentSpreadId] || [];

    // Create comparable JSON strings for relevant data of the *current* spread
    const currentImagesJson = JSON.stringify(activeSpread?.images.map(p => ({ pId: p.placeholderId, imgId: p.imageId, fit: p.transform?.fit, scale: p.transform?.scale })) || []);
    // Corrected property names for AdjustedPlaceholderPt
    const currentLayoutJson = JSON.stringify(layoutForThisSpread.map(p => ({ id: p.id, x: p.adjustedX_pt, y: p.adjustedY_pt, w: p.adjustedWidth_pt, h: p.adjustedHeight_pt })) || []);

    let shouldRunCheck = false;
    const previousData = prevSpreadCheckDataRef.current;

    if (!previousData || previousData.spreadId !== currentSpreadId) {
      // Navigation occurred OR first run for this spread
      shouldRunCheck = true;
      // console.log(`[AutoZoom useEffect Check] Navigation detected to ${currentSpreadId} or first run. Flagging for check.`);
    } else {
      // Same spread, check if relevant content changed
      if (previousData.imagesJson !== currentImagesJson || previousData.layoutJson !== currentLayoutJson) {
        shouldRunCheck = true;
        // console.log(`[AutoZoom useEffect Check] Meaningful content change detected on spread ${currentSpreadId}. Flagging for check. Images changed: ${previousData.imagesJson !== currentImagesJson}, Layout changed: ${previousData.layoutJson !== currentLayoutJson}`);
      } else {
         // console.log(`[AutoZoom useEffect Check] No meaningful change detected for spread ${currentSpreadId}. Skipping checks.`);
      }
    }

    // --- Run Checks (with delay for navigation) ---
    const runChecks = () => {
      if (activeSpread && layoutForThisSpread.length > 0) {
        // console.log(`[AutoZoom useEffect] Proceeding with checks for Spread: ${currentSpreadId}`);
        layoutForThisSpread.forEach(placeholder => {
          const placement = activeSpread.images.find(p => p.placeholderId === placeholder.id);
          if (placement?.imageId) {
            // console.log(`[AutoZoom useEffect] Calling checkAndApplyAutoCover for Placeholder: ${placeholder.id}`);
            checkAndApplyAutoCover(currentSpreadId, placeholder.id, 'useEffect[Content Change/Nav]');
          }
        });
      }
    };

    if (shouldRunCheck) {
      const isNavigation = !previousData || previousData.spreadId !== currentSpreadId;
      const layoutChanged = previousData && previousData.spreadId === currentSpreadId && previousData.layoutJson !== currentLayoutJson;

      if (isNavigation || layoutChanged) {
        const delayReason = isNavigation ? 'navigation' : 'layout change';
        const delayDuration = layoutChanged ? 600 : 300; // 600ms for layout, 300ms for nav

        // Record timestamp if layout changed
        if (layoutChanged) {
            // console.log(`[AutoZoom useEffect] Layout change detected. Updating timestamp.`);
            lastLayoutChangeTimestampRef.current = Date.now();
        }

        // console.log(`[AutoZoom useEffect] Delaying checks by ${delayDuration}ms for ${delayReason} on ${currentSpreadId}...`);
        const timerId = setTimeout(runChecks, delayDuration);
        // Cleanup function
        return () => {
            // console.log(`[AutoZoom useEffect Cleanup] Clearing timeout for ${delayReason} on ${currentSpreadId}.`); // Added cleanup log
            clearTimeout(timerId);
        };
      } else {
        // Run checks immediately for other content changes (like image fit/scale)
        // console.log(`[AutoZoom useEffect] Running checks immediately for content change (not layout) on ${currentSpreadId}.`);
        runChecks();
      }
    }
    // --- End Run Checks ---

    // Update ref for next render comparison
    prevSpreadCheckDataRef.current = {
        spreadId: currentSpreadId,
        imagesJson: currentImagesJson,
        layoutJson: currentLayoutJson
    };

  // Dependencies: Include currentSpreadId to detect navigation.
  }, [renderedSpreads, allSpreadLayouts, currentSpreadId, checkAndApplyAutoCover]);
  // --- End Auto-Cover Trigger Effect ---

  // Effect to clean up zoom animation frames on unmount
  useEffect(() => {
    return () => {
      Object.values(zoomAnimationFrameRef.current).forEach(frameId => {
        if (frameId) {
          cancelAnimationFrame(frameId);
        }
      });
      zoomAnimationFrameRef.current = {};
    };
  }, []);


  // Navigation handlers
  const handlePrevious = () => {
    if (currentSpreadNumber === 1 && hasCover && onNavigateToCover) {
      setNavigationDirection('prev');
      onNavigateToCover();
    } else if (currentSpreadNumber > 1) {
      setNavigationDirection('prev');
      onNavigateSpread('prev');
    }
  };
  const handleNext = () => {
    setNavigationDirection('next');
    onNavigateSpread('next');
  }; // Always call parent handler, let it decide whether to navigate or add

  // Wheel scroll navigation
  const handleWheelScroll = (e: React.WheelEvent<HTMLDivElement>) => {
    if (isTextEditorActive) {
        // If text editor is active, do not allow spread canvas to navigate via wheel.
        // TextEditor's own components (like textarea) should handle their scroll if focused.
        return;
    }
    // Prevent wheel scroll navigation if panning an image
    if (isPanning || isWheeling) return;
    const deltaY = e.deltaY; const threshold = 10; let navigated = false;
    if (deltaY < -threshold) {
      if (currentSpreadNumber < totalSpreads) {
        setNavigationDirection('next');
        onNavigateSpread('next');
        navigated = true;
      }
    } // Reversed: Scroll Up -> Next
    else if (deltaY > threshold) {
      if (currentSpreadNumber > 1) {
        setNavigationDirection('prev');
        onNavigateSpread('prev');
        navigated = true;
      }
    } // Reversed: Scroll Down -> Prev
    if (navigated) {
      e.preventDefault(); setIsWheeling(true);
      if (wheelTimeoutRef.current) clearTimeout(wheelTimeoutRef.current);
      wheelTimeoutRef.current = setTimeout(() => { setIsWheeling(false); wheelTimeoutRef.current = null; }, 150);
    }
  };

  // --- Panning Handlers ---
  const handleImageMouseDown = (e: React.MouseEvent<HTMLImageElement | HTMLDivElement>, placeholderId: string) => {
    if (isTextEditorActive) {
        const clickedOnTextControls = textControlsBarRef?.current && textControlsBarRef.current.contains(e.target as Node);
        // If text editor is active AND the click was NOT on the text controls bar,
        // then this click is intended for the TextEditor's outside click handling.
        // So, SpreadCanvas should not process this mousedown.
        if (!clickedOnTextControls) {
            return;
        }
    }

    // This function handles both panning start and double-click for zoom reset

    // Only handle left mouse button
    if (e.button !== 0) return;

    // Determine if propagation should be stopped for color picker logic
    let shouldStopPropagationForPicker = true; // Default to stopping propagation
    if (activeColorPickerId && colorPickerRef.current && !colorPickerRef.current.contains(e.target as Node)) {
        // If a picker is active AND the click is OUTSIDE of it,
        // then we should NOT stop propagation, to let the picker's outside click handler work.
        // The picker's own outside click handler correctly ignores clicks on its opening swatch.
        shouldStopPropagationForPicker = false;
    }

    // Get the current time for double-click detection
    const now = Date.now();

    // If this is a double-click (within 300ms), handle zoom reset
    if (lastClickTimeRef.current && (now - lastClickTimeRef.current < 300) && lastClickedPlaceholderRef.current === placeholderId) {
      lastClickTimeRef.current = 0; // Reset timer to prevent triple-click issues

      // Reset zoom for this image
      const activeSpread = renderedSpreads[currentRenderedIndex];
      const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);

      if (placement && onUpdateImageTransform) { // Check if onUpdateImageTransform exists
        // Reset to default transform based on current fit mode
        const currentFitMode = placement.transform?.fit ?? 'contain';

        // Apply reset with default focal points (use direct update for immediate feedback)
        onUpdateImageTransform(
          currentSpreadId,
          placeholderId,
          {
            scale: 1,
            focalX: 0.5,
            focalY: 0.5,
            rotation: 0, // Reset rotation to 0
            fit: currentFitMode, // Reset focal point to center and include fit
            // @ts-ignore: Mark this as a change that should trigger undo state save
            _shouldSaveUndo: true
          }
        );
        // Check auto-cover after zoom reset (might revert to contain)
        // console.log(`[handleImageMouseDown] Calling checkAndApplyAutoCover after double-click zoom reset. Placeholder: ${placeholderId}`); // VERBOSE LOG
        checkAndApplyAutoCover(currentSpreadId, placeholderId, 'handleImageMouseDown (DoubleClick)');
        toast.info("Zoom, Pan & Rotation Reset"); // Updated feedback message
      }

      return;
    }

    // Update refs for double-click detection
    lastClickTimeRef.current = now;
    lastClickedPlaceholderRef.current = placeholderId;

    // Get the current spread and image info
    const activeSpread = renderedSpreads[currentRenderedIndex];
    const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);

    // If no valid placement found or no image assigned, skip further processing
    if (!placement || !placement.imageId) return;

    // Get current transform and fit mode
    const currentScale = placement.transform?.scale ?? 1;
    const currentFitMode = placement.transform?.fit ?? 'contain'; // Get fit mode
    const isPannableState = true; // Always allow panning

    // Handle mouse down on image
    const targetElement = e.currentTarget as HTMLDivElement;
    const rect = targetElement.getBoundingClientRect();
    const relativeY = e.clientY - rect.top;
    const containerHeight = targetElement.offsetHeight;
    const topQuarter = containerHeight / 4; // Use top 1/4 as the drag handle

    if (isPannableState) {
      if (relativeY < topQuarter) {
        // Top 1/4: Allow drag initiation
        targetElement.draggable = true; // Ensure draggable is true for drag start
        // Do NOT preventDefault or stopPropagation here to allow dragging
      } else {
        // Bottom 3/4: Initiate Panning
        e.preventDefault(); // Prevent default drag behavior
        if (shouldStopPropagationForPicker) {
          e.stopPropagation(); // Conditionally stop propagation
        }
        targetElement.draggable = false; // Prevent drag start

        // Start panning - capture initial mouse position and current focal point
        setIsPanning(true);
        panningPlaceholderIdRef.current = placeholderId;

        // Store starting mouse coords and current focal point
        setPanStartCoords({
          x: e.clientX,
          y: e.clientY,
          startFocalX: placement.transform?.focalX ?? 0.5, // Get current focal point
          startFocalY: placement.transform?.focalY ?? 0.5  // Get current focal point
        });
        // No need to cache dimensions for panning anymore
      }
    } else {
      // Scale is 1 AND fit is contain: Allow default drag behavior
      targetElement.draggable = true; // Ensure draggable is true
      // Do NOT preventDefault or stopPropagation here to allow dragging
    }
  };
 
  // Refactored for Focal Points
  const handleImageMouseMove = (e: React.MouseEvent<HTMLImageElement | HTMLDivElement>) => {
    if (!isPanning || !panStartCoords || !panningPlaceholderIdRef.current || !onUpdateImageTransform) {
      return;
    }

    const placeholderId = panningPlaceholderIdRef.current;
    const activeSpread = renderedSpreads[currentRenderedIndex];
    const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
    const imageFile = placement?.imageId ? images.find(img => img.id === placement.imageId) : null;
    const containerDims = placeholderPixelDims[placeholderId];

    // Ensure we have all necessary data, including getImageDimensions function
    if (!placement || !imageFile || !containerDims || !getImageDimensions) {
      // console.warn("[MouseMove] Missing data for panning calculation:", { placement, imageFile, containerDims, getImageDimensions });
      return;
    }

    const currentTransform = placement.transform;
    const scale = currentTransform?.scale ?? 1; // Use optional chaining and default
    const fitMode = currentTransform?.fit ?? 'contain'; // Use optional chaining and default
    const imageDims = getImageDimensions(imageFile.id);

    if (!imageDims || imageDims.width <= 0 || imageDims.height <= 0) {
      // console.warn("[MouseMove] Invalid image dimensions:", imageDims);
      return; // Need valid image dimensions
    }

    // Calculate rendered image dimensions within the container based on fit mode and scale
    let renderedWidthPx: number;
    let renderedHeightPx: number;
    const containerRatio = containerDims.width / containerDims.height;
    const imageRatio = imageDims.width / imageDims.height;

    // Calculate base rendered size (before scale)
    let baseRenderedW: number;
    let baseRenderedH: number;
    if (fitMode === 'contain') {
      if (imageRatio > containerRatio) { // Fit width
        baseRenderedW = containerDims.width;
        baseRenderedH = containerDims.width / imageRatio;
      } else { // Fit height
        baseRenderedH = containerDims.height;
        baseRenderedW = containerDims.height * imageRatio;
      }
    } else { // 'cover'
      if (imageRatio > containerRatio) { // Fit height, cover width
        baseRenderedH = containerDims.height;
        baseRenderedW = containerDims.height * imageRatio;
      } else { // Fit width, cover height
        baseRenderedW = containerDims.width;
        baseRenderedH = containerDims.width / imageRatio;
      }
    }

    // Apply scale to get final rendered dimensions
    renderedWidthPx = baseRenderedW * scale;
    renderedHeightPx = baseRenderedH * scale;

    // Prevent division by zero if rendered dimensions are somehow zero
    if (renderedWidthPx <= 0 || renderedHeightPx <= 0) {
      // console.warn("[MouseMove] Calculated rendered dimensions are zero or negative:", { renderedWidthPx, renderedHeightPx });
      return;
    }

    // Calculate mouse delta in pixels
    const dx = e.clientX - panStartCoords.x;
    const dy = e.clientY - panStartCoords.y;

    // Calculate how much the focal point should shift (in relative 0-1 coordinates)
    // The shift is the pixel delta divided by the *rendered* size of the image
    // Negate because moving mouse right (positive dx) should move focal point left (negative change)
    const focalShiftX = -(dx / renderedWidthPx);
    const focalShiftY = -(dy / renderedHeightPx);

    // Calculate new focal point (before constraint)
    let newFocalX = panStartCoords.startFocalX + focalShiftX;
    let newFocalY = panStartCoords.startFocalY + focalShiftY;

    // Calculate how much of the image can be panned based on zoom level
    // For cover mode and zoomed images, we need to prevent showing whitespace
    
    // Calculate how much the focal point can deviate from center (0.5)
    // The higher the scale, the more deviation allowed
    // For scale=1 and cover mode, very little deviation is allowed
    // For higher scales, more deviation is allowed
    
    // These calculations determine how far off-center the focal point can be
    // based on how much of the image extends beyond the container's edges
    
    // Calculate the maximum allowed deviation from center for focal points
    let maxDeviationX = 0.5; // Default: full range (0-1)
    let maxDeviationY = 0.5; // Default: full range (0-1)
    
    if (renderedWidthPx > containerDims.width) {
      // When image is wider than container, calculate how much can be moved
      // The visible portion is container/rendered, remaining portion can be panned
      const visiblePortion = containerDims.width / renderedWidthPx;
      // Deviation allowed is half of the invisible portion (since 0.5 is center)
      maxDeviationX = (1 - visiblePortion) / 2;
    } else {
      // If image is narrower or equal to container, no horizontal panning
      maxDeviationX = 0;
    }
    
    if (renderedHeightPx > containerDims.height) {
      // When image is taller than container
      const visiblePortion = containerDims.height / renderedHeightPx;
      maxDeviationY = (1 - visiblePortion) / 2;
    } else {
      // If image is shorter or equal to container, no vertical panning
      maxDeviationY = 0;
    }
    
    // Clamp the focal point to prevent showing whitespace
    newFocalX = Math.max(0.5 - maxDeviationX, Math.min(0.5 + maxDeviationX, newFocalX));
    newFocalY = Math.max(0.5 - maxDeviationY, Math.min(0.5 + maxDeviationY, newFocalY));
    
    // Additional safety clamp to ensure values are within 0-1 range
    newFocalX = Math.max(0, Math.min(1, newFocalX));
    newFocalY = Math.max(0, Math.min(1, newFocalY));

    // Update transform using the throttled function
    throttledUpdateTransform(currentSpreadId, placeholderId, {
      scale, // Scale remains the same during pan
      focalX: newFocalX,
      focalY: newFocalY,
    }, fitMode); // Pass fitMode explicitly
  };

  const handleImageMouseUpOrLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    const targetElement = e.currentTarget as HTMLDivElement;
    const placeholderId = Object.keys(placeholderContainerRefs.current).find(
        key => placeholderContainerRefs.current[key] === targetElement
    );

    if (isPanning) {
      // console.log('[Mouse Up/Leave] Panning finished. Placeholder:', placeholderId); // Commented out
      setIsPanning(false);
      setPanStartCoords(null); // Clear start coords
      panningPlaceholderIdRef.current = null;
      
      // Cancel any ongoing animation
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      
      // Clear animation state
      if (placeholderId) {
        delete currentFocalPointsRef.current[placeholderId];
        delete targetFocalPointsRef.current[placeholderId];
      }
      
      // setPanningStartDims(null); // No longer needed
      document.body.style.cursor = 'default'; // Reset body cursor FIRST

      // Reset draggable state and cursor state after panning (logic remains similar)
      if (placeholderId) {
        const activeSpread = renderedSpreads[currentRenderedIndex];
        const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
        const currentScale = placement?.transform?.scale ?? 1;
        const currentFitMode = placement?.transform?.fit ?? 'contain'; // Get fit mode
        const isPannableState = true; // Always allow panning
        // console.log('[Mouse Up/Leave] Resetting state. IsPannable:', isPannableState, 'Final Transform:', placement?.transform); // Commented out

        targetElement.draggable = false; // Cannot drag immediately after pan finishes
        // Set cursor based on whether state is still pannable (usually grab, but might be default if zoom reset)
        const newCursor = isPannableState ? 'grab' : 'default';
        // console.log('[Mouse Up/Leave] Setting cursor to:', newCursor); // Commented out
        setPlaceholderCursor(prev => ({ ...prev, [placeholderId]: newCursor }));

        // When panning ends, send a final update with a flag to mark the end of this transform sequence
        // This will trigger the undo state save
        if (placement && placement.transform && onUpdateImageTransform) {
          // Mark this as the end of the transform sequence for undo state
          onUpdateImageTransform(currentSpreadId, placeholderId, {
            ...placement.transform,
            // @ts-ignore: Add special flag for undo handling
            _isTransformEnd: true
          });
          // Check auto-cover after pan ends
          // console.log(`[handleImageMouseUpOrLeave] Calling checkAndApplyAutoCover after pan end. Placeholder: ${placeholderId}`); // VERBOSE LOG
          checkAndApplyAutoCover(currentSpreadId, placeholderId, 'handleImageMouseUpOrLeave (PanEnd)');
        }
      }
    } else if (placeholderId) {
      // Handle mouseup/leave when NOT panning
      // Optional: Add logging here if needed for other scenarios
        const activeSpread = renderedSpreads[currentRenderedIndex];
        const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
        const currentScale = placement?.transform?.scale ?? 1;
      const currentFitMode = placement?.transform?.fit ?? 'contain';
      const isPannableState = true; // Always allow panning

      targetElement.draggable = !isPannableState; // Draggable ONLY if contain and scale 1
      // Reset cursor based on whether the state is pannable
      const newCursor = isPannableState ? 'grab' : 'default';
      setPlaceholderCursor(prev => ({ ...prev, [placeholderId]: newCursor }));
    }
  };

  // --- End Panning Handlers ---

  // Template change detection logic
  const prevPropsRef = useRef<{ spreadId: string | undefined; templateId: string | null | undefined }>(); // Allow undefined spreadId initially
  const activeSpread = renderedSpreads[currentRenderedIndex];
  // Removed previewTemplateId logic
  const currentProps = { spreadId: activeSpread?.id, templateId: activeSpread?.templateId ?? null };

  const isTemplateChange = useMemo(() => {
    const prevProps = prevPropsRef.current;
    const animate = prevProps?.spreadId === currentProps.spreadId && prevProps?.templateId !== currentProps.templateId;
    return !!animate; // Ensure boolean return
  }, [currentProps.spreadId, currentProps.templateId]);

  useEffect(() => {
    // Update ref only if spreadId is valid
    if (currentProps.spreadId) {
       prevPropsRef.current = { spreadId: currentProps.spreadId, templateId: currentProps.templateId };
    }
  }, [currentProps.spreadId, currentProps.templateId]); // Update when these change

  // --- Render Logic ---

  // Effect to calculate bleedInPixels when spread dimensions or bleedValue change
  useEffect(() => {
    if (spreadRenderContainerRef.current && spreadDimensionsPt.width > 0 && bleedValue > 0) {
      const containerWidthPx = spreadRenderContainerRef.current.offsetWidth;
      const totalSpreadWidthPt = spreadDimensionsPt.width * 2;
      if (totalSpreadWidthPt > 0) {
        const pixelsPerPoint = containerWidthPx / totalSpreadWidthPt;
        setBleedInPixels(bleedValue * pixelsPerPoint);
      } else {
        setBleedInPixels(0);
      }
    } else {
      setBleedInPixels(0);
    }
  }, [spreadDimensionsPt.width, bleedValue, spreadRenderContainerRef.current?.offsetWidth]);

  // Effect to calculate safetyMarginInPixels when spread dimensions or safetyMarginValuePt change
  useEffect(() => {
    if (spreadRenderContainerRef.current && spreadDimensionsPt.width > 0 && safetyMarginValuePt > 0) {
      const containerWidthPx = spreadRenderContainerRef.current.offsetWidth;
      const totalSpreadWidthPt = spreadDimensionsPt.width * 2; // Assuming full spread width for calculation consistency
      if (totalSpreadWidthPt > 0) {
        const pixelsPerPoint = containerWidthPx / totalSpreadWidthPt;
        setSafetyMarginInPixels(safetyMarginValuePt * pixelsPerPoint);
      } else {
        setSafetyMarginInPixels(0);
      }
    } else {
      setSafetyMarginInPixels(0);
    }
  }, [spreadDimensionsPt.width, safetyMarginValuePt, spreadRenderContainerRef.current?.offsetWidth]);

  // Effect to calculate pixelsPerPoint when spread dimensions or container size change
  useEffect(() => {
    if (spreadRenderContainerRef.current && spreadDimensionsPt.width > 0) {
      const containerWidthPx = spreadRenderContainerRef.current.offsetWidth;
      const totalSpreadWidthPt = spreadDimensionsPt.width * 2; // Assuming full spread width for calculation consistency
      if (totalSpreadWidthPt > 0 && containerWidthPx > 0) {
        setPixelsPerPoint(containerWidthPx / totalSpreadWidthPt);
      } else {
        setPixelsPerPoint(1); // Default to 1 if dimensions are invalid to avoid 0 or NaN
      }
    } else {
      setPixelsPerPoint(1); // Default to 1 if container ref or dimensions are not ready
    }
  }, [spreadDimensionsPt.width, spreadRenderContainerRef.current?.offsetWidth]);


  return (
    <div
      ref={canvasContainerRef}
      data-spread-canvas="true"
      tabIndex={0} // Make it focusable
      className={cn(
        "flex-1 flex items-center justify-center relative p-4 outline-none",
        theme === 'dark' ? 'bg-neutral-800 text-neutral-100' : 'bg-gray-100 text-black'
      )}
      onFocus={handleFocus} // Use the new handler
      onBlur={handleBlur}   // Use the new handler
      onWheel={handleWheelScroll} // Keep existing wheel handler
      // Add drop handlers if they were removed previously
      onDragEnter={handleDragEnter} // Add drag enter handler
      onDragEnd={handleDragEnd} // Add drag end handler
      onDragOver={(e) => {
        e.preventDefault(); // Basic drag over for canvas drop
        // Detect OS file drag and show rejection message
        if (e.dataTransfer.types.includes('Files') && !rejectionMessage) {
          setRejectionMessage("Please import images to the Photo Library first");
          setTimeout(() => setRejectionMessage(null), 3000);
        }
      }}
      onDragLeave={(e) => {
        // Clear rejection message when dragging away from canvas
        if (rejectionMessage && !e.currentTarget.contains(e.relatedTarget as Node)) {
          setRejectionMessage(null);
        }
      }}
      onDrop={handleCanvasDrop} // Use existing canvas drop handler
    >
      {/* Snap Guides Overlay - Only show within spread render area */}
      {(isResizing || isMoving) && (snapGuides.gridVisible || snapGuides.vertical.length > 0 || snapGuides.horizontal.length > 0 || snapGuides.marginVertical.length > 0 || snapGuides.marginHorizontal.length > 0 || snapGuides.gutterVertical.length > 0 || snapGuides.gutterHorizontal.length > 0) && spreadRenderContainerRef.current && (
        <div 
          className="absolute pointer-events-none z-50"
          style={(() => {
            const spreadContainer = spreadRenderContainerRef.current;
            const canvasContainer = canvasContainerRef.current;
            if (!spreadContainer || !canvasContainer) return {};
            
            const spreadRect = spreadContainer.getBoundingClientRect();
            const canvasRect = canvasContainer.getBoundingClientRect();
            
            // Calculate position relative to canvas container
            const left = spreadRect.left - canvasRect.left;
            const top = spreadRect.top - canvasRect.top;
            const width = spreadRect.width;
            const height = spreadRect.height;
            
            return {
              left: `${left}px`,
              top: `${top}px`,
              width: `${width}px`,
              height: `${height}px`
            };
          })()}
        >
          {/* Grid Lines */}
          {snapGuides.gridVisible && (
            <>
              {/* Vertical Grid Lines */}
              {Array.from({ length: 9 }, (_, i) => i * 0.125).map(x => (
                <div
                  key={`grid-v-${x}`}
                  className="absolute top-0 bottom-0 w-px bg-cyan-400/40"
                  style={{ left: `${x * 100}%` }}
                />
              ))}
              {/* Horizontal Grid Lines */}
              {Array.from({ length: 9 }, (_, i) => i * 0.125).map(y => (
                <div
                  key={`grid-h-${y}`}
                  className="absolute left-0 right-0 h-px bg-cyan-400/40"
                  style={{ top: `${y * 100}%` }}
                />
              ))}
            </>
          )}
          
          {/* Vertical Snap Lines */}
          {snapGuides.vertical.map((x, index) => (
            <div
              key={`snap-v-${x}-${index}`}
              className="absolute top-0 bottom-0 w-px bg-orange-400 shadow-sm"
              style={{ left: `${x * 100}%` }}
            />
          ))}
          
          {/* Horizontal Snap Lines */}
          {snapGuides.horizontal.map((y, index) => (
            <div
              key={`snap-h-${y}-${index}`}
              className="absolute left-0 right-0 h-px bg-orange-400 shadow-sm"
              style={{ top: `${y * 100}%` }}
            />
          ))}
          
          {/* Margin Vertical Snap Lines (light purple for equal margin snapping) */}
          {snapGuides.marginVertical.map((x, index) => (
            <div
              key={`margin-snap-v-${x}-${index}`}
              className="absolute top-0 bottom-0 w-px bg-purple-400/60 shadow-sm"
              style={{ left: `${x * 100}%` }}
            />
          ))}
          
          {/* Margin Horizontal Snap Lines (light purple for equal margin snapping) */}
          {snapGuides.marginHorizontal.map((y, index) => (
            <div
              key={`margin-snap-h-${y}-${index}`}
              className="absolute left-0 right-0 h-px bg-purple-400/60 shadow-sm"
              style={{ top: `${y * 100}%` }}
            />
          ))}
          
          {/* Gutter Vertical Snap Lines (red for equal distance from spine/gutter) */}
          {snapGuides.gutterVertical.map((x, index) => (
            <div
              key={`gutter-snap-v-${x}-${index}`}
              className="absolute top-0 bottom-0 w-px bg-red-500/70 shadow-sm"
              style={{ left: `${x * 100}%` }}
            />
          ))}
          
          {/* Gutter Horizontal Snap Lines (red for equal distance from spine/gutter) */}
          {snapGuides.gutterHorizontal.map((y, index) => (
            <div
              key={`gutter-snap-h-${y}-${index}`}
              className="absolute left-0 right-0 h-px bg-red-500/70 shadow-sm"
              style={{ top: `${y * 100}%` }}
            />
          ))}
        </div>
      )}
      
      {/* Navigation Chevrons */}
      <>
        <button
          className={cn(
            "absolute left-0 top-1/2 transform -translate-y-1/2 transition-colors disabled:opacity-30 disabled:cursor-not-allowed z-10",
            theme === 'dark' ? 'text-gray-400 hover:text-gray-100' : 'text-gray-600 hover:text-gray-900'
          )}
          onClick={handlePrevious}
          disabled={currentSpreadNumber <= 1 && !hasCover} // Disable if it's the first spread AND there's no cover to go back to
          aria-label="Previous spread"
        >
          <ChevronLeft className="w-8 h-8" />
        </button>
        <button
          className={cn(
            "absolute right-0 top-1/2 transform -translate-y-1/2 transition-colors disabled:opacity-30 disabled:cursor-not-allowed z-10",
            theme === 'dark' ? 'text-gray-400 hover:text-gray-100' : 'text-gray-600 hover:text-gray-900'
          )}
          onClick={handleNext}
          disabled={false} // Next button is never disabled by cover logic itself
          aria-label="Next spread"
        >
          <ChevronRight className="w-8 h-8" />
        </button>
      </>

      {/* Focus Mode Button - Moved to BookEditor.tsx */}
      {/* Fit Mode Toggle Button Removed */}

      {/* Outer container for aspect ratio and centering */}
      <div
        ref={spreadRenderContainerRef} // <<< ASSIGN REF HERE
        className="spread-container relative overflow-hidden" // Renamed class for clarity
        // Use spreadDimensionsPt for aspect ratio
        // Calculate spread aspect ratio (double width for two pages)
        style={{
          aspectRatio: `${(spreadDimensionsPt.width * 2) || 2} / ${spreadDimensionsPt.height || 1}`,
          width: 'calc(100% - 2rem)',
          maxWidth: '100%',
          maxHeight: 'calc(100% - 2rem)',
          overflow: 'hidden',
          willChange: 'transform',
          // In dark mode, apply white background first, then project color on top
          background: userSettings.theme === 'dark' 
            ? `linear-gradient(${backgroundColor}, ${backgroundColor}), white`
            : backgroundColor,
          padding: bleedInPixels > 0 ? `${bleedInPixels}px` : '0px',
          boxSizing: 'border-box',
          // Apply clip-path when hiding bleed to make outer 3px transparent on all sides to prevent hairlines
          ...(bleedAreaMode === 'hide' && bleedInPixels > 0 && {
            clipPath: `inset(3px 3px 3px 3px)`
          }),
          // Apply shadow for all modes except hide (which has its own shadow element)
          ...(bleedAreaMode !== 'hide' && {
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
          })
        }}
        onDragOver={(e) => {
          e.preventDefault();
          if (e.dataTransfer.types.includes('application/x-bookproofs-template') || e.dataTransfer.types.includes('application/json')) {
            e.dataTransfer.dropEffect = 'copy';
          } else {
            e.dataTransfer.dropEffect = 'none';
          }
        }}
        onDragEnter={(e) => {
          if (e.dataTransfer.types.includes('application/json')) {
            setIsDraggingImageOverCanvas(true);
          }
          setIsCanvasHovered(true); // Keep normal hover behavior as well
        }}
        onDragLeave={(e) => {
          // Check if the mouse is truly leaving the component or just moving over a child
          const currentTarget = e.currentTarget as HTMLDivElement;
          if (!currentTarget.contains(e.relatedTarget as Node)) {
            setIsDraggingImageOverCanvas(false);
            setIsCanvasHovered(false);
          }
        }}
        onDrop={handleCanvasDrop}
        onWheel={handleWheelScroll} // Also handle wheel on the spread container itself
        onMouseEnter={() => setIsCanvasHovered(true)}
        onMouseLeave={() => {
          // Only set hover to false if not currently dragging an image over
          if (!isDraggingImageOverCanvas) {
            setIsCanvasHovered(false);
          }
        }}
      >
        {/* Text Editor Layer - MOVED INSIDE EACH SPREAD'S CONTENT FADER */}
        {/* Inner "Track" div for sliding spreads - Now using motion.div */}
        <motion.div
          className="absolute top-0 left-0 h-full flex" // Removed transition classes
          style={{ width: `${renderedSpreads.length * 100}%`, overflow: 'visible' }} // Removed transform and willChange
          animate={{ x: `-${currentRenderedIndex * (100 / renderedSpreads.length)}%` }} // Animate x position
          transition={{ duration: 0.3, ease: "easeInOut" }} // Define transition properties
        >
          {/* Map over renderedSpreads */}
          {renderedSpreads.map((spread) => {
            // Get the layout for this specific spread from the map, with custom template override
            let layoutForThisSpread = allSpreadLayouts[spread.id] || []; // Ensure it's always an array
            
            // If we have a custom template for this spread, use it instead
            if (customTemplatesState[spread.id]) {
              const customLayout = customTemplatesState[spread.id];
              
              // Use custom gap adjustment function that preserves positioning
              layoutForThisSpread = adjustCustomTemplateForGap(
                customLayout,
                imageGap,
                spreadDimensionsPt.width,
                spreadDimensionsPt.height
              );
              
            }
            const isCurrentActiveSpread = spread.id === currentSpreadId; // Still needed for the blank state message

            const isActive = spread.id === currentSpreadId;
            // Style applied to ALL spread containers (active and inactive)
            // They remain in the normal flow of the track (position: relative implied by flex item)
            const spreadContainerStyle: React.CSSProperties = {
              position: 'relative', // Ensure it's relative for absolute children like centerline
              width: `${100 / renderedSpreads.length}%`,
              height: '100%',
              overflow: 'clip', // Use clip for both active and inactive to contain layout
              clipPath: 'inset(0)', // Apply clipPath to both
              // transform: 'translateX(0.25px)', // Apply slight transform to both (if needed) <-- REMOVED
              background: 'transparent', // Explicit background
            };

            return (
              // Each spread container
              // Apply the base style, className might not be needed if all styles are inline
              <div
                key={spread.id}
                style={spreadContainerStyle}
                ref={el => spreadContainerRefs.current[spread.id] = el} // Assign ref
              >
                {/* Project Background Image Layer (now per spread) */}
                {projectBackgroundImage && spreadContainerRefs.current[spread.id] && (() => {
                  const currentSpreadContainer = spreadContainerRefs.current[spread.id];
                  if (!currentSpreadContainer) return null;

                  const containerW = currentSpreadContainer.offsetWidth;
                  const containerH = currentSpreadContainer.offsetHeight;
                  
                  let imageFile: ImageFile | undefined;
                  const bgPathFromProps = projectBackgroundImagePath;
                  const imagesFromProps = images;
                  const bgDisplayUrl = projectBackgroundImage;

                  if (bgPathFromProps && !bgPathFromProps.startsWith('http:') && !bgPathFromProps.startsWith('data:') && !bgPathFromProps.startsWith('blob:')) {
                    imageFile = imagesFromProps.find(img => img.originalPath === bgPathFromProps);
                  }
                  
                  if (!imageFile && bgDisplayUrl) {
                    const derivedPath = getOriginalPathFromUrl(bgDisplayUrl);
                    if (derivedPath) {
                      imageFile = imagesFromProps.find(img => img.originalPath === derivedPath);
                    }
                  }
                  
                  const fallbackNaturalWidth = spreadDimensionsPt.width * 2;
                  const fallbackNaturalHeight = spreadDimensionsPt.height;

                  const imgNaturalWidth = imageFile?.naturalWidth || fallbackNaturalWidth;
                  const imgNaturalHeight = imageFile?.naturalHeight || fallbackNaturalHeight;

                  if (containerW <= 0 || containerH <= 0 || imgNaturalWidth <= 0 || imgNaturalHeight <= 0) {
                    return (
                      <img
                        key={`bg-${spread.id}-fallback`}
                        src={normalizeBackgroundImageUrl(projectBackgroundImage)}
                        alt="Project Background"
                        className="absolute inset-0 w-full h-full object-cover pointer-events-none"
                        style={{
                          opacity: projectBackgroundImageOpacity !== undefined ? projectBackgroundImageOpacity : 1,
                          zIndex: -1
                        }}
                        draggable={false}
                      />
                    );
                  }

                  const scale = backgroundImageZoom;
                  const focalX = backgroundImagePanX / 100;
                  const focalY = backgroundImagePanY / 100;

                  const imageRatio = imgNaturalWidth / imgNaturalHeight;
                  const containerRatio = containerW / containerH;

                  let baseRenderedW: number;
                  let baseRenderedH: number;

                  if (imageRatio > containerRatio) {
                    baseRenderedH = containerH;
                    baseRenderedW = baseRenderedH * imageRatio;
                  } else {
                    baseRenderedW = containerW;
                    baseRenderedH = baseRenderedW / imageRatio;
                  }
                  
                  const scaledW = baseRenderedW * scale;
                  const scaledH = baseRenderedH * scale;
                  
                  const calculatedOffsetX = (containerW / 2) - (scaledW * focalX);
                  const calculatedOffsetY = (containerH / 2) - (scaledH * focalY);

                  return (
                    <img
                      key={`bg-${spread.id}-${projectBackgroundImage}`} // Keyed by the original prop to trigger Data URL refetch
                      src={displayableProjectBackgroundSrc || undefined} // Use Data URL; undefined src if null
                      alt="Project Background"
                      className="absolute max-w-none max-h-none pointer-events-none"
                      style={{
                        width: `${scaledW}px`,
                        height: `${scaledH}px`,
                        left: `${calculatedOffsetX}px`,
                        top: `${calculatedOffsetY}px`,
                        opacity: projectBackgroundImageOpacity !== undefined ? projectBackgroundImageOpacity : 1,
                        willChange: 'width, height, top, left, opacity',
                        zIndex: -1
                      }}
                      draggable={false}
                      onError={(e) => {
                      }}
                    />
                  );
                })()}
                {/* Real drop shadow behind canvas content area when hiding bleed */}
                {bleedInPixels > 0 && bleedAreaMode === 'hide' && (
                  <div 
                    style={{ 
                      position: 'absolute', 
                      top: `${bleedInPixels}px`, 
                      left: `${bleedInPixels}px`, 
                      right: `${bleedInPixels}px`, 
                      bottom: `${bleedInPixels}px`,
                      backgroundColor: 'transparent',
                      boxShadow: '0 10px 15px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.15)',
                      zIndex: 6, // Above hide overlays to be visible
                      pointerEvents: 'none'
                    }} 
                  />
                )}
                
                {/* Bleed HIDING overlays (always apply if mode is 'hide' and bleed exists) */}
                 {bleedInPixels > 0 && bleedAreaMode === 'hide' && (
                  <>
                    <div style={{ position: 'absolute', top: 0, left: 0, right: 0, height: `${bleedInPixels}px`, background: theme === 'dark' ? 'rgb(39 39 42)' : '#f3f4f6', zIndex: 5, pointerEvents: 'none' }} />
                    <div style={{ position: 'absolute', bottom: 0, left: 0, right: 0, height: `${bleedInPixels}px`, background: theme === 'dark' ? 'rgb(39 39 42)' : '#f3f4f6', zIndex: 5, pointerEvents: 'none' }} />
                    <div style={{ position: 'absolute', top: `${bleedInPixels}px`, bottom: `${bleedInPixels}px`, left: 0, width: `${bleedInPixels}px`, background: theme === 'dark' ? 'rgb(39 39 42)' : '#f3f4f6', zIndex: 5, pointerEvents: 'none' }} />
                    <div style={{ position: 'absolute', top: `${bleedInPixels}px`, bottom: `${bleedInPixels}px`, right: 0, width: `${bleedInPixels}px`, background: theme === 'dark' ? 'rgb(39 39 42)' : '#f3f4f6', zIndex: 5, pointerEvents: 'none' }} />
                  </>
                )}
                {/* Bleed INDICATION overlays (always apply if mode is 'indicate' and bleed exists) */}
                {bleedInPixels > 0 && bleedAreaMode === 'indicate' && (
                  <>
                    <div style={{ position: 'absolute', top: 0, left: 0, right: 0, height: `${bleedInPixels}px`, background: 'rgba(255, 255, 255, 0.65)', zIndex: 5, pointerEvents: 'none' }} />
                    <div style={{ position: 'absolute', bottom: 0, left: 0, right: 0, height: `${bleedInPixels}px`, background: 'rgba(255, 255, 255, 0.65)', zIndex: 5, pointerEvents: 'none' }} />
                    <div style={{ position: 'absolute', top: `${bleedInPixels}px`, bottom: `${bleedInPixels}px`, left: 0, width: `${bleedInPixels}px`, background: 'rgba(255, 255, 255, 0.65)', zIndex: 5, pointerEvents: 'none' }} />
                    <div style={{ position: 'absolute', top: `${bleedInPixels}px`, bottom: `${bleedInPixels}px`, right: 0, width: `${bleedInPixels}px`, background: 'rgba(255, 255, 255, 0.65)', zIndex: 5, pointerEvents: 'none' }} />
                  </>
                )}

                {/* TRIM LINES (only for active spread) */}
                {bleedInPixels > 0 && isActive && displayTrimLines && (
                  <>
                    <div style={{ position: 'absolute', top: `${bleedInPixels}px`, left: `${bleedInPixels}px`, right: `${bleedInPixels}px`, height: '1px', backgroundColor: 'rgba(0, 255, 255, 0.7)', zIndex: 10, pointerEvents: 'none' }} />
                    <div style={{ position: 'absolute', bottom: `${bleedInPixels}px`, left: `${bleedInPixels}px`, right: `${bleedInPixels}px`, height: '1px', backgroundColor: 'rgba(0, 255, 255, 0.7)', zIndex: 10, pointerEvents: 'none' }} />
                    <div style={{ position: 'absolute', left: `${bleedInPixels}px`, top: `${bleedInPixels}px`, bottom: `${bleedInPixels}px`, width: '1px', backgroundColor: 'rgba(0, 255, 255, 0.7)', zIndex: 10, pointerEvents: 'none' }} />
                    <div style={{ position: 'absolute', right: `${bleedInPixels}px`, top: `${bleedInPixels}px`, bottom: `${bleedInPixels}px`, width: '1px', backgroundColor: 'rgba(0, 255, 255, 0.7)', zIndex: 10, pointerEvents: 'none' }} />
                  </>
                )}
                {/* State 3: Ignore Bleed - no visualization needed (implicitly handled if neither 'hide' nor 'indicate' and not drawing trim lines for active) */}
                {/* Safety Margin Lines */}
                {showSafetyMargin && safetyMarginInPixels > 0 && isActive && (
                  <>
                    {/* Top safety margin line: offset by bleedInPixels + safetyMarginInPixels from the very top of the padded container */}
                    <div style={{ position: 'absolute', top: `${bleedInPixels + safetyMarginInPixels}px`, left: `${bleedInPixels + safetyMarginInPixels}px`, right: `${bleedInPixels + safetyMarginInPixels}px`, height: '1px', borderTop: '1px dotted rgba(0, 0, 255, 0.7)', zIndex: 10, pointerEvents: 'none' }} />
                    {/* Bottom safety margin line: offset by bleedInPixels + safetyMarginInPixels from the very bottom of the padded container */}
                    <div style={{ position: 'absolute', bottom: `${bleedInPixels + safetyMarginInPixels}px`, left: `${bleedInPixels + safetyMarginInPixels}px`, right: `${bleedInPixels + safetyMarginInPixels}px`, height: '1px', borderTop: '1px dotted rgba(0, 0, 255, 0.7)', zIndex: 10, pointerEvents: 'none' }} />
                    {/* Left safety margin line */}
                    <div style={{ position: 'absolute', left: `${bleedInPixels + safetyMarginInPixels}px`, top: `${bleedInPixels + safetyMarginInPixels}px`, bottom: `${bleedInPixels + safetyMarginInPixels}px`, width: '1px', borderLeft: '1px dotted rgba(0, 0, 255, 0.7)', zIndex: 10, pointerEvents: 'none' }} />
                    {/* Right safety margin line */}
                    <div style={{ position: 'absolute', right: `${bleedInPixels + safetyMarginInPixels}px`, top: `${bleedInPixels + safetyMarginInPixels}px`, bottom: `${bleedInPixels + safetyMarginInPixels}px`, width: '1px', borderLeft: '1px dotted rgba(0, 0, 255, 0.7)', zIndex: 10, pointerEvents: 'none' }} />
                  </>
                )}

                {/* Render SpreadBackgroundManager for this spread (displays existing background and controls) */}
                {isActive && spread.spreadBackgroundData?.imagePath && (
                  <SpreadBackgroundManager
                    spreadId={spread.id}
                    backgroundData={spread.spreadBackgroundData || null}
                    aspectRatioWidthPt={spreadDimensionsPt.width * 2} // Full spread width
                    aspectRatioHeightPt={spreadDimensionsPt.height}
                    onUpdateBackground={onUpdateSpreadBackground}
                    onSetBackgroundFromImageFile={(imageFile) => {
                      // Ensure we're passing an ImageFile object
                      if (typeof imageFile === 'string') {
                        return;
                      }
                      onSetSpreadBackgroundFromImageFile(spread.id, imageFile);
                    }}
                    onApplyBackgroundToAll={onApplyBackgroundToAll ? () => onApplyBackgroundToAll(spread.id) : undefined}
                    onClearAllBackgrounds={onClearAllBackgrounds}
                    isBackgroundEditingActive={isEditingBackgroundForSpreadId === spread.id} // Pass editing state
                    onToggleBackgroundEditMode={onToggleBackgroundEditMode} // Pass the toggle function
                    showQualityIndicators={showQualityIndicators} // Pass quality indicators setting
                    dpiWarningThresholdPercent={dpiWarningThresholdPercent} // Pass DPI threshold
                    onRegenerateBackgroundPreview={handleRegenerateBackgroundPreview} // Pass the new handler
                    images={images} // Pass images array for original dimension lookup
                  />
                )}

                {/* Background Drop Overlays - Only when in background edit mode */}
                {isActive && isEditingBackgroundForSpreadId === spread.id && (
                  <>
                    {/* Case 1: Full overlay when no background exists */}
                    {!spread.spreadBackgroundData?.imagePath && (
                      <div
                        className="absolute inset-0 z-30 bg-gray-500 flex items-center justify-center transition-all duration-200"
                        onDragEnter={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onDragOver={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          if (e.dataTransfer.types.includes('application/json')) {
                            e.dataTransfer.dropEffect = 'copy';
                          } else {
                            e.dataTransfer.dropEffect = 'none';
                          }
                        }}
                        onDragLeave={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onDrop={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          if (e.dataTransfer.types.includes('application/json')) {
                            try {
                              const jsonData = e.dataTransfer.getData('application/json');
                              const droppedData = JSON.parse(jsonData);
                              const imageFile = Array.isArray(droppedData) ? droppedData[0] : droppedData;
                              
                              if (imageFile && imageFile.originalPath && imageFile.id && onSetSpreadBackgroundFromImageFile) {
                                onSetSpreadBackgroundFromImageFile(spread.id, imageFile);
                                // Stay in edit mode (user is already in edit mode since overlay is visible)
                                // No need to toggle - we want to remain in edit mode
                              }
                            } catch (error) {
                              console.error('Background drop error:', error);
                            }
                          }
                        }}
                      >
                        <div className="text-center text-white font-medium">
                          <ImagePlus className="w-12 h-12 mx-auto mb-2" />
                          <div className="text-lg">Drop image to set background</div>
                        </div>
                      </div>
                    )}

                    {/* Case 2: Small corner drop zone when background exists */}
                    {spread.spreadBackgroundData?.imagePath && (
                      <div
                        className={`absolute top-4 right-4 z-30 w-16 h-16 border-2 border-dashed border-gray-400 rounded-lg flex items-center justify-center backdrop-blur-sm transition-all duration-200 ${
                          isCornerDropZoneDraggedOver ? 'bg-gray-500/50' : 'bg-gray-500/20'
                        }`}
                        title="Drop image to replace background"
                        onDragEnter={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          if (e.dataTransfer.types.includes('application/json')) {
                            setIsCornerDropZoneDraggedOver(true);
                          }
                        }}
                        onDragOver={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          if (e.dataTransfer.types.includes('application/json')) {
                            e.dataTransfer.dropEffect = 'copy';
                          } else {
                            e.dataTransfer.dropEffect = 'none';
                          }
                        }}
                        onDragLeave={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const currentTarget = e.currentTarget as HTMLElement;
                          if (!currentTarget.contains(e.relatedTarget as Node)) {
                            setIsCornerDropZoneDraggedOver(false);
                          }
                        }}
                        onDrop={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setIsCornerDropZoneDraggedOver(false);
                          if (e.dataTransfer.types.includes('application/json')) {
                            try {
                              const jsonData = e.dataTransfer.getData('application/json');
                              const droppedData = JSON.parse(jsonData);
                              const imageFile = Array.isArray(droppedData) ? droppedData[0] : droppedData;
                              
                              if (imageFile && imageFile.originalPath && imageFile.id && onSetSpreadBackgroundFromImageFile) {
                                onSetSpreadBackgroundFromImageFile(spread.id, imageFile);
                                // Note: Don't auto-enter edit mode for replacement, user is already in edit mode
                              }
                            } catch (error) {
                              console.error('Background drop error:', error);
                            }
                          }
                        }}
                      >
                        <ImagePlus className="w-6 h-6 text-gray-600 opacity-60" />
                      </div>
                    )}
                  </>
                )}

                {/* Determine if the spread has any images actually placed (not just placeholders) */}
                {!spread.images.some(img => img.imageId) ? (
                  // Center line for blank/template spreads - Traditional gutter
                  <div className="absolute top-0 bottom-0 left-1/2 border-l border-gray-200 opacity-45" style={{ transform: 'translateX(-50%)' }} />
                ) : (
                  // White overlay gutter for spreads with images
                  // Add data-no-print attribute to exclude from PDF export
                  // Only show if showBookGutter setting is true
                  showBookGutter ? (
                    <div
                      className="absolute top-0 bottom-0 left-1/2 bg-white/45"
                      style={{
                        transform: 'translateX(-50%)',
                        width: '1px', // Narrower white overlay (1px)
                        zIndex: 3, // Above images (zIndex: -1 to 5) but below hide bleed overlay (zIndex: 5)
                      }}
                      data-no-print="true" // Mark this element to be excluded from PDF export
                    />
                  ) : null
                )}

                {/* Conditionally render placeholders/content ONLY if active, wrapped in AnimatePresence */}
                <AnimatePresence mode="wait">
                  {isActive && (
                    <motion.div
                      key={spread.id + "-content"} // Unique key for AnimatePresence
                      className="w-full h-full absolute inset-0" // Position wrapper to fill parent
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }} // Faster fade for content
                    >
                      {/* Render TextEditor HERE for the active spread */}
                      {spreadRenderContainerRef.current && spreadDimensionsPt.width > 0 && spreadDimensionsPt.height > 0 && (
                        <TextEditor
                          textOverlays={textOverlays} // Pass all overlays
                          currentSpreadId={spread.id} // Pass the ID of THIS spread
                          onUpdateTextOverlay={onUpdateTextOverlay}
                          onSelectTextOverlay={onSelectTextOverlay}
                          containerRef={spreadRenderContainerRef} // Ref to the overall spread display area
                          spreadDimensionsPt={spreadDimensionsPt}
                          textControlsBarRef={textControlsBarRef}
                          navigationDirection={navigationDirection} // For TextEditor's internal subtle animations
                        />
                      )}

                      {layoutForThisSpread.length > 0 ? (
                        layoutForThisSpread.map((adjustedPlaceholder) => {
                          // Skip rendering foreground images when in background edit mode
                          const isBackgroundEditMode = isEditingBackgroundForSpreadId === spread.id;
                          const imageAssignment = spread.images.find(img => img.placeholderId === adjustedPlaceholder.id);
                          const image = imageAssignment?.imageId ? images.find(img => img.id === imageAssignment.imageId) : null;
                          
                          // Define the default fit mode based on the prop
                          const defaultFitMode = defaultDropModeIsCover ? 'cover' : 'contain';
                          // Use the assigned transform or fall back to a default object using defaultFitMode
                          const transformToApply = imageAssignment?.transform ?? { scale: 1, focalX: 0.5, focalY: 0.5, fit: defaultFitMode };
                          // Ensure all properties are present, using defaultFitMode as the final fallback
                          const transform: ImageTransform = {
                              scale: transformToApply.scale ?? 1,
                              focalX: transformToApply.focalX ?? 0.5,
                              focalY: transformToApply.focalY ?? 0.5,
                              fit: transformToApply.fit ?? defaultFitMode, // Use defaultFitMode here
                              rotation: transformToApply.rotation ?? 0
                          };
                          
                          const fitMode = transform.fit; // Get fit mode for styling
                          const isDraggedOver = dragOverId === adjustedPlaceholder.id; // No need to check isCurrentActiveSpread again

                    // Calculate percentage styles from point values
                    const placeholderStyle: React.CSSProperties = {
                      position: 'absolute',
                      left: `${(adjustedPlaceholder.adjustedX_pt / spreadDimensionsPt.width) * 100}%`,
                      top: `${(adjustedPlaceholder.adjustedY_pt / spreadDimensionsPt.height) * 100}%`,
                      width: `${(adjustedPlaceholder.adjustedWidth_pt / spreadDimensionsPt.width) * 100}%`,
                      height: `${(adjustedPlaceholder.adjustedHeight_pt / spreadDimensionsPt.height) * 100}%`,
                      overflow: 'hidden', // Ensure content doesn't overflow
                      contain: 'paint', // Improve performance by isolating paint
                    };

                    return (
                      // Single Placeholder Div
                      <div
                        id={`placeholder-${adjustedPlaceholder.id}`}
                        key={adjustedPlaceholder.id}
                        data-placeholder-id={adjustedPlaceholder.id}
                        className={`absolute ${!image ? 'bg-gray-100' : 'bg-transparent'} ${isDraggedOver ? 'bg-blue-100' : ''} ${(() => {
                          const isBeingMoved = isMoving && moveData?.placeholdersToMove?.some(p => p.id === adjustedPlaceholder.id);
                          const isPrimaryMoveTarget = isMoving && moveData?.placeholderId === adjustedPlaceholder.id;
                          const isInSelection = selectedImages.has(adjustedPlaceholder.id);
                          
                          if (isBeingMoved) {
                            return isPrimaryMoveTarget 
                              ? 'cursor-grabbing border-2 border-orange-400' 
                              : 'cursor-grabbing border-2 border-orange-300';
                          } else if (isOptionPressed && (isInSelection || !selectedImages.size)) {
                            return 'cursor-move border-2 border-dashed border-orange-300';
                          }
                          return '';
                        })()} transition-all duration-300 ease-in-out group`}
                        style={placeholderStyle}
                        onDragOver={(e) => handleDragOver(e, adjustedPlaceholder.id)} // Only active spread is rendered, so no need for check
                        onDragLeave={handleDragLeave} // Only active spread is rendered
                        onDrop={(e) => handleDrop(e, adjustedPlaceholder.id)} // Only active spread is rendered
                        onMouseDown={(e) => handleMoveStart(e, adjustedPlaceholder.id)} // Add move functionality
                        ref={el => { placeholderContainerRefs.current[adjustedPlaceholder.id] = el; }} // Simpler ref assignment
                      >
                        {/* Image rendering logic - don't render images in background edit mode */}
                        {image && !isBackgroundEditMode ? (() => {
                          // Determine if loading state should be shown (now always onPlacement mode)
                          const isLoadingPreview = !image.previewUrl;
                          // Determine the source URL: Preview > Thumbnail > Original Path
                          const imageSrcUrl = image.previewUrl ?? image.thumbnailUrl ?? image.originalPath;

                          return (
                            <ContextMenu>
                              <ContextMenuTrigger asChild>
                                {/* This inner div handles events and contains image/controls */}
                                {/* Added 'group' class */}
                                <div
                                  className={cn(
                                    "group w-full h-full overflow-hidden relative bg-transparent", // Always transparent background
                                    // Apply cursor based on state, only if not currently panning
                                    // REMOVED: !isPanning && placeholderCursor[adjustedPlaceholder.id] === 'move' && 'cursor-move',
                                    !isPanning && placeholderCursor[adjustedPlaceholder.id] === 'grab' && 'cursor-grab',
                                  !isPanning && placeholderCursor[adjustedPlaceholder.id] === 'default' && 'cursor-default', // Default cursor for draggable areas
                                  isPanning && 'cursor-grabbing', // Use grabbing cursor directly when panning
                                  'relative' // Enable positioning for border overlays
                                )}
                                // Draggable state is managed dynamically in mouse handlers
                                draggable={!!image && transform.fit === 'contain' && transform.scale === 1} // Set initial draggable based on scale AND fitMode
                                onDragStart={image ? (e) => handleImageDragStart(e, adjustedPlaceholder.id, image.id) : undefined} // Only attach if image exists
                                onDragEnd={handleImageDragEnd} // Add drag end handler
                                onMouseEnter={(e) => {
                                  if (isTextEditorActive) return;
                                  
                                  // Clear any pending hide timeout when re-entering placeholder
                                  if (hoverDelayTimeoutRef.current) {
                                    clearTimeout(hoverDelayTimeoutRef.current);
                                    hoverDelayTimeoutRef.current = null;
                                  }
                                  
                                  setHoveredPlaceholderId(adjustedPlaceholder.id);
                                  setHoveredImage(image);
                                  const currentScale = transform.scale ?? 1;
                                  const isZoomed = currentScale > 1;
                                  // Call updated hover change handler
                                  onHoverChange?.({ placeholderId: adjustedPlaceholder.id, isZoomed });
                                  // Set initial cursor state
                                  setPlaceholderCursor(prev => ({ ...prev, [adjustedPlaceholder.id]: currentScale === 1 ? 'default' : 'grab' }));

                                  // Onboarding Tooltip Trigger Logic
                                  if (userSettings.showOnboardingTooltips !== false) {
                                    // Derive a project-specific key. This is a simplification.
                                    // A dedicated projectId prop would be more robust.
                                    const projectKeyPart = currentSpreadId.split('-')[0] || 'defaultProject';
                                    const sessionDismissKey = `onboardingTooltipDismissed_${projectKeyPart}`;
                                    const dismissedForThisProjectSession = sessionStorage.getItem(sessionDismissKey);

                                    if (dismissedForThisProjectSession !== 'true') {
                                      setHoveredPlaceholderIdForTooltip(adjustedPlaceholder.id);
                                    }
                                  }
                                }}
                                onMouseLeave={(e) => {
                                  if (isTextEditorActive) return;
                                  handleImageMouseUpOrLeave(e); // Handles stopping pan AND resetting draggable/cursor
                                  
                                  // For small placeholders with popout, use a longer delay and check if mouse is actually leaving the area
                                  if (smallPlaceholders.has(adjustedPlaceholder.id) && !isPanning) {
                                    // Clear any existing timeout
                                    if (hoverDelayTimeoutRef.current) {
                                      clearTimeout(hoverDelayTimeoutRef.current);
                                    }
                                    
                                    // Only start timeout if mouse is actually leaving toward a different area
                                    // (not just micro-movements within the placeholder)
                                    hoverDelayTimeoutRef.current = setTimeout(() => {
                                      // Double-check conditions before hiding
                                      if (!isPopoutHovered && !isPanning && hoveredPlaceholderId === adjustedPlaceholder.id) {
                                        setHoveredPlaceholderId(null);
                                        setHoveredImage(null);
                                        onHoverChange?.({ placeholderId: null, isZoomed: false });
                                      }
                                    }, 50); // Increased to 500ms for more time to navigate
                                  } else if (!isPanning) {
                                    // For normal placeholders, hide immediately
                                    setHoveredPlaceholderId(null);
                                    setHoveredImage(null);
                                    onHoverChange?.({ placeholderId: null, isZoomed: false });
                                  }
                                  
                                  // Hide tooltip when mouse leaves
                                  setHoveredPlaceholderIdForTooltip(null);
                                }}
                                onMouseDown={(e) => {
                                  // Don't handle image mouse down if rotation is active
                                  if (isRotating) return;
                                  
                                  // Handle multi-selection logic
                                  if (e.ctrlKey || e.metaKey) {
                                    // Toggle selection of this image
                                    e.preventDefault();
                                    setSelectedImages(prev => {
                                      const newSet = new Set(prev);
                                      if (newSet.has(adjustedPlaceholder.id)) {
                                        newSet.delete(adjustedPlaceholder.id);
                                      } else {
                                        newSet.add(adjustedPlaceholder.id);
                                      }
                                      return newSet;
                                    });
                                    return; // Don't proceed with normal mouse down
                                  } else if (e.button !== 2 && !selectedImages.has(adjustedPlaceholder.id)) {
                                    // Clear selection if clicking an unselected image without modifiers (but not on right-click)
                                    setSelectedImages(new Set());
                                  }
                                  
                                  // Debug log for right-clicks and capture selection state
                                  if (e.button === 2) {
                                    // Capture the selection state for the context menu
                                    contextMenuSelectionRef.current = {
                                      placeholderIds: Array.from(selectedImages),
                                      clickedPlaceholderId: adjustedPlaceholder.id
                                    };
                                  }
                                  
                                  handleImageMouseDown(e, adjustedPlaceholder.id);
                                }}
                                onMouseMove={(e) => { // Update cursor state on mouse move
                                    if (isTextEditorActive || isRotating) return; // Don't handle if rotating
                                    handleImageMouseMove(e); // Handle panning logic
                                    if (!isPanning) { // Only update cursor if not actively panning
                                        const currentScale = transform.scale ?? 1;
                                        // Check if the state allows panning (cover mode or zoomed contain)
                                        const isPannableState = true; // Always allow panning

                                        if (isPannableState) {
                                            const targetElement = e.currentTarget as HTMLDivElement;
                                            const rect = targetElement.getBoundingClientRect();
                                            const relativeY = e.clientY - rect.top;
                                            const containerHeight = targetElement.offsetHeight;
                                            const topQuarter = containerHeight / 4; // Changed from 1/3 to 1/4
                                            // Set cursor to default for top 1/4 (drag), grab for bottom 3/4 (pan)
                                            const newCursor = relativeY < topQuarter ? 'default' : 'grab';
                                            if (placeholderCursor[adjustedPlaceholder.id] !== newCursor) {
                                                setPlaceholderCursor(prev => ({ ...prev, [adjustedPlaceholder.id]: newCursor }));
                                            }
                                        } else { // Must be contain mode with scale 1
                                            // Ensure cursor is 'default'
                                            if (placeholderCursor[adjustedPlaceholder.id] !== 'default') {
                                                setPlaceholderCursor(prev => ({ ...prev, [adjustedPlaceholder.id]: 'default' }));
                                            }
                                        }
                                    }
                                }}
                                onMouseUp={handleImageMouseUpOrLeave}
                              >
                                {(() => {
                                  // For contain mode with borders and rotation, create border-aware clipping
                                  const hasRotation = (transform.rotation || 0) !== 0;
                                  
                                  // Calculate if borders should render (same logic as border rendering section)
                                  const placeholderIndividualBorder = individualBorderSettings[adjustedPlaceholder.id];
                                  const useIndividualBorder = !!placeholderIndividualBorder?.enabled;
                                  const currentBorderSizePt = useIndividualBorder ? (placeholderIndividualBorder.size ?? 0) : localImageBorderSize;
                                  const borderThicknessPx = currentBorderSizePt * pixelsPerPoint;
                                  const shouldRenderBorder = borderThicknessPx > 0 && ((useIndividualBorder && currentBorderSizePt > 0) || (!useIndividualBorder && localImageBorderSize > 0));
                                  
                                  // Get container and image dimensions (same as border calculation)
                                  const containerDims = placeholderPixelDims[adjustedPlaceholder.id];
                                  const imageNativeDims = image ? getImageDimensions?.(image.id) : undefined;
                                  
                                  const hasBorders = shouldRenderBorder && transform.fit === 'contain';
                                  
                                  if (hasRotation && hasBorders && containerDims && imageNativeDims) {
                                    // Use same calculations as the image positioning to ensure proper alignment
                                    const { scale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain' } = transform;
                                    const containerW = containerDims.width;
                                    const containerH = containerDims.height;
                                    const imageW = imageNativeDims.width;
                                    const imageH = imageNativeDims.height;
                                    const imageRatio = imageW / imageH;
                                    const containerRatio = containerW / containerH;
                                    let baseRenderedW: number;
                                    let baseRenderedH: number;
                                    
                                    // Calculate base dimensions based on fit mode
                                    if (fit === 'contain') {
                                      if (imageRatio > containerRatio) { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; } else { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; }
                                    } else {
                                      if (imageRatio > containerRatio) { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; } else { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; }
                                    }
                                    
                                    const scaledW = baseRenderedW * scale;
                                    const scaledH = baseRenderedH * scale;
                                    const focalPointXOnImage = scaledW * focalX;
                                    const focalPointYOnImage = scaledH * focalY;
                                    const containerCenterX = containerW / 2;
                                    const containerCenterY = containerH / 2;
                                    const offsetX = containerCenterX - focalPointXOnImage;
                                    const offsetY = containerCenterY - focalPointYOnImage;
                                    
                                    // Calculate the same border values as in the border rendering logic
                                    const imageEdgeTolerancePx = pixelsPerPoint * 2.0;
                                    const gapLeftImageToPlaceholderPx = offsetX;
                                    const gapRightImageToPlaceholderPx = containerW - (offsetX + scaledW);
                                    const gapTopImageToPlaceholderPx = offsetY;
                                    const gapBottomImageToPlaceholderPx = containerH - (offsetY + scaledH);

                                    const topStuckToPlaceholder = gapTopImageToPlaceholderPx <= 0;
                                    const bottomStuckToPlaceholder = gapBottomImageToPlaceholderPx <= 0;
                                    const leftStuckToPlaceholder = gapLeftImageToPlaceholderPx <= 0;
                                    const rightStuckToPlaceholder = gapRightImageToPlaceholderPx <= 0;

                                    const topTransition = topStuckToPlaceholder ? 1 : 0;
                                    const bottomTransition = bottomStuckToPlaceholder ? 1 : 0;
                                    const leftTransition = leftStuckToPlaceholder ? 1 : 0;
                                    const rightTransition = rightStuckToPlaceholder ? 1 : 0;

                                    const imageLeft = Math.max(0, offsetX);
                                    const imageRight = Math.min(containerW, offsetX + scaledW);
                                    const imageTop = Math.max(0, offsetY);
                                    const imageBottom = Math.min(containerH, offsetY + scaledH);
                                    const effectiveImageWidth = imageRight - imageLeft;
                                    const effectiveImageHeight = imageBottom - imageTop;

                                    // Calculate clip boundaries at OUTSIDE border edges (border position + border thickness)
                                    const clipTop = (offsetY * (1 - topTransition) + 0 * topTransition);
                                    const clipLeft = (offsetX * (1 - leftTransition) + 0 * leftTransition);
                                    const clipRight = ((offsetX + scaledW) * (1 - rightTransition) + containerW * rightTransition);
                                    const clipBottom = ((offsetY + scaledH) * (1 - bottomTransition) + containerH * bottomTransition);
                                    
                                    const clipWidth = clipRight - clipLeft;
                                    const clipHeight = clipBottom - clipTop;

                                    return (
                                      <div
                                        style={{
                                          position: 'absolute',
                                          top: `${clipTop}px`,
                                          left: `${clipLeft}px`,
                                          width: `${clipWidth}px`,
                                          height: `${clipHeight}px`,
                                          overflow: 'hidden',
                                          pointerEvents: 'none'
                                        }}
                                      >
                                        <div 
                                          className="relative" 
                                          style={{ 
                                            position: 'absolute', 
                                            width: `${containerW}px`, 
                                            height: `${containerH}px`,
                                            left: `${-clipLeft}px`,
                                            top: `${-clipTop}px`,
                                            pointerEvents: 'none',
                                            transform: `rotate(${transform.rotation || 0}deg)`,
                                            transformOrigin: `${containerW/2}px ${containerH/2}px`
                                          }}
                                          data-rotation={transform.rotation || 0}
                                        >
                                          <img
                                            data-image-id={image.id}
                                            src={`${imageSrcUrl}${imageRefreshTimestamps[image.id] ? `?t=${imageRefreshTimestamps[image.id]}` : ''}`}
                                            alt={image.name}
                                            draggable={false}
                                            loading="eager"
                                            className={cn(
                                              "block pointer-events-none transition-opacity duration-300",
                                              (fitMode === 'cover' || (fitMode === 'contain' && (transform.scale ?? 1) > 1))
                                                ? "absolute max-w-none max-h-none"
                                                : "w-full h-full object-contain",
                                              "opacity-100"
                                            )}
                                            style={(() => {
                                              const calculateImageStyle = () => {
                                                const containerDims = placeholderPixelDims[adjustedPlaceholder.id];
                                                const imageNativeDims = image ? getImageDimensions?.(image.id) : undefined;
                                                if (!containerDims || !imageNativeDims || containerDims.width <= 0 || containerDims.height <= 0 || imageNativeDims.width <= 0 || imageNativeDims.height <= 0) {
                                                  return { position: 'absolute', width: '100%', height: '100%', top: '0', left: '0', objectFit: 'contain' } as React.CSSProperties;
                                                }
                                                const { scale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain', rotation = 0 } = transform;
                                                
                                                const containerW = containerDims.width;
                                                const containerH = containerDims.height;
                                                const imageW = imageNativeDims.width;
                                                const imageH = imageNativeDims.height;
                                                const imageRatio = imageW / imageH;
                                                const containerRatio = containerW / containerH;
                                                let baseRenderedW: number;
                                                let baseRenderedH: number;
                                                if (fit === 'contain') {
                                                  if (imageRatio > containerRatio) { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; } else { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; }
                                                } else {
                                                  if (imageRatio > containerRatio) { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; } else { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; }
                                                }
                                                const scaledW = baseRenderedW * scale;
                                                const scaledH = baseRenderedH * scale;
                                                const focalPointXOnImage = scaledW * focalX;
                                                const focalPointYOnImage = scaledH * focalY;
                                                const containerCenterX = containerW / 2;
                                                const containerCenterY = containerH / 2;
                                                const offsetX = containerCenterX - focalPointXOnImage;
                                                const offsetY = containerCenterY - focalPointYOnImage;
                                                const finalStyle = {
                                                  position: 'absolute',
                                                  width: `${scaledW}px`,
                                                  height: `${scaledH}px`,
                                                  left: `${offsetX}px`,
                                                  top: `${offsetY}px`,
                                                  willChange: 'width, height, top, left'
                                                } as React.CSSProperties;
                                                
                                                return finalStyle;
                                              };
                                              return calculateImageStyle();
                                            })()}
                                          />
                                          
                                          {/* Transparent overlay for selected state */}
                                          {selectedImages.has(adjustedPlaceholder.id) && (
                                            <div className="absolute inset-0 bg-black bg-opacity-20 pointer-events-none" />
                                          )}
                                        </div>
                                      </div>
                                    );
                                  } else {
                                    // Fallback for no rotation or no borders - use original structure
                                    return (
                                      <div 
                                        className="relative" 
                                        style={{ 
                                          position: 'absolute', 
                                          width: '100%', 
                                          height: '100%', 
                                          pointerEvents: 'none',
                                          transform: `rotate(${transform.rotation || 0}deg)`,
                                          transformOrigin: 'center center'
                                        }}
                                        data-rotation={transform.rotation || 0}
                                      >
                                        <img
                                          data-image-id={image.id}
                                          src={`${imageSrcUrl}${imageRefreshTimestamps[image.id] ? `?t=${imageRefreshTimestamps[image.id]}` : ''}`}
                                          alt={image.name}
                                          draggable={false}
                                          loading="eager"
                                          className={cn(
                                            "block pointer-events-none transition-opacity duration-300",
                                            (fitMode === 'cover' || (fitMode === 'contain' && (transform.scale ?? 1) > 1))
                                              ? "absolute max-w-none max-h-none"
                                              : "w-full h-full object-contain",
                                            "opacity-100"
                                          )}
                                          style={(() => {
                                            const calculateImageStyle = () => {
                                              const containerDims = placeholderPixelDims[adjustedPlaceholder.id];
                                              const imageNativeDims = image ? getImageDimensions?.(image.id) : undefined;
                                              if (!containerDims || !imageNativeDims || containerDims.width <= 0 || containerDims.height <= 0 || imageNativeDims.width <= 0 || imageNativeDims.height <= 0) {
                                                return { position: 'absolute', width: '100%', height: '100%', top: '0', left: '0', objectFit: 'contain' } as React.CSSProperties;
                                              }
                                              const { scale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain', rotation = 0 } = transform;
                                              
                                              const containerW = containerDims.width;
                                              const containerH = containerDims.height;
                                              const imageW = imageNativeDims.width;
                                              const imageH = imageNativeDims.height;
                                              const imageRatio = imageW / imageH;
                                              const containerRatio = containerW / containerH;
                                              let baseRenderedW: number;
                                              let baseRenderedH: number;
                                              if (fit === 'contain') {
                                                if (imageRatio > containerRatio) { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; } else { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; }
                                              } else {
                                                if (imageRatio > containerRatio) { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; } else { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; }
                                              }
                                              const scaledW = baseRenderedW * scale;
                                              const scaledH = baseRenderedH * scale;
                                              const focalPointXOnImage = scaledW * focalX;
                                              const focalPointYOnImage = scaledH * focalY;
                                              const containerCenterX = containerW / 2;
                                              const containerCenterY = containerH / 2;
                                              const offsetX = containerCenterX - focalPointXOnImage;
                                              const offsetY = containerCenterY - focalPointYOnImage;
                                              const finalStyle = {
                                                position: 'absolute',
                                                width: `${scaledW}px`,
                                                height: `${scaledH}px`,
                                                left: `${offsetX}px`,
                                                top: `${offsetY}px`,
                                                willChange: 'width, height, top, left'
                                              } as React.CSSProperties;
                                              
                                              
                                              return finalStyle;
                                            };
                                            return calculateImageStyle();
                                          })()}
                                        />
                                        
                                        {/* Transparent overlay for selected state */}
                                        {selectedImages.has(adjustedPlaceholder.id) && (
                                          <div className="absolute inset-0 bg-black bg-opacity-20 pointer-events-none" />
                                        )}
                                      </div>
                                    );
                                  }
                                })()}
                              {/* Border rendering moved outside rotation wrapper to prevent border rotation */}
                              {(() => {
                                // Get container and image dimensions
                                const containerDims = placeholderPixelDims[adjustedPlaceholder.id];
                                const imageNativeDims = image ? getImageDimensions?.(image.id) : undefined;
                                
                                // Determine border size and color based on individual or project settings
                                const placeholderIndividualBorder = individualBorderSettings[adjustedPlaceholder.id];
                                const useIndividualBorder = !!placeholderIndividualBorder?.enabled; // Ensure boolean

                                const currentBorderSizePt = useIndividualBorder ? (placeholderIndividualBorder.size ?? 0) : localImageBorderSize;
                                const currentBorderColor = useIndividualBorder ? (placeholderIndividualBorder.color ?? '#000000') : localImageBorderColor;
                                const borderThicknessPx = currentBorderSizePt * pixelsPerPoint;


                                // Condition to render any border
                                const shouldRenderBorder = borderThicknessPx > 0 && ((useIndividualBorder && currentBorderSizePt > 0) || (!useIndividualBorder && localImageBorderSize > 0));

                                if (!shouldRenderBorder) return null; // Early exit if no border should be rendered

                                if (!containerDims || !imageNativeDims || containerDims.width <= 0 || containerDims.height <= 0 || imageNativeDims.width <= 0 || imageNativeDims.height <= 0) {
                                  return null; // Cannot calculate border if dimensions are invalid
                                }
                                
                                // Use same calculations as the image positioning to ensure proper alignment
                                const { scale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain' } = transform;
                                const containerW = containerDims.width;
                                const containerH = containerDims.height;
                                const imageW = imageNativeDims.width;
                                const imageH = imageNativeDims.height;
                                const imageRatio = imageW / imageH;
                                const containerRatio = containerW / containerH;
                                let baseRenderedW: number;
                                let baseRenderedH: number;
                                
                                // Calculate base dimensions based on fit mode
                                if (fit === 'contain') {
                                  if (imageRatio > containerRatio) { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; } else { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; }
                                } else {
                                  if (imageRatio > containerRatio) { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; } else { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; }
                                }
                                
                                const scaledW = baseRenderedW * scale;
                                const scaledH = baseRenderedH * scale;
                                const focalPointXOnImage = scaledW * focalX;
                                const focalPointYOnImage = scaledH * focalY;
                                const containerCenterX = containerW / 2;
                                const containerCenterY = containerH / 2;
                                const offsetX = containerCenterX - focalPointXOnImage;
                                const offsetY = containerCenterY - focalPointYOnImage;
                                
                                // Common variables for both 'contain' and 'cover' border logic
                                const placeholderFullSpreadX_pt = adjustedPlaceholder.adjustedX_pt;
                                const placeholderFullSpreadY_pt = adjustedPlaceholder.adjustedY_pt;
                                const placeholderFullSpreadWidth_pt = adjustedPlaceholder.adjustedWidth_pt;
                                const placeholderFullSpreadHeight_pt = adjustedPlaceholder.adjustedHeight_pt;

                                const spreadTotalWidthPt = spreadDimensionsPt.width; // Single page width
                                const spreadTotalHeightPt = spreadDimensionsPt.height;
                                const edgeTolerancePt = 2.0; // Tolerance in points
                                const bleedIsActive = (bleedValue ?? 0) > 0;

                                // --- Determine if placeholder edges are near page edges ---
                                const isTopEdgeOutside = placeholderFullSpreadY_pt < edgeTolerancePt;
                                const isBottomEdgeOutside = (placeholderFullSpreadY_pt + placeholderFullSpreadHeight_pt) >= (spreadTotalHeightPt - edgeTolerancePt);
                                const isLeftEdgeOutside = placeholderFullSpreadX_pt < edgeTolerancePt;
                                const isRightEdgeOutside = (placeholderFullSpreadX_pt + placeholderFullSpreadWidth_pt) >= (spreadTotalWidthPt - edgeTolerancePt);

                                if (fit === 'contain') {
                                  const imageEdgeTolerancePx = pixelsPerPoint * 2.0;
                                  const gapLeftImageToPlaceholderPx = offsetX;
                                  const gapRightImageToPlaceholderPx = containerW - (offsetX + scaledW);
                                  const gapTopImageToPlaceholderPx = offsetY;
                                  const gapBottomImageToPlaceholderPx = containerH - (offsetY + scaledH);

                                  const isImageNearTopPlaceholderEdge = gapTopImageToPlaceholderPx < imageEdgeTolerancePx;
                                  const isImageNearBottomPlaceholderEdge = gapBottomImageToPlaceholderPx < imageEdgeTolerancePx;
                                  const isImageNearLeftPlaceholderEdge = gapLeftImageToPlaceholderPx < imageEdgeTolerancePx;
                                  const isImageNearRightPlaceholderEdge = gapRightImageToPlaceholderPx < imageEdgeTolerancePx;

                                  // Hybrid border calculation: each edge switches immediately from image-edge to placeholder-edge
                                  
                                  // Determine if each edge should stick to placeholder (immediate switch, no transition)
                                  const transitionThreshold = 5; // pixels - when image gets this close, border switches to placeholder
                                  
                                  // Special handling: only stick to placeholder when image edge is actually AT or BEYOND placeholder edge
                                  const topStuckToPlaceholder = gapTopImageToPlaceholderPx <= 0; // Image extends above or touches placeholder top
                                  const bottomStuckToPlaceholder = gapBottomImageToPlaceholderPx <= 0; // Image extends below or touches placeholder bottom  
                                  const leftStuckToPlaceholder = gapLeftImageToPlaceholderPx <= 0; // Image extends left or touches placeholder left
                                  const rightStuckToPlaceholder = gapRightImageToPlaceholderPx <= 0; // Image extends right or touches placeholder right

                                  // For transition calculations (0 = around image, 1 = stuck to placeholder)
                                  const topTransition = topStuckToPlaceholder ? 1 : 0;
                                  const bottomTransition = bottomStuckToPlaceholder ? 1 : 0;
                                  const leftTransition = leftStuckToPlaceholder ? 1 : 0;
                                  const rightTransition = rightStuckToPlaceholder ? 1 : 0;

                                  // Border visibility still respects bleed logic
                                  const showTopBorder = !(isTopEdgeOutside && topStuckToPlaceholder && bleedIsActive);
                                  const showBottomBorder = !(isBottomEdgeOutside && bottomStuckToPlaceholder && bleedIsActive);
                                  const showLeftBorder = !(isLeftEdgeOutside && leftStuckToPlaceholder && bleedIsActive);
                                  const showRightBorder = !(isRightEdgeOutside && rightStuckToPlaceholder && bleedIsActive);

                                  // Calculate effective image bounds within placeholder
                                  const imageLeft = Math.max(0, offsetX);
                                  const imageRight = Math.min(containerW, offsetX + scaledW);
                                  const imageTop = Math.max(0, offsetY);
                                  const imageBottom = Math.min(containerH, offsetY + scaledH);
                                  const effectiveImageWidth = imageRight - imageLeft;
                                  const effectiveImageHeight = imageBottom - imageTop;

                                  // Calculate border positions and dimensions for each edge
                                  
                                  // Top border: position transitions, width respects image bounds
                                  const topBorderTop = offsetY * (1 - topTransition) + 0 * topTransition;
                                  const topBorderLeft = offsetX * (1 - topTransition) + imageLeft * topTransition;
                                  const topBorderWidth = scaledW * (1 - topTransition) + effectiveImageWidth * topTransition;
                                  
                                  // Bottom border: position transitions, width respects image bounds
                                  const bottomBorderTop = (offsetY + scaledH - borderThicknessPx) * (1 - bottomTransition) + (containerH - borderThicknessPx) * bottomTransition;
                                  const bottomBorderLeft = offsetX * (1 - bottomTransition) + imageLeft * bottomTransition;
                                  const bottomBorderWidth = scaledW * (1 - bottomTransition) + effectiveImageWidth * bottomTransition;
                                  
                                  // Left border: position transitions, height respects actual adjacent border positions
                                  const leftBorderTop = showTopBorder ? topBorderTop + borderThicknessPx : 
                                                        (offsetY * (1 - leftTransition) + imageTop * leftTransition);
                                  const leftBorderLeft = offsetX * (1 - leftTransition) + 0 * leftTransition;
                                  const leftBorderBottom = showBottomBorder ? bottomBorderTop : 
                                                          ((offsetY + scaledH) * (1 - leftTransition) + imageBottom * leftTransition);
                                  const leftBorderHeight = leftBorderBottom - leftBorderTop;
                                  
                                  // Right border: position transitions, height respects actual adjacent border positions
                                  const rightBorderTop = showTopBorder ? topBorderTop + borderThicknessPx : 
                                                         (offsetY * (1 - rightTransition) + imageTop * rightTransition);
                                  const rightBorderLeft = (offsetX + scaledW - borderThicknessPx) * (1 - rightTransition) + 
                                                          (containerW - borderThicknessPx) * rightTransition;
                                  const rightBorderBottom = showBottomBorder ? bottomBorderTop : 
                                                            ((offsetY + scaledH) * (1 - rightTransition) + imageBottom * rightTransition);
                                  const rightBorderHeight = rightBorderBottom - rightBorderTop;

                                  // Render individual divs for 'contain' mode with hybrid positioning
                                  return shouldRenderBorder ? (
                                    <>
                                      {showTopBorder && (
                                        <div style={{ 
                                          position: 'absolute', 
                                          top: `${topBorderTop}px`, 
                                          left: `${topBorderLeft}px`, 
                                          width: `${topBorderWidth}px`, 
                                          height: `${borderThicknessPx}px`, 
                                          backgroundColor: currentBorderColor, 
                                          zIndex: 5, 
                                          pointerEvents: 'none' 
                                        }} />
                                      )}
                                      {showBottomBorder && (
                                        <div style={{ 
                                          position: 'absolute', 
                                          top: `${bottomBorderTop}px`, 
                                          left: `${bottomBorderLeft}px`, 
                                          width: `${bottomBorderWidth}px`, 
                                          height: `${borderThicknessPx}px`, 
                                          backgroundColor: currentBorderColor, 
                                          zIndex: 5, 
                                          pointerEvents: 'none' 
                                        }} />
                                      )}
                                      {showLeftBorder && (
                                        <div style={{ 
                                          position: 'absolute', 
                                          top: `${leftBorderTop}px`, 
                                          left: `${leftBorderLeft}px`, 
                                          width: `${borderThicknessPx}px`, 
                                          height: `${leftBorderHeight}px`, 
                                          backgroundColor: currentBorderColor, 
                                          zIndex: 5, 
                                          pointerEvents: 'none' 
                                        }} />
                                      )}
                                      {showRightBorder && (
                                        <div style={{ 
                                          position: 'absolute', 
                                          top: `${rightBorderTop}px`, 
                                          left: `${rightBorderLeft}px`, 
                                          width: `${borderThicknessPx}px`, 
                                          height: `${rightBorderHeight}px`, 
                                          backgroundColor: currentBorderColor, 
                                          zIndex: 5, 
                                          pointerEvents: 'none' 
                                        }} />
                                      )}
                                    </>
                                  ) : null;
                                } else { // 'cover' or other fit modes
                                  // For cover mode: Border follows the placeholder container edges,
                                  // but hide edges that are "outside" and intersect with bleed.
                                  const isCoverMode = fit === 'cover';
                                  if (isCoverMode && shouldRenderBorder && containerW && containerH) { // Check shouldRenderBorder here
                                    const placeholderFullSpreadX_pt = adjustedPlaceholder.adjustedX_pt;
                                    const placeholderFullSpreadY_pt = adjustedPlaceholder.adjustedY_pt;
                                    const placeholderFullSpreadWidth_pt = adjustedPlaceholder.adjustedWidth_pt;
                                    const placeholderFullSpreadHeight_pt = adjustedPlaceholder.adjustedHeight_pt;

                                    const spreadTotalWidthPt = spreadDimensionsPt.width; // This is single page width, for spread use spreadDimensionsPt.width * 2
                                    const spreadTotalHeightPt = spreadDimensionsPt.height;
                                    const edgeTolerancePt = 2.0; // Tolerance in points

                                    // Check against full spread width for left/right edges
                                    const isTopEdgeOutside = placeholderFullSpreadY_pt < edgeTolerancePt;
                                    const isBottomEdgeOutside = (placeholderFullSpreadY_pt + placeholderFullSpreadHeight_pt) >= (spreadTotalHeightPt - edgeTolerancePt);
                                    // For a two-page spread, the "spread" width is effectively doubled.
                                    // An image on the left page is outside if its X is near 0.
                                    // An image on the right page is outside if its X + Width (relative to its own page) is near the page width.
                                    // This logic needs to be aware of which page the placeholder is on if spreadTotalWidthPt is single page.
                                    // Assuming spreadTotalWidthPt here refers to a single page for now, and placeholder X is relative to that page.
                                    const isLeftEdgeOutside = placeholderFullSpreadX_pt < edgeTolerancePt;
                                    const isRightEdgeOutside = (placeholderFullSpreadX_pt + placeholderFullSpreadWidth_pt) >= (spreadTotalWidthPt - edgeTolerancePt);


                                    const bleedIsActive = (bleedValue ?? 0) > 0;

                                    const showTopBorder = !(isTopEdgeOutside && bleedIsActive);
                                    const showBottomBorder = !(isBottomEdgeOutside && bleedIsActive);
                                    const showLeftBorder = !(isLeftEdgeOutside && bleedIsActive);
                                    const showRightBorder = !(isRightEdgeOutside && bleedIsActive);
                                    
                                    // Diagnostic logging for right edge border in cover mode
                                    // if (isCoverMode && borderThicknessPx > 0 && adjustedPlaceholder.id.includes("placeholder-1")) { // Log for a specific placeholder if needed
                                    //   const calculatedRightExtent = placeholderFullSpreadX_pt + placeholderFullSpreadWidth_pt;
                                    //   console.log(
                                    //     `[CoverBorderDebug] ID: ${adjustedPlaceholder.id}`,
                                    //     `\n  X_pt: ${placeholderFullSpreadX_pt.toFixed(3)}, W_pt: ${placeholderFullSpreadWidth_pt.toFixed(3)}, RightExtent_pt: ${calculatedRightExtent.toFixed(3)}`,
                                    //     `\n  SpreadTotalW_pt (single page): ${spreadTotalWidthPt.toFixed(3)}, EdgeTolerance_pt: ${edgeTolerancePt}`,
                                    //     `\n  isRightEdgeOutside: ${isRightEdgeOutside} (calc: ${calculatedRightExtent.toFixed(3)} >= (${spreadTotalWidthPt.toFixed(3)} - ${edgeTolerancePt}))`,
                                    //     `\n  bleedIsActive: ${bleedIsActive}`,
                                    //     `\n  showRightBorder (final): ${showRightBorder}`
                                    //   );
                                    // }


                                    const topBorderEffectiveSizePx = showTopBorder ? borderThicknessPx : 0;
                                    const bottomBorderEffectiveSizePx = showBottomBorder ? borderThicknessPx : 0;

                                    return (
                                      <>
                                        {showTopBorder && (
                                          <div style={{ position: 'absolute', top: '0px', left: '0px', width: '100%', height: `${borderThicknessPx}px`, backgroundColor: currentBorderColor, zIndex: 5, pointerEvents: 'none' }} />
                                        )}
                                        {showBottomBorder && (
                                          <div style={{ position: 'absolute', bottom: '0px', left: '0px', width: '100%', height: `${borderThicknessPx}px`, backgroundColor: currentBorderColor, zIndex: 5, pointerEvents: 'none' }} />
                                        )}
                                        {showLeftBorder && (
                                          <div style={{ position: 'absolute', top: `${topBorderEffectiveSizePx}px`, left: '0px', width: `${borderThicknessPx}px`, height: `calc(100% - ${topBorderEffectiveSizePx}px - ${bottomBorderEffectiveSizePx}px)`, backgroundColor: currentBorderColor, zIndex: 5, pointerEvents: 'none' }} />
                                        )}
                                        {showRightBorder && (
                                          <div style={{ position: 'absolute', top: `${topBorderEffectiveSizePx}px`, right: '0px', width: `${borderThicknessPx}px`, height: `calc(100% - ${topBorderEffectiveSizePx}px - ${bottomBorderEffectiveSizePx}px)`, backgroundColor: currentBorderColor, zIndex: 5, pointerEvents: 'none' }} />
                                        )}
                                      </>
                                    );
                                  }
                                  return null; // No border if not cover or no border size or no container dims
                                }
                              })()}
                              {userSettings.showOnboardingTooltips !== false && (
                                <OnboardingTooltip
                                  show={hoveredPlaceholderIdForTooltip === adjustedPlaceholder.id}
                                  targetId={adjustedPlaceholder.id}
                                  onDismiss={() => {
                                    // Derive a project-specific key for sessionStorage
                                    const projectKeyPart = currentSpreadId.split('-')[0] || 'defaultProject';
                                    const sessionDismissKey = `onboardingTooltipDismissed_${projectKeyPart}`;
                                    sessionStorage.setItem(sessionDismissKey, 'true');
                                    setHoveredPlaceholderIdForTooltip(null);
                                  }}
                                  onSetDontShowAgainGlobally={() => {
                                    userSettings.setShowOnboardingTooltips(false);
                                    setHoveredPlaceholderIdForTooltip(null); // Also dismiss it immediately
                                  }}
                                />
                              )}
                              {/* Controls */}
                              <div
                                className="absolute inset-0 pointer-events-none" // Overlay for controls
                                style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
                              >
                                {/* Only show the ImageNumberOverlay component if we have an image */}
                                  {image && (() => {
                                    // Calculate imageRenderStyle again or reuse if possible
                                    // For simplicity, recalculating here. Could be optimized.
                                    const containerDims = placeholderPixelDims[adjustedPlaceholder.id];
                                    const imageNativeDims = image ? getImageDimensions?.(image.id) : undefined;
                                    let imageRenderStyleForOverlay: ImageNumberOverlayProps['imageRenderStyle'] | undefined = undefined;
                                    if (containerDims && imageNativeDims && containerDims.width > 0 && containerDims.height > 0 && imageNativeDims.width > 0 && imageNativeDims.height > 0) {
                                      const { scale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain' } = transform;
                                      const containerW = containerDims.width;
                                      const containerH = containerDims.height;
                                      const imageW = imageNativeDims.width;
                                      const imageH = imageNativeDims.height;
                                      const imageRatio = imageW / imageH;
                                      const containerRatio = containerW / containerH;
                                      let baseRenderedW: number;
                                      let baseRenderedH: number;
                                      if (fit === 'contain') {
                                        if (imageRatio > containerRatio) { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; } else { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; }
                                      } else {
                                        if (imageRatio > containerRatio) { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; } else { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; }
                                      }
                                      const scaledW = baseRenderedW * scale;
                                      const scaledH = baseRenderedH * scale;
                                      const focalPointXOnImage = scaledW * focalX;
                                      const focalPointYOnImage = scaledH * focalY;
                                      const containerCenterX = containerW / 2;
                                      const containerCenterY = containerH / 2;
                                      const offsetX = containerCenterX - focalPointXOnImage;
                                      const offsetY = containerCenterY - focalPointYOnImage;
                                      imageRenderStyleForOverlay = {
                                        left: `${offsetX}px`,
                                        top: `${offsetY}px`,
                                        width: `${scaledW}px`,
                                        height: `${scaledH}px`,
                                      };
                                    }

                                    return (
                                      <ImageNumberOverlay
                                        imageIndex={spread.images
                                          .filter(img => img.imageId)
                                          .findIndex(img => img.placeholderId === adjustedPlaceholder.id) + 1}
                                        placeholderId={adjustedPlaceholder.id}
                                        imageRenderStyle={imageRenderStyleForOverlay}
                                        placeholderPixelDimensions={placeholderPixelDims[adjustedPlaceholder.id]}
                                        bleedAreaMode={bleedAreaMode}
                                        bleedInPixels={bleedInPixels}
                                      />
                                    );
                                  })()}
                                  
                                  {/* Remove Button */}
                                  {hoveredPlaceholderId === adjustedPlaceholder.id && (
                                    <button
                                      className={cn(
                                        "absolute p-0.5 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-opacity z-30 pointer-events-auto",
                                        ((bleedAreaMode === 'hide' || bleedAreaMode === 'indicate') || localImageBorderSize >= 8) ? "top-4 right-4" : "top-2 right-1"
                                      )} // Ensure button is clickable
                                      style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
                                      onClick={(e) => {
                                        e.stopPropagation(); // Prevent triggering context menu or drag
                                        handleRemoveButtonClick(adjustedPlaceholder.id);
                                      }}
                                      title="Remove image"
                                    >
                                      <X className="w-3 h-3" />
                                    </button>
                                  )}

                                  {/* Zoom Slider */}
                                  {/* Only show slider when hovered and image exists AND placeholder is not small */}
                                  {hoveredPlaceholderId === adjustedPlaceholder.id && image && !smallPlaceholders.has(adjustedPlaceholder.id) && (
                                    <div
                                      className={cn(
                                        "absolute z-20 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-auto flex items-center justify-between space-x-1 bg-black/30 p-1 rounded",
                                        ((bleedAreaMode === 'hide' || bleedAreaMode === 'indicate') || localImageBorderSize >= 8) ? "bottom-4 left-4 right-4" : "bottom-2 left-1 right-1"
                                      )}
                                      style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
                                    > {/* Adjusted padding/spacing */}
                                       {/* PanelsLeftBottom Icon Button */}
                                       <button
                                         className="p-0.5 text-white hover:bg-white/20 rounded"
                                         onClick={(e) => {
                                           e.stopPropagation();
                                           if (hoveredPlaceholderId && imageAssignment) {
                                             const currentSettings = individualBorderSettings[hoveredPlaceholderId] || {
                                               enabled: false,
                                               size: localImageBorderSize,
                                               color: localImageBorderColor,
                                             };
                                             const newEnabledState = !currentSettings.enabled;
                                             const updatedSettings = {
                                               ...currentSettings,
                                               enabled: newEnabledState,
                                               // If enabling and size is 0, default to project border size or a minimum like 1
                                               size: newEnabledState && currentSettings.size === 0 ? (localImageBorderSize > 0 ? localImageBorderSize : 1) : currentSettings.size,
                                               // If enabling and color is default, ensure it's set
                                               color: newEnabledState && currentSettings.color === localImageBorderColor ? localImageBorderColor : currentSettings.color,
                                             };

                                             setIndividualBorderSettings(prev => ({
                                               ...prev,
                                               [hoveredPlaceholderId!]: updatedSettings,
                                             }));

                                             // Persist this change to the main spread data
                                             const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
                                             const targetPlacement = activeSpread?.images.find(p => p.placeholderId === hoveredPlaceholderId);
                                             if (activeSpread && targetPlacement && onUpdateSpreadImages) {
                                               const updatedPlacement: ImagePlacement = {
                                                 ...targetPlacement,
                                                 individualBorder: updatedSettings,
                                               };
                                               onUpdateSpreadImages(currentSpreadId, hoveredPlaceholderId, updatedPlacement);
                                               toast.info(`Individual border ${newEnabledState ? 'enabled' : 'disabled'} for this image.`);
                                             }
                                           }
                                         }}
                                         aria-label="Image border"
                                         title="Image border"
                                       >
                                         <PanelsLeftBottom className="w-3 h-3" />
                                       </button>
                                       {/* Individual Border Controls - Show if enabled and placeholder is hovered */}
                                       {hoveredPlaceholderId === adjustedPlaceholder.id && individualBorderSettings[adjustedPlaceholder.id]?.enabled && image && (
                                         <div
                                           className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 p-2 bg-black/70 rounded-md shadow-lg flex items-center space-x-2 pointer-events-auto z-50"
                                           onMouseDown={(e) => e.stopPropagation()}
                                           onMouseMove={(e) => e.stopPropagation()}
                                           onMouseUp={(e) => e.stopPropagation()}
                                           onClick={(e) => e.stopPropagation()}
                                         >
                                           {/* Color Swatch to open SketchPicker */}
                                           <div
                                             ref={el => swatchRefs.current[adjustedPlaceholder.id] = el}
                                             className="w-6 h-6 rounded cursor-pointer border border-gray-400"
                                             style={{ backgroundColor: individualBorderSettings[adjustedPlaceholder.id]?.color || '#000000' }}
                                             onClick={(e) => {
                                               e.stopPropagation();
                                               if (activeColorPickerId === adjustedPlaceholder.id) {
                                                 setActiveColorPickerId(null);
                                                 setPickerPosition(null);
                                               } else {
                                                 const swatchEl = swatchRefs.current[adjustedPlaceholder.id];
                                                 if (swatchEl) {
                                                   const rect = swatchEl.getBoundingClientRect();
                                                   const PICKER_HEIGHT_ESTIMATE = 350; // Estimated height of the color picker popover
                                                   const PICKER_WIDTH_ESTIMATE = 280;  // Width of the color picker popover
                                                   const VIEWPORT_MARGIN = 10;         // Margin from viewport edges

                                                   // Attempt to position above the swatch initially
                                                   let top = rect.top - PICKER_HEIGHT_ESTIMATE - VIEWPORT_MARGIN;
                                                   let left = rect.left + (rect.width / 2) - (PICKER_WIDTH_ESTIMATE / 2);

                                                   // If positioning above makes it go off the top of the viewport,
                                                   // try positioning below the swatch.
                                                   if (top < VIEWPORT_MARGIN) {
                                                     top = rect.bottom + VIEWPORT_MARGIN;
                                                   }

                                                   // Clamp top: Ensure it doesn't go off the top edge
                                                   top = Math.max(VIEWPORT_MARGIN, top);
                                                   // Clamp top: Ensure it doesn't go off the bottom edge
                                                   // (window.innerHeight - PICKER_HEIGHT_ESTIMATE - VIEWPORT_MARGIN) ensures space for margin at bottom
                                                   top = Math.min(top, window.innerHeight - PICKER_HEIGHT_ESTIMATE - VIEWPORT_MARGIN);

                                                   // Clamp left: Ensure it doesn't go off the left edge
                                                   left = Math.max(VIEWPORT_MARGIN, left);
                                                   // Clamp left: Ensure it doesn't go off the right edge
                                                   left = Math.min(left, window.innerWidth - PICKER_WIDTH_ESTIMATE - VIEWPORT_MARGIN);

                                                   setPickerPosition({ top, left });
                                                 }
                                                 setActiveColorPickerId(adjustedPlaceholder.id);
                                               }
                                             }}
                                             title="Border Color"
                                           />
                                           {activeColorPickerId === adjustedPlaceholder.id && pickerPosition && (() => {
                                              const portalTargetElement = document.getElementById('portal-root') || document.body;
                                              if (!portalTargetElement) return null; // Should not happen if document.body is fallback
                                              return createPortal(
                                                <div
                                                  ref={colorPickerRef}
                                                  className="fixed z-[9999] p-3 bg-gray-800 rounded-lg shadow-xl border border-gray-700" // Added padding and background for the popover
                                                  style={{ top: `${pickerPosition.top}px`, left: `${pickerPosition.left}px`, width: '280px' }} // Set width for consistency
                                                  onClick={(e) => e.stopPropagation()}
                                                  onMouseDown={(e) => e.stopPropagation()}
                                                >
                                                  <RgbaStringColorPicker
                                                    color={ensureRgbaString(individualBorderSettings[adjustedPlaceholder.id]?.color || localImageBorderColor)}
                                                    onChange={(newRgbaColor) => {
                                                      const currentSettings = individualBorderSettings[adjustedPlaceholder.id];
                                                      if (currentSettings) {
                                                        const updatedSettings = { ...currentSettings, color: newRgbaColor };
                                                        setIndividualBorderSettings(prev => ({ ...prev, [adjustedPlaceholder.id]: updatedSettings }));
                                                        const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
                                                        const targetPlacement = activeSpread?.images.find(p => p.placeholderId === adjustedPlaceholder.id);
                                                        if (activeSpread && targetPlacement && onUpdateSpreadImages) {
                                                          onUpdateSpreadImages(currentSpreadId, adjustedPlaceholder.id, { ...targetPlacement, individualBorder: updatedSettings });
                                                        }
                                                      }
                                                    }}
                                                  />
                                                  <div className="mt-3 grid grid-cols-2 gap-x-3 gap-y-2">
                                                    {(['r', 'g', 'b', 'a'] as const).map((compKey) => {
                                                      const handleRgbaInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
                                                        setActivePickerRgbaInputStrings(prev => ({ ...prev, [compKey]: e.target.value }));
                                                      };

                                                      const commitRgbaComponentChange = (valueToCommit: string) => {
                                                        const currentStoredColor = individualBorderSettings[activeColorPickerId!]?.color || 'rgba(0,0,0,1)';
                                                        const parsedStoredColor = parseRgbaString(currentStoredColor);

                                                        let numValue = compKey === 'a' ? parseFloat(valueToCommit) : parseInt(valueToCommit, 10);
                                                        const minVal = 0;
                                                        const maxVal = compKey === 'a' ? 1 : 255;

                                                        if (isNaN(numValue)) { // If NaN, revert to the stored component value
                                                           numValue = parsedStoredColor[compKey];
                                                        }
                                                        numValue = Math.max(minVal, Math.min(maxVal, numValue));
                                                        
                                                        const finalInputStrings = { ...activePickerRgbaInputStrings, [compKey]: compKey === 'a' ? numValue.toFixed(2) : String(numValue) };
                                                        setActivePickerRgbaInputStrings(finalInputStrings);


                                                        const finalRgba = {
                                                           r: parseInt(finalInputStrings.r, 10),
                                                           g: parseInt(finalInputStrings.g, 10),
                                                           b: parseInt(finalInputStrings.b, 10),
                                                           a: parseFloat(finalInputStrings.a),
                                                        };
                                                        
                                                        const newRgbaString = `rgba(${finalRgba.r},${finalRgba.g},${finalRgba.b},${finalRgba.a.toFixed(2)})`;

                                                        if (individualBorderSettings[activeColorPickerId!] && individualBorderSettings[activeColorPickerId!].color !== newRgbaString) {
                                                          const updatedSettings = { ...individualBorderSettings[activeColorPickerId!], color: newRgbaString };
                                                          setIndividualBorderSettings(prev => ({ ...prev, [activeColorPickerId!]: updatedSettings }));
                                                          
                                                          const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
                                                          const targetPlacement = activeSpread?.images.find(p => p.placeholderId === activeColorPickerId!);
                                                          if (activeSpread && targetPlacement && onUpdateSpreadImages) {
                                                            onUpdateSpreadImages(currentSpreadId, activeColorPickerId!, { ...targetPlacement, individualBorder: updatedSettings });
                                                          }
                                                        }
                                                      };

                                                      const handleRgbaInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
                                                         commitRgbaComponentChange(e.target.value);
                                                      };

                                                      const handleRgbaInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
                                                         if (e.key === 'Enter') {
                                                           commitRgbaComponentChange((e.target as HTMLInputElement).value);
                                                           (e.target as HTMLInputElement).blur();
                                                         }
                                                      };

                                                      return (
                                                        <div key={compKey} className="flex items-center space-x-1.5">
                                                          <label htmlFor={`color-${compKey}-${activeColorPickerId}`} className="text-xs text-gray-300 w-5 text-right font-medium pr-1">{compKey.toUpperCase()}:</label>
                                                          <input
                                                            type="text"
                                                            id={`color-${compKey}-${activeColorPickerId}`}
                                                            value={activePickerRgbaInputStrings[compKey]}
                                                            onChange={handleRgbaInputChange}
                                                            onBlur={handleRgbaInputBlur}
                                                            onKeyDown={handleRgbaInputKeyDown}
                                                            className="w-16 p-1.5 border border-gray-600 rounded bg-gray-700 text-white text-xs text-center focus:ring-1 focus:ring-blue-500 outline-none appearance-none [-moz-appearance:textfield]"
                                                            onClick={(ev) => ev.stopPropagation()}
                                                            onMouseDown={(ev) => ev.stopPropagation()}
                                                          />
                                                        </div>
                                                      );
                                                    })}
                                                  </div>
                                                </div>,
                                                portalTargetElement
                                              );
                                           })()}
                                           {/* Size Slider */}
                                           <Slider
                                             min={0}
                                             max={15} // 0-15pt
                                             step={0.5}
                                             value={[individualBorderSettings[adjustedPlaceholder.id]?.size || 0]}
                                             onValueChange={([newSize]) => {
                                               const currentSettings = individualBorderSettings[adjustedPlaceholder.id];
                                               if (currentSettings) {
                                                 const updatedSettings = { ...currentSettings, size: newSize };
                                                 setIndividualBorderSettings(prev => ({ ...prev, [adjustedPlaceholder.id]: updatedSettings }));
                                                 // Persist
                                                 const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
                                                 const targetPlacement = activeSpread?.images.find(p => p.placeholderId === adjustedPlaceholder.id);
                                                 if (activeSpread && targetPlacement && onUpdateSpreadImages) {
                                                   onUpdateSpreadImages(currentSpreadId, adjustedPlaceholder.id, { ...targetPlacement, individualBorder: updatedSettings });
                                                 }
                                               }
                                             }}
                                             className="w-24 h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-white/70 [&>span:first-child>span]:h-2"
                                             aria-label="Border Size"
                                           />
                                           <span className="text-white text-xs min-w-[2.5ch] text-right">
                                             {(individualBorderSettings[adjustedPlaceholder.id]?.size || 0).toFixed(1)}pt
                                           </span>
                                         </div>
                                       )}
                                       {/* Zoom Slider */}
                                       <Slider
                                         min={1}
                                         max={3}
                                         step={0.02}
                                         value={[transform.scale ?? 1]}
                                         onValueChange={([newScaleFromSlider]) => {
                                           const currentFitMode = transform.fit ?? 'contain';
                                           const currentFocalX = transform.focalX ?? 0.5;
                                           const currentFocalY = transform.focalY ?? 0.5;

                                           const containerDims = placeholderPixelDims[adjustedPlaceholder.id];
                                           const imageNativeDims = getImageDimensions && image ? getImageDimensions(image.id) : undefined;

                                           let minRequiredScale = 1;
                                           if (currentFitMode === 'cover' && containerDims && imageNativeDims && imageNativeDims.width > 0 && imageNativeDims.height > 0) {
                                             const containerWidth = containerDims.width;
                                             const containerHeight = containerDims.height;
                                             const imageWidth = imageNativeDims.width;
                                             const imageHeight = imageNativeDims.height;
                                             const imageAspect = imageWidth / imageHeight;
                                             const containerAspect = containerWidth / containerHeight;

                                             if (imageAspect > containerAspect) { // Image is wider than container (relative to its height)
                                               minRequiredScale = containerHeight / imageHeight;
                                             } else { // Image is taller than container (relative to its width) or same aspect
                                               minRequiredScale = containerWidth / imageWidth;
                                             }
                                             minRequiredScale = Math.max(1, minRequiredScale); // Ensure it's at least 1, can be >1 if image is smaller than container
                                           }

                                           const targetScaleForAnimation = currentFitMode === 'cover' ?
                                             Math.max(newScaleFromSlider, minRequiredScale) :
                                             newScaleFromSlider;

                                           let focalXForAnimation = currentFocalX;
                                           let focalYForAnimation = currentFocalY;
                                           if (targetScaleForAnimation <= 1 && currentFitMode === 'contain') {
                                             focalXForAnimation = 0.5;
                                             focalYForAnimation = 0.5;
                                           }

                                           targetZoomLevelsRef.current[adjustedPlaceholder.id] = targetScaleForAnimation;

                                           if (currentZoomLevelsRef.current[adjustedPlaceholder.id] === undefined) {
                                             currentZoomLevelsRef.current[adjustedPlaceholder.id] = transform.scale ?? 1;
                                           }

                                           if (zoomAnimationFrameRef.current[adjustedPlaceholder.id]) {
                                             cancelAnimationFrame(zoomAnimationFrameRef.current[adjustedPlaceholder.id]!);
                                             zoomAnimationFrameRef.current[adjustedPlaceholder.id] = null;
                                           }
                                         
                                           zoomAnimationFrameRef.current[adjustedPlaceholder.id] = requestAnimationFrame(() =>
                                             animateZooming(
                                               currentSpreadId,
                                               adjustedPlaceholder.id,
                                               currentFitMode,
                                               focalXForAnimation,
                                               focalYForAnimation
                                             )
                                           );
                                           checkAndApplyAutoCover(currentSpreadId, adjustedPlaceholder.id, 'ZoomSlider');
                                         }}
                                         className="flex-1 h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-white/70 [&>span:first-child>span]:h-2" // Style slider track/thumb
                                         aria-label={`Zoom image ${image.name}`}
                                       />
                                       {/* Rotation Handle - always available when image is present */}
                                       {image && (
                                         <div className="relative">
                                           {/* Vertical rotation slider - appears during rotation, stays until dismissed */}
                                           {isRotating && rotatingPlaceholderId === adjustedPlaceholder.id && (
                                             <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 z-50">
                                               <div className="bg-black/80 text-white px-2 py-1 rounded text-xs whitespace-nowrap mb-1">
                                                 {Math.round(transform.rotation || 0)}° 
                                                 <button 
                                                   className="ml-2 text-white/70 hover:text-white"
                                                   onClick={(e) => {
                                                     e.stopPropagation();
                                                     setIsRotating(false);
                                                     setRotatingPlaceholderId(null);
                                                     document.body.style.cursor = 'default';
                                                   }}
                                                 >
                                                   ×
                                                 </button>
                                               </div>
                                               <div 
                                                 className="w-0.5 h-20 bg-white/50 mx-auto relative cursor-ns-resize"
                                                 onMouseDown={(e) => {
                                                   e.preventDefault();
                                                   e.stopPropagation();
                                                   // Allow clicking on the slider track itself
                                                 }}
                                               >
                                                 <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full border border-gray-400"></div>
                                                 <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full border border-gray-400"></div>
                                               </div>
                                             </div>
                                           )}
                                           {/* Rotation button */}
                                           <button
                                             className="p-0.5 text-white hover:bg-white/20 rounded"
                                             onMouseDown={(e) => handleRotationMouseDown(e, adjustedPlaceholder.id)}
                                             title={`Click and drag vertically to rotate image (${Math.round(transform.rotation || 0)}°)`}
                                             style={{ 
                                               cursor: 'ns-resize',
                                               userSelect: 'none',
                                               backgroundColor: isRotating && rotatingPlaceholderId === adjustedPlaceholder.id ? 'rgba(255,255,255,0.3)' : undefined
                                             }}
                                           >
                                             <RotateCw className="w-3 h-3" />
                                           </button>
                                         </div>
                                       )}
                                       {/* Toggle Fit Mode Button */}
                                       <button
                                          className="p-0.5 text-white hover:bg-white/20 rounded"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            const currentFitMode = transform.fit ?? 'contain';
                                            // Toggle between 'contain' and 'cover'
                                            const newFitMode = currentFitMode === 'contain' ? 'cover' : 'contain';
                                            
                                            // Create a new transform with the new fit mode AND RESET scale/focal points
                                            const newTransform = {
                                              scale: 1, // Reset scale
                                              focalX: 0.5, // Reset focal point X
                                              focalY: 0.5, // Reset focal point Y
                                              rotation: 0, // Reset rotation
                                              fit: newFitMode as 'contain' | 'cover',
                                              // @ts-ignore: Mark this as a change that should trigger undo state save
                                              _shouldSaveUndo: true
                                            };
                                            
                                            // Call transform update with the toggled fit mode
                                            throttledUpdateTransform(
                                              currentSpreadId,
                                              adjustedPlaceholder.id,
                                              newTransform,
                                              newFitMode // Also pass as explicit fit mode
                                            );
                                            // Check auto-cover if mode was toggled TO contain
                                            if (newFitMode === 'contain') {
                                              // The useEffect watching renderedSpreads will handle auto-cover if the new mode is 'contain'
                                            }
                                          }}
                                          aria-label={`Toggle fit mode: currently ${transform.fit}`}
                                          title={`Toggle fit mode: currently ${transform.fit}`}
                                        >
                                          {/* Conditionally render icon based on current fit mode */}
                                          {(transform.fit ?? 'contain') === 'contain' ? (
                                            <Expand className="w-3 h-3" /> // Show Expand (4 arrows out) icon when current is Contain
                                          ) : (
                                            <Shrink className="w-3 h-3" /> // Show Shrink (4 arrows in) icon when current is Cover
                                          )}
                                        </button>
                                    </div>
                                  )}

                                  {/* Drag Grip Icon - Always visible on hover, spans top 1/4 of placeholder */}
                                  {showDragIcon && (() => {
                                    const outsideEdges = calculatePlaceholderOutsideEdges(adjustedPlaceholder.id);
                                    const isAtTopSpreadEdge = outsideEdges.top;
                                    const shouldAdjustForBleed = (bleedAreaMode === 'hide' || bleedAreaMode === 'indicate') && isAtTopSpreadEdge && (bleedValue ?? 0) > 0;
                                    const bleedOffsetPx = shouldAdjustForBleed ? (bleedValue ?? 0) * pixelsPerPoint : 0;
                                    
                                    return (
                                      <div
                                        className={cn(
                                          "absolute z-20 pointer-events-auto flex items-center justify-center cursor-move",
                                          hoveredPlaceholderId === adjustedPlaceholder.id ? "opacity-100" : "opacity-0"
                                        )}
                                        style={{ 
                                          top: bleedOffsetPx,
                                          left: '0%',
                                          width: '100%',
                                          height: '25%',
                                          filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' 
                                        }}
                                      >
                                        <div className="bg-black/15 w-full h-full flex items-center justify-center hover:bg-black/25 transition-colors">
                                          <GripHorizontal className="w-12 h-5 text-white" />
                                        </div>
                                      </div>
                                    );
                                  })()}


                                  {/* Drag Over Overlay - Shows when image is being dragged over this placeholder */}
                                  {isDraggedOver && (
                                    <div className="absolute inset-0 z-30 pointer-events-none">
                                      {/* Semi-transparent overlay */}
                                      <div className="absolute inset-0 bg-black/20 animate-pulse" />
                                      
                                      {/* Center indicator */}
                                      {isDragFromCanvas && (
                                        <div className="absolute inset-0 flex items-center justify-center">
                                          <div className="bg-black/70 text-white px-3 py-1.5 rounded shadow-lg flex items-center space-x-1.5">
                                            <Replace className="w-4 h-4" />
                                            <span className="text-sm">Drop to Swap</span>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </ContextMenuTrigger>
                            <ContextMenuContent className="w-48">
                              {/* NEW: Image Preview Item */}
                              <ContextMenuItem
                                onSelect={() => {
                                  if (image) { // Only proceed if there is an image
                                    showImagePreviewModal(image); // Call the preview function
                                  }
                                }}
                                disabled={!image} // Disable if no image
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                Image Preview
                              </ContextMenuItem>
                              <ContextMenuSeparator />
                              {/* Reveal in Finder */}
                              <ContextMenuItem
                                onSelect={() => {
                                  if (image && typeof window.electronAPI?.showItemInFolder === 'function') {
                                    // Get the most up-to-date path for this image
                                    let imagePath = image.originalPath;
                                    
                                    // Check if we have an updated path in our cache
                                    if (window.bookProofsApp?.getUpdatedFilePath) {
                                      const updatedPath = window.bookProofsApp.getUpdatedFilePath(image.originalPath);
                                      if (updatedPath) {
                                        imagePath = updatedPath;
                                      }
                                    }
                                    
                                    window.electronAPI.showItemInFolder(imagePath);
                                  } else {
                                    toast.error('Could not reveal file: Feature not available.');
                                  }
                                }}
                                disabled={!image} // Disable if no image
                              >
                                <FolderSearch className="mr-2 h-4 w-4" />
                                Reveal in Finder {/* Consider OS-specific text later if needed */}
                              </ContextMenuItem>

                              {/* Edit in Photoshop */}
                              <ContextMenuItem
                                onSelect={async () => { // Make the handler async
                                  if (typeof window.electronAPI?.editInPhotoshop === 'function') {
                                    // Use the captured selection state from right-click time
                                    const capturedSelection = contextMenuSelectionRef.current;
                                    const currentSelectionSize = capturedSelection ? capturedSelection.placeholderIds.length : 0;
                                    const currentSelectedPlaceholders = capturedSelection ? capturedSelection.placeholderIds : [];
                                    const clickedPlaceholderId = capturedSelection ? capturedSelection.clickedPlaceholderId : adjustedPlaceholder.id;
                                    const isClickedPlaceholderSelected = currentSelectedPlaceholders.includes(clickedPlaceholderId);
                                    
                                    
                                    const imagesToEditObjects = currentSelectionSize > 0 && isClickedPlaceholderSelected ?
                                      // If the right-clicked placeholder is selected, edit all selected images
                                      currentSelectedPlaceholders.map(placeholderId => {
                                        const activeSpread = renderedSpreads[currentRenderedIndex];
                                        const placement = activeSpread?.images.find(p => p.placeholderId === placeholderId);
                                        const foundImage = placement?.imageId ? images.find(img => img.id === placement.imageId) : null;
                                        return foundImage;
                                      }).filter(Boolean) :
                                      // If the right-clicked placeholder is NOT selected, edit only this image
                                      [image].filter(Boolean);
                                    

                                    // Check if selection exceeds the maximum limit
                                    if (imagesToEditObjects.length > 20) {
                                      toast.warning('You can edit a maximum of 20 images in Photoshop at once.', {
                                        description: `You have selected ${imagesToEditObjects.length} images.`
                                      });
                                      return;
                                    }

                                    const filePathsToEdit = imagesToEditObjects.map(img => {
                                      let imagePath = img.originalPath;
                                      if (window.bookProofsApp?.getUpdatedFilePath) {
                                        const updatedPath = window.bookProofsApp.getUpdatedFilePath(img.originalPath);
                                        if (updatedPath) {
                                          imagePath = updatedPath;
                                        }
                                      }
                                      return imagePath;
                                    });

                                    if (filePathsToEdit.length === 0) {
                                      toast.error('No valid image paths found to edit.');
                                      return;
                                    }

                                    try {
                                      // Send all file paths at once
                                      await window.electronAPI.editInPhotoshop(filePathsToEdit);
                                      
                                      if (filePathsToEdit.length > 1) {
                                        toast.success(`Sent ${filePathsToEdit.length} images to Photoshop.`);
                                      } else {
                                        toast.success(`Sent ${imagesToEditObjects[0].name} to Photoshop.`);
                                      }
                                    } catch (error: any) {
                                      console.error('Error sending images to Photoshop:', error);
                                      const errorMessage = error.message || (error.toString ? error.toString() : "Unknown error");
                                      toast.error(
                                        `Failed to send images to Photoshop.`,
                                        {
                                          description: `${errorMessage}. Check console for details.`
                                        }
                                      );
                                    }
                                  } else {
                                    console.warn('electronAPI.editInPhotoshop is not available.');
                                    toast.error('Edit in Photoshop feature not available.');
                                  }
                                }}
                                disabled={!image} // Disable if no image
                              >
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit in Photoshop{(() => {
                                  const capturedSelection = contextMenuSelectionRef.current;
                                  const currentSelectionSize = capturedSelection ? capturedSelection.placeholderIds.length : 0;
                                  const clickedPlaceholderId = capturedSelection ? capturedSelection.clickedPlaceholderId : adjustedPlaceholder.id;
                                  const isClickedSelected = capturedSelection ? capturedSelection.placeholderIds.includes(clickedPlaceholderId) : false;
                                  
                                  // Show count when editing multiple - if right-clicked placeholder is selected, edit all selected
                                  if (currentSelectionSize > 0 && isClickedSelected) {
                                    return currentSelectionSize > 1 ? ` (${currentSelectionSize})` : '';
                                  }
                                  return '';
                                })()}
                              </ContextMenuItem>

                              {/* Regenerate Preview option */}
                              <ContextMenuSeparator />
                              <ContextMenuItem
                                onSelect={async () => {
                                  if (typeof window.electronAPI?.regenerateThumbnails === 'function') {
                                    // Use the captured selection state from right-click time
                                    const capturedSelection = contextMenuSelectionRef.current;
                                    const currentSelectionSize = capturedSelection ? capturedSelection.placeholderIds.length : 0;
                                    const currentSelectedPlaceholders = capturedSelection ? capturedSelection.placeholderIds : [];
                                    const clickedPlaceholderId = capturedSelection ? capturedSelection.clickedPlaceholderId : adjustedPlaceholder.id;
                                    const isClickedPlaceholderSelected = currentSelectedPlaceholders.includes(clickedPlaceholderId);
                                    
                                    // Determine which images to regenerate - use same logic as remove/edit
                                    let placeholdersToRegenerate: string[] = [];
                                    
                                    // Priority 1: If we have selected images and the clicked image is among them, regenerate all selected
                                    if (currentSelectionSize > 0 && clickedPlaceholderId && isClickedPlaceholderSelected) {
                                      placeholdersToRegenerate = currentSelectedPlaceholders;
                                    }
                                    // Priority 2: If we have selected images but clicked image is not selected, regenerate all selected
                                    else if (currentSelectionSize > 0 && (!clickedPlaceholderId || !isClickedPlaceholderSelected)) {
                                      placeholdersToRegenerate = currentSelectedPlaceholders;
                                    }
                                    // Priority 3: Fallback to single clicked image (original behavior)
                                    else if (clickedPlaceholderId) {
                                      placeholdersToRegenerate = [clickedPlaceholderId];
                                    }
                                    
                                    // Filter out empty placeholders and get image objects
                                    const activeSpread = renderedSpreads[currentRenderedIndex];
                                    const imagesToRegenerate = placeholdersToRegenerate
                                      .map(placeholderId => {
                                        const placement = activeSpread.images.find(p => p.placeholderId === placeholderId);
                                        if (placement && placement.imageId) {
                                          return images.find(img => img.id === placement.imageId);
                                        }
                                        return null;
                                      })
                                      .filter(Boolean);
                                    
                                    if (imagesToRegenerate.length === 0) {
                                      toast.error('No images found to regenerate.');
                                      return;
                                    }
                                    
                                    // Show toast indicating preview regeneration
                                    const count = imagesToRegenerate.length;
                                    toast.info(`Regenerating ${count} preview${count > 1 ? 's' : ''}...`);
                                    
                                    let successCount = 0;
                                    let errorCount = 0;
                                    
                                    // Process all images
                                    for (const imageToRegenerate of imagesToRegenerate) {
                                      if (!imageToRegenerate) continue;
                                      
                                      try {
                                        // Get the most up-to-date path for this image
                                        let imagePath = imageToRegenerate.originalPath;
                                        
                                        // Check if we have an updated path in our cache
                                        if (window.bookProofsApp?.getUpdatedFilePath) {
                                          const updatedPath = window.bookProofsApp.getUpdatedFilePath(imageToRegenerate.originalPath);
                                          if (updatedPath) {
                                            imagePath = updatedPath;
                                          }
                                        }
                                        
                                        // Regenerate thumbnails for this image
                                        const refreshedImage = await window.electronAPI.regenerateThumbnails(imagePath);
                                        
                                        if (refreshedImage) {
                                          // Store the regenerated image data in our local state to ensure it persists
                                          setUpdatedImagePaths(prev => ({
                                            ...prev,
                                            [imageToRegenerate.originalPath]: imagePath
                                          }));
                                          
                                          // Create a direct cache entry for this specific image
                                          if (!window.bookProofsApp) window.bookProofsApp = {};
                                          // Safely add the imageCache property to the global object
                                          if (!(window.bookProofsApp as any).imageCache) {
                                            (window.bookProofsApp as any).imageCache = {};
                                          }
                                          // Store the image data in the cache
                                          (window.bookProofsApp as any).imageCache[imageToRegenerate.id] = {
                                            thumbnailUrl: refreshedImage.thumbnailUrl,
                                            previewUrl: refreshedImage.previewUrl,
                                            originalPath: imagePath,
                                            timestamp: Date.now()
                                          };
                                          
                                          // Update the global image state with the new thumbnails
                                          if (onUpdateImageThumbnails) {
                                            onUpdateImageThumbnails(imageToRegenerate.id, {
                                              thumbnailUrl: refreshedImage.thumbnailUrl,
                                              previewUrl: refreshedImage.previewUrl
                                            });
                                          }
                                          
                                          // Force refresh the image by updating its timestamp
                                          setImageRefreshTimestamps(prev => ({
                                            ...prev,
                                            [imageToRegenerate.id]: Date.now()
                                          }));
                                          
                                          successCount++;
                                        } else {
                                          errorCount++;
                                        }
                                      } catch (err) {
                                        errorCount++;
                                      }
                                    }
                                    
                                    // Show final result toast
                                    if (errorCount === 0) {
                                      toast.success(`${successCount} preview${successCount > 1 ? 's' : ''} regenerated successfully`);
                                    } else if (successCount === 0) {
                                      toast.error(`Failed to regenerate ${errorCount} preview${errorCount > 1 ? 's' : ''}`);
                                    } else {
                                      toast.warning(`${successCount} preview${successCount > 1 ? 's' : ''} regenerated, ${errorCount} failed`);
                                    }
                                  } else {
                                    toast.error('Could not regenerate preview: Feature not available.');
                                  }
                                }}
                                disabled={!image} // Disable if no image
                              >
                                <RefreshCcw className="mr-2 h-4 w-4" />
                                Regenerate Preview{(() => {
                                  const capturedSelection = contextMenuSelectionRef.current;
                                  const currentSelectionSize = capturedSelection ? capturedSelection.placeholderIds.length : 0;
                                  const clickedPlaceholderId = capturedSelection ? capturedSelection.clickedPlaceholderId : adjustedPlaceholder.id;
                                  const isClickedSelected = capturedSelection ? capturedSelection.placeholderIds.includes(clickedPlaceholderId) : false;
                                  
                                  // Show count when regenerating multiple - matches same logic as remove
                                  if (currentSelectionSize > 0) {
                                    // Will regenerate all selected in both Priority 1 and Priority 2 cases
                                    if ((currentSelectionSize > 0 && clickedPlaceholderId && isClickedSelected) ||
                                        (currentSelectionSize > 0 && (!clickedPlaceholderId || !isClickedSelected))) {
                                      return currentSelectionSize > 1 ? ` (${currentSelectionSize})` : '';
                                    }
                                  }
                                  return '';
                                })()}
                              </ContextMenuItem>

                              <ContextMenuSeparator />
                              
                              {/* Remove Image */}
                              <ContextMenuItem
                                onSelect={() => {
                                  // Use the captured selection state from right-click time
                                  const capturedSelection = contextMenuSelectionRef.current;
                                  const currentSelectionSize = capturedSelection ? capturedSelection.placeholderIds.length : 0;
                                  const currentSelectedPlaceholders = capturedSelection ? capturedSelection.placeholderIds : [];
                                  const clickedPlaceholderId = capturedSelection ? capturedSelection.clickedPlaceholderId : adjustedPlaceholder.id;
                                  const isClickedPlaceholderSelected = currentSelectedPlaceholders.includes(clickedPlaceholderId);
                                  
                                  // Determine which images to remove - match deleteHoveredImage logic exactly
                                  let placeholdersToRemove: string[] = [];
                                  
                                  // Use the EXACT same logic as deleteHoveredImage but with captured state
                                  // Priority 1: If we have selected images and the clicked image is among them, remove all selected
                                  if (currentSelectionSize > 0 && clickedPlaceholderId && isClickedPlaceholderSelected) {
                                    placeholdersToRemove = currentSelectedPlaceholders;
                                  }
                                  // Priority 2: If we have selected images but clicked image is not selected, remove all selected
                                  else if (currentSelectionSize > 0 && (!clickedPlaceholderId || !isClickedPlaceholderSelected)) {
                                    placeholdersToRemove = currentSelectedPlaceholders;
                                  }
                                  // Priority 3: Fallback to single clicked image (original behavior)
                                  else if (clickedPlaceholderId) {
                                    placeholdersToRemove = [clickedPlaceholderId];
                                  } else {
                                    // No valid target for deletion
                                    return;
                                  }
                                  
                                  // Filter out empty placeholders (same as deleteHoveredImage)
                                  const validPlaceholdersToRemove = placeholdersToRemove.filter(placeholderId => {
                                    const placement = activeSpread.images.find(p => p.placeholderId === placeholderId);
                                    return placement && placement.imageId;
                                  });
                                  
                                  if (validPlaceholdersToRemove.length === 0) return;
                                  
                                  // Clear selection after removal
                                  setSelectedImages(new Set());
                                  
                                  // Handle auto template switching if enabled - use EXACT same logic as deleteHoveredImage
                                  if (autoSwitchTemplateOnRemove && onRemoveImageAndSwitchTemplate && validPlaceholdersToRemove.length === 1) {
                                    // For single image removal, use existing template switching logic (matching deleteHoveredImage)
                                    const placeholderToRemove = validPlaceholdersToRemove[0];
                                    const currentImageCount = activeSpread.images.filter(p => p.imageId).length;
                                    const targetImageCount = currentImageCount - 1;
                                    
                                    if (targetImageCount > 0) {
                                      const newTemplate = findExactTemplateForImageCount ? findExactTemplateForImageCount(targetImageCount) : null;
                                      if (newTemplate) {
                                        onRemoveImageAndSwitchTemplate(currentSpreadId, placeholderToRemove);
                                        toast.info(`Image removed and template switched to '${newTemplate.name}'.`);
                                        return; // Early return to avoid duplicate toast
                                      } else {
                                        // No exact template found, just remove without switching
                                        onUpdateSpreadImages(currentSpreadId, placeholderToRemove, null);
                                        toast.info(`Image removed. No template found for ${targetImageCount} images.`);
                                        return;
                                      }
                                    } else {
                                      // Last image being removed
                                      onUpdateSpreadImages(currentSpreadId, placeholderToRemove, null);
                                      toast.info("Last image removed.");
                                      return;
                                    }
                                  } else {
                                    // For multiple image removal or when auto-switch is disabled - match deleteHoveredImage logic
                                    if (validPlaceholdersToRemove.length > 1 && onRemoveMultipleImagesAndSwitchTemplate) {
                                      // Use the new multi-image removal function that handles proper image mapping
                                      // (The function itself will check autoSwitchTemplateOnRemove internally)
                                      onRemoveMultipleImagesAndSwitchTemplate(currentSpreadId, validPlaceholdersToRemove);
                                      // Toast is handled by the function itself
                                      return; // Early return to avoid duplicate toast
                                    } else {
                                      // Fallback to sequential removal (matching deleteHoveredImage)
                                      validPlaceholdersToRemove.forEach(placeholderId => {
                                        onUpdateSpreadImages(currentSpreadId, placeholderId, null);
                                      });

                                      // Try to switch template after removing all images if auto-switch is enabled
                                      if (autoSwitchTemplateOnRemove && findExactTemplateForImageCount) {
                                        const currentImageCount = activeSpread.images.filter(p => p.imageId).length;
                                        const targetImageCount = currentImageCount - validPlaceholdersToRemove.length;
                                        
                                        if (targetImageCount > 0) {
                                          const newTemplate = findExactTemplateForImageCount(targetImageCount);
                                          if (newTemplate && onSelectTemplate) {
                                            onSelectTemplate(newTemplate.id);
                                            toast.info(`Images removed and template switched to '${newTemplate.name}'.`);
                                            return; // Early return to avoid duplicate toast
                                          }
                                        }
                                      }
                                    }
                                  }
                                  
                                  // Notify selection change
                                  onSelectionChange?.(0);
                                  
                                  // Show success message
                                  const removedCount = validPlaceholdersToRemove.length;
                                  toast.success(`Removed ${removedCount} image${removedCount > 1 ? 's' : ''}`);
                                }}
                                disabled={!image} // Disable if no image
                                className="text-red-600 focus:text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Remove Image{(() => {
                                  const capturedSelection = contextMenuSelectionRef.current;
                                  const currentSelectionSize = capturedSelection ? capturedSelection.placeholderIds.length : 0;
                                  const clickedPlaceholderId = capturedSelection ? capturedSelection.clickedPlaceholderId : adjustedPlaceholder.id;
                                  const isClickedSelected = capturedSelection ? capturedSelection.placeholderIds.includes(clickedPlaceholderId) : false;
                                  
                                  // Show count when removing multiple - matches deleteHoveredImage logic
                                  if (currentSelectionSize > 0) {
                                    // Will remove all selected in both Priority 1 and Priority 2 cases
                                    if ((currentSelectionSize > 0 && clickedPlaceholderId && isClickedSelected) ||
                                        (currentSelectionSize > 0 && (!clickedPlaceholderId || !isClickedSelected))) {
                                      return currentSelectionSize > 1 ? ` (${currentSelectionSize})` : '';
                                    }
                                  }
                                  return '';
                                })()}
                              </ContextMenuItem>
                            </ContextMenuContent>
                          </ContextMenu>
                          );
                        })() : (
                          // Placeholder content
                          <div className="w-full h-full overflow-hidden relative bg-gray-100 flex items-center justify-center">
                            <div className="flex flex-col items-center justify-center gap-2 opacity-50 pointer-events-none"> <ImagePlus className="w-6 h-6 text-gray-400" /> <span className="text-xs text-gray-500">Drop photo here</span> </div>
                          </div>
                        )}
                        
                        {/* Selection Border Overlays - Moved outside image container to avoid dark mode CSS conflicts */}
                        {selectedImages.has(adjustedPlaceholder.id) && (() => {
                          const outsideEdges = calculatePlaceholderOutsideEdges(adjustedPlaceholder.id);
                          const isHideBleedMode = bleedAreaMode === 'hide' && (bleedValue ?? 0) > 0;
                          const bleedOffsetPx = isHideBleedMode ? (bleedValue ?? 0) * pixelsPerPoint : 0;
                          
                          return (
                            <>
                              {/* Top Border */}
                              <div 
                                className="absolute left-0 right-0 pointer-events-none"
                                style={{
                                  top: isHideBleedMode && outsideEdges.top ? bleedOffsetPx : 0,
                                  height: '4px',
                                  backgroundColor: '#0ea5e9',
                                  zIndex: 10
                                }}
                              />
                              {/* Bottom Border */}
                              <div 
                                className="absolute left-0 right-0 pointer-events-none"
                                style={{
                                  bottom: isHideBleedMode && outsideEdges.bottom ? bleedOffsetPx : 0,
                                  height: '4px',
                                  backgroundColor: '#0ea5e9',
                                  zIndex: 10
                                }}
                              />
                              {/* Left Border */}
                              <div 
                                className="absolute top-0 bottom-0 pointer-events-none"
                                style={{
                                  left: isHideBleedMode && outsideEdges.left ? bleedOffsetPx : 0,
                                  width: '4px',
                                  backgroundColor: '#0ea5e9',
                                  zIndex: 10
                                }}
                              />
                              {/* Right Border */}
                              <div 
                                className="absolute top-0 bottom-0 pointer-events-none"
                                style={{
                                  right: isHideBleedMode && outsideEdges.right ? bleedOffsetPx : 0,
                                  width: '4px',
                                  backgroundColor: '#0ea5e9',
                                  zIndex: 10
                                }}
                              />
                            </>
                          );
                        })()}
                        
                        {/* Image Quality Warning - Apply filtering directly in render */}
                        {(() => {
                          const issue = qualityIssues[adjustedPlaceholder.id];
                          
                          // Only proceed if there's an image and an issue
                          if (!image || !issue) return null;
                          
                          // First check if quality indicators are enabled globally
                          if (!showQualityIndicators) return null;
                          
                          // Removed the "Only Show Poor Quality" check here, as the setting is removed.
                          // We just check if indicators are globally enabled and if there's an issue.
                          // console.log(`[Quality Render EVAL] Placeholder: ${adjustedPlaceholder.id}, Status: ${issue.status}, showIndicators: ${showQualityIndicators}, Should Render: true`); // DEBUG LOG REMOVED
                          
                          // Render the warning component
                          return (
                            <ImageQualityWarning
                              qualityInfo={issue}
                              bleedAreaMode={bleedAreaMode} // Pass bleedAreaMode prop
                              imageBorderSize={
                                individualBorderSettings[adjustedPlaceholder.id]?.enabled
                                ? individualBorderSettings[adjustedPlaceholder.id].size
                                : localImageBorderSize
                              }
                            />
                          );
                        })()}
                        
                        {/* Template Editor Resize Handles - Show for all placeholders */}
                        {(() => {
                          const isCustomTemplate = !!customTemplatesState[spread.id];
                          const isActivelyResizing = isResizing && resizeData?.placeholderId === adjustedPlaceholder.id;
                          
                          // Helper function to get handle visibility classes
                          const getHandleClasses = (baseClasses: string, isCorner: boolean = false) => {
                            // Show border indicators when actively resizing, but no background for corners
                            if (isCorner) {
                              // For corners: no background fill, just transparent to avoid green circles
                              return `${baseClasses} bg-transparent`;
                            } else {
                              // For edges: show green/blue background when actively resizing
                              const activeClasses = isActivelyResizing 
                                ? (image ? 'bg-green-400/60' : 'bg-blue-400/60')
                                : 'bg-transparent';
                              return `${baseClasses} ${activeClasses}`;
                            }
                          };
                          
                          return (
                          <>
                            {/* Top Edge Handle */}
                            <div
                              className={getHandleClasses('absolute top-0 left-0 right-0 h-1 cursor-ns-resize transition-colors z-50')}
                              onMouseDown={(e) => {
                                handleResizeStart(e, adjustedPlaceholder.id, 'top');
                              }}
                              onClick={(e) => {
                              }}
                              title="Drag to resize template placeholder"
                              style={{ pointerEvents: 'auto' }}
                            />
                            {/* Bottom Edge Handle */}
                            <div
                              className={getHandleClasses('absolute bottom-0 left-0 right-0 h-1 cursor-ns-resize transition-colors z-50')}
                              onMouseDown={(e) => {
                                handleResizeStart(e, adjustedPlaceholder.id, 'bottom');
                              }}
                              onClick={(e) => {
                              }}
                              title="Drag to resize template placeholder"
                              style={{ pointerEvents: 'auto' }}
                            />
                            {/* Left Edge Handle */}
                            <div
                              className={getHandleClasses('absolute top-0 bottom-0 left-0 w-1 cursor-ew-resize transition-colors z-30')}
                              onMouseDown={(e) => handleResizeStart(e, adjustedPlaceholder.id, 'left')}
                              title="Drag to resize template placeholder"
                            />
                            {/* Right Edge Handle */}
                            <div
                              className={getHandleClasses('absolute top-0 bottom-0 right-0 w-1 cursor-ew-resize transition-colors z-30')}
                              onMouseDown={(e) => handleResizeStart(e, adjustedPlaceholder.id, 'right')}
                              title="Drag to resize template placeholder"
                            />
                            {/* Corner Handles */}
                            <div
                              className="absolute top-0 left-0 w-4 h-4 cursor-nw-resize z-30 flex items-center justify-center"
                              onMouseDown={(e) => handleResizeStart(e, adjustedPlaceholder.id, 'top-left')}
                              title="Drag to resize template placeholder"
                            >
                              <div className={getHandleClasses('w-2 h-2 transition-colors rounded-sm', true)} />
                            </div>
                            <div
                              className="absolute top-0 right-0 w-4 h-4 cursor-ne-resize z-30 flex items-center justify-center"
                              onMouseDown={(e) => handleResizeStart(e, adjustedPlaceholder.id, 'top-right')}
                              title="Drag to resize template placeholder"
                            >
                              <div className={getHandleClasses('w-2 h-2 transition-colors rounded-sm', true)} />
                            </div>
                            <div
                              className="absolute bottom-0 left-0 w-4 h-4 cursor-sw-resize z-30 flex items-center justify-center"
                              onMouseDown={(e) => handleResizeStart(e, adjustedPlaceholder.id, 'bottom-left')}
                              title="Drag to resize template placeholder"
                            >
                              <div className={getHandleClasses('w-2 h-2 transition-colors rounded-sm', true)} />
                            </div>
                            <div
                              className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize z-30 flex items-center justify-center"
                              onMouseDown={(e) => handleResizeStart(e, adjustedPlaceholder.id, 'bottom-right')}
                              title="Drag to resize template placeholder"
                            >
                              <div className={getHandleClasses('w-2 h-2 transition-colors rounded-sm', true)} />
                            </div>
                          </>
                          );
                        })()}
                        
                      </div> // End Single Placeholder Div
                    );
                  }) // End adjustedLayout.map
                      ) : (
                        // Render blank state ONLY if it's the active spread AND (layout is empty OR template is blank)
                        (layoutForThisSpread.length === 0 || spread.templateId === '__blank__') && (
                          <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-auto">
                            <ImagePlus className="w-16 h-16 text-gray-300 mb-4" />
                            {rejectionMessage ? (
                              <p className="text-red-500 text-center font-medium">{rejectionMessage}</p>
                            ) : (
                              <p className="text-gray-500 text-center"> Drop one or more imported photo library images here...<br/>or choose a template...</p>
                            )}
                          </div>
                        )
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div> // End spread container
            );
          })}
        </motion.div> {/* End Inner Track */}
      </div> {/* End Outer Container */}

      {/* Full Image Preview Modal */}
      <Dialog open={showFullImageModal} onOpenChange={(open) => {
        if (!open) {
          setImageExifData(null); // Clear EXIF data when closing
        }
        setShowFullImageModal(open);
      }}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] flex flex-col p-2 sm:p-4">
          {/* Add VisuallyHidden title for accessibility */}
          <VisuallyHidden>
            <DialogTitle>Image Preview</DialogTitle>
            {/* Add VisuallyHidden description for accessibility */}
            <DialogDescription>Full resolution preview of the selected image.</DialogDescription>
          </VisuallyHidden>
          
          {/* Image container */}
          <div className="relative w-full flex-1 flex items-center justify-center overflow-hidden"> {/* Added overflow-hidden */}
            {modalImageSrc ? (
              <img
                src={modalImageSrc}
                alt="Full resolution preview"
                className="max-w-full max-h-[80vh] object-contain" // Adjusted max-h to leave room for EXIF info
              />
            ) : (
              <p>Loading image...</p> // Placeholder while fetching or if src is null
            )}
          </div>
          
          {/* EXIF Information Section */}
          {(imageExifData || modalImageFilename) && ( // Show grey area if either EXIF or filename exists
            <div className="w-full mt-3 px-2 py-1.5 bg-gray-100 rounded text-xs text-gray-700 flex flex-wrap gap-x-3 gap-y-1 justify-center"> {/* Adjusted gap */}
              {/* Filename display */}
              {modalImageFilename && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">File:</span>
                  <span className="truncate" title={modalImageFilename}>{modalImageFilename}</span>
                </div>
              )}
              {/* Dimensions */}
              {imageExifData && imageExifData.dimensions && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Dimensions:</span>
                  <span>{imageExifData.dimensions.width} × {imageExifData.dimensions.height}px</span>
                </div>
              )}
              
              {/* Color Profile - Enhanced to show more details */}
              {imageExifData && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Color Profile:</span>
                  <span>
                    {/* For Unknown ICC Profiles, check if we have a ProfileClass or other identifiers */}
                    {imageExifData.colorProfile === "ICC Profile (Unknown)" && imageExifData.raw?.ICC
                      ? `ICC Profile (${imageExifData.raw.ICC.ProfileClass || "Unknown Type"})`
                      : imageExifData.colorProfile?.replace(" (default)", "") || "sRGB"}
                      
                    {/* Only show ColorSpaceData if it's not redundant with the profile name */}
                    {imageExifData.raw?.ICC?.ColorSpaceData &&
                     imageExifData.colorProfile && !imageExifData.colorProfile.includes(imageExifData.raw.ICC.ColorSpaceData) &&
                     ` • ${imageExifData.raw.ICC.ColorSpaceData}`}
                      
                    {/* Only show sRGB if it's not already mentioned in the profile name */}
                    {imageExifData.raw?.ColorSpace === 1 &&
                     imageExifData.colorProfile && !imageExifData.colorProfile.toLowerCase().includes('srgb') &&
                     ` • sRGB`}
                      
                    {/* Only show Adobe RGB if it's not already mentioned in the profile name */}
                    {imageExifData.raw?.ColorSpace === 2 &&
                     imageExifData.colorProfile && !imageExifData.colorProfile.toLowerCase().includes('adobe') &&
                     ` • Adobe RGB`}
                  </span>
                </div>
              )}
              
              {/* Star Rating */}
              {imageExifData && imageExifData.rating > 0 && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Rating:</span>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-3 h-3 ${star <= imageExifData.rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
                      />
                    ))}
                  </div>
                </div>
              )}
              
              {/* Time Captured */}
              {imageExifData && imageExifData.captureTime && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Captured:</span>
                  <span>{imageExifData.captureTime}</span>
                </div>
              )}
            </div>
          )}
          
          {/* Loading indicator for EXIF data */}
          {loadingExif && !imageExifData && (
            <div className="w-full mt-3 px-2 py-1.5 bg-gray-100 rounded text-xs text-gray-500 text-center">
              Loading image information...
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Duplicate Image Warning Dialog */}
      <AlertDialog open={showDuplicateWarning} onOpenChange={setShowDuplicateWarning}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Image Already Used</AlertDialogTitle>
            <AlertDialogDescription>
              {duplicateWarningData && (
                <>
                  The image "{duplicateWarningData.image.name}" has already been used in this project.
                  <br /><br />
                  Would you like to use it again? This will create a duplicate placement of the same image.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDuplicateCancel}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDuplicateConfirm}>
              Use Again
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Portal-based Visual Drop Choice Overlay */}
      <VisualDropChoice
        isVisible={visualDropChoice.isVisible}
        onChoiceSelect={handleVisualDropChoice}
        placeholderId={visualDropChoice.placeholderId}
        onProcessDrop={handleDrop}
        bleedAreaMode={bleedAreaMode}
        bleedInPixels={bleedInPixels}
        outsideEdges={calculatePlaceholderOutsideEdges(visualDropChoice.placeholderId)}
        onDragLeave={() => {
          if (dragOverTimeoutRef.current) {
            clearTimeout(dragOverTimeoutRef.current);
          }
          lastVisualDropPlaceholderRef.current = '';
          setVisualDropChoice({ isVisible: false, placeholderId: '' });
        }}
      />

      {/* Portal-based Controls Popout for Small Placeholders */}
      {hoveredPlaceholderId && 
       smallPlaceholders.has(hoveredPlaceholderId) && 
       placeholderPositions[hoveredPlaceholderId] && 
       (() => {
         const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
         const imageAssignment = activeSpread?.images.find(img => img.placeholderId === hoveredPlaceholderId);
         const image = imageAssignment?.imageId ? images.find(img => img.id === imageAssignment.imageId) : null;
         
         if (!image) return null;
         
         const defaultFitMode = defaultDropModeIsCover ? 'cover' : 'contain';
         const transformToApply = imageAssignment?.transform ?? { scale: 1, focalX: 0.5, focalY: 0.5, fit: defaultFitMode };
         const transform: ImageTransform = {
           scale: transformToApply.scale ?? 1,
           focalX: transformToApply.focalX ?? 0.5,
           focalY: transformToApply.focalY ?? 0.5,
           fit: transformToApply.fit ?? defaultFitMode,
           rotation: transformToApply.rotation ?? 0
         };
         
         const currentBorderSettings = individualBorderSettings[hoveredPlaceholderId] || {
           enabled: false,
           size: imageBorderSize || 0,
           color: imageBorderColor || '#000000',
         };

         return createPortal(
           <PlaceholderControlsPopout
             placeholderId={hoveredPlaceholderId}
             position={placeholderPositions[hoveredPlaceholderId]}
             image={image}
             transform={transform}
             theme={theme}
             individualBorderSettings={currentBorderSettings}
             onZoomChange={([newScaleFromSlider]) => {
               const currentFitMode = transform.fit ?? 'contain';
               const currentFocalX = transform.focalX ?? 0.5;
               const currentFocalY = transform.focalY ?? 0.5;

               const containerDims = placeholderPixelDims[hoveredPlaceholderId];
               const imageNativeDims = getImageDimensions && image ? getImageDimensions(image.id) : undefined;

               let minRequiredScale = 1;
               if (currentFitMode === 'cover' && containerDims && imageNativeDims && imageNativeDims.width > 0 && imageNativeDims.height > 0) {
                 const containerWidth = containerDims.width;
                 const containerHeight = containerDims.height;
                 const imageWidth = imageNativeDims.width;
                 const imageHeight = imageNativeDims.height;
                 const imageAspect = imageWidth / imageHeight;
                 const containerAspect = containerWidth / containerHeight;

                 if (imageAspect > containerAspect) {
                   minRequiredScale = containerHeight / imageHeight;
                 } else {
                   minRequiredScale = containerWidth / imageWidth;
                 }
                 minRequiredScale = Math.max(1, minRequiredScale);
               }

               const targetScaleForAnimation = currentFitMode === 'cover' ?
                 Math.max(newScaleFromSlider, minRequiredScale) :
                 newScaleFromSlider;

               let focalXForAnimation = currentFocalX;
               let focalYForAnimation = currentFocalY;
               if (targetScaleForAnimation <= 1 && currentFitMode === 'contain') {
                 focalXForAnimation = 0.5;
                 focalYForAnimation = 0.5;
               }

               targetZoomLevelsRef.current[hoveredPlaceholderId] = targetScaleForAnimation;

               if (currentZoomLevelsRef.current[hoveredPlaceholderId] === undefined) {
                 currentZoomLevelsRef.current[hoveredPlaceholderId] = transform.scale ?? 1;
               }

               if (zoomAnimationFrameRef.current[hoveredPlaceholderId]) {
                 cancelAnimationFrame(zoomAnimationFrameRef.current[hoveredPlaceholderId]!);
                 zoomAnimationFrameRef.current[hoveredPlaceholderId] = null;
               }
             
               zoomAnimationFrameRef.current[hoveredPlaceholderId] = requestAnimationFrame(() =>
                 animateZooming(
                   currentSpreadId,
                   hoveredPlaceholderId,
                   currentFitMode,
                   focalXForAnimation,
                   focalYForAnimation
                 )
               );
               checkAndApplyAutoCover(currentSpreadId, hoveredPlaceholderId, 'ZoomSlider');
             }}
             onRotationStart={(e) => handleRotationMouseDown(e, hoveredPlaceholderId)}
             onFitModeToggle={() => {
               const currentFitMode = transform.fit ?? 'contain';
               const newFitMode = currentFitMode === 'contain' ? 'cover' : 'contain';
               
               const newTransform = {
                 scale: 1,
                 focalX: 0.5,
                 focalY: 0.5,
                 rotation: 0,
                 fit: newFitMode as 'contain' | 'cover',
                 // @ts-ignore: Mark this as a change that should trigger undo state save
                 _shouldSaveUndo: true
               };
               
               throttledUpdateTransform(
                 currentSpreadId,
                 hoveredPlaceholderId,
                 newTransform,
                 newFitMode
               );
             }}
             onBorderToggle={() => {
               if (hoveredPlaceholderId && imageAssignment) {
                 const newEnabledState = !currentBorderSettings.enabled;
                 const updatedSettings = {
                   ...currentBorderSettings,
                   enabled: newEnabledState,
                   size: newEnabledState && currentBorderSettings.size === 0 ? (imageBorderSize > 0 ? imageBorderSize : 1) : currentBorderSettings.size,
                   color: newEnabledState && currentBorderSettings.color === imageBorderColor ? imageBorderColor : currentBorderSettings.color,
                 };

                 setIndividualBorderSettings(prev => ({
                   ...prev,
                   [hoveredPlaceholderId!]: updatedSettings,
                 }));

                 const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
                 const targetPlacement = activeSpread?.images.find(p => p.placeholderId === hoveredPlaceholderId);
                 if (activeSpread && targetPlacement && onUpdateSpreadImages) {
                   const updatedPlacement: ImagePlacement = {
                     ...targetPlacement,
                     individualBorder: updatedSettings,
                   };
                   onUpdateSpreadImages(currentSpreadId, hoveredPlaceholderId, updatedPlacement);
                   toast.info(`Individual border ${newEnabledState ? 'enabled' : 'disabled'} for this image.`);
                 }
               }
             }}
             onBorderSizeChange={([newSize]) => {
               const updatedSettings = { ...currentBorderSettings, size: newSize };
               setIndividualBorderSettings(prev => ({
                 ...prev,
                 [hoveredPlaceholderId!]: updatedSettings,
               }));
               
               const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
               const targetPlacement = activeSpread?.images.find(p => p.placeholderId === hoveredPlaceholderId);
               if (activeSpread && targetPlacement && onUpdateSpreadImages) {
                 const updatedPlacement: ImagePlacement = {
                   ...targetPlacement,
                   individualBorder: updatedSettings,
                 };
                 onUpdateSpreadImages(currentSpreadId, hoveredPlaceholderId, updatedPlacement);
               }
             }}
             onBorderColorChange={(color) => {
               const updatedSettings = { ...currentBorderSettings, color };
               setIndividualBorderSettings(prev => ({
                 ...prev,
                 [hoveredPlaceholderId!]: updatedSettings,
               }));
               
               const activeSpread = renderedSpreads.find(s => s.id === currentSpreadId);
               const targetPlacement = activeSpread?.images.find(p => p.placeholderId === hoveredPlaceholderId);
               if (activeSpread && targetPlacement && onUpdateSpreadImages) {
                 const updatedPlacement: ImagePlacement = {
                   ...targetPlacement,
                   individualBorder: updatedSettings,
                 };
                 onUpdateSpreadImages(currentSpreadId, hoveredPlaceholderId, updatedPlacement);
               }
             }}
                           onPopoutEnter={() => {
                // Clear any pending hide timeout when entering popout
                if (hoverDelayTimeoutRef.current) {
                  clearTimeout(hoverDelayTimeoutRef.current);
                  hoverDelayTimeoutRef.current = null;
                }
                setIsPopoutHovered(true);
              }}
                             onPopoutLeave={() => {
                 const currentPlaceholderId = hoveredPlaceholderId; // Capture current value
                 setIsPopoutHovered(false);
                 
                 // Check if mouse is still within SpreadCanvas bounds
                 const checkMouseInCanvas = () => {
                   const canvasContainer = canvasContainerRef.current;
                   if (!canvasContainer) {
                     // Can't determine canvas bounds, clean up to be safe
                     setHoveredPlaceholderId(null);
                     setHoveredImage(null);
                     onHoverChange?.({ placeholderId: null, isZoomed: false });
                     return;
                   }

                   // Get current mouse position
                   const handleMouseMove = (e: MouseEvent) => {
                     const rect = canvasContainer.getBoundingClientRect();
                     const isOutsideCanvas = (
                       e.clientX < rect.left ||
                       e.clientX > rect.right ||
                       e.clientY < rect.top ||
                       e.clientY > rect.bottom
                     );

                     if (isOutsideCanvas) {
                       // Mouse is outside canvas, clean up immediately
                       setHoveredPlaceholderId(null);
                       setHoveredImage(null);
                       onHoverChange?.({ placeholderId: null, isZoomed: false });
                       document.removeEventListener('mousemove', handleMouseMove);
                     } else {
                       // Mouse is still in canvas, use normal timeout logic
                       setTimeout(() => {
                         if (!isPopoutHovered && hoveredPlaceholderId === currentPlaceholderId) {
                           setHoveredPlaceholderId(null);
                           setHoveredImage(null);
                           onHoverChange?.({ placeholderId: null, isZoomed: false });
                         }
                       }, 50);
                       document.removeEventListener('mousemove', handleMouseMove);
                     }
                   };

                   // Add temporary listener to check next mouse move
                   document.addEventListener('mousemove', handleMouseMove, { once: true });
                   
                   // Fallback timeout in case no mouse move occurs
                   setTimeout(() => {
                     document.removeEventListener('mousemove', handleMouseMove);
                     if (!isPopoutHovered && hoveredPlaceholderId === currentPlaceholderId) {
                       setHoveredPlaceholderId(null);
                       setHoveredImage(null);
                       onHoverChange?.({ placeholderId: null, isZoomed: false });
                     }
                   }, 200);
                 };

                 checkMouseInCanvas();
               }}
              isRotating={isRotating && rotatingPlaceholderId === hoveredPlaceholderId}
              bleedAreaMode={bleedAreaMode}
              localImageBorderSize={imageBorderSize || 0}
            />,
           document.body
         );
       })()}

    </div> // End Main Div
  ); // <<< END OF RETURN STATEMENT
}); // <<< END OF forwardRef

// PlaceholderControlsPopout component for small placeholders
interface PlaceholderControlsPopoutProps {
  placeholderId: string;
  position: DOMRect;
  image: ImageFile;
  transform: ImageTransform;
  theme: 'light' | 'dark';
  individualBorderSettings: { enabled: boolean; size: number; color: string };
  onZoomChange: (value: number[]) => void;
  onRotationStart: (e: React.MouseEvent) => void;
  onFitModeToggle: () => void;
  onBorderToggle: () => void;
  onBorderSizeChange: (value: number[]) => void;
  onBorderColorChange: (color: string) => void;
  onPopoutEnter: () => void;
  onPopoutLeave: () => void;
  isRotating: boolean;
  bleedAreaMode?: 'hide' | 'indicate' | 'ignore';
  localImageBorderSize: number;
}

const PlaceholderControlsPopout: React.FC<PlaceholderControlsPopoutProps> = ({
  placeholderId,
  position,
  image,
  transform,
  theme,
  individualBorderSettings,
  onZoomChange,
  onRotationStart,
  onFitModeToggle,
  onBorderToggle,
  onBorderSizeChange,
  onBorderColorChange,
  onPopoutEnter,
  onPopoutLeave,
  isRotating,
  bleedAreaMode,
  localImageBorderSize
}) => {
  // Calculate popout position below the placeholder
  const popoutTop = position.bottom + window.scrollY + 8; // 8px offset from placeholder
  const popoutLeft = Math.max(8, Math.min(
    position.left + window.scrollX,
    window.innerWidth - 320 - 8 // Ensure popout fits in viewport (320px width + 8px margin)
  ));

  // Adjust if too close to bottom of screen
  const adjustedTop = Math.min(popoutTop, window.innerHeight - 150 + window.scrollY);

  return (
    <div
      className="fixed z-[9999] bg-black/75 text-white p-3 rounded-lg shadow-xl border border-gray-600 pointer-events-auto"
      style={{
        top: `${adjustedTop}px`,
        left: `${popoutLeft}px`,
        width: '320px',
        filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none'
      }}
      onClick={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.stopPropagation()}
      onMouseEnter={onPopoutEnter}
      onMouseLeave={onPopoutLeave}
    >
      {/* Arrow pointing to placeholder */}
      <div
        className="absolute w-0 h-0 border-l-[8px] border-r-[8px] border-b-[8px] border-l-transparent border-r-transparent border-b-black/75"
        style={{
          top: '-8px',
          left: `${Math.min(Math.max(16, position.left + position.width/2 - popoutLeft), 304)}px` // Center arrow on placeholder
        }}
      />
      
      <div className="space-y-3">
        {/* Zoom Slider */}
        <div className="space-y-2">
          <label className="text-xs text-gray-300">Zoom</label>
          <Slider
            min={1}
            max={3}
            step={0.02}
            value={[transform.scale ?? 1]}
            onValueChange={onZoomChange}
            className="w-full h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-white/70 [&>span:first-child>span]:h-2"
            aria-label={`Zoom image ${image.name}`}
          />
        </div>

        {/* Controls row */}
        <div className="flex items-center justify-between space-x-2">
          {/* Border toggle */}
          <button
            className="p-2 text-white hover:bg-white/20 rounded"
            onClick={onBorderToggle}
            aria-label="Image border"
            title="Image border"
          >
            <PanelsLeftBottom className="w-4 h-4" />
          </button>

          {/* Rotation button */}
          <button
            className="p-2 text-white hover:bg-white/20 rounded"
            onMouseDown={onRotationStart}
            title={`Click and drag vertically to rotate image (${Math.round(transform.rotation || 0)}°)`}
            style={{ 
              cursor: 'ns-resize',
              userSelect: 'none',
              backgroundColor: isRotating ? 'rgba(255,255,255,0.3)' : undefined
            }}
          >
            <RotateCw className="w-4 h-4" />
          </button>

          {/* Fit mode toggle */}
          <button
            className="p-2 text-white hover:bg-white/20 rounded"
            onClick={onFitModeToggle}
            aria-label={`Toggle fit mode: currently ${transform.fit}`}
            title={`Toggle fit mode: currently ${transform.fit}`}
          >
            {(transform.fit ?? 'contain') === 'contain' ? (
              <Expand className="w-4 h-4" />
            ) : (
              <Shrink className="w-4 h-4" />
            )}
          </button>
        </div>

        {/* Border controls - shown when border is enabled */}
        {individualBorderSettings.enabled && (
          <div className="space-y-2 p-2 bg-white/10 rounded">
            <label className="text-xs text-gray-300">Border Size</label>
            <div className="flex items-center space-x-2">
              <Slider
                min={0}
                max={20}
                step={0.1}
                value={[individualBorderSettings.size]}
                onValueChange={onBorderSizeChange}
                className="flex-1 h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-white/70 [&>span:first-child>span]:h-2"
                aria-label="Border Size"
              />
              <span className="text-xs min-w-[3ch] text-right">
                {individualBorderSettings.size.toFixed(1)}pt
              </span>
            </div>
            
            {/* Simple color picker */}
            <div className="flex items-center space-x-2">
              <label className="text-xs text-gray-300">Color</label>
              <input
                type="color"
                value={rgbaToHex(individualBorderSettings.color || 'rgba(0,0,0,1)')}
                onChange={(e) => onBorderColorChange(hexToRgba(e.target.value))}
                className="w-8 h-6 rounded border border-gray-400 cursor-pointer"
                style={{
                  filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none'
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

SpreadCanvas.displayName = 'SpreadCanvas';
export default React.memo(SpreadCanvas);
