import React, { useState, useEffect, useRef } from 'react';
import { ImageQualityInfo } from '@/lib/imageQuality';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import {
  AlertTriangle,
  CheckCircle2,
  FileText,
  Image as ImageIcon,
  CloudUpload,
  Settings2,
  BookOpen,
  Copy,
  Layers,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Palette, // Added for Color Space
  FolderSymlink // Added for Export button
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export type CoverExportOption = 'separate' | 'include' | 'none';
export type PdfFormatOption = 'pdf' | 'jpeg';
export type PdfLayoutOption = 'spreads' | 'pages';
export type CoverLayoutOption = 'single' | 'double';
export type ColorSpaceOption = 'srgb' | 'adobergb' | 'passthrough';

// Import the existing WarningSpreadDetail interface
import { WarningSpreadDetail } from './ImageQualityExportWarning';

interface ExportDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onExport: (
    coverOption: CoverExportOption,
    pdfFormat: PdfFormatOption,
    pdfLayout: PdfLayoutOption,
    coverLayout: CoverLayoutOption,
    colorSpace: ColorSpaceOption,
    exportEmptySpreadsAsIs: boolean
  ) => void;
  hasCover: boolean;
  hasCoverImage: boolean;
  missingFilesCount: number;
  lowResImagesCount: number;
  emptySpreadsCount: number;
  onLocateFilesClick: () => void;
  onReviewImagesClick: () => void;
  onDeleteEmptySpreads: () => void; // Added
  qualityIssueDetails?: WarningSpreadDetail[];
  onNavigateToSpread?: (spreadId: string) => void;
  onExportCancel?: () => void; // New prop to signal cancellation
  isExporting?: boolean; // New prop to track export in progress
}

const ExportDialog: React.FC<ExportDialogProps> = ({
  isOpen,
  onOpenChange,
  onExport,
  hasCover,
  hasCoverImage,
  missingFilesCount,
  lowResImagesCount,
  emptySpreadsCount,
  onLocateFilesClick,
  onReviewImagesClick,
  onDeleteEmptySpreads, // Added
  qualityIssueDetails = [],
  onNavigateToSpread,
  onExportCancel, // Destructure the new prop
  isExporting = false, // Destructure the new prop with default value
}) => {
  const [selectedCoverOption, setSelectedCoverOption] = useState<CoverExportOption>('include');
  const [selectedPdfFormat, setSelectedPdfFormat] = useState<PdfFormatOption>('pdf');
  const [selectedPdfLayout, setSelectedPdfLayout] = useState<PdfLayoutOption>('spreads');
  const [selectedCoverLayout, setSelectedCoverLayout] = useState<CoverLayoutOption>('double');
  const [selectedColorSpace, setSelectedColorSpace] = useState<ColorSpaceOption>('srgb');
  const [isQualityPopoverOpen, setIsQualityPopoverOpen] = useState(false);
  const [ignoreEmptySpreads, setIgnoreEmptySpreads] = useState(false);
  const [ignoreLowResWarnings, setIgnoreLowResWarnings] = useState(false); // New state

  const canExportCover = hasCover && hasCoverImage;

  useEffect(() => {
    if (isOpen) {
      // Reset to default or sensible option when dialog opens
      if (canExportCover) {
        setSelectedCoverOption('include');
      } else {
        setSelectedCoverOption('none');
      }
      setSelectedPdfFormat('pdf');
      setSelectedPdfLayout('spreads');
      setSelectedCoverLayout('double');
      setSelectedColorSpace('srgb');
      setIgnoreEmptySpreads(false); // Reset ignore state
      setIgnoreLowResWarnings(false); // Reset ignore low-res state
    }
  }, [isOpen, canExportCover]);

  useEffect(() => {
    // If empty spreads are deleted (count becomes 0), reset ignore state
    if (emptySpreadsCount === 0) {
      setIgnoreEmptySpreads(false);
    }
    // If low res image count becomes 0 (e.g., user fixes them), reset ignore state
    if (lowResImagesCount === 0) {
      setIgnoreLowResWarnings(false);
    }
  }, [emptySpreadsCount, lowResImagesCount]);

  // Adjust cover option if format changes to JPEG and cover was 'separate'
  useEffect(() => {
    if (selectedPdfFormat === 'jpeg' && selectedCoverOption === 'separate') {
      setSelectedCoverOption('include');
    }
  }, [selectedPdfFormat, selectedCoverOption]);

  const handleExportClick = () => {
    onExport(selectedCoverOption, selectedPdfFormat, selectedPdfLayout, selectedCoverLayout, selectedColorSpace, emptySpreadsCount > 0 && ignoreEmptySpreads);
    // onOpenChange(false); // Keep dialog open until export process is confirmed or handled by parent
  };

  interface PreflightCheck {
    id: string;
    label: string;
    count: number;
    Icon: React.ElementType;
    color: string;
    actionLabel: string;
    hasIssue: boolean;
    action?: () => void;
    isExportingAsIs?: boolean;
  }

  const preflightChecks: PreflightCheck[] = [
    {
      id: 'missing-files',
      label: 'Missing Image Files',
      count: missingFilesCount,
      Icon: AlertTriangle,
      color: 'text-red-500',
      actionLabel: 'Locate Files',
      hasIssue: missingFilesCount > 0,
      action: onLocateFilesClick,
    },
    {
      id: 'low-res',
      label: 'Image Resolution Warnings',
      count: lowResImagesCount,
      Icon: AlertTriangle,
      color: 'text-amber-500',
      actionLabel: 'Review Images',
      hasIssue: lowResImagesCount > 0 && !ignoreLowResWarnings,
      action: qualityIssueDetails.length > 0 ? () => setIsQualityPopoverOpen(true) : onReviewImagesClick,
    },
    {
      id: 'empty-spreads',
      label: 'Empty Spreads',
      count: emptySpreadsCount,
      Icon: AlertTriangle,
      color: 'text-yellow-500',
      actionLabel: '', // Custom buttons will be used
      hasIssue: emptySpreadsCount > 0 && !ignoreEmptySpreads,
      action: undefined,
    },
  ];

  const hasPreflightIssues = preflightChecks.some(check => check.hasIssue);

  const handleOpenChange = (open: boolean) => {
    onOpenChange(open); // Call the original onOpenChange handler
    if (!open && onExportCancel) {
      // If the dialog is closing and onExportCancel is provided, call it
      onExportCancel();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-semibold">Export Project</DialogTitle>
          <DialogDescription />
        </DialogHeader>

        <div className="grid gap-6 py-4 max-h-[70vh] overflow-y-auto pr-2">
          {/* Pre-flight Checks Section */}
          <div>
            <h3 className="text-lg font-medium mb-3">Pre-export Checks</h3>
            <div className="space-y-3">
              {preflightChecks.map(check => (
                <div key={check.id} className={`flex items-center justify-between p-3 rounded-md border ${check.hasIssue ? 'border-destructive/50 bg-destructive/10' : 'border-green-500/50 bg-green-500/10'}`}>
                  <div className="flex items-center gap-3">
                    {check.hasIssue ? (
                      <check.Icon className={`h-5 w-5 ${check.color}`} />
                    ) : (
                      <CheckCircle2 className="h-5 w-5 text-green-600" />
                    )}
                    <span className={`font-medium ${check.hasIssue ? check.color : 'text-green-700'}`}>
                      {check.label}: {
                        check.id === 'empty-spreads'
                          ? (
                            emptySpreadsCount > 0
                              ? (ignoreEmptySpreads ? `${emptySpreadsCount} Export as is` : `${emptySpreadsCount} found`)
                              : 'OK!'
                          )
                          : check.id === 'low-res'
                            ? (
                              lowResImagesCount > 0
                                ? (ignoreLowResWarnings ? `${lowResImagesCount} Ignored` : `${lowResImagesCount} warning(s)`)
                                : 'OK!'
                            )
                            : (check.hasIssue ? `${check.count} issue(s)` : 'OK!')
                      }
                    </span>
                  </div>
                  {/* Action Area */}
                  {check.id === 'empty-spreads' && emptySpreadsCount > 0 ? (
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          onDeleteEmptySpreads();
                          setIgnoreEmptySpreads(false); // Ensure not ignored after deletion attempt
                        }}
                      >
                        Delete Spreads
                      </Button>
                      {!ignoreEmptySpreads && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIgnoreEmptySpreads(true)}
                        >
                          Ignore
                        </Button>
                      )}
                    </div>
                  ) : check.id === 'low-res' && lowResImagesCount > 0 ? (
                    <div className="flex gap-2">
                      {qualityIssueDetails.length > 0 ? (
                        <Popover open={isQualityPopoverOpen} onOpenChange={setIsQualityPopoverOpen}>
                          <PopoverTrigger asChild>
                            <Button variant="outline" size="sm">
                              {check.actionLabel} {isQualityPopoverOpen ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-80 max-h-96 overflow-y-auto p-0" align="end">
                            <div className="p-4 border-b">
                              <h4 className="font-medium">Image Resolution Warnings</h4>
                              <p className="text-xs text-muted-foreground mt-1">
                                The following spreads contain images that may print at lower quality than recommended. Adjust the slider in settings to see less warnings related to effective DPI.
                              </p>
                            </div>
                            <div className="p-2">
                              {qualityIssueDetails.map((detail) => {
                                const issueCount = Object.keys(detail.issues).length;
                                return (
                                  <div
                                    key={detail.spreadId}
                                    className="p-2 hover:bg-muted rounded-md cursor-pointer transition-colors"
                                    onClick={() => {
                                      if (onNavigateToSpread) {
                                        onNavigateToSpread(detail.spreadId);
                                        setIsQualityPopoverOpen(false);
                                        onOpenChange(false);
                                      }
                                    }}
                                  >
                                    <div className="flex justify-between items-center">
                                      <div>
                                        <span className="font-medium">Spread {detail.displayIndex}</span>
                                        <span className="text-xs text-muted-foreground ml-2">
                                          ({issueCount} image{issueCount !== 1 ? 's' : ''})
                                        </span>
                                      </div>
                                      {onNavigateToSpread && (
                                        <ExternalLink className="h-3.5 w-3.5 text-muted-foreground" />
                                      )}
                                    </div>
                                    <div className="mt-1 pl-2 text-xs text-muted-foreground">
                                      {Object.entries(detail.issues).map(([placeholderId, issue]) => {
                                        const warningThresholdDpi = 300 * 0.7;
                                        return (
                                          <div key={placeholderId} className="flex justify-between py-0.5">
                                            <span className="text-amber-600 w-full">
                                              {issue.actualDpi && !isNaN(issue.actualDpi)
                                                ? `${Math.round(issue.actualDpi)} Effective DPI (below threshold ${Math.round(warningThresholdDpi)} DPI)`
                                                : 'Low resolution image'}
                                            </span>
                                          </div>
                                        );
                                      })}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </PopoverContent>
                        </Popover>
                      ) : (
                        check.action && <Button variant="outline" size="sm" onClick={check.action}>{check.actionLabel}</Button>
                      )}
                      {!ignoreLowResWarnings && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIgnoreLowResWarnings(true)}
                        >
                          Ignore
                        </Button>
                      )}
                    </div>
                  ) : check.hasIssue && check.action && ( // For other actions like "Locate Files"
                    <Button variant="outline" size="sm" onClick={check.action}>
                      {check.actionLabel}
                    </Button>
                  )}
                </div>
              ))}
              {!hasPreflightIssues && (
                 <div className="flex items-center gap-3 p-3 rounded-md border border-green-500/50 bg-green-500/10">
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                    <span className="font-medium text-green-700">All checks passed!</span>
                  </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Cover Export Options */}
          {hasCover && (
            <div>
              <h3 className="text-lg font-medium mb-3">Cover Export</h3>
              {!hasCoverImage && (
                <p className="text-sm text-amber-600 bg-amber-50 border border-amber-200 p-3 rounded-md mb-3">
                  <AlertTriangle className="inline h-4 w-4 mr-1" />
                  Cover is enabled in settings, but no image has been placed on the cover. It will not be exported.
                </p>
              )}
              <RadioGroup
                value={selectedCoverOption}
                onValueChange={(value: CoverExportOption) => setSelectedCoverOption(value)}
                disabled={!canExportCover}
              >
                <div className="space-y-2">
                  {selectedPdfFormat === 'pdf' ? (
                    <>
                      <div className="flex items-center space-x-2 p-3 border rounded-md hover:bg-muted/50 transition-colors">
                        <RadioGroupItem value="include" id="cover-include-pdf" disabled={!canExportCover} />
                        <Label htmlFor="cover-include-pdf" className="flex-1 cursor-pointer">
                          Include cover at the beginning of the main book PDF
                          <p className="text-xs text-muted-foreground">The cover will be the first page(s) of your book file.</p>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 p-3 border rounded-md hover:bg-muted/50 transition-colors">
                        <RadioGroupItem value="separate" id="cover-separate-pdf" disabled={!canExportCover} />
                        <Label htmlFor="cover-separate-pdf" className="flex-1 cursor-pointer">
                          Export cover as a separate PDF file
                          <p className="text-xs text-muted-foreground">A second PDF file will be generated for the cover.</p>
                        </Label>
                      </div>
                    </>
                  ) : ( // JPEG format
                    <div className="flex items-center space-x-2 p-3 border rounded-md hover:bg-muted/50 transition-colors">
                      <RadioGroupItem value="include" id="cover-include-jpeg" disabled={!canExportCover} />
                      <Label htmlFor="cover-include-jpeg" className="flex-1 cursor-pointer">
                        Export cover
                        <p className="text-xs text-muted-foreground">The cover will be exported as well as the internal spreads.</p>
                      </Label>
                    </div>
                  )}
                  {/* Common "Do not export cover" option */}
                  <div className="flex items-center space-x-2 p-3 border rounded-md hover:bg-muted/50 transition-colors">
                    <RadioGroupItem value="none" id="cover-none" disabled={!canExportCover} />
                    <Label htmlFor="cover-none" className="flex-1 cursor-pointer">
                      Do not export cover
                      <p className="text-xs text-muted-foreground">Only the internal spreads of the book will be exported.</p>
                    </Label>
                  </div>
                </div>
              </RadioGroup>
            </div>
          )}

          <Separator />
          
          {/* Export Settings */}
          <div>
            <h3 className="text-lg font-medium mb-3">Export Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6"> {/* Changed to md:grid-cols-3 */}
              {/* Format */}
              <div>
                <Label className="text-base font-medium mb-2 flex items-center"><FileText className="h-4 w-4 mr-2"/>Format</Label>
                <RadioGroup
                  value={selectedPdfFormat}
                  onValueChange={(value: PdfFormatOption) => {
                    setSelectedPdfFormat(value);
                    // If switching to JPEG and cover was 'separate', default to 'include'
                    if (value === 'jpeg' && selectedCoverOption === 'separate') {
                      setSelectedCoverOption('include');
                    }
                    // If switching to PDF and cover was 'include' (which is the only option for JPEG export other than 'none')
                    // we can leave it as 'include' or decide on a default for PDF, e.g., 'include'.
                    // For now, let's leave it, the useEffect above will also handle this.
                  }}
                >
                  <div className="flex items-center space-x-2"><RadioGroupItem value="pdf" id="format-pdf" /><Label htmlFor="format-pdf">PDF</Label></div>
                  <div className="flex items-center space-x-2"><RadioGroupItem value="jpeg" id="format-jpeg" /><Label htmlFor="format-jpeg">JPEG Images</Label></div>
                </RadioGroup>
              </div>

              {/* PDF Layout */}
              <div>
                <Label className="text-base font-medium mb-2 flex items-center"><BookOpen className="h-4 w-4 mr-2"/>Page Layout</Label>
                <RadioGroup
                  value={selectedPdfLayout}
                  onValueChange={(value: PdfLayoutOption) => setSelectedPdfLayout(value)}
                >
                  <div className="flex items-center space-x-2"><RadioGroupItem value="spreads" id="layout-spreads" /><Label htmlFor="layout-spreads">Spreads</Label></div>
                  <div className="flex items-center space-x-2"><RadioGroupItem value="pages" id="layout-pages" /><Label htmlFor="layout-pages">Single Pages</Label></div>
                </RadioGroup>
              </div>

              {/* Cover Layout (if separate or included) */}
              {(selectedCoverOption === 'separate' || selectedCoverOption === 'include') && canExportCover && (
                <div>
                  <Label className="text-base font-medium mb-2 flex items-center"><Copy className="h-4 w-4 mr-2"/>Cover File Layout</Label>
                  <RadioGroup
                    value={selectedCoverLayout}
                    onValueChange={(value: CoverLayoutOption) => setSelectedCoverLayout(value)}
                  >
                    <div className="flex items-center space-x-2"><RadioGroupItem value="double" id="coverlayout-double" /><Label htmlFor="coverlayout-double">Double Page (as in editor)</Label></div>
                    <div className="flex items-center space-x-2"><RadioGroupItem value="single" id="coverlayout-single" /><Label htmlFor="coverlayout-single">Single Page (front cover only)</Label></div>
                  </RadioGroup>
                </div>
              )}
              
              {/* Color Space */}
              <div>
                <Label className="text-base font-medium mb-2 flex items-center"><Palette className="h-4 w-4 mr-2"/>Color Space</Label>
                <RadioGroup
                  value={selectedColorSpace}
                  onValueChange={(value: ColorSpaceOption) => setSelectedColorSpace(value)}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="srgb" id="colorspace-srgb" />
                    <Label htmlFor="colorspace-srgb">sRGB</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="adobergb" id="colorspace-adobergb" />
                    <Label htmlFor="colorspace-adobergb">Adobe RGB (1998)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem
                      value="passthrough"
                      id="colorspace-passthrough"
                      disabled={selectedPdfFormat !== 'pdf'}
                    />
                    <Label
                      htmlFor="colorspace-passthrough"
                      className={selectedPdfFormat !== 'pdf' ? 'text-muted-foreground cursor-not-allowed' : ''}
                    >
                      Pass Embedded ICC Profiles to PDF
                      {selectedPdfFormat !== 'pdf' && <span className="text-xs ml-1">(PDF only)</span>}
                    </Label>
                  </div>
                </RadioGroup>
              </div>

            </div>
          </div>
        </div>

        <DialogFooter className="sm:justify-between items-center gap-2 pt-4">
          <div className="text-xs text-muted-foreground">
            {/* Placeholder for summary or additional info */}
          </div>
          <div className="flex gap-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              onClick={handleExportClick}
              disabled={
                isExporting ||
                missingFilesCount > 0 ||
                (emptySpreadsCount > 0 && !ignoreEmptySpreads) ||
                (lowResImagesCount > 0 && !ignoreLowResWarnings)
              }
            >
              {isExporting ? (
                <>
                  <CloudUpload className="mr-2 h-4 w-4 animate-pulse" /> Exporting...
                </>
              ) : (
                <>
                  <FolderSymlink className="mr-2 h-4 w-4" /> Proceed to Export
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ExportDialog;