import React from 'react';
import { Button } from '@/components/ui/button';
import bookproofsLogo from '/BookProofs-WI.png'; // Import the logo

interface StartPageProps {
  theme: 'light' | 'dark';
  onOpenRecent: () => void;
  onCreateNew: () => void;
}

const StartPage: React.FC<StartPageProps> = ({ theme, onOpenRecent, onCreateNew }) => {
  return (
    <div 
      className="flex flex-col items-center justify-center min-h-screen p-6 animate-fade-in"
      style={theme === 'dark' ? { 
        filter: 'invert(1) hue-rotate(180deg)',
        backgroundColor: '#f3f4f6' // Light gray that will invert to dark
      } : { 
        backgroundColor: '#f3f4f6' // Standard light background
      }}
    >
      <img src={bookproofsLogo} alt="Bookproofs Logo" className="mb-12 h-16" /> {/* Logo with specified height */}

      <div className="space-y-6 w-full max-w-xs">
        <Button
          variant="outline" // Use outline style for distinction
          size="lg" // Make buttons larger
          className="w-full py-4 text-lg" // Custom padding and text size
          onClick={onOpenRecent}
        >
          Open Recent Projects
        </Button>

        <Button
          size="lg" // Make buttons larger
          className="w-full py-4 text-lg" // Custom padding and text size
          onClick={onCreateNew}
        >
          Create New Project
        </Button>
      </div>
    </div>
  );
};

export default StartPage;