import React, { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { ImagePlus, X, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ImageFile } from './ImageTray';

interface UploadAreaProps {
  onImagesAdded: (newImages: ImageFile[]) => void;
  onClose: () => void;
}

const UploadArea = ({ onImagesAdded, onClose }: UploadAreaProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState<{ stage?: string; current: number; total: number; filename?: string; error?: string } | null>(null);

  useEffect(() => {
    const removeProgressListener = window.electronAPI.onDroppedFilesProgress?.((eventData) => {
      if (eventData === null) { // Completion signal from main process
        // This might indicate overall completion if not all files were processed successfully
        // or if the main process sends a final null after all individual file updates.
        // For now, individual file results will drive `onImagesAdded`.
        // If `processDroppedFiles` resolves, that's the primary success indicator.
      } else if (eventData.stage === 'error') {
        toast.error(`Error processing ${eventData.filename || 'a file'}: ${eventData.error}`);
        setProgress(prev => prev ? { ...prev, current: eventData.current, error: eventData.error } : null);
      } else {
        setProgress({
          stage: eventData.stage,
          current: eventData.current,
          total: eventData.total,
          filename: eventData.filename,
        });
      }
    });

    return () => {
      removeProgressListener?.();
    };
  }, []); // Runs once on mount

  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    // Filter for actual image files by type and ensure 'path' property exists
    // Electron provides 'file.path' for files from the filesystem.
    const imageFilesToProcess = Array.from(files).filter(
      file => file.type.startsWith('image/') && (file as any).path
    );

    if (imageFilesToProcess.length === 0) {
      toast.error('No valid image files with paths found. Please select files from your computer.');
      return;
    }

    const filePaths = imageFilesToProcess.map(file => (file as any).path as string);

    setIsProcessing(true);
    setProgress({ stage: 'starting_import', current: 0, total: filePaths.length });

    try {
      // Call the new IPC function to handle file processing in the main process
      const processedImages: ImageFile[] = await window.electronAPI.processDroppedFiles(filePaths, { generatePreviews: false });

      if (processedImages && processedImages.length > 0) {
        onImagesAdded(processedImages);
        toast.success(`Added ${processedImages.length} images.`);
      } else if (filePaths.length > 0) {
        // This case implies all files failed processing in the main process,
        // or no valid images were returned.
        toast.error('Could not process the selected images. Check console for details.');
      }
      // If processedImages is null/empty and no files were attempted, do nothing.

    } catch (error: any) {
      console.error('Error invoking processDroppedFiles:', error);
      toast.error(`Failed to add images: ${error.message || 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
      setProgress(null); // Reset progress
      // Optionally close the modal, e.g., if (processedImages && processedImages.length > 0) onClose();
    }
  }, [onImagesAdded, onClose]);

  const onDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const onDragLeave = useCallback(() => {
    setIsDragging(false);
  }, []);

  const onDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    if (!isProcessing) { // Added check
      handleFileSelect(e.dataTransfer.files);
    }
  }, [handleFileSelect, isProcessing, onClose]); // Added onClose to dependencies

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 animate-fade-in">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 animate-scale-in">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-medium">Add Images</h2>
          <button
            onClick={!isProcessing ? onClose : undefined}
            disabled={isProcessing}
            className={`text-gray-400 hover:text-gray-600 ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {isProcessing && progress ? (
          <div className="flex flex-col items-center justify-center p-8 space-y-4">
            <Loader2 className="w-10 h-10 text-primary animate-spin" />
            <p className="text-center text-gray-700 text-sm">
              {progress.stage === 'error' && progress.error ? (
                <span className="text-red-600">{progress.error}</span>
              ) : progress.stage === 'starting_import' ? (
                `Preparing to import ${progress.total} images...`
              ) : (
                <>
                  Processing: {progress.filename || `image ${progress.current}`}<br />
                  ({progress.current} of {progress.total})
                </>
              )}
            </p>
            <Progress value={(progress.current / progress.total) * 100} className="w-full" />
          </div>
        ) : (
          <div
            className={`
              border-2 border-dashed rounded-lg p-8 flex flex-col items-center
              ${isDragging ? 'border-primary bg-primary/5' : 'border-gray-200'}
              ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            onDragOver={!isProcessing ? onDragOver : undefined}
            onDragLeave={!isProcessing ? onDragLeave : undefined}
            onDrop={!isProcessing ? onDrop : undefined} // Ensure onDrop is also disabled during processing
          >
            <ImagePlus className="w-12 h-12 text-gray-300 mb-4" />
            <p className="text-center mb-1 text-gray-700">
              Drag and drop image files here
            </p>
            <p className="text-xs text-gray-400 mb-4 text-center">
              Supports JPG, PNG, WEBP, HEIC, AVIF, TIFF
            </p>

            <input
              type="file"
              id="file-upload"
              className="hidden"
              multiple
              accept="image/*" // More generic, main process will filter
              onChange={(e) => !isProcessing && handleFileSelect(e.target.files)}
              disabled={isProcessing}
            />
            <label htmlFor="file-upload" className={isProcessing ? 'pointer-events-none' : ''}>
              <Button
                variant="outline"
                className={`cursor-pointer ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
                asChild
                disabled={isProcessing}
                onClick={(e) => { if (isProcessing) e.preventDefault(); }} // Prevent click during processing
              >
                <span>Browse Files</span>
              </Button>
            </label>
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadArea;
