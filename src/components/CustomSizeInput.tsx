import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { XCircle } from 'lucide-react';
import AspectRatioCard from './AspectRatioCard';

interface CustomSizeInputProps {
  onSizeCreate: (customSize: {
    id: string;
    title: string;
    ratio: string;
    dimensions: string;
    bleed: string; // Added bleed
    safetyMargin: string; // Added safety margin
  }) => void;
  isActive?: boolean; // Add isActive prop
  onClear?: () => void; // Add onClear prop to handle clearing custom dimensions
}

const CustomSizeInput: React.FC<CustomSizeInputProps> = ({ onSizeCreate, isActive = false, onClear }) => { // Destructure isActive and onClear
  const [width, setWidth] = useState<string>('');
  const [height, setHeight] = useState<string>('');
  const [bleed, setBleed] = useState<string>(''); // Added bleed state
  const [safetyMargin, setSafetyMargin] = useState<string>(''); // Added safety margin state
  const [error, setError] = useState<string | null>(null);
  const [preview, setPreview] = useState<{
    id: string;
    title: string;
    ratio: string;
    dimensions: string;
    bleed: string; // Added bleed
    safetyMargin: string; // Added safety margin
  } | null>(null);
  const justDeactivatedByEditRef = React.useRef(false); // Added ref

  // Calculate greatest common divisor for aspect ratio
  const gcd = (a: number, b: number): number => {
    // Ensure inputs are integers for GCD calculation
    const factor = 100; // Scale by 100 to handle two decimal places
    const intA = Math.round(a * factor);
    const intB = Math.round(b * factor);
    
    const calculateGcd = (x: number, y: number): number => {
        return y === 0 ? x : calculateGcd(y, x % y);
    }
    // Return the GCD scaled back down
    return calculateGcd(intA, intB) / factor;
  };

  // Debounced effect for processing and validating dimensions
  useEffect(() => {
    const handler = setTimeout(() => {
      // Core logic moved here
      const widthNum = parseFloat(width);
      const heightNum = parseFloat(height);
      const bleedNum = parseFloat(bleed) || 0; // Default to 0 if not a number or empty
      const safetyMarginNum = parseFloat(safetyMargin) || 0.6; // Default to 0.6 if not a number or empty

      if (!isNaN(widthNum) && !isNaN(heightNum) && widthNum > 0 && heightNum > 0 && bleedNum >= 0 && safetyMarginNum >= 0) {
        if (widthNum > 36 || heightNum > 36) {
          setError('Maximum dimension is 36 inches');
          setPreview(null);
          if (isActive && onClear) {
            onClear();
          }
          return;
        }
        if (bleedNum > 0.250) {
          setError('Bleed cannot exceed 0.250 inches.');
          setPreview(null);
          if (isActive && onClear) {
            onClear();
          }
          return;
        }
        if (safetyMarginNum > 0.750) {
          setError('Safety margin cannot exceed 0.750 inches.');
          setPreview(null);
          if (isActive && onClear) {
            onClear();
          }
          return;
        }
        if (bleedNum > Math.min(widthNum, heightNum) / 2) {
          setError('Bleed cannot exceed half of the smallest dimension.');
          setPreview(null);
          if (isActive && onClear) {
            onClear();
          }
          return;
        }
        if (safetyMarginNum > Math.min(widthNum, heightNum) / 2) {
          setError('Safety margin cannot exceed half of the smallest dimension.');
          setPreview(null);
          if (isActive && onClear) {
            onClear();
          }
          return;
        }

        const divisor = gcd(widthNum, heightNum);
        const formatRatioPart = (num: number) => {
          return Number.isInteger(num) ? num.toString() : num.toFixed(2);
        };
        const ratioWidth = formatRatioPart(widthNum / divisor);
        const ratioHeight = formatRatioPart(heightNum / divisor);

        const newPreviewData = {
          id: `custom-${width}x${height}b${bleed}s${safetyMargin}`,
          title: `Custom ${width}×${height}" (Bleed: ${bleed || 0}", Safety: ${safetyMargin || 0.6}")`,
          ratio: `${ratioWidth}:${ratioHeight}`,
          dimensions: `${width}" × ${height}"`,
          bleed: bleed || "0",
          safetyMargin: safetyMargin || "0.6",
        };

        setPreview(newPreviewData);
        setError(null);

        if (onSizeCreate) {
          onSizeCreate(newPreviewData);
        }

      } else {
        setPreview(null);
        if (isActive && onClear) {
          onClear();
        }
        if (width !== '' || height !== '' || bleed !== '' || safetyMargin !== '') {
          // setError('Please enter valid numeric dimensions for width, height, bleed, and safety margin.');
        } else {
          setError(null);
        }
      }
    }, 400); // 400ms debounce delay

    return () => {
      clearTimeout(handler);
    };
  }, [width, height, bleed, safetyMargin, isActive, onSizeCreate, onClear, gcd]); // Added bleed, safetyMargin and gcd to dependencies


  // Clear input fields when component becomes inactive
  useEffect(() => {
    if (!isActive) {
      // Always clear preview and error display when inactive
      setPreview(null);
      setError(null); // Main debounced effect re-evaluates and sets errors based on current inputs

      // If justDeactivatedByEditRef.current is true, it means the 'X' (clear custom) button was pressed.
      // In this case, we DO want to clear the input fields.
      if (justDeactivatedByEditRef.current) {
        setWidth('');
        setHeight('');
        setBleed(''); // Clear bleed input
        setSafetyMargin(''); // Reset safety margin
        justDeactivatedByEditRef.current = false; // Reset the flag
      }
      // If justDeactivatedByEditRef.current is false, it means isActive became false
      // due to either a preset selection OR internal invalidation of custom inputs.
      // In these cases, we want the typed input text (width/height/bleed/safetyMargin) to REMAIN.
      // This allows users to see their previous (now inactive or invalid) custom
      // dimensions and either correct them or see them while a preset is chosen.
    }
  }, [isActive]);


  // Add a class to indicate when the custom size is active/selected
  const containerClass = isActive
    ? "space-y-4 p-4 border-2 border-primary rounded-lg bg-primary/5"
    : "space-y-4";

  return (
    <div className={containerClass}>
      <div className="grid grid-cols-4 gap-4"> {/* Changed to grid-cols-4 for width, height, bleed, safety margin */}
        <div>
          <label htmlFor="custom-width" className="block text-sm font-medium mb-1">Width (in)</label>
          <Input
            id="custom-width"
            type="text"
            value={width}
            onChange={(e) => {
            let val = e.target.value;
            // Allow starting with just a decimal point and treat it as "0."
            if (val === '.') {
              val = '0.';
            }
            // Allow numbers, one decimal point, and ensure it doesn't start with multiple zeros unless it's '0.'
            if (/^((0|[1-9]\d*)(\.\d*)?|0\.\d*|\.\d*)$/.test(val) || val === '') {
              setWidth(val);
            }
          }}
            placeholder="e.g., 11"
            aria-describedby="width-error"
            className="focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none"
          />
        </div>
        <div>
          <label htmlFor="custom-height" className="block text-sm font-medium mb-1">Height (in)</label>
          <Input
            id="custom-height"
            type="text"
            value={height}
            onChange={(e) => {
            let val = e.target.value;
            // Allow starting with just a decimal point and treat it as "0."
            if (val === '.') {
              val = '0.';
            }
            if (/^((0|[1-9]\d*)(\.\d*)?|0\.\d*|\.\d*)$/.test(val) || val === '') {
              setHeight(val);
            }
          }}
            placeholder="e.g., 8.5"
            aria-describedby="height-error"
            className="focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none"
          />
        </div>
        <div> {/* Bleed Input Field */}
          <label htmlFor="custom-bleed" className="block text-sm font-medium mb-1">Bleed (in)</label>
          <Input
            id="custom-bleed"
            type="text"
            value={bleed}
            onChange={(e) => {
              let val = e.target.value;
              // Allow starting with just a decimal point and treat it as "0."
              if (val === '.') {
                val = '0.';
              }
              if (/^((0|[1-9]\d*)(\.\d*)?|0\.\d*|\.\d*)$/.test(val) || val === '') {
                setBleed(val);
              }
            }}
            placeholder="e.g., 0.125 (max 0.250)"
            aria-describedby="bleed-error"
            className="focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none"
          />
        </div>
        <div> {/* Safety Margin Input Field */}
          <label htmlFor="custom-safety-margin" className="block text-sm font-medium mb-1">Safety Margin (in)</label>
          <Input
            id="custom-safety-margin"
            type="text"
            value={safetyMargin}
            onChange={(e) => {
              let val = e.target.value;
              // Allow starting with just a decimal point and treat it as "0."
              if (val === '.') {
                val = '0.';
              }
              if (/^((0|[1-9]\d*)(\.\d*)?|0\.\d*|\.\d*)$/.test(val) || val === '') {
                setSafetyMargin(val);
              }
            }}
            placeholder="e.g., 0.6 (max 0.750)"
            aria-describedby="safety-margin-error"
            className="focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none"
          />
        </div>
      </div>
      
      {error && (
        <div id="width-error" className="text-red-500 text-sm col-span-4">{error}</div>
      )}
      {!error && (width || height || bleed || safetyMargin) && !preview && isActive && (
        <div id="width-error" className="text-red-500 text-sm col-span-4">Invalid dimensions for preview.</div>
      )}
      
      {preview && (
        <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 pt-4 border-t border-gray-100">
          <div className="flex-shrink-0">
            <AspectRatioCard
              title={preview.title}
              ratio={preview.ratio}
              dimensions={`${preview.dimensions}${preview.bleed && preview.bleed !== "0" ? ` (Bleed: ${preview.bleed}")` : ''}${preview.safetyMargin && preview.safetyMargin !== "0" ? ` (Safety: ${preview.safetyMargin}")` : ''}`}
              selected={isActive || false} // Show as selected if isActive is true
              onClick={() => {}} // No action needed on click here
            />
          </div>
        </div>
      )}
      
      {isActive && (
        <div className="mt-2 text-sm text-primary font-medium flex items-center justify-end">
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center text-xs text-muted-foreground hover:text-destructive"
            onClick={() => {
              justDeactivatedByEditRef.current = true; // Signal that this deactivation IS from 'X' button
              if (onClear) {
                onClear();
              }
            }}
          >
            <XCircle className="h-4 w-4 mr-1" />
            Clear custom size
          </Button>
        </div>
      )}
    </div>
  );
};

export default CustomSizeInput;