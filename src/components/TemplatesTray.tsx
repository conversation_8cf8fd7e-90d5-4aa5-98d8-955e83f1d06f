import React, { useState, useEffect, useRef, useCallback, forwardRef, useImperativeHandle, useMemo } from 'react'; // Import hooks and forwardRef/useImperativeHandle
import { LayoutGrid, Filter, Star } from 'lucide-react'; // Add Filter and Star icons
import { Button } from '@/components/ui/button'; // Import Button
import { ScrollArea } from '@/components/ui/scroll-area';
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"; // Import ToggleGroup
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import Template from './Template';
import { allBookTemplates } from '@/lib/templates'; // Import central templates array
import { TemplateData, ProjectDimension, isTemplateAllowedForProject } from '@/lib/templates/interfaces'; // Import interface from new location
import { useStatusBar } from '@/contexts/StatusBarContext'; // Import the status bar context hook
import { toast } from 'sonner'; // Import toast for user notifications

interface TemplatesTrayProps {
  onSelectTemplate: (template: TemplateData) => void;
  onPreviewTemplate: (templateId: string | null) => void;
  onFocus?: () => void; // Add focus handler prop
  onBlur?: () => void; // Add blur handler prop
  favoriteTemplateIds: Set<string>; // Set of favorite template IDs
  onToggleFavorite: (templateId: string) => void; // Function to toggle favorite status
  triggerFilterCount?: number | null; // Prop to trigger filter externally
  triggerSelectFirst?: boolean; // Prop to trigger selecting the first item after filtering
  templateIdToSkip?: string | null; // Prop to indicate a template ID to skip on first selection
  onEscape?: () => void; // New prop for handling Escape key press
  onNavigateSpread?: (direction: 'prev' | 'next') => void; // Prop to delegate navigation
  activeTemplateId?: string | null; // ID of the template currently applied to the spread
  currentSpreadPlacedImageCount?: number; // Number of images placed on the current spread
  theme?: 'light' | 'dark'; // Add theme prop
  projectDimension?: ProjectDimension; // Add project dimension for template filtering
  spreadAspectRatio?: number; // Add spread aspect ratio for letterboxing/pillarboxing
  onFiltersChange?: (filters: { imageCountFilter: string; filterByFavorites: boolean }) => void; // Callback for filter changes
}

// Define the handle type that will be exposed via the ref
export interface TemplatesTrayHandle {
  focusTray: (continueWithArrowKey?: 'ArrowUp' | 'ArrowDown') => void;
  selectFirstVisibleTemplate: (idToSkip?: string | null) => void; // Add method signature
  hasSavedUserFocus: () => boolean;
  getCurrentFilters: () => {
    imageCountFilter: string;
    filterByFavorites: boolean;
  };
}

// Wrap component with forwardRef
const TemplatesTray = forwardRef<TemplatesTrayHandle, TemplatesTrayProps>(
  ({ onSelectTemplate, onPreviewTemplate, onFocus, onBlur, favoriteTemplateIds, onToggleFavorite, triggerFilterCount, triggerSelectFirst, templateIdToSkip, onEscape, onNavigateSpread, activeTemplateId, currentSpreadPlacedImageCount, theme = 'light', projectDimension, spreadAspectRatio, onFiltersChange }, ref) => { // Destructure new props, add ref, onEscape, onNavigateSpread, activeTemplateId, currentSpreadPlacedImageCount, theme, projectDimension, spreadAspectRatio, and onFiltersChange
    const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
    const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
    const [filterByFavorites, setFilterByFavorites] = useState<boolean>(false); // State for filtering by favorites
    const [imageCountFilter, setImageCountFilter] = useState<string>('all'); // State for filter ('all' or number as string)
    const [templateThumbnailSize, setTemplateThumbnailSize] = useState<number>(1); // State for template thumbnail size
    const [isTemplateHovered, setIsTemplateHovered] = useState<boolean>(false); // State to track if any template is hovered
    
    const containerRef = useRef<HTMLDivElement>(null); // Ref for the main scrollable container
    const templateRefs = useRef<(HTMLDivElement | null)[]>([]); // Refs for individual template elements
    
    // Virtualization state
    const [scrollTop, setScrollTop] = useState<number>(0);
    const [containerHeight, setContainerHeight] = useState<number>(0);
    const selectionPending = useRef(false); // Ref to track if selection is pending due to trigger
    const lastManualFilterChange = useRef<number>(0); // Track time of last manual filter change
    const internalClickTriggeredUpdate = useRef(false); // Ref to track if update was triggered by internal click
    const userIsScrolling = useRef(false); // Ref to track if user is manually scrolling
    const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Ref to store the timeout ID
    
    // Get the status bar context to update when templates are hovered
    const { canvasHoverInfo, setCanvasHoverInfo } = useStatusBar();

   // Dynamically determine grid columns based on thumbnail size
   const gridCols = useMemo(() => {
     if (templateThumbnailSize <= 0.8) return 3; // For slider values 0.6, 0.8
     if (templateThumbnailSize <= 1.0) return 2; // For slider value 1.0
     return 1; // For slider values 1.2, 1.4
   }, [templateThumbnailSize]);
   
   const gridColsClass = useMemo(() => {
     switch (gridCols) {
       case 1: return 'grid-cols-1';
       case 2: return 'grid-cols-2';
       case 3: return 'grid-cols-3';
       default: return 'grid-cols-2';
     }
   }, [gridCols]);

   // Filter and sort templates with duplicate detection
    const displayableTemplates = useMemo(() => {
      const filtered = allBookTemplates.filter(t => {
        if (t.id === '__blank__') return false;

        // Check project dimension filter first
        if (projectDimension) {
          if (!isTemplateAllowedForProject(t.classification, projectDimension)) {
            return false;
          }
        }

        // Check favorite filter if active
        if (filterByFavorites && !favoriteTemplateIds.has(t.id)) {
          return false;
        }

        // Then check image count filter (if not 'all')
        if (imageCountFilter === 'all') return true;
        const filterNum = parseInt(imageCountFilter, 10);
        return t.images.length === filterNum;
      });

      // Check for duplicate template IDs in development
      if (process.env.NODE_ENV === 'development') {
        const templateIds = filtered.map(t => t.id);
        const uniqueIds = new Set(templateIds);
        if (templateIds.length !== uniqueIds.size) {
          const duplicates = templateIds.filter((id, index) => templateIds.indexOf(id) !== index);
          console.warn('🚨 DUPLICATE TEMPLATE IDs DETECTED:', {
            duplicateIds: duplicates,
            totalFiltered: filtered.length,
            uniqueCount: uniqueIds.size,
            message: 'This may cause focus/selection issues in TemplatesTray'
          });
        }
      }

      return filtered;
    }, [allBookTemplates, filterByFavorites, imageCountFilter, projectDimension, favoriteTemplateIds]);

    // Virtualization calculations
    const ITEM_HEIGHT = 120; // Approximate height of each template item in pixels
    const GAP_SIZE = 16; // Gap between items (4 from Tailwind gap-4)
    const OVERSCAN = 5; // Render extra items outside viewport for smooth scrolling
    
    const totalItems = displayableTemplates.length;
    const itemsPerRow = gridCols;
    const totalRows = Math.ceil(totalItems / itemsPerRow);
    const rowHeight = ITEM_HEIGHT + GAP_SIZE;
    // Only calculate height for actual content, don't add extra space
    const totalHeight = totalRows > 0 ? (totalRows * rowHeight) - GAP_SIZE : 0;
    
    // Calculate visible range
    const startRow = Math.max(0, Math.floor(scrollTop / rowHeight) - OVERSCAN);
    const endRow = Math.min(totalRows - 1, Math.ceil((scrollTop + containerHeight) / rowHeight) + OVERSCAN);
    const visibleStartIndex = startRow * itemsPerRow;
    const visibleEndIndex = Math.min(totalItems - 1, (endRow + 1) * itemsPerRow - 1);
    
    // Get only visible templates
    const visibleTemplates = displayableTemplates.slice(visibleStartIndex, visibleEndIndex + 1);
    
    // Expose methods via the ref *after* displayableTemplates is defined
    useImperativeHandle(ref, () => ({
      focusTray: (continueWithArrowKey?: 'ArrowUp' | 'ArrowDown') => {
        const container = containerRef.current;
        if (container) {
          container.focus();
          // Immediately trigger focus callback to update BookEditor state
          onFocus();
        } else {
          console.warn('🚨 Container ref is null, cannot focus');
        }
        // When focusing the tray, restore the user's last focus position
        if (focusedIndex === null) {
          let restoreIndex = -1;
          let restoreReason = '';
          
          // First try to restore user's saved focus position
          if (lastUserFocusedTemplateId.current) {
            const savedIndex = displayableTemplates.findIndex(t => t.id === lastUserFocusedTemplateId.current);
            if (savedIndex !== -1) {
              restoreIndex = savedIndex;
              restoreReason = 'saved user position';
            }
          }
          
          // Fallback to active template if no saved position
          if (restoreIndex === -1 && activeTemplateId) {
            const activeIndex = displayableTemplates.findIndex(t => t.id === activeTemplateId);
            if (activeIndex !== -1) {
              restoreIndex = activeIndex;
              restoreReason = 'active template fallback';
            }
          }
          
          
          if (restoreIndex !== -1) {
            // Mark this as an external change to prevent the filter effect from resetting it
            lastExternalFilterChangeTime.current = Date.now();
            isRestoringFocus.current = true; // Mark that we're restoring focus programmatically
            setFocusedIndex(restoreIndex);
            // Clear the flag after a short delay and ensure container keeps focus
            setTimeout(() => {
              isRestoringFocus.current = false;
              // Re-focus the container to ensure it can receive keyboard events
              const container = containerRef.current;
              if (container) {
                container.focus({ preventScroll: true });
                
                // Force focus and ensure tabindex
                container.setAttribute('tabindex', '0');
                container.focus({ preventScroll: true });
                
                // Verify focus was set
                setTimeout(() => {
                }, 10);
              }
              
              // If we should continue with arrow key navigation after focus restoration
              // Use the restored index (restoreIndex) instead of current focusedIndex
              if (continueWithArrowKey && restoreIndex !== -1) {
                
                // Simulate the arrow key navigation
                let newIndex = restoreIndex;
                if (continueWithArrowKey === 'ArrowDown') {
                  newIndex = Math.min(restoreIndex + 1, displayableTemplates.length - 1);
                } else if (continueWithArrowKey === 'ArrowUp') {
                  newIndex = Math.max(restoreIndex - 1, 0);
                }
                
                if (newIndex !== restoreIndex && newIndex >= 0 && newIndex < displayableTemplates.length) {
                  
                  setFocusedIndex(newIndex);
                  const newTemplate = displayableTemplates[newIndex];
                  if (newTemplate) {
                    onSelectTemplate(newTemplate);
                    
                    // Save the new user focus position
                    lastUserFocusedIndex.current = newIndex;
                    lastUserFocusedTemplateId.current = newTemplate.id;
                    
                    // Scroll to new template
                    setTimeout(() => {
                      templateRefs.current[newIndex]?.scrollIntoView({ block: 'nearest' });
                    }, 10);
                  }
                }
              }
            }, 50);
          }
        }
      },
      selectFirstVisibleTemplate: (idToSkip?: string | null) => {
        const firstIndex = displayableTemplates.findIndex(t => t.id !== idToSkip);
        if (firstIndex !== -1) {
          const firstTemplate = displayableTemplates[firstIndex];
          setFocusedIndex(firstIndex);
          if (firstTemplate) {
            onSelectTemplate(firstTemplate);
          }
          setTimeout(() => {
            templateRefs.current[firstIndex]?.scrollIntoView({ block: 'nearest' });
          }, 50);
        } else {
          setFocusedIndex(null);
        }
      },
      hasSavedUserFocus: () => {
        return lastUserFocusedTemplateId.current !== null;
      },
      getCurrentFilters: () => ({
        imageCountFilter,
        filterByFavorites
      })
    }), [displayableTemplates, onSelectTemplate, setFocusedIndex, imageCountFilter, filterByFavorites, activeTemplateId, focusedIndex]); // Dependencies for useImperativeHandle
    
    // Notify parent when filters change
    useEffect(() => {
      if (onFiltersChange) {
        onFiltersChange({ imageCountFilter, filterByFavorites });
      }
    }, [imageCountFilter, filterByFavorites, onFiltersChange]);
    
    // Track when filter changes were triggered externally (timestamp-based)
    const lastExternalFilterChangeTime = useRef(0);
    
    // Track when focusedIndex is being restored programmatically (not by user input)
    const isRestoringFocus = useRef(false);
    
    // Track the user's last manually selected template focus position
    const lastUserFocusedIndex = useRef<number | null>(null);
    const lastUserFocusedTemplateId = useRef<string | null>(null);

    // Effect 1: Handle filter changes triggered externally by triggerFilterCount (from spread change)
    useEffect(() => {
     // Check if the update was potentially triggered by an internal click and reset the flag
     const wasInternalClick = internalClickTriggeredUpdate.current;
     internalClickTriggeredUpdate.current = false;
 
      // Ignore external trigger if a manual change happened very recently
      const timeSinceManualChange = Date.now() - lastManualFilterChange.current;
      if (timeSinceManualChange < 500) { // Ignore trigger within 500ms of manual change
        return;
      }
 
      // Dynamically get available numeric counts (needed for validation)
      const availableCounts = Array.from(new Set(allBookTemplates.filter(t => t.id !== '__blank__').map(t => t.images.length))).sort((a, b) => a - b);
 
      // Only react if triggerFilterCount has a meaningful value (number or null)
      if (triggerFilterCount !== undefined) {
        const newFilterValue = triggerFilterCount === null ? 'all' : String(triggerFilterCount);
 
        // Check if the new numeric value is actually valid based on available counts
        if (newFilterValue !== 'all') {
          const filterNum = parseInt(newFilterValue, 10);
          const isValidNumericFilter = availableCounts.includes(filterNum);
          if (!isValidNumericFilter) {
            console.warn(`TemplatesTray: Ignoring invalid triggerFilterCount: ${triggerFilterCount}`);
            return; // Don't proceed with invalid numeric filter
          }
        }
 
        // Only update if the derived newFilterValue is different from the current state,
        // UNLESS it was triggered by an internal click while the filter was 'all'.
        const shouldPreventUpdate = wasInternalClick && imageCountFilter === 'all';

        if (newFilterValue !== imageCountFilter && !shouldPreventUpdate) {
          lastExternalFilterChangeTime.current = Date.now(); // Mark timestamp of external change
          setImageCountFilter(newFilterValue);
        }
      }
      // Dependencies: triggerFilterCount (external prop) and imageCountFilter (internal state to compare against)
    }, [triggerFilterCount, imageCountFilter]); // Dependencies are correct

    // Effect 2: Scroll the active template into view when it changes or when the list updates
    useEffect(() => {
      // Skip auto-scroll if user is manually scrolling
      if (userIsScrolling.current) {
        return;
      }

      // Comment out previous selection logic based on selectionPending ref:
      // if (selectionPending.current && focusedIndex === null && displayableTemplates.length > 0) { ... }

      // --- New Logic: Scroll active template into view or scroll to top ---
      if (activeTemplateId && displayableTemplates.length > 0) {
        const activeIndex = displayableTemplates.findIndex(t => t.id === activeTemplateId);
        if (activeIndex !== -1) {
          // Scroll the active template into view
          setTimeout(() => {
            templateRefs.current[activeIndex]?.scrollIntoView({
              block: 'nearest', // 'center' might be better if possible
              // behavior: 'smooth' // Optional: smooth scrolling
            });
          }, 50); // Small delay might be needed
        } else {
          // If active template isn't in the filtered list, scroll to top as a fallback
           setTimeout(() => {
              containerRef.current?.scrollTo({ top: 0 /*, behavior: 'smooth' */ });
           }, 50);
        }
      } else if (!activeTemplateId) {
         // If activeTemplateId is null (e.g., blank spread), scroll to top
         setTimeout(() => {
            containerRef.current?.scrollTo({ top: 0 /*, behavior: 'smooth' */ });
         }, 50);
      }

      // This effect now primarily handles scrolling the relevant template into view based on the active spread.
    }, [displayableTemplates, activeTemplateId]); // Dependencies are the filtered list and the active template ID

    // Reset focus when filter changes manually via ToggleGroup OR via trigger prop
    // Effect to reset focus and scroll to top when filter changes *manually* via ToggleGroup
    // This should NOT run when the filter is changed by the trigger prop if we intend to select the first item.
    useEffect(() => {
      // Check if this filter change was triggered externally (within the last 500ms)
      const timeSinceExternalChange = Date.now() - lastExternalFilterChangeTime.current;
      if (timeSinceExternalChange < 500) {
        return;
      }

      // Only reset focus if the change wasn't triggered by the selectFirst mechanism
      if (!triggerSelectFirst) {
         setFocusedIndex(null);
         // Scroll to top when filter changes manually
         containerRef.current?.scrollTo(0, 0);
      }
    }, [imageCountFilter, triggerSelectFirst]); // Remove focusedIndex from deps to prevent reset loop

    // Ensure refs array has the correct size based on filtered list
    useEffect(() => {
      templateRefs.current = templateRefs.current.slice(0, displayableTemplates.length);
    }, [displayableTemplates.length]);

    // Handle user interaction with debounced timeout clearing
    const handleUserInteraction = useCallback(() => {
      userIsScrolling.current = true;
      
      // Clear any existing timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      
      // Set a new timeout to clear the flag
      scrollTimeoutRef.current = setTimeout(() => {
        userIsScrolling.current = false;
        scrollTimeoutRef.current = null;
      }, 2000); // 2 second timeout
    }, []);

    // Update scroll position and container height
    const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
      const target = event.currentTarget;
      setScrollTop(target.scrollTop);
      handleUserInteraction();
    }, [handleUserInteraction]);

    // Update container height when it changes
    useEffect(() => {
      const updateContainerHeight = () => {
        if (containerRef.current) {
          const rect = containerRef.current.getBoundingClientRect();
          setContainerHeight(rect.height);
        }
      };

      updateContainerHeight();
      const resizeObserver = new ResizeObserver(updateContainerHeight);
      if (containerRef.current) {
        resizeObserver.observe(containerRef.current);
      }

      return () => resizeObserver.disconnect();
    }, []);

    // Effect to update status bar when template hover state changes
  useEffect(() => {
    // When a template is hovered, update the status bar with the arrow key hint
    if (isTemplateHovered) {
      // Use a special placeholder ID to indicate template hover
      setCanvasHoverInfo({
        placeholderId: 'template-hover', // Special ID to indicate template hover
        isZoomed: false
      });
    } else if (canvasHoverInfo.placeholderId === 'template-hover') {
      // Only clear if our special ID is set (don't interfere with other components)
      setCanvasHoverInfo({
        placeholderId: null,
        isZoomed: false
      });
    }
  }, [isTemplateHovered, setCanvasHoverInfo, canvasHoverInfo.placeholderId]);

  // Combined click handler: selects template and sets focus index
    const handleTemplateClick = useCallback((template: TemplateData, index: number) => {
      setSelectedTemplateId(template.id);
      setFocusedIndex(index);
      
      // Save user's manual focus position
      lastUserFocusedIndex.current = index;
      lastUserFocusedTemplateId.current = template.id;
      
       internalClickTriggeredUpdate.current = true; // Signal that the upcoming trigger is from an internal click
      onPreviewTemplate(null); // Clear preview on actual selection
      onSelectTemplate(template);
      // Optionally focus the container to ensure subsequent keydowns are captured
      containerRef.current?.focus();
    }, [onSelectTemplate, onPreviewTemplate]); // Added dependencies

    // Keyboard navigation handler
    const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLDivElement>) => {
      
      // Dynamically get all available numeric counts
      const availableCounts = Array.from(new Set(allBookTemplates.filter(t => t.id !== '__blank__').map(t => t.images.length))).sort((a, b) => a - b);
      // const maxCount = availableCounts.length > 0 ? availableCounts[availableCounts.length - 1] : 0; // Not strictly needed for wrapping logic
      // const minCount = availableCounts.length > 0 ? availableCounts[0] : 0; // Not strictly needed for wrapping logic

      // Ignore Left/Right arrows - let global listener handle them
      if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
        return;
      }

      // Existing checks and logic for other keys
      if (!displayableTemplates || displayableTemplates.length === 0) return;

      // Determine the starting index for navigation.
      // If focus is already set, use it. Otherwise, try to find the active template's index.
      // If neither is available, default to -1 (will be handled by ArrowUp/Down logic).
      let startIndex = focusedIndex;
      if (startIndex === null && activeTemplateId) {
        startIndex = displayableTemplates.findIndex(t => t.id === activeTemplateId);
        if (startIndex === -1) { // Active template not found in current list
          startIndex = null; // Treat as if no focus/active template
        }
      }
      let currentIndex = startIndex ?? -1; // Use -1 if startIndex is null or -1
      let nextIndex = currentIndex; // Initialize nextIndex to the current index

      const numCols = 2; // Defined by grid-cols-2
      const numItems = displayableTemplates.length;
      const numRows = Math.ceil(numItems / numCols);

      if (event.key === 'ArrowDown') {
        event.preventDefault();
        if (currentIndex === -1) {
          nextIndex = 0; // Start at top-left
          console.log('\ud83d\udd04 No current focus, setting nextIndex to 0');
        } else {
          let moved = false;
          // 1. Try moving right
          const rightIndex = currentIndex + 1;
          if (rightIndex < numItems && rightIndex % numCols !== 0) { // Check bounds and ensure it's in the second column
            nextIndex = rightIndex;
            moved = true;
          }

          // 2. If couldn't move right, try moving down to the start of the next row
          if (!moved) {
            const startOfCurrentRow = Math.floor(currentIndex / numCols) * numCols;
            const belowStartIndex = startOfCurrentRow + numCols;
            if (belowStartIndex < numItems) {
              nextIndex = belowStartIndex;
              moved = true;
            }
          }

          // 3. If couldn't move right or down, handle end of list / filter switch
          if (!moved) {
            // Check conditions for filter switching (at the end of the list)
             if (currentSpreadPlacedImageCount === 0 && imageCountFilter !== 'all') {
                // Determine which counts to cycle through based on favorite filter
                let countsToCycle = availableCounts;
                if (filterByFavorites) {
                    const favoriteCountsSet = new Set<number>();
                    allBookTemplates.forEach(t => {
                        if (favoriteTemplateIds.has(t.id)) {
                            favoriteCountsSet.add(t.images.length);
                        }
                    });
                    countsToCycle = Array.from(favoriteCountsSet).sort((a, b) => a - b);
                }

                if (countsToCycle.length > 0) { // Only proceed if there are counts to cycle through
               const currentFilterNum = parseInt(imageCountFilter, 10);
               const currentFilterIndex = countsToCycle.indexOf(currentFilterNum);

               if (currentFilterIndex !== -1) { // Ensure current filter is valid within the cycle list
                 const nextCountIndex = (currentFilterIndex + 1) % countsToCycle.length; // Wrap around within the cycle list
                 const nextFilterNum = countsToCycle[nextCountIndex];
                 // Find the first template matching the next count (and favorite status if applicable)
                 const firstTemplateOfNextFilter = allBookTemplates.find(t =>
                   t.images.length === nextFilterNum && (!filterByFavorites || favoriteTemplateIds.has(t.id))
                 );

                 if (firstTemplateOfNextFilter) {
                   setImageCountFilter(String(nextFilterNum));
                   onSelectTemplate(firstTemplateOfNextFilter);
                   setFocusedIndex(0); // Focus the first item of the new list
                   setTimeout(() => templateRefs.current[0]?.scrollIntoView({ block: 'nearest' }), 50); // Scroll after state update
                   return; // Exit handler after switching filter
                 }
               }
                }
             }
             // If not switching filter, wrap to the beginning
             nextIndex = 0;
             toast.info("Reached end of templates, wrapping to beginning");
          }
        }
      } else if (event.key === 'ArrowUp') {
        event.preventDefault(); // Prevent default scroll unconditionally for ArrowUp
        if (currentIndex === -1) {
          nextIndex = numItems - 1; // Start at bottom-right
        } else {
          // Check if user is on the *first* item of the current list
          if (currentIndex === 0) {
            // Check conditions for filter switching (at the start of the list)
            if (currentSpreadPlacedImageCount === 0 && imageCountFilter !== 'all') {
                // Determine which counts to cycle through based on favorite filter
                let countsToCycle = availableCounts;
                if (filterByFavorites) {
                    const favoriteCountsSet = new Set<number>();
                    allBookTemplates.forEach(t => {
                        if (favoriteTemplateIds.has(t.id)) {
                            favoriteCountsSet.add(t.images.length);
                        }
                    });
                    countsToCycle = Array.from(favoriteCountsSet).sort((a, b) => a - b);
                }

                if (countsToCycle.length > 0) { // Only proceed if there are counts to cycle through
              const currentFilterNum = parseInt(imageCountFilter, 10);
              const currentFilterIndex = countsToCycle.indexOf(currentFilterNum);

              if (currentFilterIndex !== -1) { // Ensure current filter is valid within the cycle list
                const prevCountIndex = (currentFilterIndex - 1 + countsToCycle.length) % countsToCycle.length; // Wrap around within the cycle list
                const prevFilterNum = countsToCycle[prevCountIndex];
                // Find the first template matching the previous count (and favorite status if applicable)
                const firstTemplateOfPrevFilter = allBookTemplates.find(t =>
                  t.images.length === prevFilterNum && (!filterByFavorites || favoriteTemplateIds.has(t.id))
                );

                if (firstTemplateOfPrevFilter) {
                  setImageCountFilter(String(prevFilterNum));
                  onSelectTemplate(firstTemplateOfPrevFilter);
                  setFocusedIndex(0); // Focus the first item of the new list
                  setTimeout(() => templateRefs.current[0]?.scrollIntoView({ block: 'nearest' }), 50); // Scroll after state update
                  return; // Exit handler after switching filter
                }
              }
                }
            }
            // If not switching filter (or conditions failed), wrap to the end of the current list
            nextIndex = numItems - 1;
            toast.info("Reached beginning of templates, wrapping to end");
          } else {
            // Not on the first item, mimic ArrowDown's row-first logic in reverse
            let moved = false;
            // 1. Try moving left
            const leftIndex = currentIndex - 1;
            // Check bounds and ensure it's still on the same row (not wrapping to previous row's end)
            if (leftIndex >= 0 && Math.floor(leftIndex / numCols) === Math.floor(currentIndex / numCols)) {
              nextIndex = leftIndex;
              moved = true;
            }

            // 2. If couldn't move left, try moving up to the end of the previous row
            if (!moved) {
              const endOfPrevRow = Math.floor(currentIndex / numCols) * numCols - 1;
              if (endOfPrevRow >= 0) {
                nextIndex = endOfPrevRow;
                moved = true;
              } else {
                 // If already in the first row and couldn't move left, wrap to the end
                 nextIndex = numItems - 1;
               }
            }
          }
        }
      // Comments removed as the logic is now handled by the check at the top
       } else if (event.key === 'Enter' || event.key === ' ') {
        if (focusedIndex !== null && displayableTemplates[focusedIndex]) { // Use filtered array
          event.preventDefault();
          handleTemplateClick(displayableTemplates[focusedIndex], focusedIndex); // Use filtered array
        }
        return; // Don't scroll or change focus on Enter/Space
      } else if (event.key === 'Escape') {
        event.preventDefault();
        setFocusedIndex(null); // Clear internal keyboard focus/highlight
        setSelectedTemplateId(null); // Clear click selection highlight
        onEscape?.(); // Call the escape handler if provided (this clears preview in BookEditor)
        return;
      }
      // Final 'else { return }' removed as Left/Right are handled explicitly now.
      // Other unhandled keys will implicitly return.

      // Ensure nextIndex is valid AND different from the starting index before updating
      if (nextIndex >= 0 && nextIndex < displayableTemplates.length && nextIndex !== currentIndex) { // Use currentIndex for comparison
          const templateToApply = displayableTemplates[nextIndex]; // Get the template object
          

          // Save user's manual focus position
          lastUserFocusedIndex.current = nextIndex;
          lastUserFocusedTemplateId.current = templateToApply?.id || null;

          setFocusedIndex(nextIndex);
          // Trigger preview for the newly focused template (optional, could be removed if applying immediately)
          // onPreviewTemplate(templateToApply?.id ?? null); // Commented out: Apply instead of just previewing

          // --- Apply the template immediately on arrow key navigation ---
          if (templateToApply) {
            onSelectTemplate(templateToApply); // Call the prop passed from BookEditor
          } else {
            console.log('⚠️ No template to apply in keyboard navigation');
          }
          // --- End Apply Logic ---

          // Scroll the newly focused item into view
          templateRefs.current[nextIndex]?.scrollIntoView({
              // behavior: 'smooth', // Consider removing smooth for faster feedback
              block: 'nearest',
          });
      }
    // Update dependencies for keyboard handler
    // Added filterByFavorites and favoriteTemplateIds
    }, [displayableTemplates, handleTemplateClick, onPreviewTemplate, onEscape, onNavigateSpread, currentSpreadPlacedImageCount, imageCountFilter, setImageCountFilter, onSelectTemplate, setFocusedIndex, filterByFavorites, favoriteTemplateIds]); // Removed focusedIndex to prevent automatic triggering

    // Internal blur handler to clear local state and call parent handler
    const handleInternalBlur = useCallback(() => {
      setFocusedIndex(null);
      setSelectedTemplateId(null);
      onBlur?.(); // Call the parent's onBlur handler if provided
    }, [onBlur, focusedIndex, activeTemplateId, selectedTemplateId]); // Dependency on parent's onBlur


    return (
      <div className="flex-1 overflow-hidden flex flex-col bg-white">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <LayoutGrid className="w-5 h-5 text-gray-500" />
            <span className="font-medium text-lg">Layout Templates</span>
          </div>
        </div>
        {/* Filter Controls */}
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="flex items-center gap-2 mb-2">
             <Filter className="w-4 h-4 text-gray-500" />
             <span className="text-sm font-medium">Filter by Image Count:</span>
          </div>
          <ToggleGroup
            type="single"
            value={imageCountFilter}
            onValueChange={(value) => {
              if (value) {
                setImageCountFilter(value); // Update state only if value is not empty
                lastManualFilterChange.current = Date.now(); // Record time of manual change
              }
            }}
            className="flex flex-wrap gap-1 justify-start items-center" // Allow wrapping
          >
            <ToggleGroupItem value="all" size="sm" className="text-xs px-2.5">All</ToggleGroupItem>
            {/* Dynamically generate buttons for available counts (e.g., 1-10) */}
            {[...Array(26)].map((_, i) => {
              const count = i + 1;
              // Optional: Check if any templates actually exist for this count before rendering button
              const hasTemplatesForCount = allBookTemplates.some(t => t.id !== '__blank__' && t.images.length === count);
              if (!hasTemplatesForCount) return null; // Don't show button if no templates exist for this count

              return (
                <ToggleGroupItem key={count} value={String(count)} size="sm" className="text-xs px-2.5">
                  {count}
                </ToggleGroupItem>
              );
            })}
            {/* Filter by Favorites Toggle */}
            <Button
              variant={filterByFavorites ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setFilterByFavorites(prev => !prev)}
              className="text-xs px-2.5 focus:ring-0 focus-visible:ring-0 focus:ring-offset-0"
              title={filterByFavorites ? "Show all templates (respecting count filter)" : "Show only favorite templates"}
            >
              <Star className={`w-3.5 h-3.5 mr-1 ${filterByFavorites ? 'fill-yellow-400 text-yellow-500' : 'text-gray-400'}`} />
              Filter Favs
            </Button>
          </ToggleGroup>
       </div>

        <div className="flex-1 overflow-hidden">
          {/* Make container focusable and attach keydown listener */}
          <div
            ref={containerRef}
            className="h-full p-6 outline-none overflow-auto" // Added overflow-auto and h-full
            tabIndex={0} // Make it focusable
            onKeyDown={handleKeyDown}
            onFocus={onFocus} // Attach focus handler
            onBlur={handleInternalBlur} // Use the internal blur handler
            onScroll={handleScroll} // Use virtualized scroll handler
            onWheel={handleUserInteraction}
            onMouseMove={handleUserInteraction}
            onMouseDown={handleUserInteraction}
          >
            <TooltipProvider>
              {/* Virtual scrolling container */}
              <div style={{ height: totalHeight, position: 'relative' }}>
                <div 
                  className={`grid ${gridColsClass} gap-4 absolute w-full`}
                  style={{ 
                    top: startRow * rowHeight,
                    transform: `translateY(0px)` // Keep for potential future optimizations
                  }}
                >
                  {visibleTemplates.map((template: TemplateData, virtualIndex: number) => {
                    const actualIndex = visibleStartIndex + virtualIndex;
                    return (
                      <div
                        key={template.id}
                        ref={el => templateRefs.current[actualIndex] = el}
                        className="flex flex-col rounded-md outline-none cursor-grab relative group/template" // Add relative and group
                        draggable={true} // Make draggable
                        onMouseEnter={() => {
                          setIsTemplateHovered(true);
                        }}
                        onMouseLeave={() => {
                          setIsTemplateHovered(false);
                        }}
                        onDragStart={(e) => {
                          // Prevent drag if clicking the favorite button
                          if ((e.target as HTMLElement).closest('.favorite-button')) {
                             e.preventDefault();
                             return;
                          }
                          e.dataTransfer.setData('application/x-bookproofs-template', template.id);
                          e.dataTransfer.effectAllowed = 'copy';
                        }}
                        onClick={(e) => {
                           // Prevent click if clicking the favorite button
                           if ((e.target as HTMLElement).closest('.favorite-button')) {
                             return;
                           }
                           handleTemplateClick(template, actualIndex)
                        }}
                      >
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div>
                              <Template
                                id={template.id}
                                name={template.name}
                                images={template.images}
                                // Highlight based on keyboard focus OR the active template ID from the current spread
                                // Prioritize focusedIndex for highlight during keyboard nav
                                selected={
                                  focusedIndex !== null
                                    ? focusedIndex === actualIndex // Highlight based on keyboard focus
                                    : activeTemplateId === template.id // Otherwise, highlight if it's the active spread's template
                                }
                                onClick={() => {}} // Still pass dummy onClick
                                spreadAspectRatio={spreadAspectRatio} // Pass spread aspect ratio for letterboxing/pillarboxing
                              />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{template.name}</p>
                          </TooltipContent>
                        </Tooltip>
                        {/* Favorite Button Overlay */}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="favorite-button absolute top-1 right-1 w-6 h-6 p-0 opacity-50 group-hover/template:opacity-100 transition-opacity z-10 bg-white/30 hover:bg-white/70"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent card click
                            onToggleFavorite(template.id);
                          }}
                          title={favoriteTemplateIds.has(template.id) ? "Remove from favorites" : "Add to favorites"}
                        >
                          <Star className={`w-4 h-4 ${favoriteTemplateIds.has(template.id) ? 'fill-yellow-400 text-yellow-500' : 'text-gray-400 hover:text-yellow-400'}`} />
                        </Button>
                      </div>
                    );
                  })}
                </div>
              </div>
            </TooltipProvider>
          </div>
        </div>

        {/* Thumbnail Size Slider for Templates */}
        {displayableTemplates.length > 0 && (
          <div className="p-4 border-t border-gray-200">
            <input
              type="range"
              min="0.6" // Adjusted min for more distinct column changes
              max="1.4" // Adjusted max
              step="0.01" // Coarser steps for 3 distinct sizes (0.6, 0.8 for 3-col; 1.0 for 2-col; 1.2, 1.4 for 1-col)
              value={templateThumbnailSize}
              onChange={(e) => setTemplateThumbnailSize(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              title="Thumbnail Size"
            />
          </div>
        )}
      </div>
    );
  }
); // Close forwardRef

// Add display name for React DevTools - Moved before export
TemplatesTray.displayName = 'TemplatesTray';
 
// Removed React.memo wrapper
export default TemplatesTray;
