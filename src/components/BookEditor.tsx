import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import BookProofsHeader from './BookProofsHeader';
import { Loader2, Maximize, Minimize2, Shrink, Expand, Maximize2, Type, Trash2, AlignLeft, AlignCenter, AlignRight, Bold, Italic, Underline, X, Layers } from 'lucide-react'; // Added Text Control Icons
import SpreadCan<PERSON>, { SpreadCanvasHandle, ImageQualityWarnings } from './SpreadCanvas';
import TemplatesTray, { TemplatesTrayHandle } from './TemplatesTray';
import { getProjectDimension, TemplateImage } from '@/lib/templates/interfaces';
import ImageTray, { ImageFile, ImageTrayHandle } from './ImageTray'; // Import ImageTrayHandle
import SpreadsTray, { Spread, ImageTransform, ImagePlacement } from './SpreadsTray'; // Assuming SpreadsTray doesn't export a handle
import ImageGapControl from './ImageGapControl';
import SettingsPanel from './SettingsPanel';
import ContextualStatusTray from './ContextualStatusTray'; // Import ContextualStatusTray
import ProjectCover, { ProjectCoverHandle } from './ProjectCover'; // Import ProjectCover and ProjectCoverHandle
import { ProofingControls } from './ProofingComponents'; // Import ProofingControls
import { toast } from 'sonner';
import { allBookTemplates, createTemplateMap, adjustTemplateForGap, AdjustedPlaceholderPt } from '@/lib/templates'; // Added adjustTemplateForGap, AdjustedPlaceholderPt
// import jsPDF from 'jspdf'; // REMOVED
import { PDFDocument, rgb, degrees, StandardFonts, PDFFont, PDFImage, PDFRef, PDFName, PDFArray, PDFDict, PDFRawStream, pushGraphicsState, clip, popGraphicsState, RGB } from 'pdf-lib'; // ADDED pdf-lib imports + operators, and RGB type
import html2canvas from 'html2canvas-pro';
import pako from 'pako'; // ADDED for PNG ICC profile decompression
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from '@/components/ui/input'; // Though not used in controls, keep for potential future use
import { ChromePicker } from 'react-color';
import { cn } from '@/lib/utils';
import { useUserSettings } from '@/hooks/useUserSettings';
import { useUndoRedo, HistoryEntry as UndoRedoHistoryEntry, CoverState } from '@/hooks/useUndoRedo'; // Renamed HistoryEntry, import CoverState
import { useImageImport } from '@/hooks/useImageImport';
import { useProjectPersistence, ProjectData as ProjectPersistenceData } from '@/hooks/useProjectPersistence'; // Import ProjectData type
import { useFileAvailability } from '@/hooks/useFileAvailability'; // Added file availability hook
import { useStatusBar } from '@/contexts/StatusBarContext'; // Import the context hook
import { useProofing } from '@/components/ProofingComponents'; // Import the proofing context hook
import '@/styles/resizing.css'; // Import our custom resizing styles
import { WarningSpreadDetail } from './ImageQualityExportWarning';
import MissingFilesDialog from './MissingFilesDialog'; // Import the missing files dialog
import { checkSpreadImagesQuality } from '@/lib/imageQuality'; // Import the specific function
import { autoBuildSpreadInstructions, AutoBuildInstruction, ImageMetadata } from '@/lib/autobuild';
import { TextEditor, TextOverlay, fontFamilyToPdfFontMap, fontFamilies } from '@/components/TextEditor'; // Import TextOverlay type, fontFamilyToPdfFontMap and fontFamilies
import { normalizeBackgroundImageUrl, getOriginalPathFromUrl } from '@/utils/imageUtils';
import { SpreadBackgroundData } from './SpreadBackgroundManager'; // Import for per-spread background
import ExportDialog, { CoverExportOption, PdfFormatOption, PdfLayoutOption, CoverLayoutOption, ColorSpaceOption } from './ExportDialog'; // Import ExportDialog and ColorSpaceOption
import { useTemplateEditorContext } from '../App'; // Import template editor context

// Add TypeScript declaration for the window object extension if needed
declare global {
  interface Window {
    // electronAPI is now declared in electron.d.ts
    resizeTimeout?: NodeJS.Timeout;
    bookProofsApp?: {
      getUpdatedFilePath?: (originalPath: string) => string | null;
      getAllUpdatedPaths?: () => Record<string, string>;
      updatedPaths?: Record<string, string>;
      updateImagePaths?: (images: ImageFile[]) => ImageFile[];
      isRestoringState?: boolean;
      imageCache?: Record<string, any>;
    };
  }
}

// REMOVED local ProjectData interface definition

interface BookEditorProps {
  aspectRatio: {
    id: string;
    title: string;
    ratio: string;
    dimensions: string;
    widthPt?: number; // Added
    heightPt?: number; // Added
    bleed?: string; // Added bleed
    safetyMargin?: string; // Added safety margin
  };
  bookPageWidthInches: number; // This might become redundant or derived from widthPt
  onLoadAspectRatio: (aspectRatio: { id: string; title: string; ratio: string; dimensions: string; widthPt?: number; heightPt?: number; bleed?: string; safetyMargin?: string; }) => void; // Updated type
  onUpdateAspectRatioPoints: (points: { widthPt: number; heightPt: number }) => void; // Added callback
  onNewProjectRequest: () => void;
  initialProjectData?: ProjectPersistenceData | null; // Use imported type
  projectDirectoryPath: string | null;
  projectFilePath: string | null;
  onProjectSaveAs: (newFilePath: string) => void;
  projectBackgroundImage?: string | null; // Add this prop
  projectBackgroundImageOpacity: number; // Add opacity prop
  setProjectBackgroundImageOpacity: React.Dispatch<React.SetStateAction<number>>; // Add opacity setter prop
  showBleedArea: boolean; // Add bleed area visibility prop
  setShowBleedArea: React.Dispatch<React.SetStateAction<boolean>>; // Add bleed area visibility setter prop
  // REMOVED: onCanvasHoverChange prop
  onTriggerLoadAndNavigate?: () => Promise<void>; // New prop for navigating to start and then loading
}

// Editor-only gap multipliers to adjust visual gap appearance in SpreadCanvas
// Horizontal multiplier of 0.5 corrects ~2x exaggeration in editor rendering
// Vertical multiplier of 0.8 fine-tunes slight rendering discrepancy
const EDITOR_HORIZ_GAP_MULTIPLIER = .5; // Adjust horizontal gap
const EDITOR_VERT_GAP_MULTIPLIER = 0.8; // Adjust vertical gap
const BookEditor = ({
  aspectRatio,
  bookPageWidthInches, // Destructure this prop
  onLoadAspectRatio,
  onUpdateAspectRatioPoints, // Destructure the new callback
  onNewProjectRequest,
  initialProjectData,
  projectDirectoryPath, // Use renamed prop
  projectFilePath: initialProjectFilePath, // Use prop for initial file path
  onProjectSaveAs, // Use callback prop
  projectBackgroundImage: projectBackgroundImageFromProps, // Destructure with a new name to avoid conflict
  projectBackgroundImageOpacity, // Destructure opacity prop
  setProjectBackgroundImageOpacity, // Destructure opacity setter prop
  showBleedArea, // Destructure bleed area visibility prop
  setShowBleedArea, // Destructure bleed area visibility setter prop
  // REMOVED: onCanvasHoverChange prop from destructuring
  onTriggerLoadAndNavigate, // Destructure the new prop
}: BookEditorProps) => {
  // --- Core State ---
  const [images, setImages] = useState<ImageFile[]>([]);
  const [spreads, setSpreads] = useState<Spread[]>([
    { id: 'spread-1', templateId: '__blank__', images: [], spreadBackgroundData: null } // Initialize spreadBackgroundData
  ]);
  const [currentSpreadId, setCurrentSpreadId] = useState('spread-1');
  const [imageGap, setImageGap] = useState(10);
  const [backgroundColor, setBackgroundColor] = useState('#FFFFFF'); // Added for background color
  const [projectBackgroundImage, setProjectBackgroundImage] = useState<string | null>(projectBackgroundImageFromProps || null); // URL for display
  const [projectBackgroundImagePath, setProjectBackgroundImagePath] = useState<string | null>(null); // Original path to track updates
  const [projectBackgroundImageOriginalDimensions, setProjectBackgroundImageOriginalDimensions] = useState<{ width: number; height: number } | null>(null); // Original dimensions for DPI calculation
  // Add state for background image zoom and pan
  const [backgroundImageZoom, setBackgroundImageZoom] = useState<number>(1); // Default zoom level: 1 (100%)
  const [backgroundImagePanX, setBackgroundImagePanX] = useState<number>(50); // Default pan X: 50% (centered)
  const [backgroundImagePanY, setBackgroundImagePanY] = useState<number>(50); // Default pan Y: 50% (centered)
  const [hasCover, setHasCover] = useState<boolean>(false); // Project-specific cover setting
  const [projectBleed, setProjectBleed] = useState<number>(parseFloat(aspectRatio.bleed || "0.125")); // New state for project bleed in inches
  const [projectSafetyMargin, setProjectSafetyMargin] = useState<number>(parseFloat(aspectRatio.safetyMargin || "0.6")); // New state for project safety margin in inches
  const [imageBorderSize, setImageBorderSize] = useState<number>(0); // Project-specific image border size in points
  const [imageBorderColor, setImageBorderColor] = useState<string>('#000000'); // Project-specific image border color


  // Add state for cover image
  const [coverImage, setCoverImage] = useState<ImageFile | null>(null);
  
  // Add state for cover transform (zoom and focal points)
  const [coverScale, setCoverScale] = useState<number>(1);
  const [coverFocalX, setCoverFocalX] = useState<number>(0.5); // Default to center
  const [coverFocalY, setCoverFocalY] = useState<number>(0.5); // Default to center

  // Add state for tracking images updated via Photoshop roundtrip
  const [updatedImageIds, setUpdatedImageIds] = useState<string[]>([]);

  // Text overlay state
  const [textOverlays, setTextOverlays] = useState<TextOverlay[]>([]);
  
  // State for duplicate image warning dialog for autobuild
  const [showAutobuildDuplicateWarning, setShowAutobuildDuplicateWarning] = useState(false);
  const [autobuildDuplicateWarningData, setAutobuildDuplicateWarningData] = useState<{
    selectedImages: ImageFile[];
    duplicateImages: ImageFile[];
    initialFit: 'cover' | 'contain';
  } | null>(null);

  const [selectedTextOverlayData, setSelectedTextOverlayData] = useState<TextOverlay | null>(null);
  const [isTextControlsColorPickerOpen, setIsTextControlsColorPickerOpen] = useState(false);
  // Removed textControlsPosition, isDraggingTextControls, and textControlsDragStart states


  // --- UI State ---
  const [selectedTab, setSelectedTab] = useState<'templates' | 'settings'>('templates');
  const [previewTemplateId, setPreviewTemplateId] = useState<string | null>(null);
  const [isTemplatesTrayFocused, setIsTemplatesTrayFocused] = useState(false); // Keep for template tray logic
  // Local Set for TemplatesTray, will be synced with global state from useUserSettings
  const [localFavoriteTemplateIds, setLocalFavoriteTemplateIds] = useState<Set<string>>(new Set());
  const [filterToTrigger, setFilterToTrigger] = useState<number | null>(null);
  const [triggerSelectFirst, setTriggerSelectFirst] = useState<boolean>(false);
  const [templateIdToSkip, setTemplateIdToSkip] = useState<string | null>(null);
  const [activeTemplateIdForTray, setActiveTemplateIdForTray] = useState<string | null>(null);
  const [isFocusMode, setIsFocusMode] = useState(false);
  const [isImageDeleteAlertOpen, setIsImageDeleteAlertOpen] = useState(false);
  const [imageIdsToDelete, setImageIdsToDelete] = useState<string[]>([]);
  const [hoveredCanvasPlaceholderId, setHoveredCanvasPlaceholderId] = useState<string | null>(null); // Keep for other uses if needed
  const [hoveredCanvasInfo, setHoveredCanvasInfo] = useState<{ placeholderId: string | null, isZoomed: boolean }>({ placeholderId: null, isZoomed: false }); // New state for hover info
  const [isSpreadThumbnailHovered, setIsSpreadThumbnailHovered] = useState<boolean>(false); // State for spread tray hover
  
  // --- Multi-select state lifted from ImageTray for ContextualStatusTray ---
  const [isMultiSelectActive, setIsMultiSelectActive] = useState<boolean>(false);
  const [imageSelectionCount, setImageSelectionCount] = useState<number>(0);
  const [imageTrayWidth, setImageTrayWidth] = useState<number>(256); // Default width (w-64 = 16rem = 256px)
  const [imageTrayHeight, setImageTrayHeight] = useState<number>(266); // Default height when docked at bottom - will auto-adjust with compact mode
  const [isImageTrayCollapsed, setIsImageTrayCollapsed] = useState<boolean>(false); // Track collapsed state
  const [isResizingTray, setIsResizingTray] = useState<boolean>(false); // Track active resizing
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Add ref for timeout

  // Add state for templates/settings tray width
  const [templatesTrayWidth, setTemplatesTrayWidth] = useState<number>(320); // Default width (w-80 = 20rem = 320px)
  const [isTemplatesTrayCollapsed, setIsTemplatesTrayCollapsed] = useState<boolean>(false); // Track collapsed state
  const [isResizingTemplatesTray, setIsResizingTemplatesTray] = useState<boolean>(false); // Track active resizing
  const [isInCollapseZone, setIsInCollapseZone] = useState<boolean>(false); // Track when user is approaching collapse
  const templatesResizeTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Add ref for timeout

  // Add state for spreads tray height - will be initialized after useUserSettings
  const [spreadsTrayHeight, setSpreadsTrayHeight] = useState<number>(175); // Default starting height wrapper
  const [isSpreadsTrayCollapsed, setIsSpreadsTrayCollapsed] = useState<boolean>(false); // Track collapsed state
  const [isResizingSpreadsTray, setIsResizingSpreadsTray] = useState<boolean>(false); // Track active resizing
  const spreadsResizeTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Add ref for timeout

  // --- State for Child Component Status ---
  const [isCanvasFocused, setIsCanvasFocused] = useState(false);
  const [isImageTrayFocused, setIsImageTrayFocused] = useState(false);
  const [isSpreadsTrayFocused, setIsSpreadsTrayFocused] = useState(false);
  const [selectedImageIdsState, setSelectedImageIdsState] = useState<string[]>([]);
  const [selectedCanvasImagesCount, setSelectedCanvasImagesCount] = useState(0);
  const [selectedSpreadIdsState, setSelectedSpreadIdsState] = useState<string[]>([]);
  const [isSpreadDeleteAlertOpen, setIsSpreadDeleteAlertOpen] = useState(false);
  const [spreadIdsToDeleteState, setSpreadIdsToDeleteState] = useState<string[]>([]);
  const [showMissingFilesDialog, setShowMissingFilesDialog] = useState(false);
  const [isNoTemplateDialogVisible, setIsNoTemplateDialogVisible] = useState(false);
  const [noTemplateDialogData, setNoTemplateDialogData] = useState<{ imageCount: number; droppedImages: ImageFile[]; targetIndex: number; initialFit: 'cover' | 'contain'; buildInstructions: AutoBuildInstruction[] } | null>(null);
  // State for "Offer Autobuild" dialog when dropping on thumbnail
  const [isOfferAutobuildDialogVisible, setIsOfferAutobuildDialogVisible] = useState(false);
  const [offerAutobuildDialogData, setOfferAutobuildDialogData] = useState<{ droppedImages: ImageFile[]; currentSpreadId: string; existingImageCount: number; maxTemplateSize: number; } | null>(null);
  const [isEditingBackgroundForSpreadId, setIsEditingBackgroundForSpreadId] = useState<string | null>(null); // State for active background editing
  const [defaultSpreadBackground, setDefaultSpreadBackground] = useState<SpreadBackgroundData | null>(null); // Default background to apply to new spreads
  const [isCoverVisible, setIsCoverVisible] = useState<boolean>(false); // State for cover visibility
  
  // --- Custom Templates State ---
  const [customTemplates, setCustomTemplates] = useState<Record<string, TemplateImage[]>>({}); // Custom templates by spreadId

  // --- State for Export Dialog Quality Information ---
  const [dialogQualityIssueDetails, setDialogQualityIssueDetails] = useState<WarningSpreadDetail[]>([]); // For quality review
  const [qualityIssues, setQualityIssues] = useState<ImageQualityWarnings>({ hasWarnings: false, qualityIssues: {} }); // This will store the raw result from SpreadCanvas
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [exportDialogCounts, setExportDialogCounts] = useState({ missingFiles: 0, lowResImages: 0, emptySpreads: 0 });
  const [isExporting, setIsExporting] = useState(false);
  // To store arguments for the actual PDF generation, to be passed from the dialog
  // pendingExportRef removed as it's no longer needed
  const pdfGenerationArgsRef = useRef<{
    spreadsForExport: Spread[],
    coverPdfDoc?: PDFDocument, // For pre-generated separate cover (if any)
    capturedCoverState?: ReturnType<ProjectCoverHandle['captureCurrentCoverState']> | null // For cover rendering
  }>({ spreadsForExport: [] });
  const exportCancelledRef = useRef(false); // Ref to signal export cancellation

  // --- Refs ---
  const templatesTrayRef = useRef<TemplatesTrayHandle>(null);
  const spreadCanvasRef = useRef<SpreadCanvasHandle>(null);
  const imageTrayRef = useRef<ImageTrayHandle>(null); // Update ref type
  const projectCoverRef = useRef<ProjectCoverHandle>(null); // ADDED: Ref for ProjectCover
  
  // Ref to store the last cover state for persistence across navigation
  const lastCoverState = useRef<CoverState | null>(null);
  
  const checkIntervalStartedRef = useRef<boolean>(false); // Track if periodic checking has started
  const spreadContainerRef = useRef<HTMLDivElement>(null); // Ref for the SpreadCanvas container (already exists)
  const centralAreaRef = useRef<HTMLDivElement>(null); // Ref for the central area, parent of controls
  const textControlsBarRef = useRef<HTMLDivElement>(null); // Ref for the text controls bar
  const spreadsTrayRef = useRef<HTMLDivElement>(null); // Use appropriate type if SpreadsTray exposes a handle
  // Refs for passing current state to hooks without causing re-renders
  const aspectRatioRef = useRef(aspectRatio); // This will now include bleed
  const imagesRef = useRef(images);
  const spreadsRef = useRef(spreads);
  const imageGapRef = useRef(imageGap);
  const backgroundColorRef = useRef(backgroundColor); // Added for background color
  const projectBackgroundImageRef = useRef(projectBackgroundImage); // Ref for the URL
  const projectBackgroundImagePathRef = useRef(projectBackgroundImagePath); // Ref for the path
  const projectBackgroundImageOriginalDimensionsRef = useRef(projectBackgroundImageOriginalDimensions); // Ref for original dimensions
  const backgroundImageZoomRef = useRef(backgroundImageZoom); // Zoom level
  const backgroundImagePanXRef = useRef(backgroundImagePanX); // Pan X position
  const backgroundImagePanYRef = useRef(backgroundImagePanY); // Pan Y position
  const projectBackgroundImageOpacityRef = useRef(projectBackgroundImageOpacity); // Opacity
  const textOverlaysRef = useRef(textOverlays); // Ref for text overlays
  const isEditingBackgroundForSpreadIdRef = useRef(isEditingBackgroundForSpreadId); // Ref for background editing state
  const projectBleedRef = useRef(projectBleed); // Ref for project bleed value
  const projectSafetyMarginRef = useRef(projectSafetyMargin); // Ref for project safety margin value
  const imageBorderSizeRef = useRef(imageBorderSize); // Ref for image border size
  const imageBorderColorRef = useRef(imageBorderColor); // Ref for image border color
  const defaultSpreadBackgroundRef = useRef(defaultSpreadBackground); // Ref for default background to apply to new spreads
  const customTemplatesRef = useRef(customTemplates); // Ref for custom templates by spreadId
  const isCoverVisibleRef = useRef(isCoverVisible); // Ref for isCoverVisible
  const coverImageRef = useRef(coverImage);
  const coverScaleRef = useRef(coverScale);
  const coverFocalXRef = useRef(coverFocalX); // Ref for cover focal X
  const coverFocalYRef = useRef(coverFocalY); // Ref for cover focal Y
  // hasCoverRef will be initialized after useUserSettings hook
  // Removed useState for bookPageWidthInches as it's now derived from props

  const [spreadContainerSize, setSpreadContainerSize] = useState({ width: 0, height: 0 });
  // --- Hooks ---
  const { setCanvasHoverInfo } = useStatusBar(); // Consume the context setter
  const { showImageNumbers, toggleImageNumbers } = useProofing(); // Use the proofing context
  const templateEditorContext = useTemplateEditorContext(); // Use template editor context
  
  // Update template editor context when relevant BookEditor state changes
  useEffect(() => {
    // Map aspect ratio to project dimension
    const projectDimension = getProjectDimension(aspectRatio);
    templateEditorContext.setCurrentDimension(projectDimension);
  }, [aspectRatio, templateEditorContext]);

  useEffect(() => {
    if (activeTemplateIdForTray) {
      templateEditorContext.setCurrentTemplateId(activeTemplateIdForTray);
    }
  }, [activeTemplateIdForTray, templateEditorContext]);

  // Callback to update template editor context when TemplatesTray filters change
  const handleTemplateFiltersChange = useCallback((filters: { imageCountFilter: string; filterByFavorites: boolean }) => {
    templateEditorContext.setCurrentImageCountFilter(filters.imageCountFilter);
    // For classification filter, we'll use 'all' for now since TemplatesTray doesn't have classification filtering
    // The project dimension filtering is handled separately
    templateEditorContext.setCurrentClassificationFilter('all');
  }, [templateEditorContext]);

  const {
    showFilenames, setShowFilenames,
    showEndOfFilename, setShowEndOfFilename,
    showRatingFilter, setShowRatingFilter,
    defaultDropModeIsCover, setDefaultDropModeIsCover,
    addSpreadOnArrow, setAddSpreadOnArrow,
    showVisualDropChoice, setShowVisualDropChoice,
    showDragIcon, setShowDragIcon,
    autoSwitchTemplateOnRemove, setAutoSwitchTemplateOnRemove,
    autosaveOnSpreadTurn, setAutosaveOnSpreadTurn, // Add new settings
    resetImageTransformsOnTemplateSwitch, setResetImageTransformsOnTemplateSwitch, // Add new setting
    showBookGutter, setShowBookGutter, // Add book gutter setting
    photoLibraryDisplayMode, setPhotoLibraryDisplayMode, // Add photo library display mode
    autoCoverNearEdges, setAutoCoverNearEdges, // Add auto-cover setting
    showQualityIndicators, setShowQualityIndicators, // Add quality indicators setting
    dpiWarningThresholdPercent, setDpiWarningThresholdPercent, // Add DPI threshold setting
    // Safety margin settings from useUserSettings hook
    showSafetyMargin: userShowSafetyMargin,
    setShowSafetyMargin: userSetShowSafetyMargin,
    showBleedLines: userShowBleedLines,
    setShowBleedLines: userSetShowBleedLines,
    // Bleed area mode settings
    bleedAreaMode, setBleedAreaMode,
    // projectBackgroundImage, setProjectBackgroundImage, // REMOVED from useUserSettings
    // Removed onlyShowPoorQuality, setOnlyShowPoorQuality
    // previewGenerationMode, setPreviewGenerationMode // <<< REMOVED preview generation mode settings
    // imageBorderSize, setImageBorderSize, // MOVED to project settings
    // imageBorderColor, setImageBorderColor, // MOVED to project settings
    theme, setTheme, // Add theme and setTheme
    favoriteTemplates: globalFavoriteTemplates, // Get global favorites array
    setFavoriteTemplates: setGlobalFavoriteTemplates, // Get global favorites setter
    imageTrayPosition, setImageTrayPosition, // Add imageTray position from user settings
  } = useUserSettings();

  // Initialize SpreadsTray height based on ImageTray position
  useEffect(() => {
    setSpreadsTrayHeight(imageTrayPosition === 'bottom' ? 130 : 175);
  }, []); // Only run once on mount

  const hasCoverRef = useRef(hasCover); // Initialize hasCoverRef after hasCover is declared
  interface HistoryEntry extends UndoRedoHistoryEntry {
    textOverlays: TextOverlay[];
    backgroundImageZoom: number;
    backgroundImagePanX: number;
    backgroundImagePanY: number;
    projectBackgroundImage: string | null;
    projectBackgroundImagePath: string | null;
    projectBackgroundImageOpacity: number; // ADDED: Opacity
    hasCover: boolean; // ADDED: hasCover to HistoryEntry
    isCoverVisible: boolean; // ADDED: isCoverVisible to HistoryEntry
    projectBleed: number; // ADDED: projectBleed to HistoryEntry
    projectSafetyMargin: number; // ADDED: projectSafetyMargin to HistoryEntry
    imageBorderSize: number; // ADDED: imageBorderSize to HistoryEntry
    imageBorderColor: string; // ADDED: imageBorderColor to HistoryEntry
    // coverState is inherited from UndoRedoHistoryEntry and will be of type CoverState | undefined
  }

  const {
    saveState: saveUndoState,
    undo,
    redo,
    canUndo,
    canRedo,
    isRestoringRef,
    resetHistory, // Destructure resetHistory
  } = useUndoRedo<HistoryEntry>();

  // Ref to track images for which on-demand preview generation has been requested
  const requestedPreviewPathsRef = useRef(new Set<string>());

  // Project Persistence Hook
  const {
    isDirty,
    setIsDirty,
    currentProjectFilePath,
    handleSaveProject,
    handleSaveProjectAs,
    handleLoadProjectRequest,
    handleNewProjectRequest,
  } = useProjectPersistence({
    initialProjectFilePath: initialProjectFilePath,
    initialProjectData: initialProjectData,
    aspectRatioRef,
    imagesRef,
    spreadsRef,
    imageGapRef,
    backgroundColorRef,
    projectBackgroundImageRef,
    projectBackgroundImagePathRef,
    backgroundImageZoomRef,
    backgroundImagePanXRef,
    backgroundImagePanYRef,
    projectBackgroundImageOpacityRef, // Add opacity ref
    projectBackgroundImageOriginalDimensionsRef, // Add original dimensions ref
    textOverlaysRef, // Add textOverlays ref
    isEditingBackgroundForSpreadIdRef, // Add background editing state ref
    hasCoverRef, // Pass hasCoverRef
    isCoverVisibleRef, // Pass isCoverVisibleRef
    coverImageRef,
    coverScaleRef,
    coverTranslateXRef: coverFocalXRef, // Pass coverFocalXRef as coverTranslateXRef
    coverTranslateYRef: coverFocalYRef, // Pass coverFocalYRef as coverTranslateYRef
    projectBleedRef, // Pass projectBleedRef
    projectSafetyMarginRef, // Pass projectSafetyMarginRef
    imageBorderSizeRef, // Pass imageBorderSizeRef
    imageBorderColorRef, // Pass imageBorderColorRef
    customTemplatesRef, // Pass customTemplatesRef
    captureCoverState: () => {
      // Capture cover state using the ProjectCover ref if available
      if (projectCoverRef.current && hasCoverRef.current) {
        return projectCoverRef.current.captureCurrentCoverState();
      }
      return null;
    },
    applyRestoredCoverState: (coverState: any) => {
      // Apply restored cover state using the ProjectCover ref if available
      if (projectCoverRef.current && hasCoverRef.current) {
        projectCoverRef.current.applyRestoredState(coverState);
      }
    },
    onLoadAspectRatio,
    setImages,
    setSpreads,
    setImageGap,
    setBackgroundColor,
    setProjectBackgroundImage,
    setProjectBackgroundImagePath,
    setBackgroundImageZoom, // Add zoom setter
    setBackgroundImagePanX, // Add pan X setter
    setBackgroundImagePanY, // Add pan Y setter
    setProjectBackgroundImageOpacity, // Add opacity setter
    setProjectBackgroundImageOriginalDimensions, // Add original dimensions setter
    setTextOverlays, // Add setTextOverlays
    setCustomTemplates, // Add setCustomTemplates
    setIsEditingBackgroundForSpreadId, // Add background editing state setter
    setHasCover, // ADDED: Pass setHasCover
    setIsCoverVisible, // ADDED: Pass setIsCoverVisible
    setCoverImage,
    setCoverScale,
    setCoverTranslateX: setCoverFocalX, // Pass setCoverFocalX as setCoverTranslateX
    setCoverTranslateY: setCoverFocalY, // Pass setCoverFocalY as setCoverTranslateY
    setProjectBleed, // ADDED: Pass setProjectBleed
    setProjectSafetyMargin, // ADDED: Pass setProjectSafetyMargin
    setImageBorderSize, // ADDED: Pass setImageBorderSize
    setImageBorderColor, // ADDED: Pass setImageBorderColor
    setCurrentSpreadId,
    onNewProjectRequest,
    onProjectSaveAs: (newPath) => {
      onProjectSaveAs(newPath);
      toast.success(`Project saved to ${newPath.split('/').pop() || newPath}`);
    },
    resetUndoHistory: resetHistory,
  });

  // Image Import Hook
  const { importProgress, handleAddImages, previewProgress, trackPreviewRequest: imageImportTrackPreviewRequest } = useImageImport({ setImages, setIsDirty });

  // Local wrapper for trackPreviewRequest to avoid TypeScript issues
  const handleTrackPreviewRequest = useCallback((originalPath: string) => {
    if (imageImportTrackPreviewRequest) {
      imageImportTrackPreviewRequest(originalPath);
    }
  }, [imageImportTrackPreviewRequest]);

  // File availability hook
  const { fileAvailability, checkFilesAvailability, updateBasePath: originalUpdateBasePath, updateIndividualPath: originalUpdateIndividualPath, startPeriodicCheck, stopPeriodicCheck, setIgnoreAllMissingFiles } = useFileAvailability({ // Added setIgnoreAllMissingFiles
    images,
    setImages,
    setIsDirty,
    projectDirectoryPath,
    setSpreads,
    projectBackgroundImagePath,
    setProjectBackgroundImagePath,
    coverImage, // Pass cover image
    setCoverImage // Pass setter for cover image
  });

  // Wrapper for updateBasePath that ensures isDirty is set
  const updateBasePath = useCallback(async (newBasePath: string): Promise<boolean> => {
    const result = await originalUpdateBasePath(newBasePath);
    if (result) {
      // Explicitly set isDirty to true when paths are updated successfully
      setIsDirty(() => true);

      // Force a save prompt by dispatching a custom event
      window.dispatchEvent(new CustomEvent('book-proofs-paths-updated', { detail: { isDirty: true } }));
    }
    return result;
  }, [originalUpdateBasePath, setIsDirty]);

  // Wrapper for updateIndividualPath that ensures isDirty is set
  const updateIndividualPath = useCallback(async (originalPath: string, newPath: string): Promise<boolean> => {
    const result = await originalUpdateIndividualPath(originalPath, newPath);
    if (result) {
      // Explicitly set isDirty to true when individual paths are updated successfully
      setIsDirty(() => true);

      // Force a save prompt by dispatching a custom event
      window.dispatchEvent(new CustomEvent('book-proofs-paths-updated', { detail: { isDirty: true } }));
    }
    return result;
  }, [originalUpdateIndividualPath, setIsDirty]);

  // Effect to calculate point dimensions from aspectRatio prop
  useEffect(() => {
    if (aspectRatio?.dimensions) {
      try {
        // Clean the dimensions string: remove inch marks, replace '×' with 'x', trim whitespace
        const cleanedDimensions = aspectRatio.dimensions
          .replace(/"/g, '')      // Remove inch marks (")
          .replace(/×/g, 'x')     // Replace multiplication symbol (×) with 'x'
          .replace(/\s+/g, '');   // Remove any whitespace
        const dims = cleanedDimensions.toLowerCase().split('x');
        if (dims.length === 2) {
          const widthInches = parseFloat(dims[0]);
          const heightInches = parseFloat(dims[1]);
          if (!isNaN(widthInches) && !isNaN(heightInches)) {
            const widthPt = widthInches * 72;
            const heightPt = heightInches * 72;
            // Check if points actually changed before calling callback
            if (aspectRatio.widthPt !== widthPt || aspectRatio.heightPt !== heightPt) {
               // Call the callback passed from the parent (Index.tsx)
               onUpdateAspectRatioPoints({ widthPt, heightPt });
            }
          } else {
          }
        } else {
        }
      } catch (error) {
      }
    }
  }, [aspectRatio, onUpdateAspectRatioPoints]); // Run when aspectRatio prop or the callback changes

  // Effect to apply initial project data when the component mounts or data changes
  useEffect(() => {

  if (initialProjectData) {

    setImages(initialProjectData.images);
    
    // Ensure there's at least one spread - if loading project with no spreads, create default
    let spreadsToSet = initialProjectData.spreads || [];
    if (spreadsToSet.length === 0) {
      spreadsToSet = [{ id: 'spread-1', templateId: '__blank__', images: [], spreadBackgroundData: null }];
    }
    setSpreads(spreadsToSet);
    
    setImageGap(initialProjectData.imageGap);
    setBackgroundColor(initialProjectData.backgroundColor || '#FFFFFF'); // Added, with default
    // Use the normalized URL if it exists in the project data, otherwise normalize the background image path
    const normalizedUrl = normalizeBackgroundImageUrl(initialProjectData.projectBackgroundImageNormalizedUrl || initialProjectData.projectBackgroundImage);
    setProjectBackgroundImage(normalizedUrl); // Set normalized URL
    setProjectBackgroundImagePath(initialProjectData.projectBackgroundImagePath || null); // Set Path
    setHasCover(initialProjectData.hasCover || false); // Load hasCover state, ensure it defaults to false
    setProjectBleed(initialProjectData.projectBleed !== undefined ? initialProjectData.projectBleed : parseFloat(aspectRatio.bleed || "0.125"));
    setProjectSafetyMargin(initialProjectData.projectSafetyMargin !== undefined ? initialProjectData.projectSafetyMargin : parseFloat(aspectRatio.safetyMargin || "0.6"));
    setImageBorderSize(initialProjectData.imageBorderSize !== undefined ? initialProjectData.imageBorderSize : 0);
    setImageBorderColor(initialProjectData.imageBorderColor || '#000000');
    
    // Load cover image if it exists in project data
    if (initialProjectData.coverImage) {
      setCoverImage(initialProjectData.coverImage);
    }

    // ADDED: Apply background image transforms from initialProjectData
    setBackgroundImageZoom(typeof initialProjectData.backgroundImageZoom === 'number' ? initialProjectData.backgroundImageZoom : 1);
    setBackgroundImagePanX(typeof initialProjectData.backgroundImagePanX === 'number' ? initialProjectData.backgroundImagePanX : 50);
    setBackgroundImagePanY(typeof initialProjectData.backgroundImagePanY === 'number' ? initialProjectData.backgroundImagePanY : 50);
    setProjectBackgroundImageOpacity(typeof initialProjectData.projectBackgroundImageOpacity === 'number' ? initialProjectData.projectBackgroundImageOpacity : 1); // Load opacity
    setIsCoverVisible(initialProjectData.isCoverVisible || false); // Load isCoverVisible state
    
    // Load cover transform state (reading focal points from coverTranslateX/Y fields)
    setCoverScale(typeof initialProjectData.coverScale === 'number' ? initialProjectData.coverScale : 1);
    setCoverFocalX(typeof initialProjectData.coverTranslateX === 'number' ? initialProjectData.coverTranslateX : 0.5);
    setCoverFocalY(typeof initialProjectData.coverTranslateY === 'number' ? initialProjectData.coverTranslateY : 0.5);

    setTextOverlays(initialProjectData.textOverlays || []); // Load text overlays
    setCustomTemplates(initialProjectData.customTemplates || {}); // Load custom templates
    // Ensure aspectRatio is also handled if needed, though it's passed as a direct prop
    // onLoadAspectRatio(initialProjectData.aspectRatio); // Might be redundant if Index already sets it
    const firstSpreadId = spreadsToSet[0]?.id; // Use spreadsToSet instead of initialProjectData.spreads

    setCurrentSpreadId(firstSpreadId || 'spread-1'); // Reset to first spread or default
    setIsDirty(false); // Start clean after loading initial data

    // Check file availability after a short delay to allow UI to render
    setTimeout(() => {

      checkFilesAvailability().then(allFilesAvailable => {

        if (!allFilesAvailable) {
          setShowMissingFilesDialog(true);
        }
      });
    }, 1000);
  } else {

  }
  // This effect should only run when initialProjectData itself changes,
  // not when the setters or checkFilesAvailability change identity.
}, [initialProjectData]); // REMOVED setters and checkFilesAvailability from dependencies to prevent re-running

  // Effect to synchronize local favoriteTemplateIds (Set) with globalFavoriteTemplates (array)
  useEffect(() => {
    setLocalFavoriteTemplateIds(new Set(globalFavoriteTemplates || []));
  }, [globalFavoriteTemplates]);

  // Effect to update the missing files dialog when fileAvailability changes
  useEffect(() => {
    // If there are no missing files, hide the dialog
    if (fileAvailability.missingFiles.length === 0 && showMissingFilesDialog) {
      setShowMissingFilesDialog(false);
    }
  }, [fileAvailability.missingFiles.length, showMissingFilesDialog]);

  // Effect to stop periodic file checking when component unmounts
  useEffect(() => {
    return () => {
      // Stop periodic file checking when component unmounts
      stopPeriodicCheck();
    };
  }, [stopPeriodicCheck]);

  // We previously had an effect here to start periodic file checking
  // This has been replaced with targeted checks during spread navigation

  // Effect to set initial image tray width based on aspect ratio, disabling transition during set
  useEffect(() => {
    if (aspectRatio?.widthPt && aspectRatio?.heightPt) {
      const isVertical = aspectRatio.heightPt > aspectRatio.widthPt;
      const targetWidth = isVertical ? 320 : 256;
      // Temporarily disable transition by setting resizing state
      setIsResizingTray(true);
      setImageTrayWidth(targetWidth);
      // Re-enable transition shortly after state update
      // Use setTimeout to ensure it happens after the render cycle triggered by setImageTrayWidth
      const timer = setTimeout(() => setIsResizingTray(false), 0);
      return () => clearTimeout(timer); // Cleanup timeout if effect re-runs before timeout finishes
    }
    // We only want this to run once when the dimensions are first available,
    // or if the aspect ratio fundamentally changes later (which shouldn't happen often in editor).
    // Relying on widthPt/heightPt ensures it runs after they are calculated.
  }, [aspectRatio?.widthPt, aspectRatio?.heightPt, setImageTrayWidth, setIsResizingTray]); // Added setters to dependencies


  // --- Memoized Calculations ---
  const usedImageIds = useMemo(() => {
    const usedIds = new Set<string>();
    spreads.forEach(spread => {
      spread.images.forEach(img => {
        if (img.imageId) usedIds.add(img.imageId);
      });
    });
    return Array.from(usedIds);
  }, [spreads]);

  const currentSpreadIndex = useMemo(() => {
    return spreads.findIndex(s => s.id === currentSpreadId);
  }, [spreads, currentSpreadId]);

  const currentSpreadDisplayIndex = useMemo(() => {
    const displayIndex = currentSpreadIndex + 1;
    return displayIndex;
  }, [currentSpreadIndex, spreads.length]);

  const currentSpreadPlacedImageCount = useMemo(() => {
    const currentSpreadData = spreads.find(s => s.id === currentSpreadId);
    return currentSpreadData?.images.filter(p => p.imageId).length ?? 0;
  }, [spreads, currentSpreadId]);

  const templateMap = useMemo(() => createTemplateMap(allBookTemplates), []);
  const allTemplates = useMemo(() => allBookTemplates, []);

  // Calculate the maximum number of images any template supports
  const maxTemplateSize = useMemo(() => {
    if (!allTemplates || allTemplates.length === 0) return 0;
    return Math.max(...allTemplates.map(t => t.images.length));
  }, [allTemplates]);

  // Calculate the adjusted layout for ALL spreads in points
  const allSpreadLayouts = useMemo((): Record<string, AdjustedPlaceholderPt[]> => {
    const layouts: Record<string, AdjustedPlaceholderPt[]> = {};
    const widthPt = aspectRatio?.widthPt;
    const heightPt = aspectRatio?.heightPt;

    // Determine the gap value to use for the editor layout calculation
    let gapForEditorLayout: number | { horizontal: number; vertical: number };
    if (imageGap > 0) {
      // If gap > 0, apply multipliers
      gapForEditorLayout = {
        horizontal: Math.max(0, imageGap * EDITOR_HORIZ_GAP_MULTIPLIER),
        vertical: Math.max(0, imageGap * EDITOR_VERT_GAP_MULTIPLIER)
      };
    } else {
      // If gap is 0 or less, use 0 directly to trigger zero-gap logic without multipliers
      gapForEditorLayout = 0;
    }

    // Ensure we have valid dimensions before calculating for any spread
    if (imageGap === undefined || widthPt === undefined || heightPt === undefined || widthPt <= 0 || heightPt <= 0) {
      // If dimensions are invalid, we might return empty layouts for all or default ones
      // Let's return default (0 gap) layouts for all spreads in this case
      spreads.forEach(spread => {
        const template = templateMap[spread.templateId];
        if (template && template.images) {
          // Pass 0 for gap if dimensions were invalid
          layouts[spread.id] = adjustTemplateForGap(template, 0, widthPt || 1, heightPt || 1);
        } else {
          layouts[spread.id] = []; // Empty layout if no template
        }
      });
      return layouts;
    }

    // Calculate layout for each spread using the determined gap value
    spreads.forEach(spread => {
      const template = templateMap[spread.templateId];
      if (!template || !template.images) {
        layouts[spread.id] = []; // Assign empty array if no template/images
        return; // Continue to next spread
      }
      // Pass the full template object for custom gap handling
      layouts[spread.id] = adjustTemplateForGap(template, gapForEditorLayout, widthPt, heightPt);
    });

    return layouts;

  }, [spreads, templateMap, imageGap, aspectRatio]); // Keep original dependencies, multipliers are constants

  // Calculate the gap value for editor (used by SpreadCanvas for custom templates)
  const gapForEditor = useMemo((): number | { horizontal: number; vertical: number } => {
    if (imageGap > 0) {
      return {
        horizontal: Math.max(0, imageGap * EDITOR_HORIZ_GAP_MULTIPLIER),
        vertical: Math.max(0, imageGap * EDITOR_VERT_GAP_MULTIPLIER)
      };
    } else {
      return 0;
    }
  }, [imageGap]);

  // --- Callbacks ---
  const saveStateForUndo = useCallback(() => {
    const currentState: HistoryEntry = {
      spreads,
      currentSpreadId,
      backgroundColor,
      textOverlays,
      customTemplates,
      backgroundImageZoom,
      backgroundImagePanX,
      backgroundImagePanY,
      projectBackgroundImage,
      projectBackgroundImagePath,
      projectBackgroundImageOpacity, // Add opacity
      hasCover, // from state
      isCoverVisible, // from state
      projectBleed, // from state
      projectSafetyMargin, // from state
      imageBorderSize, // from state
      imageBorderColor, // from state
      coverState: (hasCoverRef.current && projectCoverRef.current) ? projectCoverRef.current.captureCurrentCoverState() ?? undefined : undefined
    };
    saveUndoState(currentState);
  }, [
    spreads,
    currentSpreadId,
    backgroundColor,
    textOverlays,
    customTemplates,
    backgroundImageZoom,
    backgroundImagePanX,
    backgroundImagePanY,
    projectBackgroundImage,
    projectBackgroundImagePath,
    projectBackgroundImageOpacity, // Add opacity
    hasCover, // from state, for dependency array
    isCoverVisible, // from state, for dependency array
    projectBleed, // from state
    projectSafetyMargin, // from state
    imageBorderSize, // from state
    imageBorderColor, // from state
    saveUndoState
    // hasCoverRef and projectCoverRef are refs, not needed in deps for their .current access
  ]);

  const handleUndo = useCallback(() => {
    if (!canUndo) return;
    isRestoringRef.current = true;

    if (!window.bookProofsApp) window.bookProofsApp = {};
    window.bookProofsApp.isRestoringState = true;

    const currentState: HistoryEntry = {
      spreads,
      currentSpreadId,
      backgroundColor,
      textOverlays,
      backgroundImageZoom,
      backgroundImagePanX,
      backgroundImagePanY,
      projectBackgroundImage,
      projectBackgroundImagePath,
      projectBackgroundImageOpacity, // Add opacity
      hasCover,
      isCoverVisible,
      projectBleed,
      projectSafetyMargin,
      imageBorderSize,
      imageBorderColor,
      coverState: (hasCoverRef.current && projectCoverRef.current) ? projectCoverRef.current.captureCurrentCoverState() ?? undefined : undefined,
    };
    const previousState = undo(currentState);

    if (previousState) {
      setSpreads(previousState.spreads);
      setCurrentSpreadId(previousState.currentSpreadId);
      setBackgroundColor(previousState.backgroundColor || '#FFFFFF');
      setTextOverlays(previousState.textOverlays || []);
      setCustomTemplates(previousState.customTemplates || {});
      setBackgroundImageZoom(previousState.backgroundImageZoom || 1);
      setBackgroundImagePanX(previousState.backgroundImagePanX || 50);
      setBackgroundImagePanY(previousState.backgroundImagePanY || 50);
      setProjectBackgroundImage(previousState.projectBackgroundImage);
      setProjectBackgroundImagePath(previousState.projectBackgroundImagePath);
      setProjectBackgroundImageOpacity(previousState.projectBackgroundImageOpacity !== undefined ? previousState.projectBackgroundImageOpacity : 1); // Restore opacity
      setProjectBleed(previousState.projectBleed !== undefined ? previousState.projectBleed : 0.125);
      setProjectSafetyMargin(previousState.projectSafetyMargin !== undefined ? previousState.projectSafetyMargin : 0.6);
      setImageBorderSize(previousState.imageBorderSize !== undefined ? previousState.imageBorderSize : 0);
      setImageBorderColor(previousState.imageBorderColor || '#000000');
    
      const prevHasCover = previousState.hasCover || false;
      const prevIsCoverVisible = previousState.isCoverVisible || false;
      setHasCover(prevHasCover);
      setIsCoverVisible(prevIsCoverVisible);
  
      setTimeout(() => {
        if (prevHasCover && projectCoverRef.current) {
          if (previousState.coverState) {
            setCoverImage(previousState.coverState.coverImage);
            projectCoverRef.current.applyRestoredState(previousState.coverState);
          } else {
            setCoverImage(null);
            projectCoverRef.current.applyRestoredState({ coverImage: null, scale: 1, translateX: 0, translateY: 0 });
          }
          projectCoverRef.current.resetLocalHistory();
        } else {
          setCoverImage(null);
          if (projectCoverRef.current) { // Reset even if cover becomes inactive
            projectCoverRef.current.resetLocalHistory();
          }
        }
      }, 0);
      toast.info("Undo");
    }
  
    setTimeout(() => {
      isRestoringRef.current = false;
      if (window.bookProofsApp) window.bookProofsApp.isRestoringState = false;
    }, 0);
  }, [
    canUndo, spreads, currentSpreadId, backgroundColor, textOverlays, customTemplates,
    backgroundImageZoom, backgroundImagePanX, backgroundImagePanY,
    projectBackgroundImage, projectBackgroundImagePath, projectBackgroundImageOpacity, hasCover, isCoverVisible, projectBleed,
    undo, isRestoringRef, setSpreads, setCurrentSpreadId, setBackgroundColor,
    setTextOverlays, setBackgroundImageZoom, setBackgroundImagePanX,
    setBackgroundImagePanY, setProjectBackgroundImage, setProjectBackgroundImagePath, setProjectBackgroundImageOpacity,
    setHasCover, setIsCoverVisible, setCoverImage, setProjectBleed
    // projectCoverRef and hasCoverRef are refs
  ]);

  const handleRedo = useCallback(() => {
    if (!canRedo) return;
    isRestoringRef.current = true;

    if (!window.bookProofsApp) window.bookProofsApp = {};
    window.bookProofsApp.isRestoringState = true;

    const currentState: HistoryEntry = {
      spreads,
      currentSpreadId,
      backgroundColor,
      textOverlays,
      backgroundImageZoom,
      backgroundImagePanX,
      backgroundImagePanY,
      projectBackgroundImage,
      projectBackgroundImagePath,
      projectBackgroundImageOpacity, // Add opacity
      hasCover,
      isCoverVisible,
      projectBleed,
      projectSafetyMargin,
      imageBorderSize,
      imageBorderColor,
      coverState: (hasCoverRef.current && projectCoverRef.current) ? projectCoverRef.current.captureCurrentCoverState() ?? undefined : undefined,
    };
    const nextState = redo(currentState);

    if (nextState) {
      setSpreads(nextState.spreads);
      setCurrentSpreadId(nextState.currentSpreadId);
      setBackgroundColor(nextState.backgroundColor || '#FFFFFF');
      setTextOverlays(nextState.textOverlays || []);
      setCustomTemplates(nextState.customTemplates || {});
      setBackgroundImageZoom(nextState.backgroundImageZoom || 1);
      setBackgroundImagePanX(nextState.backgroundImagePanX || 50);
      setBackgroundImagePanY(nextState.backgroundImagePanY || 50);
      setProjectBackgroundImage(nextState.projectBackgroundImage);
      setProjectBackgroundImagePath(nextState.projectBackgroundImagePath);
      setProjectBackgroundImageOpacity(nextState.projectBackgroundImageOpacity !== undefined ? nextState.projectBackgroundImageOpacity : 1); // Restore opacity
      setProjectBleed(nextState.projectBleed !== undefined ? nextState.projectBleed : 0.125);
      setProjectSafetyMargin(nextState.projectSafetyMargin !== undefined ? nextState.projectSafetyMargin : 0.6);
      setImageBorderSize(nextState.imageBorderSize !== undefined ? nextState.imageBorderSize : 0);
      setImageBorderColor(nextState.imageBorderColor || '#000000');
    
      const nextHasCover = nextState.hasCover || false;
      const nextIsCoverVisible = nextState.isCoverVisible || false;
      setHasCover(nextHasCover);
      setIsCoverVisible(nextIsCoverVisible);
  
      setTimeout(() => {
        if (nextHasCover && projectCoverRef.current) {
          if (nextState.coverState) {
            setCoverImage(nextState.coverState.coverImage);
            projectCoverRef.current.applyRestoredState(nextState.coverState);
          } else {
            setCoverImage(null);
            projectCoverRef.current.applyRestoredState({ coverImage: null, scale: 1, translateX: 0, translateY: 0 });
          }
          projectCoverRef.current.resetLocalHistory();
        } else {
          setCoverImage(null);
          if (projectCoverRef.current) { // Reset even if cover becomes inactive
             projectCoverRef.current.resetLocalHistory();
          }
        }
      }, 0);
      toast.info("Redo");
    }
  
    setTimeout(() => {
      isRestoringRef.current = false;
      if (window.bookProofsApp) window.bookProofsApp.isRestoringState = false;
    }, 0);
  }, [
    canRedo, spreads, currentSpreadId, backgroundColor, textOverlays, customTemplates,
    backgroundImageZoom, backgroundImagePanX, backgroundImagePanY,
    projectBackgroundImage, projectBackgroundImagePath, projectBackgroundImageOpacity, hasCover, isCoverVisible, projectBleed, projectSafetyMargin,
    redo, isRestoringRef, setSpreads, setCurrentSpreadId, setBackgroundColor,
    setTextOverlays, setBackgroundImageZoom, setBackgroundImagePanX,
    setBackgroundImagePanY, setProjectBackgroundImage, setProjectBackgroundImagePath, setProjectBackgroundImageOpacity,
    setHasCover, setIsCoverVisible, setCoverImage, setProjectBleed, setProjectSafetyMargin
    // projectCoverRef and hasCoverRef are refs
  ]);

  const toggleFocusMode = useCallback(() => setIsFocusMode(prev => !prev), []);

  const handleAddSpread = useCallback(() => {
    const newSpreadId = `spread-${Date.now()}`;
    saveStateForUndo();
    setIsDirty(true);
    setSpreads(prev => [
      ...prev,
      {
        id: newSpreadId,
        templateId: '__blank__',
        images: [],
        // Apply default background if it exists
        spreadBackgroundData: defaultSpreadBackground ? {...defaultSpreadBackground} : null
      }
    ]);
    setCurrentSpreadId(newSpreadId);
    toast.success("Added new spread");
  }, [saveStateForUndo, setIsDirty, setSpreads, setCurrentSpreadId, defaultSpreadBackground]);

  const handleNavigateSpread = useCallback((direction: 'prev' | 'next') => {
    
    // FOCUS MODE NAVIGATION LOGGING - BEFORE
    if (isFocusMode) {
      const containerElement = spreadContainerRef.current;
      const containerRect = containerElement?.getBoundingClientRect();
    }

    // --- Save transforms before navigating ---
    if (spreadCanvasRef.current) {
      spreadCanvasRef.current.saveTransformsBeforeNavigation();
    }
    // --- End save transforms ---

    // Autosave check before navigation
    if (autosaveOnSpreadTurn && isDirty) {

      handleSaveProject();
    }

    const currentIndex = spreads.findIndex(s => s.id === currentSpreadId);
    if (currentIndex === -1) {
       return;
    }


    let targetSpreadId = currentSpreadId; // Use a different variable name
    let navigationHappened = false;

    if (direction === 'prev') {
      if (currentIndex > 0) {
        targetSpreadId = spreads[currentIndex - 1].id;

        setCurrentSpreadId(targetSpreadId);
        setPreviewTemplateId(null);
        navigationHappened = true;
      } else {

      }
    } else { // direction === 'next'
      if (currentIndex < spreads.length - 1) {
        targetSpreadId = spreads[currentIndex + 1].id;

        setCurrentSpreadId(targetSpreadId);
        setPreviewTemplateId(null);
        navigationHappened = true;
      } else if (addSpreadOnArrow) {

        handleAddSpread();
        return; // Exit early as handleAddSpread sets current ID
      } else {

      }
    }

    // If navigation occurred, select and focus the tray
    if (navigationHappened) {

      setSelectedSpreadIdsState([targetSpreadId]);
      // Directly focus the SpreadsTray component using its ref
      setTimeout(() => {
        spreadsTrayRef.current?.focus({ preventScroll: true });
      }, 0);

      // Check file availability when navigating to a new spread (silent mode)

      setTimeout(() => {

        checkFilesAvailability(true).then(allFilesAvailable => {

          if (!allFilesAvailable && fileAvailability.missingFiles.length > 0) {
            const currentSpread = spreads.find(s => s.id === targetSpreadId); // Use targetSpreadId
            const spreadHasMissingImages = currentSpread?.images.some(placement => {
              if (!placement.imageId) return false;
              const image = images.find(img => img.id === placement.imageId);
              if (!image) return false;
              return fileAvailability.missingFiles.some(missing => missing.originalPath === image.originalPath);
            });
            if (spreadHasMissingImages) {

              setShowMissingFilesDialog(true);
            }
          }
        });
      }, 500); // Small delay to ensure the spread is fully loaded
    }
    
    // FOCUS MODE NAVIGATION LOGGING - AFTER
    if (isFocusMode && navigationHappened) {
      setTimeout(() => {
        const containerElement = spreadContainerRef.current;
        const containerRect = containerElement?.getBoundingClientRect();
      }, 100); // Small delay to capture post-render position
    }
  }, [
    spreads, currentSpreadId, setCurrentSpreadId, setPreviewTemplateId,
    addSpreadOnArrow, handleAddSpread, autosaveOnSpreadTurn, isDirty,
    handleSaveProject, setSelectedSpreadIdsState,
    checkFilesAvailability, fileAvailability, setShowMissingFilesDialog, images,
    isFocusMode, spreadsTrayHeight // Added for focus mode logging
  ]);

  const handleToggleFavoriteTemplate = useCallback((templateId: string) => {
    setGlobalFavoriteTemplates(prevGlobalFavorites => {
      const currentFavoritesArray = prevGlobalFavorites || [];
      const newFavoritesArray = [...currentFavoritesArray];
      const index = newFavoritesArray.indexOf(templateId);
      if (index > -1) {
        newFavoritesArray.splice(index, 1);
        toast.info("Template removed from favorites");
      } else {
        newFavoritesArray.push(templateId);
        toast.success("Template added to favorites");
      }
      return newFavoritesArray;
    });
    // The localFavoriteTemplateIds Set will be updated by the useEffect listening to globalFavoriteTemplates
  }, [setGlobalFavoriteTemplates]);

  const handleSelectTemplate = useCallback(async (templateToApply: any) => {

    const currentSpread = spreads.find(s => s.id === currentSpreadId);
    if (!currentSpread) {
      return;
    }
    const existingPlacements = currentSpread.images.filter(p => p.imageId);
    const existingImageCount = existingPlacements.length;
    const newTemplateCapacity = templateToApply.images.length;


    let proceed = true;
    if (!autoSwitchTemplateOnRemove && existingImageCount > 0 && newTemplateCapacity < existingImageCount) {
      if (!window.electronAPI) { toast.error("Confirmation dialog requires the desktop app."); return; }

      const confirmationResult = await window.electronAPI.confirmDialog({
        title: 'Confirm Template Change',
        message: `The selected template '${templateToApply.name}' has only ${newTemplateCapacity} image slot(s), but you currently have ${existingImageCount} image(s) placed.`,
        detail: 'Proceeding will remove the extra images from this spread. Do you want to continue?',
        buttons: ['Cancel', 'Proceed'], defaultId: 0, cancelId: 0,
      });
      proceed = confirmationResult === 1;

    }
    if (!proceed) {
      toast.info("Template change cancelled.");

      return;
    }
    saveStateForUndo();
    setIsDirty(true);
    
    // Clear custom template for the current spread when new template is selected
    setCustomTemplates(prev => {
      if (prev[currentSpreadId]) {
        const newCustomTemplates = { ...prev };
        delete newCustomTemplates[currentSpreadId];
        return newCustomTemplates;
      }
      return prev;
    });
    
    setSpreads(prev => {

      const newSpreadsState = prev.map(spread => {
        if (spread.id !== currentSpreadId) return spread;

        const newPlacements: ImagePlacement[] = templateToApply.images.map((newPlaceholder: { id: string }, index: number): ImagePlacement => {
          if (index < existingImageCount && index < newTemplateCapacity) {
            const existingPlacement = existingPlacements[index];
            const existingFit = existingPlacement.transform?.fit || 'contain';
            let newTransform;
            if (resetImageTransformsOnTemplateSwitch) {
              newTransform = { scale: 1, focalX: 0.5, focalY: 0.5, fit: existingFit, rotation: 0 };

            } else {
              newTransform = {
                scale: existingPlacement.transform.scale || 1,
                focalX: existingPlacement.transform.focalX ?? 0.5,
                focalY: existingPlacement.transform.focalY ?? 0.5,
                fit: existingFit,
                rotation: existingPlacement.transform.rotation ?? 0
              };

            }
            return { placeholderId: newPlaceholder.id, imageId: existingPlacement.imageId, transform: newTransform };
          } else {
            const defaultFitMode = defaultDropModeIsCover ? 'cover' : 'contain';
            const emptyPlacementTransform: ImageTransform = { scale: 1, focalX: 0.5, focalY: 0.5, fit: defaultFitMode, rotation: 0 }; // Explicitly typed

            return { placeholderId: newPlaceholder.id, imageId: undefined, transform: emptyPlacementTransform };
          }
        });

        return { ...spread, templateId: templateToApply.id, images: newPlacements };
      });

      return newSpreadsState;
    });
    
  }, [spreads, currentSpreadId, saveStateForUndo, setIsDirty, setSpreads, setCustomTemplates, autoSwitchTemplateOnRemove, resetImageTransformsOnTemplateSwitch, defaultDropModeIsCover]);

  const handleSelectTemplateById = useCallback((templateId: string) => {
    const template = templateMap[templateId];
    if (template) handleSelectTemplate(template);
  }, [templateMap, handleSelectTemplate]);

  const handleSelectSpread = useCallback((spreadId: string) => {
    // If the cover is visible, hide it to navigate to the selected spread
    if (isCoverVisibleRef.current) { // Use ref to get current value without adding to deps
      saveStateForUndo(); // Save state before changing visibility
      setIsCoverVisible(false);
      setIsDirty(true); // Mark as dirty since we are changing view state
    }

    if (spreadId !== currentSpreadId) {
      // It's good practice to save state before changing the current spread as well,
      // if not already handled by the cover visibility change.
      if (!isCoverVisibleRef.current) { // Only save if not already saved by cover logic
        saveStateForUndo();
        setIsDirty(true);
      }
      setCurrentSpreadId(spreadId);
      setPreviewTemplateId(null);
    } else {
      // If clicking the already current spread AND the cover was visible,
      // we still want to hide the cover. This is handled by the first if block.
    }
  }, [currentSpreadId, setCurrentSpreadId, setPreviewTemplateId, setIsCoverVisible, saveStateForUndo, setIsDirty]);


  const handleDeleteSpread = useCallback((spreadId: string) => {
    if (spreads.length <= 1) { toast.error("Cannot delete the only spread"); return; }
    const spreadIndex = spreads.findIndex(s => s.id === spreadId);
    if (spreadIndex === -1) return;
    const newSpreads = spreads.filter(s => s.id !== spreadId);
    saveStateForUndo();
    setIsDirty(true);
    setSpreads(newSpreads);
    if (spreadId === currentSpreadId) {
      const newIndex = Math.max(0, spreadIndex - 1);
      setCurrentSpreadId(newSpreads[newIndex].id);
    }
    toast.success(`Spread deleted`);
  }, [spreads, saveStateForUndo, setIsDirty, setSpreads, currentSpreadId, setCurrentSpreadId]);

  const handleDeleteMultipleSpreads = useCallback((spreadIdsToDelete: string[]) => {
    if (!spreadIdsToDelete || spreadIdsToDelete.length === 0) return;
    // The decision to call this function (vs. handleDeleteAllSpreadsAndAddBlank)
    // is made by the calling confirmSpreadDeletion or SpreadsTray's confirmDelete.
    // So, this function should proceed assuming it's a valid partial delete.
    saveStateForUndo();
    setIsDirty(true);
    const firstDeletedIndex = spreads.findIndex(s => spreadIdsToDelete.includes(s.id));
    const newSpreads = spreads.filter(s => !spreadIdsToDelete.includes(s.id));
    let newCurrentSpreadId = currentSpreadId;
    if (spreadIdsToDelete.includes(currentSpreadId)) {
      const targetIndex = Math.max(0, firstDeletedIndex - 1);
      newCurrentSpreadId = newSpreads[targetIndex]?.id || newSpreads[0]?.id;
    }
    setSpreads(newSpreads);
    if (newCurrentSpreadId !== currentSpreadId) {
      setCurrentSpreadId(newCurrentSpreadId);
    }
    toast.success(`Deleted ${spreadIdsToDelete.length} spread(s)`);
  }, [spreads, currentSpreadId, saveStateForUndo, setIsDirty, setSpreads, setCurrentSpreadId]);

  const handleDeleteAllSpreadsAndAddBlank = useCallback(() => {
    saveStateForUndo();
    setIsDirty(true);
    const newSpreadId = `spread-${Date.now()}`;
    const newBlankSpread: Spread = {
      id: newSpreadId,
      templateId: '__blank__',
      images: [],
      // Apply default background if it exists
      spreadBackgroundData: defaultSpreadBackground ? {...defaultSpreadBackground} : null
    };
    setSpreads([newBlankSpread]);
    setCurrentSpreadId(newSpreadId);
    toast.success("All spreads deleted. New blank spread added.");
  }, [saveStateForUndo, setIsDirty, setSpreads, setCurrentSpreadId, defaultSpreadBackground]);

  const handleUpdateSpreadImages = useCallback((
    spreadId: string,
    placeholderId: string,
    newPlacementData: ImagePlacement | null | undefined
  ) => {

    saveStateForUndo();
    setIsDirty(true);
    setSpreads(prevSpreads => {

      const newSpreadsState = prevSpreads.map(spread => {
        if (spread.id !== spreadId) return spread;
        const placementIndex = spread.images.findIndex(p => p.placeholderId === placeholderId);
        if (placementIndex === -1) {
          return spread;
        }
        const updatedImages = [...spread.images];
        if (newPlacementData === null || newPlacementData === undefined) {
           updatedImages[placementIndex] = {
             ...updatedImages[placementIndex],
             imageId: undefined,
             transform: { scale: 1, focalX: 0.5, focalY: 0.5, rotation: 0 } // Reset transform
           };

         } else {
           updatedImages[placementIndex] = {
             ...newPlacementData,
             placeholderId: updatedImages[placementIndex].placeholderId // Ensure placeholderId is preserved
           };

        }
        return { ...spread, images: updatedImages };
      });
      const finalSpreadsState = newSpreadsState; // Assign to a new variable for logging


      return finalSpreadsState; // Return the logged state
    });
    // Clear selection in ImageTray after updating spread
    imageTrayRef.current?.clearSelection();
  }, [saveStateForUndo, setIsDirty, setSpreads]);

  const handleImageGapChange = useCallback((newValue: number) => {
    setImageGap(newValue);
    setIsDirty(true); // Mark project as dirty when gap changes
  }, [setImageGap, setIsDirty]); // Add setIsDirty to dependencies

  const handleBackgroundColorChange = useCallback((newColor: string) => {
    setBackgroundColor(newColor);
    saveStateForUndo(); // Save state for undo when background color changes
    setIsDirty(true);
  }, [setBackgroundColor, saveStateForUndo, setIsDirty]);

  const handleSetProjectBackground = useCallback(async (imageUrl: string | null, rawPathOrUrl: string | null) => {
    saveStateForUndo();
    setIsDirty(true);

    let actualFilePath: string | null = null;
    let displayUrl: string | null = null;

    if (rawPathOrUrl) {
      // Determine if rawPathOrUrl is a file path or a URL
      const isLikelyFileSystemPath = !rawPathOrUrl.startsWith('http:') && !rawPathOrUrl.startsWith('https:') && !rawPathOrUrl.startsWith('data:') && !rawPathOrUrl.startsWith('blob:');
      
      if (isLikelyFileSystemPath) {
        actualFilePath = rawPathOrUrl;
        displayUrl = normalizeBackgroundImageUrl(actualFilePath); // Normalize the file path for display
      } else {
        // It's a URL (http, data, blob)
        displayUrl = normalizeBackgroundImageUrl(rawPathOrUrl); // Normalize the URL itself for display
        actualFilePath = null; // No direct file system path
      }

      if (actualFilePath && actualFilePath !== projectBackgroundImagePathRef.current) {
        setBackgroundImageZoom(1);
        setBackgroundImagePanX(50);
        setBackgroundImagePanY(50);
      }
      
      setProjectBackgroundImage(displayUrl); // Set the URL for display
      setProjectBackgroundImagePath(actualFilePath); // Store the actual file path, or null if it was a URL

      // If we have an actual file path, try to get its dimensions and update the images state
      if (actualFilePath) {
        const existingImageEntry = imagesRef.current.find(img => img.originalPath === actualFilePath);
        if (!existingImageEntry || !existingImageEntry.naturalWidth || !existingImageEntry.naturalHeight) {
          if (window.electronAPI?.getExifData) {
            try {
              const exifResult = await window.electronAPI.getExifData(actualFilePath);
              if (exifResult.success && exifResult.exifData?.dimensions) {
                const { width: naturalWidth, height: naturalHeight } = exifResult.exifData.dimensions;
                setImages(prevImages => {
                  const imgIndex = prevImages.findIndex(img => img.originalPath === actualFilePath);
                  if (imgIndex > -1) {
                    const updatedImages = [...prevImages];
                    updatedImages[imgIndex] = { ...updatedImages[imgIndex], naturalWidth, naturalHeight };
                    return updatedImages;
                  } else {
                    const newImageFile: ImageFile = {
                      id: `gbl_bg_${Date.now()}`,
                      name: actualFilePath.split(/[\\/]/).pop() || 'global-background',
                      originalPath: actualFilePath,
                      thumbnailUrl: displayUrl || '', // Use displayUrl for thumbnail
                      previewUrl: displayUrl || '',   // Use displayUrl for preview
                      naturalWidth,
                      naturalHeight,
                      dateModified: Date.now(),
                    };
                    return [...prevImages, newImageFile];
                  }
                });
              } else {
              }
            } catch (error) {
            }
          } else {
          }
        }
      }
    } else { // Clearing the background
      setProjectBackgroundImage(null);
      setProjectBackgroundImagePath(null);
      setBackgroundImageZoom(1);
      setBackgroundImagePanX(50);
      setBackgroundImagePanY(50);
    }
  }, [
    saveStateForUndo, setIsDirty,
    setBackgroundImageZoom, setBackgroundImagePanX, setBackgroundImagePanY,
    setProjectBackgroundImage, setProjectBackgroundImagePath,
    setImages, imagesRef, projectBackgroundImagePathRef // Added imagesRef and projectBackgroundImagePathRef
  ]);

  // Handler for background image zoom changes
  const handleBackgroundImageZoomChange = useCallback((zoom: number) => {
    setBackgroundImageZoom(zoom);
    // If zoom is reset to 1 (or very close), also reset pan to center
    if (zoom <= 1.01) {
      setBackgroundImagePanX(50);
      setBackgroundImagePanY(50);
    }
    saveStateForUndo();
    setIsDirty(true);
  }, [setBackgroundImageZoom, setBackgroundImagePanX, setBackgroundImagePanY, saveStateForUndo, setIsDirty]);

  // Handler for background image pan X changes
  const handleBackgroundImagePanXChange = useCallback((panX: number) => {
    setBackgroundImagePanX(panX);
    saveStateForUndo();
    setIsDirty(true);
  }, [setBackgroundImagePanX, saveStateForUndo, setIsDirty]);

  // Handler for background image pan Y changes
  const handleBackgroundImagePanYChange = useCallback((panY: number) => {
    setBackgroundImagePanY(panY);
    saveStateForUndo();
    setIsDirty(true);
  }, [setBackgroundImagePanY, saveStateForUndo, setIsDirty]);

  const handleReorderSpreads = useCallback((reorderedSpreads: Spread[]) => {
    saveStateForUndo();
    setIsDirty(true);
    setSpreads(reorderedSpreads);
  }, [saveStateForUndo, setIsDirty, setSpreads]);

  const handleUpdateImageTransform = useCallback((
    spreadId: string,
    placeholderId: string,
    newTransform: ImageTransform
  ) => {
    // @ts-ignore
    const isResizeAdjustment = newTransform._isResizeAdjustment === true;
    // @ts-ignore
    const isTransformEnd = newTransform._isTransformEnd === true;
    // @ts-ignore
    const shouldSaveUndo = newTransform._shouldSaveUndo === true || isTransformEnd;

    if (!isResizeAdjustment) {

    }

    if (!isResizeAdjustment && shouldSaveUndo) {

      saveStateForUndo();
    }

    setSpreads(prevSpreads => {
      if (!isResizeAdjustment) {

      }
      const newSpreadsState = prevSpreads.map(spread => {
        if (spread.id !== spreadId) return spread;
        const updatedImages = spread.images.map(img => {
          if (img.placeholderId === placeholderId) {
            const cleanTransform: ImageTransform = {
              scale: newTransform.scale,
              focalX: newTransform.focalX ?? 0.5,
              focalY: newTransform.focalY ?? 0.5,
              fit: newTransform.fit ?? 'contain',
              rotation: newTransform.rotation ?? 0
            };
            // @ts-ignore
            delete cleanTransform._isResizeAdjustment;
            // @ts-ignore
            delete cleanTransform._isTransformEnd;
            // @ts-ignore
            delete cleanTransform._shouldSaveUndo;
            if (!isResizeAdjustment) {

            }
            return { ...img, transform: cleanTransform };
          }
          return img;
        });
        return { ...spread, images: updatedImages };
      });
      if (!isResizeAdjustment) {

      }
      return newSpreadsState;
    });

    if (!isResizeAdjustment) {
      setIsDirty(true);

    }
  }, [setSpreads, setIsDirty, saveStateForUndo]);
const handleUpdateSpreadBackground = useCallback((
    spreadId: string,
    updates: Partial<SpreadBackgroundData>
  ) => {
    saveStateForUndo();
    setIsDirty(true);
    setSpreads(prevSpreads =>
      prevSpreads.map(spread => {
        if (spread.id !== spreadId) return spread;

        const currentBgData = spread.spreadBackgroundData || {
          imagePath: null,
          transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'cover' },
        };

        // If updates include a transform, merge it with the existing transform
        const newTransform = updates.transform
          ? { ...currentBgData.transform, ...updates.transform }
          : currentBgData.transform;

        return {
          ...spread,
          spreadBackgroundData: {
            ...currentBgData, // Spread existing data first
            ...updates,       // Then apply updates
            transform: newTransform, // Ensure transform is correctly merged
          },
        };
      })
    );
    
    // If the background is being removed (imagePath set to null), exit background edit mode
    if (updates.imagePath === null && isEditingBackgroundForSpreadId === spreadId) {
      setIsEditingBackgroundForSpreadId(null);
    }
  }, [saveStateForUndo, setIsDirty, setSpreads, isEditingBackgroundForSpreadId, setIsEditingBackgroundForSpreadId]);

  // Handle custom template updates
  const handleUpdateCustomTemplate = useCallback((
    spreadId: string,
    customLayout: TemplateImage[]
  ) => {
    saveStateForUndo();
    setIsDirty(true);
    
    setCustomTemplates(prev => ({
      ...prev,
      [spreadId]: customLayout
    }));
  }, [saveStateForUndo, setIsDirty]);

  const handleSetSpreadBackgroundFromImageFile = useCallback((
    spreadId: string,
    imageFile: ImageFile
  ) => {
    saveStateForUndo();
    setIsDirty(true);

    setImages(prevImages => {
      if (!prevImages.find(img => img.id === imageFile.id)) {
        return [...prevImages, imageFile];
      }
      return prevImages;
    });

    setSpreads(prevSpreads =>
      prevSpreads.map(spread => {
        if (spread.id !== spreadId) return spread;
        return {
          ...spread,
          spreadBackgroundData: {
            imagePath: imageFile.originalPath, // Use originalPath for consistency
            originalPath: imageFile.originalPath, // Store original path for matching during regeneration
            transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'cover' }, // Default transform
            naturalWidth: imageFile.naturalWidth, // Add naturalWidth
            naturalHeight: imageFile.naturalHeight, // Add naturalHeight
          },
        };
      })
    );
    toast.success(`Background set for spread using ${imageFile.name}`);
  }, [saveStateForUndo, setIsDirty, setImages, setSpreads]);

  const toggleBackgroundEditMode = useCallback((spreadId: string) => {
    setIsEditingBackgroundForSpreadId(prevId => {
      if (prevId === spreadId) {
        toast.info("Exited background editing mode.");
        return null; // Toggle off if it's the same spread
      }
      toast.info("Background editing mode activated.");
      return spreadId; // Activate for the new spreadId
    });
  }, [setIsEditingBackgroundForSpreadId]);

  const handleApplyBackgroundToAll = useCallback((sourceSpreadId: string) => {
    const sourceSpread = spreads.find(s => s.id === sourceSpreadId);
    if (!sourceSpread || !sourceSpread.spreadBackgroundData) {
      toast.error("Source spread has no background to apply.");
      return;
    }
    saveStateForUndo();
    setIsDirty(true);

    // Store the background as the default for new spreads
    setDefaultSpreadBackground({...sourceSpread.spreadBackgroundData});

    // Apply to all existing spreads
    setSpreads(prevSpreads =>
      prevSpreads.map(spread => {
        if (spread.id === sourceSpreadId) return spread; // Don't apply to itself
        return {
          ...spread,
          spreadBackgroundData: { ...sourceSpread.spreadBackgroundData! } // Create a new object copy
        };
      })
    );
    toast.success("Background applied to all spreads and will be used for new spreads.");
    // Exit edit mode after applying to all
    if (isEditingBackgroundForSpreadId === sourceSpreadId) { // Only toggle if currently editing this spread
        toggleBackgroundEditMode(sourceSpreadId);
    }
  }, [spreads, saveStateForUndo, setIsDirty, setSpreads, toggleBackgroundEditMode, isEditingBackgroundForSpreadId]);

  const handleClearAllBackgrounds = useCallback(() => {
    // Check if any spread actually has a background
    const hasAnyBackground = spreads.some(s => s.spreadBackgroundData?.imagePath);
    if (!hasAnyBackground) {
      toast.info("No spread backgrounds to clear.");
      return;
    }

    saveStateForUndo();
    setIsDirty(true);
    setSpreads(prevSpreads =>
      prevSpreads.map(spread => {
        // Only create a new object if the background needs clearing
        if (spread.spreadBackgroundData?.imagePath) {
          return {
            ...spread,
            spreadBackgroundData: null // Clear the background data
          };
        }
        return spread; // Return existing spread object if no background
      })
    );
    // Also clear the default background for new spreads
    setDefaultSpreadBackground(null);

    toast.success("Cleared background from all spreads.");
    // Also turn off background editing mode if it was active
    setIsEditingBackgroundForSpreadId(null);
  }, [spreads, saveStateForUndo, setIsDirty, setSpreads, setIsEditingBackgroundForSpreadId, setDefaultSpreadBackground]);

  // Handler for hover changes from SpreadCanvas
  // Updated handler to use context setter directly
  const handleCanvasHoverChange = useCallback((hoverInfo: { placeholderId: string | null, isZoomed: boolean }) => {
    // Update local state if still needed elsewhere
    setHoveredCanvasInfo(hoverInfo);
    setHoveredCanvasPlaceholderId(hoverInfo.placeholderId);
    // Update the global context state
    setCanvasHoverInfo(hoverInfo);
  }, [setHoveredCanvasInfo, setHoveredCanvasPlaceholderId, setCanvasHoverInfo]); // Add context setter to dependencies

  // --- Utility function to get image dimensions ---
  const getImageDimensions = useCallback((imageId: string): { width: number; height: number } | undefined => {
    const img = images.find(i => i.id === imageId);
    if (img && img.naturalWidth && img.naturalHeight) {
      return { width: img.naturalWidth, height: img.naturalHeight };
    }
    return undefined;
  }, [images]); // Dependency on images state

  const handleRemoveImageAndSwitchTemplate = useCallback((
    spreadId: string,
    placeholderId: string
  ) => {
    saveStateForUndo();
    setIsDirty(true);
    setSpreads(prevSpreads => {
      const spreadIndex = prevSpreads.findIndex(s => s.id === spreadId);
      if (spreadIndex === -1) return prevSpreads;
      const originalSpread = prevSpreads[spreadIndex];
      const originalTemplate = templateMap[originalSpread.templateId];
      if (!originalTemplate) return prevSpreads;
      const placementsWithRemoved = originalSpread.images.map(p =>
        p.placeholderId === placeholderId
          // Reset transform with default focal points when removing image
          ? { ...p, imageId: undefined, transform: { scale: 1, focalX: 0.5, focalY: 0.5, rotation: 0 } }
          : p
      );
      const remainingImageCount = placementsWithRemoved.filter(p => p.imageId).length;
      let newTemplateId = originalSpread.templateId;
      let finalPlacements = placementsWithRemoved;
      if (autoSwitchTemplateOnRemove && remainingImageCount > 0) {
        const smallerTemplate = allTemplates.find(t => t.images.length === remainingImageCount);
        if (smallerTemplate && smallerTemplate.id !== originalTemplate.id) {
          newTemplateId = smallerTemplate.id;
          const remainingImages = placementsWithRemoved.filter(p => p.imageId);
          finalPlacements = smallerTemplate.images.map((newPh, index) => {
            if (index < remainingImages.length) {
              // Preserve existing transform when remapping
              return { ...remainingImages[index], placeholderId: newPh.id };
            } else {
              // Default transform for new empty placeholders
              return { placeholderId: newPh.id, imageId: undefined, transform: { scale: 1, focalX: 0.5, focalY: 0.5 } };
            }
          });
        }
      }
      const newSpreads = [...prevSpreads];
      newSpreads[spreadIndex] = { ...originalSpread, templateId: newTemplateId, images: finalPlacements };
      // Clear custom template for this spread if template switched
      if (newTemplateId !== originalSpread.templateId) {
        setCustomTemplates(prev => {
          if (prev[spreadId]) {
            const newCustomTemplates = { ...prev };
            delete newCustomTemplates[spreadId];
            return newCustomTemplates;
          }
          return prev;
        });
      }
      
      return newSpreads;
    });
  }, [saveStateForUndo, setIsDirty, setSpreads, templateMap, allTemplates, autoSwitchTemplateOnRemove, setCustomTemplates]);

  // Handle removing multiple images and switching template with proper image mapping
  const handleRemoveMultipleImagesAndSwitchTemplate = useCallback((
    spreadId: string,
    placeholderIdsToRemove: string[]
  ) => {
    saveStateForUndo();
    setIsDirty(true);
    setSpreads(prevSpreads => {
      const spreadIndex = prevSpreads.findIndex(s => s.id === spreadId);
      if (spreadIndex === -1) return prevSpreads;
      const originalSpread = prevSpreads[spreadIndex];
      const originalTemplate = templateMap[originalSpread.templateId];
      if (!originalTemplate) return prevSpreads;
      
      // First, identify which images should remain BEFORE any removal
      const imagesToRemove = new Set(placeholderIdsToRemove);
      const remainingImagePlacements = originalSpread.images.filter(p => 
        p.imageId && !imagesToRemove.has(p.placeholderId)
      );
      
      const targetImageCount = remainingImagePlacements.length;
      let newTemplateId = originalSpread.templateId;
      let finalPlacements = originalSpread.images;
      
      if (autoSwitchTemplateOnRemove && targetImageCount > 0) {
        const smallerTemplate = allTemplates.find(t => t.images.length === targetImageCount);
        if (smallerTemplate && smallerTemplate.id !== originalTemplate.id) {
          newTemplateId = smallerTemplate.id;
          // Map remaining images to new template in the order they appear in remainingImagePlacements
          finalPlacements = smallerTemplate.images.map((newPh, index) => {
            if (index < remainingImagePlacements.length) {
              // Preserve existing transform when remapping
              return { ...remainingImagePlacements[index], placeholderId: newPh.id };
            } else {
              // Default transform for new empty placeholders
              return { placeholderId: newPh.id, imageId: undefined, transform: { scale: 1, focalX: 0.5, focalY: 0.5 } };
            }
          });
        } else {
          // No template switch needed, just remove the specified images
          finalPlacements = originalSpread.images.map(p =>
            imagesToRemove.has(p.placeholderId)
              ? { ...p, imageId: undefined, transform: { scale: 1, focalX: 0.5, focalY: 0.5, rotation: 0 } }
              : p
          );
        }
      } else {
        // Auto-switch disabled or no remaining images, just remove the specified images
        finalPlacements = originalSpread.images.map(p =>
          imagesToRemove.has(p.placeholderId)
            ? { ...p, imageId: undefined, transform: { scale: 1, focalX: 0.5, focalY: 0.5, rotation: 0 } }
            : p
        );
      }
      
      // Show success toast at the end
      setTimeout(() => {
        if (autoSwitchTemplateOnRemove && targetImageCount > 0 && newTemplateId !== originalSpread.templateId) {
          toast.success(`${placeholderIdsToRemove.length} image${placeholderIdsToRemove.length > 1 ? 's' : ''} removed and template switched.`);
        } else {
          toast.success(`${placeholderIdsToRemove.length} image${placeholderIdsToRemove.length > 1 ? 's' : ''} removed.`);
        }
      }, 100);
      
      const newSpreads = [...prevSpreads];
      newSpreads[spreadIndex] = { ...originalSpread, templateId: newTemplateId, images: finalPlacements };
      // Clear custom template for this spread if template switched
      if (newTemplateId !== originalSpread.templateId) {
        setCustomTemplates(prev => {
          if (prev[spreadId]) {
            const newCustomTemplates = { ...prev };
            delete newCustomTemplates[spreadId];
            return newCustomTemplates;
          }
          return prev;
        });
      }
      
      return newSpreads;
    });
  }, [saveStateForUndo, setIsDirty, setSpreads, templateMap, allTemplates, autoSwitchTemplateOnRemove, setCustomTemplates]);

  // Handle removing multiple images from source spread (for canvas drag operations)
  const handleRemoveImagesFromSource = useCallback(async (
    sourceSpreadId: string,
    placeholderIds: string[]
  ) => {
    if (!placeholderIds.length) return;
    
    saveStateForUndo();
    setIsDirty(true);
    
    // Capture original template ID before state update
    const currentSpread = spreads.find(s => s.id === sourceSpreadId);
    const originalTemplateId = currentSpread?.templateId;
    const hasCustomTemplate = customTemplates && customTemplates[sourceSpreadId];
    
    // Use refs to capture values from inside setSpreads callback
    const templateSwitchedRef = { current: false };
    const newTemplateIdRef = { current: originalTemplateId };
    
    setSpreads(prevSpreads => {
      const spreadIndex = prevSpreads.findIndex(s => s.id === sourceSpreadId);
      if (spreadIndex === -1) return prevSpreads;
      
      const originalSpread = prevSpreads[spreadIndex];
      const originalTemplate = templateMap[originalSpread.templateId];
      if (!originalTemplate) return prevSpreads;
      
      // Remove images from specified placeholders
      const placementsWithRemoved = originalSpread.images.map(p =>
        placeholderIds.includes(p.placeholderId)
          ? { ...p, imageId: undefined, transform: { scale: 1, focalX: 0.5, focalY: 0.5, rotation: 0 } }
          : p
      );
      
      const remainingImageCount = placementsWithRemoved.filter(p => p.imageId).length;
      
      // If Auto-Switch Template on Remove is enabled and no images remain, remove the spread entirely
      if (autoSwitchTemplateOnRemove && remainingImageCount === 0) {
        const newSpreads = [...prevSpreads];
        newSpreads.splice(spreadIndex, 1); // Remove the empty spread
        templateSwitchedRef.current = true; // Spread removed, so clear custom template
        return newSpreads;
      }
      
      newTemplateIdRef.current = originalSpread.templateId;
      let finalPlacements = placementsWithRemoved;
      
      // Apply auto-switch template logic if enabled and images remain
      if (autoSwitchTemplateOnRemove && remainingImageCount > 0) {
        const smallerTemplate = allTemplates.find(t => t.images.length === remainingImageCount);
        // Check if we need to switch: either custom template exists or different template needed
        const shouldSwitch = smallerTemplate && (
          hasCustomTemplate || // Always switch if currently using custom template
          smallerTemplate.id !== originalTemplate.id // Or if new template differs from base template
        );
        if (shouldSwitch) {
          newTemplateIdRef.current = smallerTemplate.id;
          templateSwitchedRef.current = true; // Mark that template changed
          const remainingImages = placementsWithRemoved.filter(p => p.imageId);
          finalPlacements = smallerTemplate.images.map((newPh, index) => {
            if (index < remainingImages.length) {
              return { ...remainingImages[index], placeholderId: newPh.id };
            } else {
              return { placeholderId: newPh.id, imageId: undefined, transform: { scale: 1, focalX: 0.5, focalY: 0.5 } };
            }
          });
        }
      }
      
      const newSpreads = [...prevSpreads];
      newSpreads[spreadIndex] = { ...originalSpread, templateId: newTemplateIdRef.current, images: finalPlacements };
      return newSpreads;
    });
    
    // Clear custom template if template switched or spread was removed
    // Note: This must be done outside setSpreads to avoid state update conflicts
    setTimeout(() => {
      if (templateSwitchedRef.current || (newTemplateIdRef.current && originalTemplateId && newTemplateIdRef.current !== originalTemplateId)) {
        setCustomTemplates(prev => {
          if (prev[sourceSpreadId]) {
            const newCustomTemplates = { ...prev };
            delete newCustomTemplates[sourceSpreadId];
            return newCustomTemplates;
          }
          return prev;
        });
      }
    }, 0);
    
    // Clear image selection after successful move
    if (clearImageSelectionRef.current) {
      clearImageSelectionRef.current();
    }
  }, [saveStateForUndo, setIsDirty, setSpreads, templateMap, allTemplates, autoSwitchTemplateOnRemove, setCustomTemplates, spreads, customTemplates]);

  // Store clear image selection function from SpreadCanvas
  const clearImageSelectionRef = useRef<(() => void) | null>(null);
  const handleClearImageSelection = useCallback((clearFunction: () => void) => {
    clearImageSelectionRef.current = clearFunction;
  }, []);

  // Modified to accept initialFit mode
  const handleAddSpreadFromImages = useCallback((
    droppedImages: ImageFile[],
    targetIndex: number,
    initialFit: 'cover' | 'contain', // Added initialFit parameter
    sourceSpreadId?: string,
    sourcePlaceholderId?: string
  ) => {
    if (!droppedImages || droppedImages.length === 0) {
      console.warn("handleAddSpreadFromImages called with no images.");
      return;
    }
    const imageCount = droppedImages.length;
    const suitableTemplate = allTemplates.find(t => t.images.length === imageCount);

    if (!suitableTemplate) {
      // Define buildInstructions here, so it's in scope for the button text
      // Create image metadata for better template selection
      const imageMetadata: ImageMetadata[] = droppedImages.map(img => ({
        id: img.id,
        width: img.naturalWidth || 0,
        height: img.naturalHeight || 0,
        lastModified: img.dateModified,
        isPortrait: (img.naturalHeight || 0) > (img.naturalWidth || 0)
      }));
      const buildInstructions = autoBuildSpreadInstructions(imageCount, imageMetadata);
      setNoTemplateDialogData({ imageCount, droppedImages, targetIndex, initialFit, buildInstructions });
      setIsNoTemplateDialogVisible(true);
      console.warn(`No direct template found for ${imageCount} images. Opening auto-build dialog.`);
      return;
    }
    // If suitableTemplate IS found, proceed as before:
    const newSpreadId = `spread-${Date.now()}`;
    const newPlacements: ImagePlacement[] = suitableTemplate.images.map((placeholder, index) => {
      const imageId = index < droppedImages.length ? droppedImages[index].id : undefined;
      return {
        placeholderId: placeholder.id,
        imageId: imageId,
        // Use the passed initialFit mode here
        transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: initialFit }
      };
    });
    const newSpread: Spread = {
      id: newSpreadId,
      templateId: suitableTemplate.id,
      images: newPlacements,
      // Apply default background if it exists
      spreadBackgroundData: defaultSpreadBackground ? {...defaultSpreadBackground} : null
    };
    saveStateForUndo();
    setIsDirty(true);
    setSpreads(prevSpreads => {
      const updatedSpreads = [...prevSpreads];
      updatedSpreads.splice(targetIndex, 0, newSpread);
      return updatedSpreads;
    });
    setCurrentSpreadId(newSpreadId);
    toast.success(`Created new spread with ${imageCount} image(s) using template '${suitableTemplate.name}'.`);

    // --- Trigger On-Demand Preview Generation for newly placed images (now always onPlacement) ---
    newPlacements.forEach(placement => {
      if (placement.imageId) {
        const image = images.find(img => img.id === placement.imageId);
        if (image && !image.previewUrl && !requestedPreviewPathsRef.current.has(image.originalPath)) {
          let pathForPreview = image.originalPath;
          if (window.bookProofsApp?.getUpdatedFilePath) {
            const updatedPath = window.bookProofsApp.getUpdatedFilePath(image.originalPath);
            if (updatedPath) pathForPreview = updatedPath;
          }

          requestedPreviewPathsRef.current.add(image.originalPath);
          window.electronAPI.regenerateThumbnails(pathForPreview)
            .then(result => {
              if (!result) requestedPreviewPathsRef.current.delete(image.originalPath);
            })
            .catch(error => {
              toast.error(`Failed to generate preview for ${image.name}.`);
              requestedPreviewPathsRef.current.delete(image.originalPath);
            });
        }
      }
    });
    // --- End On-Demand Preview ---

    // Clear selection in ImageTray after adding spread
    imageTrayRef.current?.clearSelection();

    // If the image came from SpreadCanvas, remove it from its original placeholder
    // NOTE: Source cleanup is now handled by onRemoveImagesFromSource in SpreadsTray
    // to support both single and multi-image operations consistently
    if (false && sourceSpreadId && sourcePlaceholderId) {
      // Use a timeout to ensure the add operation completes visually first
      setTimeout(() => {
        handleRemoveImageAndSwitchTemplate(sourceSpreadId, sourcePlaceholderId);
        toast.info("Image moved from canvas to new spread.");
      }, 50); // Small delay
    }
  }, [allTemplates, saveStateForUndo, setIsDirty, setSpreads, setCurrentSpreadId, images, handleRemoveImageAndSwitchTemplate]);

  const handleAutoBuildFromCanvasDrop = useCallback((
    droppedImages: ImageFile[],
    anchorSpreadId: string, // The spread ID from which the drop originated, to determine insertion point
    // currentTemplateCapacity: number, // Not directly used by this simplified autobuild
    // availableSlots: number // Not directly used by this simplified autobuild
    initialFit: 'cover' | 'contain' = defaultDropModeIsCover ? 'cover' : 'contain' // Use user setting
  ) => {
    if (!droppedImages || droppedImages.length === 0) {
      toast.error("Auto-build called with no images.");
      return;
    }

    const imageCount = droppedImages.length;
    // Create image metadata for better template selection
    const imageMetadata: ImageMetadata[] = droppedImages.map(img => ({
      id: img.id,
      width: img.naturalWidth || 0,
      height: img.naturalHeight || 0,
      lastModified: img.dateModified,
      isPortrait: (img.naturalHeight || 0) > (img.naturalWidth || 0)
    }));
    const buildInstructions = autoBuildSpreadInstructions(imageCount, imageMetadata);

    if (buildInstructions.length === 0) {
      toast.error("Auto-build failed: No instructions generated.");
      return;
    }

    saveStateForUndo();
    setIsDirty(true);

    const newAutoSpreads: Spread[] = [];
    let imagesConsumedCount = 0;
    let firstNewSpreadId = '';

    const anchorIndex = spreads.findIndex(s => s.id === anchorSpreadId);
    const targetIndex = anchorIndex !== -1 ? anchorIndex + 1 : spreads.length; // Insert after anchor, or at the end

    buildInstructions.forEach((instruction, index) => {
      const autoSpreadId = `spread-auto-${Date.now()}-${index}`;
      if (index === 0) firstNewSpreadId = autoSpreadId;
      const templateForSpread = instruction.template;
      // Use the imageCount from the instruction instead of the template's image count
      // This allows for better distribution of images across spreads
      const imagesForThisSpread = droppedImages.slice(imagesConsumedCount, imagesConsumedCount + instruction.imageCount);

      const placements: ImagePlacement[] = templateForSpread.images.map((ph, pIdx) => ({
        placeholderId: ph.id,
        imageId: imagesForThisSpread[pIdx]?.id,
        transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: initialFit }
      }));

      newAutoSpreads.push({
        id: autoSpreadId,
        templateId: templateForSpread.id,
        images: placements,
        // Apply default background if it exists
        spreadBackgroundData: defaultSpreadBackground ? {...defaultSpreadBackground} : null
      });
      imagesConsumedCount += imagesForThisSpread.length;
    });

    setSpreads(prevSpreads => {
      // Check if this is the start of a project (first autobuilding operation)
      // and if the first spread is blank or has no images
      const isFirstSpreadBlankOrEmpty = prevSpreads.length === 1 &&
        (prevSpreads[0].templateId === '__blank__' ||
         !prevSpreads[0].images.some(img => img.imageId));

      // If the first spread is blank or has no images, remove it
      const updatedSpreads = [...prevSpreads];
      if (isFirstSpreadBlankOrEmpty) {
        updatedSpreads.splice(0, 1); // Remove the first spread
        // Adjust the target index since we removed the first spread
        const adjustedTargetIndex = Math.max(0, targetIndex - 1);
        updatedSpreads.splice(adjustedTargetIndex, 0, ...newAutoSpreads);
      } else {
        updatedSpreads.splice(targetIndex, 0, ...newAutoSpreads);
      }
      return updatedSpreads;
    });

    if (firstNewSpreadId) {
      setCurrentSpreadId(firstNewSpreadId);
    }

    toast.success(`Auto-built ${newAutoSpreads.length} new spread(s) for ${imageCount} images.`);

    // Trigger preview generation for newly placed images
    newAutoSpreads.forEach(spread => {
      spread.images.forEach(placement => {
        if (placement.imageId) {
          const image = imagesRef.current.find(img => img.id === placement.imageId);
          if (image && !image.previewUrl && !requestedPreviewPathsRef.current.has(image.originalPath)) {
            let pathForPreview = image.originalPath;
            if (window.bookProofsApp?.getUpdatedFilePath) {
              const updatedPath = window.bookProofsApp.getUpdatedFilePath(image.originalPath);
              if (updatedPath) pathForPreview = updatedPath;
            }
            requestedPreviewPathsRef.current.add(image.originalPath);
            // Track preview request to show notification
            handleTrackPreviewRequest(image.originalPath);
            window.electronAPI.regenerateThumbnails(pathForPreview)
              .then(result => { if (!result) requestedPreviewPathsRef.current.delete(image.originalPath); })
              .catch(error => {
                console.error(`[BookEditor AutoBuildCanvasDrop] Error requesting preview for ${pathForPreview}:`, error);
                toast.error(`Failed to generate preview for ${image.name}.`);
                requestedPreviewPathsRef.current.delete(image.originalPath);
              });
          }
        }
      });
    });

    imageTrayRef.current?.clearSelection();

  }, [saveStateForUndo, setIsDirty, setSpreads, setCurrentSpreadId, spreads, imagesRef, defaultDropModeIsCover, defaultSpreadBackground, handleTrackPreviewRequest]); // Added handleTrackPreviewRequest dependency

  // Handler for requesting the "Offer Autobuild" dialog (e.g., from SpreadsTray drop)
  const handleOfferAutobuildRequest = useCallback((data: { droppedImages: ImageFile[]; currentSpreadId: string; existingImageCount: number; maxTemplateSize: number; }) => {
    setOfferAutobuildDialogData(data);
    setIsOfferAutobuildDialogVisible(true);
  }, [setOfferAutobuildDialogData, setIsOfferAutobuildDialogVisible]);

  // Handler for autobuild from ImageTray when multiple images are selected
  // Handler for autobuild from ImageTray when multiple images are selected in the context menu
  // Process images in batches to prevent crashes with large numbers of images
  const handleAutoBuildInBatches = useCallback(async (
    allImages: ImageFile[],
    anchorSpreadId: string,
    initialFit: 'cover' | 'contain' = defaultDropModeIsCover ? 'cover' : 'contain',
    batchSize: number = 50 // Keep batch size at 50 even though threshold is 100
  ) => {
    if (!allImages || allImages.length === 0) {
      toast.error("Auto-build called with no images.");
      return;
    }

    const totalImages = allImages.length;
    const totalBatches = Math.ceil(totalImages / batchSize);
    
    // Show initial toast with progress information
    toast.info(`Starting auto-build with ${totalImages} images in ${totalBatches} batches...`);
    
    // Process each batch sequentially
    let processedImages = 0;
    let currentAnchorId = anchorSpreadId;
    let allCreatedSpreads: Spread[] = [];
    
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const startIdx = batchIndex * batchSize;
      const endIdx = Math.min(startIdx + batchSize, totalImages);
      const currentBatch = allImages.slice(startIdx, endIdx);
      const currentBatchSize = currentBatch.length;
      
      // Show progress toast
      toast.info(`Processing batch ${batchIndex + 1} of ${totalBatches} (${processedImages} of ${totalImages} images processed)`);
      
      // Process this batch
      await new Promise<void>(resolve => {
        // Use setTimeout to allow UI to update between batches
        setTimeout(() => {
          try {
            // Get the current number of spreads before adding new ones
            const spreadCountBefore = spreads.length;
            
            // Call the original handler with the current batch
            handleAutoBuildFromCanvasDrop(currentBatch, currentAnchorId, initialFit);
            
            // Update the anchor ID to be the last spread created
            if (spreads.length > 0) {
              currentAnchorId = spreads[spreads.length - 1].id;
              
              // Collect the newly created spreads for later preview generation
              const newSpreads = spreads.slice(spreadCountBefore);
              allCreatedSpreads = [...allCreatedSpreads, ...newSpreads];
            }
            
            processedImages += currentBatchSize;
            resolve();
          } catch (error) {
            console.error(`Error processing batch ${batchIndex + 1}:`, error);
            toast.error(`Error in batch ${batchIndex + 1}. Some images may not have been processed.`);
            resolve(); // Continue with next batch even if there's an error
          }
        }, 100); // Small delay to prevent UI freezing
      });
    }
    
    // Generate previews for all images in all created spreads
    toast.info(`Generating previews for all placed images...`);
    
    // Ensure we generate previews for all images across all batches
    setTimeout(() => {
      try {
        // Trigger preview generation for all newly placed images
        allCreatedSpreads.forEach(spread => {
          spread.images.forEach(placement => {
            if (placement.imageId) {
              const image = imagesRef.current.find(img => img.id === placement.imageId);
              if (image && !image.previewUrl && !requestedPreviewPathsRef.current.has(image.originalPath)) {
                let pathForPreview = image.originalPath;
                if (window.bookProofsApp?.getUpdatedFilePath) {
                  const updatedPath = window.bookProofsApp.getUpdatedFilePath(image.originalPath);
                  if (updatedPath) pathForPreview = updatedPath;
                }
                requestedPreviewPathsRef.current.add(image.originalPath);
                // Track preview request to show notification
                handleTrackPreviewRequest(image.originalPath);
                window.electronAPI.regenerateThumbnails(pathForPreview)
                  .then(result => { if (!result) requestedPreviewPathsRef.current.delete(image.originalPath); })
                  .catch(error => {
                    console.error(`[BookEditor BatchAutoBuild] Error requesting preview for ${pathForPreview}:`, error);
                    requestedPreviewPathsRef.current.delete(image.originalPath);
                  });
              }
            }
          });
        });
        
        toast.success(`Completed auto-build with ${totalImages} images across ${totalBatches} batches.`);
      } catch (error) {
        console.error(`Error generating previews:`, error);
        toast.error(`Error generating some previews. The book layout was created successfully.`);
      }
    }, 500); // Give a bit more time after all batches are processed before generating previews
  }, [handleAutoBuildFromCanvasDrop, spreads, imagesRef, defaultDropModeIsCover, handleTrackPreviewRequest]);

  const handleAutoBuildFromImageTray = useCallback((selectedImages: ImageFile[], initialFit?: 'cover' | 'contain') => {
    if (!selectedImages || selectedImages.length === 0) {
      toast.error("Auto-build called with no images.");
      return;
    }

    // Check for duplicate images
    const duplicateImages = selectedImages.filter(img => usedImageIds.includes(img.id));
    
    if (duplicateImages.length > 0) {
      // Show duplicate warning dialog
      setAutobuildDuplicateWarningData({
        selectedImages,
        duplicateImages,
        initialFit: initialFit || (defaultDropModeIsCover ? 'cover' : 'contain')
      });
      setShowAutobuildDuplicateWarning(true);
      return; // Exit early, dialog will handle the logic
    }

    // No duplicates, proceed with autobuild
    performAutobuild(selectedImages, initialFit || (defaultDropModeIsCover ? 'cover' : 'contain'));
  }, [usedImageIds, defaultDropModeIsCover]);

  // Extracted autobuild logic for reuse
  const performAutobuild = useCallback((selectedImages: ImageFile[], initialFit: 'cover' | 'contain') => {
    // Use current spread as anchor point for insertion
    const anchorSpreadId = currentSpreadId || (spreads.length > 0 ? spreads[spreads.length - 1].id : '');
    
    // Check if we need to use batch processing (more than 100 images)
    if (selectedImages.length > 100) {
      // Use the batch processing function for large image sets
      handleAutoBuildInBatches(
        selectedImages,
        anchorSpreadId,
        initialFit
      );
      toast.info(`Processing ${selectedImages.length} images in batches to prevent memory issues...`);
    } else {
      // Use the original handler for smaller image sets
      handleAutoBuildFromCanvasDrop(
        selectedImages,
        anchorSpreadId,
        initialFit
      );
      toast.success(`Creating auto-built spreads with ${selectedImages.length} selected images.`);
    }
  }, [handleAutoBuildFromCanvasDrop, handleAutoBuildInBatches, currentSpreadId, spreads]);

  // Dialog handlers for autobuild duplicate warning
  const handleAutobuildDuplicateConfirm = useCallback(() => {
    if (autobuildDuplicateWarningData) {
      performAutobuild(autobuildDuplicateWarningData.selectedImages, autobuildDuplicateWarningData.initialFit);
    }
    setShowAutobuildDuplicateWarning(false);
    setAutobuildDuplicateWarningData(null);
  }, [autobuildDuplicateWarningData, performAutobuild]);

  const handleAutobuildDuplicateCancel = useCallback(() => {
    setShowAutobuildDuplicateWarning(false);
    setAutobuildDuplicateWarningData(null);
    toast.info("Autobuild cancelled.");
  }, []);

  const handleSpreadThumbnailHover = useCallback((isHovering: boolean) => {
    setIsSpreadThumbnailHovered(isHovering);
  }, [setIsSpreadThumbnailHovered]);

  const handleUpdateThumbnailSnapshot = useCallback((spreadId: string, dataUrl: string) => {
    setSpreads(prevSpreads =>
      prevSpreads.map(spread =>
        spread.id === spreadId ? { ...spread, thumbnailDataUrl: dataUrl } : spread
      )
    );
  }, [setSpreads]); // Dependency on the state setter

  const handleFilesDroppedOnTrayPlaceholder = useCallback((newlyProcessedImages: ImageFile[]) => {
    if (newlyProcessedImages && newlyProcessedImages.length > 0) {
      saveStateForUndo(); // Save state before adding new images
      setImages(prevImages => {
        // Prevent duplicates by checking originalPath if that's a reliable unique identifier from the main process
        // Or, if the main process already ensures uniqueness or provides truly unique IDs, this check can be simpler.
        const existingPaths = new Set(prevImages.map(img => img.originalPath));
        const uniqueNewImages = newlyProcessedImages.filter(newImg => !existingPaths.has(newImg.originalPath));

        if (uniqueNewImages.length === 0 && newlyProcessedImages.length > 0) {
          toast.info("Some or all dropped images are already in the library.");
        }
        if (uniqueNewImages.length > 0) {
          setIsDirty(true);
        }
        return [...prevImages, ...uniqueNewImages];
      });
      // Optionally, trigger a check for file availability for these new images
      // setTimeout(() => checkFilesAvailability(true), 500);
    }
  }, [setImages, setIsDirty, saveStateForUndo]); // Added saveStateForUndo

  // --- Utility Function ---
  const findExactTemplateForImageCount = useCallback((imageCount: number) => {
    if (!allTemplates || allTemplates.length === 0 || imageCount < 0) return null;
    return allTemplates.find(t => t.images.length === imageCount) || null;
  }, [allTemplates]);

  // --- Callbacks for Child Focus/Selection ---
  const handleCanvasFocusChange = useCallback((focused: boolean) => setIsCanvasFocused(focused), []);
  const handleImageTrayFocusChange = useCallback((focused: boolean) => setIsImageTrayFocused(focused), []);
  const handleSpreadsTrayFocusChange = useCallback((focused: boolean) => setIsSpreadsTrayFocused(focused), []);
  const handleImageSelectionChange = useCallback((ids: string[]) => {
    setSelectedImageIdsState(ids);
    setImageSelectionCount(ids.length);
  }, []);
  const handleMultiSelectStateChange = useCallback((isActive: boolean) => setIsMultiSelectActive(isActive), []);
  const handleImageTrayPositionChange = useCallback((newPosition: 'left' | 'bottom') => {
    setImageTrayPosition(newPosition);
    
    if (newPosition === 'bottom') {
      // When moving to bottom, use smaller SpreadsTray height
      setSpreadsTrayHeight(130);
    } else {
      // When moving to left, use larger SpreadsTray height
      setSpreadsTrayHeight(175);
    }
  }, [setImageTrayPosition]);
  const handleResetTrays = useCallback(() => {
    // Reset ImageTray to left position and default width/height
    setImageTrayPosition('left');
    setImageTrayWidth(256);
    setImageTrayHeight(266); // Reset bottom position height to default
    setIsImageTrayCollapsed(false);
    
    // Reset ImageTray thumbnail size to default
    imageTrayRef.current?.resetThumbnailSize();
    
    // Reset SpreadsTray to default height (175px since we're resetting to left position)
    setSpreadsTrayHeight(175);
    setIsSpreadsTrayCollapsed(false);
    
    // Reset Templates/Settings tray to default width
    setTemplatesTrayWidth(320);
    setIsTemplatesTrayCollapsed(false);
  }, [imageTrayWidth, imageTrayHeight, isImageTrayCollapsed, spreadsTrayHeight, isSpreadsTrayCollapsed, templatesTrayWidth, isTemplatesTrayCollapsed]);
  const handleCanvasSelectionChange = useCallback((count: number) => setSelectedCanvasImagesCount(count), []);
  const handleSpreadSelectionChange = useCallback((ids: string[]) => setSelectedSpreadIdsState(ids), []);
  const handleAddImageToExistingSpread = useCallback(async (
    targetSpreadId: string,
    imageToAdd: ImageFile,
    initialFit: 'cover' | 'contain',
    sourceSpreadId?: string,
    sourcePlaceholderId?: string
  ) => {
    saveStateForUndo();
    setIsDirty(true);

    // Clear custom template for target spread if it exists
    // This allows the system to properly select a new template that accommodates all images
    setCustomTemplates(prev => {
      if (prev[targetSpreadId]) {
        const newCustomTemplates = { ...prev };
        delete newCustomTemplates[targetSpreadId];
        return newCustomTemplates;
      }
      return prev;
    });

    setSpreads(prevSpreads => {
      const spreadIndex = prevSpreads.findIndex(s => s.id === targetSpreadId);
      if (spreadIndex === -1) {
        toast.error("Target spread not found.");
        return prevSpreads;
      }

      const targetSpread = prevSpreads[spreadIndex];
      const currentTemplate = templateMap[targetSpread.templateId];
      if (!currentTemplate) {
        toast.error("Current template definition not found.");
        return prevSpreads;
      }

      const existingPlacements = targetSpread.images.filter(p => p.imageId);
      const emptyPlaceholdersInCurrentTemplate = currentTemplate.images.filter(
        ph => !targetSpread.images.some(p => p.placeholderId === ph.id && p.imageId)
      );

      if (emptyPlaceholdersInCurrentTemplate.length > 0) {
        // Place in the first empty slot of the current template
        const newPlacements = [...targetSpread.images];
        const targetPlaceholderCurrentTemplate = emptyPlaceholdersInCurrentTemplate[0];
        const placementIndex = newPlacements.findIndex(p => p.placeholderId === targetPlaceholderCurrentTemplate.id);

        if (placementIndex !== -1) {
          newPlacements[placementIndex] = {
            placeholderId: targetPlaceholderCurrentTemplate.id,
            imageId: imageToAdd.id,
            transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: initialFit }
          };
          const updatedSpread = { ...targetSpread, images: newPlacements };
          const newSpreads = [...prevSpreads];
          newSpreads[spreadIndex] = updatedSpread;
          toast.success(`Image "${imageToAdd.name}" added to spread.`);
          return newSpreads;
        } else {
          // Should not happen if emptyPlaceholdersInCurrentTemplate found one
          toast.error("Error finding placeholder to update in current template.");
          return prevSpreads;
        }
      } else {
        // No empty slots, try to find a new template
        const requiredImageCount = existingPlacements.length + 1;
        const newSuitableTemplate = allTemplates.find(t => t.images.length === requiredImageCount);

        if (newSuitableTemplate) {
          const newPlacements: ImagePlacement[] = newSuitableTemplate.images.map((newPlaceholder, index) => {
            if (index < existingPlacements.length) {
              // Re-map existing images
              const existingPlacement = existingPlacements[index];
              return {
                placeholderId: newPlaceholder.id,
                imageId: existingPlacement.imageId,
                transform: existingPlacement.transform // Preserve existing transform
              };
            } else if (index === existingPlacements.length) {
              // Place the new image
              return {
                placeholderId: newPlaceholder.id,
                imageId: imageToAdd.id,
                transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: initialFit }
              };
            } else {
              // Empty slot in the new template
              return {
                placeholderId: newPlaceholder.id,
                imageId: undefined,
                transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: defaultDropModeIsCover ? 'cover' : 'contain' }
              };
            }
          });
          const updatedSpread = {
            ...targetSpread,
            templateId: newSuitableTemplate.id,
            images: newPlacements
          };
          const newSpreads = [...prevSpreads];
          newSpreads[spreadIndex] = updatedSpread;
          toast.success(`Switched to template "${newSuitableTemplate.name}" and added image "${imageToAdd.name}".`);
          return newSpreads;
        } else {
          // Instead of showing an error, we'll return the unchanged spreads
          // and trigger the autobuild dialog outside of this state update
          // This is necessary because we can't trigger state updates during a state update
          setTimeout(() => {
            // Find the maximum template size available from all templates
            const maxTemplateSize = allTemplates.reduce((max, template) =>
              Math.max(max, template.images.length), 0);

            // Offer autobuild with the dropped image
            setOfferAutobuildDialogData({
              droppedImages: [imageToAdd],
              currentSpreadId: targetSpreadId,
              existingImageCount: existingPlacements.length,
              maxTemplateSize
            });
            setIsOfferAutobuildDialogVisible(true);
          }, 0);

          // Log the issue for debugging

          return prevSpreads;
        }
      }
    });
    imageTrayRef.current?.clearSelection();

    // --- Trigger On-Demand Preview Generation for the added image ---
    if (imageToAdd && !imageToAdd.previewUrl && !requestedPreviewPathsRef.current.has(imageToAdd.originalPath)) {
      let pathForPreview = imageToAdd.originalPath;
      if (window.bookProofsApp?.getUpdatedFilePath) {
        const updatedPath = window.bookProofsApp.getUpdatedFilePath(imageToAdd.originalPath);
        if (updatedPath) pathForPreview = updatedPath;
      }

      requestedPreviewPathsRef.current.add(imageToAdd.originalPath);
      window.electronAPI.regenerateThumbnails(pathForPreview)
        .then(result => {
          if (!result) requestedPreviewPathsRef.current.delete(imageToAdd.originalPath);
        })
        .catch(error => {
          console.error(`[BookEditor AddToExisting] Error requesting preview for ${pathForPreview}:`, error);
          toast.error(`Failed to generate preview for ${imageToAdd.name}.`);
          requestedPreviewPathsRef.current.delete(imageToAdd.originalPath);
        });
    }
    // --- End On-Demand Preview ---

    // NOTE: Source cleanup is now handled by onRemoveImagesFromSource in SpreadsTray
    // to support both single and multi-image operations consistently
    if (false && sourceSpreadId && sourcePlaceholderId) {
      // Disabled - now handled by onRemoveImagesFromSource
      setTimeout(() => {
        handleRemoveImageAndSwitchTemplate(sourceSpreadId, sourcePlaceholderId);
        toast.info(`Image moved from canvas to spread.`);
      }, 50); // Small delay
    }
  }, [saveStateForUndo, setIsDirty, setSpreads, templateMap, allTemplates, defaultDropModeIsCover, handleRemoveImageAndSwitchTemplate, setCustomTemplates]);


  // --- Request Handlers for Deletion Confirmation ---
  const handleRemoveImagesRequest = useCallback((idsToRemove: string[]) => {
    if (!idsToRemove || idsToRemove.length === 0) return;
    setImageIdsToDelete(idsToRemove);
    setIsImageDeleteAlertOpen(true);
  }, []);

  const confirmImageDeletion = useCallback(() => {
    if (imageIdsToDelete.length === 0) return;
    saveStateForUndo();
    setIsDirty(true);

    // Get originalPaths of images being deleted, before modifying the 'images' state
    const imagesBeingDeleted = images.filter(img => imageIdsToDelete.includes(img.id));
    const originalPathsToDelete = imagesBeingDeleted.map(img => img.originalPath);

    // Remove images from the main images state
    setImages(prevImages => prevImages.filter(img => !imageIdsToDelete.includes(img.id)));

    // Remove images from all spreads (placements and backgrounds)
    setSpreads(prevSpreads =>
      prevSpreads.map(spread => {
        let newSpreadBackgroundData = spread.spreadBackgroundData;
        if (spread.spreadBackgroundData?.imagePath && originalPathsToDelete.includes(spread.spreadBackgroundData.imagePath)) {
          newSpreadBackgroundData = null;
        }
        return {
          ...spread,
          images: spread.images.map(placement =>
            imageIdsToDelete.includes(placement.imageId ?? '')
              // Reset transform with default focal points when deleting image
              ? { ...placement, imageId: undefined, transform: { scale: 1, focalX: 0.5, focalY: 0.5 } }
              : placement
          ),
          spreadBackgroundData: newSpreadBackgroundData,
        };
      })
    );

    // Clear project-wide background if it uses one of the deleted images
    if (projectBackgroundImagePath && originalPathsToDelete.includes(projectBackgroundImagePath)) {
      setProjectBackgroundImage(null);
      setProjectBackgroundImagePath(null);
    }

    // Clear default background for new spreads if it uses one of the deleted images
    if (defaultSpreadBackground?.imagePath && originalPathsToDelete.includes(defaultSpreadBackground.imagePath)) {
      setDefaultSpreadBackground(null);
    }

    toast.success(`Removed ${imageIdsToDelete.length} image(s) from the project.`);
    setIsImageDeleteAlertOpen(false);
    setImageIdsToDelete([]);
  }, [
    imageIdsToDelete,
    saveStateForUndo,
    setIsDirty,
    setImages,
    setSpreads,
    images, // Added: to get originalPaths
    projectBackgroundImagePath, // Added: to check project background
    setProjectBackgroundImage, // Added: to clear project background
    setProjectBackgroundImagePath, // Added: to clear project background path
    defaultSpreadBackground, // Added: to check default background
    setDefaultSpreadBackground, // Added: to clear default background
  ]);

  const handleDeleteSpreadsRequest = useCallback((idsToConfirm: string[]) => {
    if (!idsToConfirm || idsToConfirm.length === 0) return;
    // The check for "cannot delete all spreads" will now be handled by confirmSpreadDeletion
    // or by the logic within handleDeleteAllSpreadsAndAddBlank if it's invoked.
    setSpreadIdsToDeleteState(idsToConfirm);
    setIsSpreadDeleteAlertOpen(true);
  }, []); // Removed spreads.length dependency as the check is moved

  const confirmSpreadDeletion = useCallback(() => {
    if (spreadIdsToDeleteState.length === spreads.length) {
      // If all spreads are selected for deletion, call the specific handler
      handleDeleteAllSpreadsAndAddBlank();
    } else if (spreadIdsToDeleteState.length > 0) {
      // Otherwise, handle normal multiple spread deletion
      handleDeleteMultipleSpreads(spreadIdsToDeleteState);
    }
    setIsSpreadDeleteAlertOpen(false);
    setSpreadIdsToDeleteState([]);
  }, [spreads.length, spreadIdsToDeleteState, handleDeleteMultipleSpreads, handleDeleteAllSpreadsAndAddBlank]);

  // --- Other Callbacks ---
  const activateTemplateFilter = useCallback((count: number, selectFirst: boolean = false, currentTemplateId?: string | null, arrowKey?: 'ArrowUp' | 'ArrowDown') => {
    
    if (selectedTab !== 'templates') setSelectedTab('templates');
    setFilterToTrigger(count);
    
    // Check if templates tray has saved user focus - if so, don't select first
    const hasSavedFocus = templatesTrayRef.current?.hasSavedUserFocus() ?? false;
    const shouldSelectFirst = selectFirst && !hasSavedFocus;
    
    
    if (shouldSelectFirst) {
      setTriggerSelectFirst(true);
      setTemplateIdToSkip(currentTemplateId ?? null);
    } else {
      setTemplateIdToSkip(null);
    }
    setTimeout(() => {
      // Pass the arrow key to focusTray when we have saved focus
      templatesTrayRef.current?.focusTray(hasSavedFocus ? arrowKey : undefined);
      if (shouldSelectFirst) {
        setTimeout(() => {
          templatesTrayRef.current?.selectFirstVisibleTemplate(templateIdToSkip);
          setTriggerSelectFirst(false); // Reset trigger
          setTemplateIdToSkip(null); // Reset skip ID
        }, 50); // Short delay to allow filtering
      }
    }, 0); // Delay focus slightly
    
  }, [selectedTab, templateIdToSkip]); // Added templateIdToSkip dependency

  const handleExitTemplateSelection = useCallback(() => {
    spreadCanvasRef.current?.focusCanvas();
    setFilterToTrigger(null); // Clear filter trigger
    setActiveTemplateIdForTray(null); // Clear active template
  }, []);

  const handleTemplatesTrayFocus = useCallback(() => {
    const currentSpread = spreads.find(s => s.id === currentSpreadId);
    setIsTemplatesTrayFocused(true);
    // When templates tray gains focus, update the active template ID based on the current spread
    setActiveTemplateIdForTray(currentSpread?.templateId ?? null);
  }, [currentSpreadId, spreads]);

  const handleTemplatesTrayBlur = useCallback(() => {
    setIsTemplatesTrayFocused(false);
    // Optionally clear preview when tray loses focus? Or keep it?
    // setPreviewTemplateId(null);
  }, []);

  const handleEscapeFromTemplates = useCallback(() => {
    spreadCanvasRef.current?.focusCanvas(); // Focus canvas on escape
  }, []);

  // --- PDF Export Logic ---

  const hexToRgbCanvas = (hex: string): string | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? `rgb(${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)})` : null;
  };

  interface Rect { x: number; y: number; width: number; height: number; }
  const intersectRects = (r1: Rect, r2: Rect): Rect | null => {
    const x = Math.max(r1.x, r2.x);
    const y = Math.max(r1.y, r2.y);
    const width = Math.min(r1.x + r1.width, r2.x + r2.width) - x;
    const height = Math.min(r1.y + r1.height, r2.y + r2.height) - y;
    if (width <= 0 || height <= 0) return null;
    return { x, y, width, height };
  };

  const executeJpegExport = useCallback(async (
    format: string, // This will be 'jpeg'
    options: {
      coverExportOption: CoverExportOption;
      pdfLayout: PdfLayoutOption;
      coverLayout: CoverLayoutOption;
      colorSpace: ColorSpaceOption;
      exportEmptySpreadsAsIs: boolean;
    }
  ) => {
    if (exportCancelledRef.current) {
      console.log('JPEG export cancelled before starting.');
      return;
    }
    console.log('JPEG export triggered:', { format, ...options });

    // Use hasCoverRef and coverImageRef for more reliable check if cover can be exported
    const canExportCoverActually = hasCoverRef.current && coverImageRef.current;
    let coverExportedSuccessfully = false;
    let coverExportSkippedOrFailed = false;

    let targetSpreadsToProcess: Spread[] = pdfGenerationArgsRef.current.spreadsForExport.filter(s => {
      if (options.exportEmptySpreadsAsIs) return true;
      return !(s.templateId === '__blank__' || !s.images.some(img => img.imageId));
    });

    if (targetSpreadsToProcess.length === 0) {
      toast.error('No spreads available to export as JPEGs.', { id: 'jpeg-export-progress' });
      return;
    }

    if (!window.electronAPI?.showOpenDialog) {
      toast.error("Folder selection is not available. Cannot export JPEGs.", { id: 'jpeg-export-progress' });
      return;
    }
    const dialogResult = await window.electronAPI.showOpenDialog({
      title: "Select Folder to Save JPEG Pages",
      properties: ["openDirectory", "createDirectory"],
      buttonLabel: "Select Folder",
    });

    if (dialogResult.canceled || !dialogResult.filePaths || dialogResult.filePaths.length === 0) {
      toast.info("JPEG export cancelled: No folder selected.", { id: 'jpeg-export-progress' });
      return;
    }
    if (exportCancelledRef.current) { toast.info("JPEG export cancelled after folder selection."); return; }
    const exportDirectoryPath = dialogResult.filePaths[0];

    const existingFilesToCheck: Array<{ name: string, type: 'cover' | 'spread' }> = [];

    // Check for existing cover file
    if (canExportCoverActually && options.coverExportOption === 'include' && window.electronAPI?.checkFileExists) {
      const coverFilename = "Cover.jpg"; // Or based on coverLayout if needed, e.g., Cover-Double.jpg
      if (await window.electronAPI.checkFileExists(`${exportDirectoryPath}/${coverFilename}`)) {
        existingFilesToCheck.push({ name: coverFilename, type: 'cover' });
      }
    }

    // Check for existing spread files
    if (window.electronAPI?.checkFileExists) {
      for (const spreadToCheck of targetSpreadsToProcess) {
        const originalIndex = spreads.findIndex(s => s.id === spreadToCheck.id);
        if (options.pdfLayout === 'pages') {
          const filenameL = `spread-${String(originalIndex + 1).padStart(3, '0')}-L.jpg`;
          const filenameR = `spread-${String(originalIndex + 1).padStart(3, '0')}-R.jpg`;
          if (await window.electronAPI.checkFileExists(`${exportDirectoryPath}/${filenameL}`)) existingFilesToCheck.push({ name: filenameL, type: 'spread' });
          if (await window.electronAPI.checkFileExists(`${exportDirectoryPath}/${filenameR}`)) existingFilesToCheck.push({ name: filenameR, type: 'spread' });
        } else { // 'spreads' layout
          const filename = `spread-${String(originalIndex + 1).padStart(3, '0')}.jpg`;
          if (await window.electronAPI.checkFileExists(`${exportDirectoryPath}/${filename}`)) existingFilesToCheck.push({ name: filename, type: 'spread' });
        }
      }
    }

    if (existingFilesToCheck.length > 0) {
      if (!window.electronAPI?.confirmDialog) {
        toast.error("Confirmation dialog API is not available. Cannot check for overwrites.", { id: 'jpeg-export-progress' });
        return;
      }
      const overwriteConfirmation = await window.electronAPI.confirmDialog({
        title: 'Overwrite Existing Files?',
        message: `The following files already exist in the selected folder:\n\n${existingFilesToCheck.map(f => f.name).join('\n')}\n\nDo you want to overwrite them?`,
        buttons: ['Cancel', 'Overwrite'], defaultId: 1, cancelId: 0, type: 'warning',
      });
      if (overwriteConfirmation === 0) {
        toast.info("JPEG export cancelled by user (overwrite).", { id: 'jpeg-export-progress' });
        return;
      }
    }
    if (exportCancelledRef.current) { toast.info("JPEG export cancelled after overwrite confirmation."); return; }

    if (options.pdfLayout === 'pages') {
      toast.info(`Exporting ${targetSpreadsToProcess.length} spread(s) as individual pages to folder...`, { id: 'jpeg-export-progress', duration: 30000 * targetSpreadsToProcess.length * 2 });
    } else {
      toast.info(`Exporting ${targetSpreadsToProcess.length} spread(s) to folder...`, { id: 'jpeg-export-progress', duration: 30000 * targetSpreadsToProcess.length });
    }

    let pageWidthPt: number;
    let pageHeightPt: number;

    if (aspectRatio?.widthPt && aspectRatio?.heightPt && aspectRatio.widthPt > 0 && aspectRatio.heightPt > 0) {
      pageWidthPt = aspectRatio.widthPt;
      pageHeightPt = aspectRatio.heightPt;
    } else if (aspectRatio?.dimensions) {
      try {
        const cleanedDimensions = aspectRatio.dimensions.replace(/"/g, '').replace(/×/g, 'x').replace(/\s+/g, '');
        const dims = cleanedDimensions.toLowerCase().split('x');
        if (dims.length === 2) {
          const widthInches = parseFloat(dims[0]); const heightInches = parseFloat(dims[1]);
          if (!isNaN(widthInches) && !isNaN(heightInches) && widthInches > 0 && heightInches > 0) {
            pageWidthPt = widthInches * 72; pageHeightPt = heightInches * 72;
          } else { throw new Error('Invalid number format in dimensions string.'); }
        } else { throw new Error('Invalid dimensions string format.'); }
      } catch (error) {
        toast.error('Error parsing project dimensions. Cannot generate JPEG.', { id: 'jpeg-export-progress' });
        return;
      }
    } else {
      toast.error('Project dimensions not available. Cannot generate JPEG.', { id: 'jpeg-export-progress' });
      return;
    }

    const dpi = 300;
    const spreadWidthPt = pageWidthPt * 2; // Full spread width in points
    const singlePageCanvasWidthPx = Math.round((pageWidthPt / 72) * dpi);
    const singlePageCanvasHeightPx = Math.round((pageHeightPt / 72) * dpi);
    const fullSpreadCanvasWidthPx = Math.round((spreadWidthPt / 72) * dpi);
    const fullSpreadCanvasHeightPx = Math.round((pageHeightPt / 72) * dpi);

    const offscreenCanvas = document.createElement('canvas');
    const ctx = offscreenCanvas.getContext('2d');

    if (!ctx) {
      toast.error('Failed to create offscreen canvas context.', { id: 'jpeg-export-progress' });
      return;
    }

    // hexToRgbCanvas is already defined globally in the component scope

    let exportSuccessCount = 0;
    let exportErrorCount = 0;

    // --- Cover Export for JPEG ---
    if (canExportCoverActually && options.coverExportOption === 'include') {
      if (exportCancelledRef.current) { toast.info("JPEG Cover export cancelled."); return; }
      const capturedCoverState = pdfGenerationArgsRef.current.capturedCoverState;
      if (capturedCoverState && capturedCoverState.coverImage) {
        toast.info(`Processing cover...`, { id: 'jpeg-export-progress', duration: 30000 });
        const coverImageFile = capturedCoverState.coverImage;
        const coverFilename = "Cover.jpg"; // Simple filename for the cover

        const isDoubleLayout = options.coverLayout === 'double';
        const coverCanvasWidthPt = isDoubleLayout ? pageWidthPt * 2 : pageWidthPt;
        const coverCanvasHeightPt = pageHeightPt;

        const coverCanvasRenderWidthPx = Math.round((coverCanvasWidthPt / 72) * dpi);
        const coverCanvasRenderHeightPx = Math.round((coverCanvasHeightPt / 72) * dpi);

        offscreenCanvas.width = coverCanvasRenderWidthPx;
        offscreenCanvas.height = coverCanvasRenderHeightPx;
        ctx.clearRect(0, 0, coverCanvasRenderWidthPx, coverCanvasRenderHeightPx);

        // 1. Cover Background (White)
        ctx.fillStyle = 'rgb(255,255,255)';
        ctx.fillRect(0, 0, coverCanvasRenderWidthPx, coverCanvasRenderHeightPx);

        // 2. Cover Image
        try {
          let coverImagePath = getOriginalPathFromUrl(coverImageFile.originalPath);
          if (window.bookProofsApp?.getUpdatedFilePath) {
            const updatedPath = window.bookProofsApp.getUpdatedFilePath(coverImagePath);
            if (updatedPath) coverImagePath = updatedPath;
          }

          // Determine target dimensions for loadImageDataForCanvas based on coverLayout
          // The capturedCoverState transforms (scale, translateX/Y) are relative to the ProjectCover component's view,
          // which is a single page width for the image content itself.
          const imageContentTargetWidthPt = pageWidthPt; // Cover image content is always based on single page width
          const imageContentTargetHeightPt = pageHeightPt;

          // --- MIRROR PDF LOGIC FOR JPEG COVER TRANSFORM ---
          const imgNativeWidthPx_jpeg_cover = coverImageFile.naturalWidth || 1000;
          const imgNativeHeightPx_jpeg_cover = coverImageFile.naturalHeight || 1000;
          const currentCoverScale_jpeg_cover = capturedCoverState.scale;
          const imageRatioNative_jpeg_cover = imgNativeWidthPx_jpeg_cover / imgNativeHeightPx_jpeg_cover;
          const containerRatioPt_jpeg_cover = imageContentTargetWidthPt / imageContentTargetHeightPt;

          let baseRenderedW_pt_canvas_jpeg_cover: number, baseRenderedH_pt_canvas_jpeg_cover: number;

          // 'contain' fit logic for the canvas rendering container
          if (imageRatioNative_jpeg_cover > containerRatioPt_jpeg_cover) {
              baseRenderedW_pt_canvas_jpeg_cover = imageContentTargetWidthPt;
              baseRenderedH_pt_canvas_jpeg_cover = imageContentTargetWidthPt / imageRatioNative_jpeg_cover;
          } else {
              baseRenderedH_pt_canvas_jpeg_cover = imageContentTargetHeightPt;
              baseRenderedW_pt_canvas_jpeg_cover = imageContentTargetHeightPt * imageRatioNative_jpeg_cover;
          }

          const userScale_jpeg_cover = capturedCoverState.scale;
          const userFocalX_jpeg_cover = capturedCoverState.focalX || 0.5;
          const userFocalY_jpeg_cover = capturedCoverState.focalY || 0.5;

          const finalScaledWidthForFocalCalc_jpeg_cover = baseRenderedW_pt_canvas_jpeg_cover * userScale_jpeg_cover;
          const finalScaledHeightForFocalCalc_jpeg_cover = baseRenderedH_pt_canvas_jpeg_cover * userScale_jpeg_cover;

          const offsetX_for_api_jpeg_cover = (0.5 - userFocalX_jpeg_cover) * finalScaledWidthForFocalCalc_jpeg_cover;
          const offsetY_for_api_jpeg_cover = (0.5 - userFocalY_jpeg_cover) * finalScaledHeightForFocalCalc_jpeg_cover;

          const focalCoverTransformForCanvas: { scale: number; offsetX: number; offsetY: number; fit: "cover" | "contain" } = {
              scale: userScale_jpeg_cover,
              offsetX: offsetX_for_api_jpeg_cover,
              offsetY: offsetY_for_api_jpeg_cover,
              fit: 'contain', // Consistent with PDF logic
          };
          // --- END MIRRORED LOGIC ---

          const coverImageData = await window.electronAPI.loadImageDataForCanvas(coverImagePath, {
            targetWidthPt: imageContentTargetWidthPt,
            targetHeightPt: imageContentTargetHeightPt,
            transform: focalCoverTransformForCanvas, // Use the new consistent transform
            colorSpace: options.colorSpace,
          });

          if (coverImageData.success && coverImageData.imageBuffer && coverImageData.width && coverImageData.height) {
            const coverBitmap = await createImageBitmap(new Blob([coverImageData.imageBuffer], { type: coverImageData.imageType === 'png' ? 'image/png' : 'image/jpeg' }));
            
            // Determine where to draw the (potentially single-page sized) bitmap on the (single or double page) canvas
            let drawX = 0;
            let drawW = coverCanvasRenderWidthPx; // Default to full canvas width

            if (isDoubleLayout) {
              // For double layout, the image content (single page) is drawn on the right half
              drawX = coverCanvasRenderWidthPx / 2;
              drawW = coverCanvasRenderWidthPx / 2; // Image content width
            } else {
              // For single layout, image content takes full canvas width
              drawW = coverCanvasRenderWidthPx;
            }
            // The bitmap from loadImageDataForCanvas is already transformed.
            // We draw it into the target area (right half for double, full for single).
            ctx.drawImage(coverBitmap, drawX, 0, drawW, coverCanvasRenderHeightPx);
          } else {
            throw new Error(coverImageData.error || "Failed to load cover image data for JPEG.");
          }
        } catch (e) {
          console.error(`Error rendering cover image for JPEG:`, e);
          toast.error(`Error rendering cover image: ${e.message}`, { id: 'jpeg-export-progress' });
          coverExportSkippedOrFailed = true;
        }

        // 3. Cover Text Overlays
        if (!coverExportSkippedOrFailed) {
          const coverTextOverlays = textOverlays.filter(overlay => overlay.spreadId === 'cover');
          for (const overlay of coverTextOverlays) {
            try {
              const style = overlay.style;
              const fontSizePx = (style.fontSize / 100) * coverCanvasRenderHeightPx; // Relative to cover canvas height
              ctx.font = `${style.bold ? 'bold ' : ''}${style.italic ? 'italic ' : ''}${fontSizePx}px ${style.fontFamily}`;
              ctx.fillStyle = style.color;
              ctx.globalAlpha = style.opacity;
              ctx.textAlign = style.textAlign as CanvasTextAlign;

              // Text block width on the full double-page editor spread
              const textBlockWidthOnEditorSpreadPx = (style.width / 100) * (pageWidthPt * 2 / 72 * dpi);
              // Text block center X on the full double-page editor spread
              const textBlockCenterXOnEditorSpreadPx = (style.x / 100) * (pageWidthPt * 2 / 72 * dpi);

              let textDrawXOnCanvasPx;

              if (isDoubleLayout) {
                // Map editor's X (relative to double spread) to the double layout canvas
                if (style.textAlign === 'center') { textDrawXOnCanvasPx = textBlockCenterXOnEditorSpreadPx; }
                else if (style.textAlign === 'right') { textDrawXOnCanvasPx = textBlockCenterXOnEditorSpreadPx + textBlockWidthOnEditorSpreadPx / 2; }
                else { textDrawXOnCanvasPx = textBlockCenterXOnEditorSpreadPx - textBlockWidthOnEditorSpreadPx / 2; }
              } else { // Single Layout
                // Map editor's X (relative to double spread) to the single layout canvas
                // Editor X=50% (center of double) -> Canvas X=0 (left of single)
                // Editor X=75% (center of right page) -> Canvas X=50% (center of single)
                const editorXRelativeToSinglePage = (style.x - 50); // -50 (left edge of right page) to +50 (right edge of right page)
                const mappedEditorXPercent = editorXRelativeToSinglePage / 50; // -1 to +1 relative to single page center
                
                const textBlockCenterOnSingleCanvasPx = (mappedEditorXPercent * 0.5 + 0.5) * coverCanvasRenderWidthPx;

                if (style.textAlign === 'center') { textDrawXOnCanvasPx = textBlockCenterOnSingleCanvasPx; }
                else if (style.textAlign === 'right') { textDrawXOnCanvasPx = textBlockCenterOnSingleCanvasPx + (textBlockWidthOnEditorSpreadPx / 2) * (coverCanvasRenderWidthPx / (pageWidthPt*2/72*dpi)) ; } // Scale width
                else { textDrawXOnCanvasPx = textBlockCenterOnSingleCanvasPx - (textBlockWidthOnEditorSpreadPx / 2) * (coverCanvasRenderWidthPx / (pageWidthPt*2/72*dpi)); } // Scale width
              }
              
              const textDrawYOnCanvasPx = (style.y / 100) * coverCanvasRenderHeightPx + (fontSizePx * 0.3); // Baseline adjustment

              const words = overlay.content.split(' ');
              let line = ''; const lines = []; const lineHeight = fontSizePx * 1.2;
              // Use textBlockWidthOnEditorSpreadPx for measuring, as style.width is relative to that
              for (let n = 0; n < words.length; n++) {
                const testLine = line + words[n] + ' '; const metrics = ctx.measureText(testLine); const testWidth = metrics.width;
                if (testWidth > textBlockWidthOnEditorSpreadPx && n > 0) { lines.push(line); line = words[n] + ' '; } else { line = testLine; }
              }
              lines.push(line);
              let currentY = textDrawYOnCanvasPx - ((lines.length -1) * lineHeight / 2);
              for (const l of lines) { ctx.fillText(l.trim(), textDrawXOnCanvasPx, currentY); currentY += lineHeight; }
            } catch (e) { console.error(`Error rendering text overlay ${overlay.id} for JPEG cover:`, e); }
          }
          ctx.globalAlpha = 1.0;
        }

        // 4. Export Cover JPEG
        if (!coverExportSkippedOrFailed) {
          try {
            const pixelData = ctx.getImageData(0, 0, coverCanvasRenderWidthPx, coverCanvasRenderHeightPx);
            const targetColorSpaceForApi: ColorSpaceOption = options.colorSpace === 'passthrough' ? 'srgb' : options.colorSpace;
            if (!window.electronAPI?.convertAndGenerateJpeg) throw new Error("convertAndGenerateJpeg API not found for cover");

            const jpegResult = await window.electronAPI.convertAndGenerateJpeg(
              { data: pixelData.data, width: pixelData.width, height: pixelData.height },
              { quality: 92, colorSpace: targetColorSpaceForApi }
            );
            if (!jpegResult.success || !jpegResult.dataUrl) throw new Error(jpegResult.error || 'Failed to generate JPEG cover via Electron API.');

            const filePath = `${exportDirectoryPath}/${coverFilename}`;
            const success = await window.electronAPI.saveFile(filePath, jpegResult.dataUrl);
            if (success) {
              exportSuccessCount++;
              coverExportedSuccessfully = true;
            } else {
              exportErrorCount++;
              coverExportSkippedOrFailed = true;
            }
          } catch (e) {
            console.error(`Error generating or saving JPEG cover:`, e);
            exportErrorCount++;
            coverExportSkippedOrFailed = true;
          }
        }
      } else {
        // No captured cover state or image
        coverExportSkippedOrFailed = true;
      }
    } else {
      // Cover export not requested or not possible
      coverExportSkippedOrFailed = true;
    }
    // --- End Cover Export for JPEG ---


    for (let i = 0; i < targetSpreadsToProcess.length; i++) {
      if (exportCancelledRef.current) { toast.info(`JPEG export cancelled before processing spread ${i + 1}.`); return; }
      const spreadToRender = targetSpreadsToProcess[i];
      const spreadIndexForFilename = spreads.findIndex(s => s.id === spreadToRender.id);

      toast.info(`Processing spread ${i + 1} of ${targetSpreadsToProcess.length}...`, { id: 'jpeg-export-progress', duration: 30000 });

      if (options.pdfLayout === 'pages') {
        for (const pageSide of ['L', 'R'] as const) {
          if (exportCancelledRef.current) { toast.info(`JPEG export cancelled before processing page ${pageSide} of spread ${i + 1}.`); return; }
          offscreenCanvas.width = singlePageCanvasWidthPx;
          offscreenCanvas.height = singlePageCanvasHeightPx;
          ctx.clearRect(0, 0, singlePageCanvasWidthPx, singlePageCanvasHeightPx); // Clear for each page

          // 1. Render Background Color
          ctx.fillStyle = hexToRgbCanvas(backgroundColorRef.current);
          ctx.fillRect(0, 0, singlePageCanvasWidthPx, singlePageCanvasHeightPx);

          // Common rendering function for backgrounds (project and spread-specific)
          const renderBackgroundImageToPage = async (
            bgPath: string | null,
            bgTransform: { scale: number; focalX: number; focalY: number; fit?: 'cover' | 'contain' },
            bgOpacity: number,
            isProjectBg: boolean
          ) => {
            if (!bgPath) return;
            try {
              let actualBgPath = bgPath;
              if (window.bookProofsApp?.getUpdatedFilePath) {
                const updatedPath = window.bookProofsApp.getUpdatedFilePath(actualBgPath);
                if (updatedPath) actualBgPath = updatedPath;
              }

              let imgNaturalWidth: number | undefined, imgNaturalHeight: number | undefined;
              const imageFileEntry = images.find(img => img.originalPath === actualBgPath);
              if (imageFileEntry?.naturalWidth && imageFileEntry?.naturalHeight) {
                imgNaturalWidth = imageFileEntry.naturalWidth; imgNaturalHeight = imageFileEntry.naturalHeight;
              } else if (window.electronAPI?.getExifData) {
                const exif = await window.electronAPI.getExifData(actualBgPath);
                if (exif.success && exif.exifData?.dimensions) {
                  imgNaturalWidth = exif.exifData.dimensions.width; imgNaturalHeight = exif.exifData.dimensions.height;
                }
              }
              if (!imgNaturalWidth || !imgNaturalHeight) { // Fallback
                imgNaturalWidth = spreadWidthPt; imgNaturalHeight = pageHeightPt;
              }

              const spreadRatio = spreadWidthPt / pageHeightPt;
              const imgRatio = imgNaturalWidth / imgNaturalHeight;
              let interimW, interimH;
              if (imgRatio > spreadRatio) { interimH = pageHeightPt; interimW = interimH * imgRatio; }
              else { interimW = spreadWidthPt; interimH = interimW / imgRatio; }

              const scaledW = interimW * bgTransform.scale;
              const scaledH = interimH * bgTransform.scale;
              const offsetX = (0.5 - bgTransform.focalX) * scaledW;
              const offsetY = (0.5 - bgTransform.focalY) * scaledH;

              const bgData = await window.electronAPI.loadImageDataForCanvas(actualBgPath, {
                targetWidthPt: spreadWidthPt, targetHeightPt: pageHeightPt, // Load for full spread
                transform: { scale: bgTransform.scale, offsetX, offsetY, fit: 'cover' },
                colorSpace: options.colorSpace,
              });

              if (bgData.success && bgData.imageBuffer) {
                const image = await createImageBitmap(new Blob([bgData.imageBuffer], { type: bgData.imageType === 'png' ? 'image/png' : 'image/jpeg' }));
                ctx.save();
                ctx.rect(0, 0, singlePageCanvasWidthPx, singlePageCanvasHeightPx); ctx.clip();
                ctx.globalAlpha = bgOpacity;
                // Draw the correct half of the full-spread image
                const drawX = pageSide === 'L' ? 0 : -singlePageCanvasWidthPx;
                ctx.drawImage(image, drawX, 0, fullSpreadCanvasWidthPx, fullSpreadCanvasHeightPx);
                ctx.globalAlpha = 1.0;
                ctx.restore();
              }
            } catch (e) { console.error(`Error rendering ${isProjectBg ? 'project' : 'spread'} background for JPEG page:`, e); }
          };

          // 2. Render Project Background Image (clipped for page)
          await renderBackgroundImageToPage(
            projectBackgroundImagePathRef.current,
            { scale: backgroundImageZoomRef.current, focalX: backgroundImagePanXRef.current / 100, focalY: backgroundImagePanYRef.current / 100 },
            projectBackgroundImageOpacityRef.current,
            true
          );

          // 3. Render Spread-Specific Background Image (clipped for page)
          if (spreadToRender.spreadBackgroundData?.imagePath) {
            await renderBackgroundImageToPage(
              spreadToRender.spreadBackgroundData.imagePath,
              spreadToRender.spreadBackgroundData.transform || { scale: 1, focalX: 0.5, focalY: 0.5 },
              spreadToRender.spreadBackgroundData.opacity ?? 1.0,
              false
            );
          }

          // 4. Render Image Placeholders (clipped for page)
          const template = templateMap[spreadToRender.templateId];
          if (template && template.id !== '__blank__') {
            const spreadAdjustedLayout = adjustTemplateForGap(template, imageGap, spreadWidthPt, pageHeightPt);
            for (const placement of spreadToRender.images) {
              if (!placement.imageId) continue;
              const imageFile = images.find(img => img.id === placement.imageId);
              if (!imageFile || !imageFile.naturalWidth || !imageFile.naturalHeight) continue;
              const adjustedPlaceholder = spreadAdjustedLayout.find(p => p.id === placement.placeholderId);
              if (!adjustedPlaceholder) continue;

              try {
                const { scale: userScale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain', rotation = 0 } = placement.transform;
                const phWidthPt = adjustedPlaceholder.adjustedWidth_pt;
                const phHeightPt = adjustedPlaceholder.adjustedHeight_pt;

                // Calculate interim and scaled dimensions for offset calculation
                const imgNaturalWidth_placeholder = imageFile.naturalWidth || 1;
                const imgNaturalHeight_placeholder = imageFile.naturalHeight || 1;
                const naturalImgRatio_placeholder = imgNaturalWidth_placeholder / imgNaturalHeight_placeholder;
                const placeholderRatio_placeholder = phWidthPt / phHeightPt;
                let interimW_placeholder, interimH_placeholder;
                if (fit === 'cover') {
                  if (naturalImgRatio_placeholder > placeholderRatio_placeholder) { interimH_placeholder = phHeightPt; interimW_placeholder = interimH_placeholder * naturalImgRatio_placeholder; }
                  else { interimW_placeholder = phWidthPt; interimH_placeholder = interimW_placeholder / naturalImgRatio_placeholder; }
                } else { // 'contain'
                  if (naturalImgRatio_placeholder > placeholderRatio_placeholder) { interimW_placeholder = phWidthPt; interimH_placeholder = interimW_placeholder / naturalImgRatio_placeholder; }
                  else { interimH_placeholder = phHeightPt; interimW_placeholder = interimH_placeholder * naturalImgRatio_placeholder; }
                }
                const scaledW_placeholder = interimW_placeholder * userScale;
                const scaledH_placeholder = interimH_placeholder * userScale;
                const offsetX_placeholder = (0.5 - focalX) * scaledW_placeholder;
                const offsetY_placeholder = (0.5 - focalY) * scaledH_placeholder;
                
                const imgData = await window.electronAPI.loadImageDataForCanvas(imageFile.originalPath, {
                  targetWidthPt: phWidthPt, targetHeightPt: phHeightPt,
                  transform: { scale: userScale, offsetX: offsetX_placeholder, offsetY: offsetY_placeholder, fit: fit as 'cover' | 'contain', rotation: rotation },
                  colorSpace: options.colorSpace,
                });

                if (imgData.success && imgData.imageBuffer && imgData.width && imgData.height) {
                  const sourceImageBitmap = await createImageBitmap(new Blob([imgData.imageBuffer], { type: imgData.imageType === 'png' ? 'image/png' : 'image/jpeg' }));
                  
                  const placeholderXonSpreadPx = (adjustedPlaceholder.adjustedX_pt / 72) * dpi;
                  const placeholderYonSpreadPx = (adjustedPlaceholder.adjustedY_pt / 72) * dpi;
                  const placeholderWidthPx = (phWidthPt / 72) * dpi;
                  const placeholderHeightPx = (phHeightPt / 72) * dpi;

                  ctx.save();
                  ctx.rect(0, 0, singlePageCanvasWidthPx, singlePageCanvasHeightPx); ctx.clip();
                  
                  let drawX = placeholderXonSpreadPx;
                  if (pageSide === 'R') {
                    drawX -= singlePageCanvasWidthPx; // Adjust X for right page
                  }
                  // The sourceImageBitmap is already transformed by loadImageDataForCanvas.
                  // For 'contain', we need to calculate the final drawing dimensions and position
                  // to ensure it's centered and not cropped within the placeholder.

                  // Create a clipping path for the placeholder before drawing the image
                  ctx.beginPath();
                  ctx.rect(drawX, placeholderYonSpreadPx, placeholderWidthPx, placeholderHeightPx);
                  ctx.clip(); // Apply the placeholder clip

                  const sourceImgWidthPx = sourceImageBitmap.width; // Actual width of the bitmap
                  const sourceImgHeightPx = sourceImageBitmap.height; // Actual height of the bitmap

                  let actualRenderedImageWidthPx = placeholderWidthPx;
                  let actualRenderedImageHeightPx = placeholderHeightPx;
                  let actualRenderedImageX = drawX;
                  let actualRenderedImageY = placeholderYonSpreadPx;

                  if (fit === 'contain') {
                    const placeholderAspectRatio = placeholderWidthPx / placeholderHeightPx;
                    const imageAspectRatio = sourceImgWidthPx / sourceImgHeightPx;
                    
                    if (imageAspectRatio > placeholderAspectRatio) {
                      actualRenderedImageWidthPx = placeholderWidthPx;
                      actualRenderedImageHeightPx = placeholderWidthPx / imageAspectRatio;
                    } else {
                      actualRenderedImageHeightPx = placeholderHeightPx;
                      actualRenderedImageWidthPx = placeholderHeightPx * imageAspectRatio;
                    }

                    actualRenderedImageWidthPx = Math.min(actualRenderedImageWidthPx, placeholderWidthPx);
                    actualRenderedImageHeightPx = Math.min(actualRenderedImageHeightPx, placeholderHeightPx);

                    actualRenderedImageX = drawX + (placeholderWidthPx - actualRenderedImageWidthPx) / 2;
                    actualRenderedImageY = placeholderYonSpreadPx + (placeholderHeightPx - actualRenderedImageHeightPx) / 2;
                  }
                  // For 'cover', initial values of actualRenderedImageWidthPx, actualRenderedImageHeightPx, actualRenderedImageX, actualRenderedImageY are correct.
                  
                  ctx.drawImage(sourceImageBitmap, actualRenderedImageX, actualRenderedImageY, actualRenderedImageWidthPx, actualRenderedImageHeightPx);

                  // Draw borders if applicable (New conditional logic)
                  const borderSizePx = Math.round((imageBorderSize / 72) * dpi);
                  const borderColorCanvas = hexToRgbCanvas(imageBorderColor);

                  if (borderSizePx > 0 && borderColorCanvas) {
                    ctx.fillStyle = borderColorCanvas;
                    const bleedIsActive = projectBleed > 0; // projectBleed is in inches
                    const edgeTolerancePx = (2.0 / 72) * dpi; // Convert points to pixels

                    // Placeholder's position and dimensions on the current page canvas
                    const phX_onPage_canvas = drawX; // drawX is already placeholder's X on this page's canvas
                    const phY_onPage_canvas = placeholderYonSpreadPx;
                    const phW_canvas = placeholderWidthPx;
                    const phH_canvas = placeholderHeightPx;

                    // Image content's position and dimensions relative to placeholder's top-left on canvas
                    // destX, destY, destWidth, destHeight are already calculated for 'contain'
                    const imgContentX_inPh_canvas = (fit === 'contain') ? (phW_canvas - actualRenderedImageWidthPx) / 2 : 0;
                    const imgContentY_inPh_canvas = (fit === 'contain') ? (phH_canvas - actualRenderedImageHeightPx) / 2 : 0;
                    const imgContentW_canvas = actualRenderedImageWidthPx;
                    const imgContentH_canvas = actualRenderedImageHeightPx;

                    // Proximity checks (placeholder to page edges)
                    const isTopEdgeOutside = phY_onPage_canvas < edgeTolerancePx;
                    const isBottomEdgeOutside = (phY_onPage_canvas + phH_canvas) >= (singlePageCanvasHeightPx - edgeTolerancePx);
                    const isLeftEdgeOfPage = phX_onPage_canvas < edgeTolerancePx;
                    const isRightEdgeOfPage = (phX_onPage_canvas + phW_canvas) >= (singlePageCanvasWidthPx - edgeTolerancePx);

                    let showTopBorder_canvas = true, showBottomBorder_canvas = true, showLeftBorder_canvas = true, showRightBorder_canvas = true;

                    if (fit === 'contain') {
                      const gapLeftImageToPlaceholderPx = imgContentX_inPh_canvas;
                      const gapRightImageToPlaceholderPx = phW_canvas - (imgContentX_inPh_canvas + imgContentW_canvas);
                      const gapTopImageToPlaceholderPx = imgContentY_inPh_canvas;
                      const gapBottomImageToPlaceholderPx = phH_canvas - (imgContentY_inPh_canvas + imgContentH_canvas);

                      const isImageNearTopPlaceholderEdge = gapTopImageToPlaceholderPx < edgeTolerancePx;
                      const isImageNearBottomPlaceholderEdge = gapBottomImageToPlaceholderPx < edgeTolerancePx;
                      const isImageNearLeftPlaceholderEdge = gapLeftImageToPlaceholderPx < edgeTolerancePx;
                      const isImageNearRightPlaceholderEdge = gapRightImageToPlaceholderPx < edgeTolerancePx;

                      showTopBorder_canvas = !(isTopEdgeOutside && isImageNearTopPlaceholderEdge && bleedIsActive);
                      showBottomBorder_canvas = !(isBottomEdgeOutside && isImageNearBottomPlaceholderEdge && bleedIsActive);
                      showLeftBorder_canvas = !(isLeftEdgeOfPage && isImageNearLeftPlaceholderEdge && bleedIsActive);
                      showRightBorder_canvas = !(isRightEdgeOfPage && isImageNearRightPlaceholderEdge && bleedIsActive);
                    } else { // fit === 'cover'
                      showTopBorder_canvas = !(isTopEdgeOutside && bleedIsActive);
                      showBottomBorder_canvas = !(isBottomEdgeOutside && bleedIsActive);
                      showLeftBorder_canvas = !(isLeftEdgeOfPage && bleedIsActive);
                      showRightBorder_canvas = !(isRightEdgeOfPage && bleedIsActive);
                    }

                    // Explicitly hide borders at the gutter for spanning images
                    const placeholderGlobalX_px = adjustedPlaceholder.adjustedX_pt / 72 * dpi; // X on full spread canvas
                    const placeholderGlobalWidth_px = phW_canvas; // Full placeholder width on canvas
                    const singlePageWidth_canvas_px = singlePageCanvasWidthPx; // Width of one canvas page

                    if (pageSide === 'L' && (placeholderGlobalX_px + placeholderGlobalWidth_px) > singlePageWidth_canvas_px - edgeTolerancePx) { // Spans onto right page
                      showRightBorder_canvas = false; // Hide right border (gutter)
                    }
                    if (pageSide === 'R' && placeholderGlobalX_px < singlePageWidth_canvas_px + edgeTolerancePx) { // Spans onto left page (phX_onPage_canvas will be negative)
                      showLeftBorder_canvas = false; // Hide left border (gutter)
                    }

                    // Define the full placeholder rectangle on the current page canvas for border drawing
                    const borderDrawRect: Rect = { x: phX_onPage_canvas, y: phY_onPage_canvas, width: phW_canvas, height: phH_canvas };
                    const pageCanvasRect: Rect = { x: 0, y: 0, width: singlePageCanvasWidthPx, height: singlePageCanvasHeightPx };
                    const visibleBorderDrawRect = intersectRects(borderDrawRect, pageCanvasRect);

                    if (visibleBorderDrawRect) {
                      const vbdX = visibleBorderDrawRect.x;
                      const vbdY = visibleBorderDrawRect.y;
                      const vbdW = visibleBorderDrawRect.width;
                      const vbdH = visibleBorderDrawRect.height;

                      // Draw borders ensuring vertical ones use full placeholder height (vbdH) if visible
                      if (showTopBorder_canvas) ctx.fillRect(vbdX, vbdY, vbdW, Math.min(borderSizePx, vbdH));
                      if (showBottomBorder_canvas) ctx.fillRect(vbdX, vbdY + vbdH - Math.min(borderSizePx, vbdH), vbdW, Math.min(borderSizePx, vbdH));
                      if (showLeftBorder_canvas) ctx.fillRect(vbdX, vbdY, Math.min(borderSizePx, vbdW), vbdH);
                      if (showRightBorder_canvas) ctx.fillRect(vbdX + vbdW - Math.min(borderSizePx, vbdW), vbdY, Math.min(borderSizePx, vbdW), vbdH);
                    }
                  }
                  // The ctx.restore() here will undo the placeholder-specific clip,
                  // and also the page-level clip established by ctx.save() at line 2474
                  ctx.restore();
                }
              } catch (e) { console.error(`Error rendering image ${imageFile.name} for JPEG page:`, e); }
            }
          }

          // 5. Render Text Overlays (clipped for page)
          const spreadTextOverlays = textOverlays.filter(overlay => overlay.spreadId === spreadToRender.id);
          for (const overlay of spreadTextOverlays) {
            try {
              const style = overlay.style;
              const fontSizePx = (style.fontSize / 100) * singlePageCanvasHeightPx; // Relative to single page height
              ctx.font = `${style.bold ? 'bold ' : ''}${style.italic ? 'italic ' : ''}${fontSizePx}px ${style.fontFamily}`;
              ctx.fillStyle = style.color;
              ctx.globalAlpha = style.opacity;
              ctx.textAlign = style.textAlign as CanvasTextAlign;

              const textBlockWidthOnSpreadPx = (style.width / 100) * fullSpreadCanvasWidthPx;
              const textBlockCenterXOnSpreadPx = (style.x / 100) * fullSpreadCanvasWidthPx;
              
              let textDrawXOnSpreadPx;
              if (style.textAlign === 'center') { textDrawXOnSpreadPx = textBlockCenterXOnSpreadPx; }
              else if (style.textAlign === 'right') { textDrawXOnSpreadPx = textBlockCenterXOnSpreadPx + textBlockWidthOnSpreadPx / 2; }
              else { textDrawXOnSpreadPx = textBlockCenterXOnSpreadPx - textBlockWidthOnSpreadPx / 2; }
              
              const textDrawYOnPagePx = (style.y / 100) * singlePageCanvasHeightPx + (fontSizePx * 0.3); // Baseline adjustment

              ctx.save();
              ctx.rect(0, 0, singlePageCanvasWidthPx, singlePageCanvasHeightPx); ctx.clip();
              
              let finalDrawX = textDrawXOnSpreadPx;
              if (pageSide === 'R') {
                finalDrawX -= singlePageCanvasWidthPx;
              }

              // Basic word wrapping (simplified)
              const words = overlay.content.split(' ');
              let line = '';
              const lines = [];
              const lineHeight = fontSizePx * 1.2;
              for (let n = 0; n < words.length; n++) {
                const testLine = line + words[n] + ' ';
                const metrics = ctx.measureText(testLine);
                const testWidth = metrics.width;
                // Check against the text block's width on the spread, not the single page canvas width
                if (testWidth > textBlockWidthOnSpreadPx && n > 0) { lines.push(line); line = words[n] + ' '; }
                else { line = testLine; }
              }
              lines.push(line);
              let currentY = textDrawYOnPagePx - ((lines.length -1) * lineHeight / 2); // Center vertically for multi-line
              for (const l of lines) { ctx.fillText(l.trim(), finalDrawX, currentY); currentY += lineHeight; }
              
              ctx.restore();
            } catch (e) { console.error(`Error rendering text overlay ${overlay.id} for JPEG page:`, e); }
          }
          ctx.globalAlpha = 1.0;

          // 6. Export current page to JPEG
          try {
            const pixelData = ctx.getImageData(0, 0, singlePageCanvasWidthPx, singlePageCanvasHeightPx);
            const targetColorSpaceForApi: ColorSpaceOption = options.colorSpace === 'passthrough' ? 'srgb' : options.colorSpace;
            if (!window.electronAPI?.convertAndGenerateJpeg) throw new Error("convertAndGenerateJpeg API not found");

            const jpegResult = await window.electronAPI.convertAndGenerateJpeg(
              { data: pixelData.data, width: pixelData.width, height: pixelData.height },
              { quality: 92, colorSpace: targetColorSpaceForApi }
            );
            if (!jpegResult.success || !jpegResult.dataUrl) throw new Error(jpegResult.error || 'Failed to generate JPEG page via Electron API.');

            const filename = `spread-${String(spreadIndexForFilename + 1).padStart(3, '0')}-${pageSide}.jpg`;
            const filePath = `${exportDirectoryPath}/${filename}`;
            if (exportCancelledRef.current) { toast.info(`JPEG export cancelled before saving page ${filename}.`); return; }
            const success = await window.electronAPI.saveFile(filePath, jpegResult.dataUrl);
            if (success) exportSuccessCount++; else exportErrorCount++;
          } catch (e) {
            console.error(`Error generating or saving JPEG page ${pageSide}:`, e);
            exportErrorCount++;
          }
        } // End loop for L/R page
      } else { // Existing 'spreads' layout logic
        if (exportCancelledRef.current) { toast.info(`JPEG export cancelled before processing spread ${i + 1} (full spread layout).`); return; }
        offscreenCanvas.width = fullSpreadCanvasWidthPx;
        offscreenCanvas.height = fullSpreadCanvasHeightPx;
        ctx.clearRect(0, 0, fullSpreadCanvasWidthPx, fullSpreadCanvasHeightPx);

        // 1. Render Background Color
        ctx.fillStyle = hexToRgbCanvas(backgroundColorRef.current);
        ctx.fillRect(0, 0, fullSpreadCanvasWidthPx, fullSpreadCanvasHeightPx);

        // 2. Render Project Background Image (if any) - Full spread
        if (projectBackgroundImagePathRef.current) {
          try {
            const bgPath = projectBackgroundImagePathRef.current;
            const userScale = backgroundImageZoomRef.current;
            const focalX = backgroundImagePanXRef.current / 100;
            const focalY = backgroundImagePanYRef.current / 100;

            let imgNaturalWidth: number | undefined, imgNaturalHeight: number | undefined;
            const imageFileEntry = images.find(img => img.originalPath === bgPath);
            if (imageFileEntry?.naturalWidth && imageFileEntry?.naturalHeight) {
              imgNaturalWidth = imageFileEntry.naturalWidth; imgNaturalHeight = imageFileEntry.naturalHeight;
            } else if (window.electronAPI?.getExifData) {
              const exif = await window.electronAPI.getExifData(bgPath);
              if (exif.success && exif.exifData?.dimensions) {
                imgNaturalWidth = exif.exifData.dimensions.width; imgNaturalHeight = exif.exifData.dimensions.height;
              }
            }
            if (!imgNaturalWidth || !imgNaturalHeight) { imgNaturalWidth = spreadWidthPt; imgNaturalHeight = pageHeightPt; }
            
            const targetRatio = spreadWidthPt / pageHeightPt;
            const naturalImgRatio = imgNaturalWidth / imgNaturalHeight;
            let interimW, interimH;
            if (naturalImgRatio > targetRatio) { interimH = pageHeightPt; interimW = interimH * naturalImgRatio; }
            else { interimW = spreadWidthPt; interimH = interimW / naturalImgRatio; }
            const scaledW = interimW * userScale; const scaledH = interimH * userScale;
            const offsetX = (0.5 - focalX) * scaledW; const offsetY = (0.5 - focalY) * scaledH;

            const bgData = await window.electronAPI.loadImageDataForCanvas(bgPath, {
              targetWidthPt: spreadWidthPt, targetHeightPt: pageHeightPt,
              transform: { scale: userScale, offsetX: offsetX, offsetY: offsetY, fit: 'cover' },
              colorSpace: options.colorSpace,
            });
            if (bgData.success && bgData.imageBuffer) {
              const image = await createImageBitmap(new Blob([bgData.imageBuffer], { type: bgData.imageType === 'png' ? 'image/png' : 'image/jpeg' }));
              ctx.globalAlpha = projectBackgroundImageOpacityRef.current;
              ctx.drawImage(image, 0, 0, fullSpreadCanvasWidthPx, fullSpreadCanvasHeightPx);
              ctx.globalAlpha = 1.0;
            }
          } catch (e) { console.error("Error rendering project background for JPEG spread:", e); }
        }

        // 3. Render Spread-Specific Background Image (if any) - Full spread
        if (spreadToRender.spreadBackgroundData?.imagePath) {
          try {
            const spreadBg = spreadToRender.spreadBackgroundData;
            const spreadBgPath = spreadBg.imagePath;
            const { scale: userScale = 1, focalX = 0.5, focalY = 0.5, fit = 'cover' } = spreadBg.transform || {};
            const imgNaturalWidth = spreadBg.naturalWidth || spreadWidthPt;
            const imgNaturalHeight = spreadBg.naturalHeight || pageHeightPt;
            const targetRatio = spreadWidthPt / pageHeightPt;
            const naturalImgRatio = imgNaturalWidth / imgNaturalHeight;
            let interimW, interimH;
            if (fit === 'cover') {
                if (naturalImgRatio > targetRatio) { interimH = pageHeightPt; interimW = interimH * naturalImgRatio; }
                else { interimW = spreadWidthPt; interimH = interimW / naturalImgRatio; }
            } else {
                if (naturalImgRatio > targetRatio) { interimW = spreadWidthPt; interimH = interimW / naturalImgRatio; }
                else { interimH = pageHeightPt; interimW = interimH * naturalImgRatio; }
            }
            const scaledW = interimW * userScale; const scaledH = interimH * userScale;
            const offsetX = (0.5 - focalX) * scaledW; const offsetY = (0.5 - focalY) * scaledH;

            const spreadBgData = await window.electronAPI.loadImageDataForCanvas(spreadBgPath, {
              targetWidthPt: spreadWidthPt, targetHeightPt: pageHeightPt,
              transform: { scale: userScale, offsetX: offsetX, offsetY: offsetY, fit: fit as 'cover' | 'contain' },
              colorSpace: options.colorSpace,
            });
            if (spreadBgData.success && spreadBgData.imageBuffer) {
              const image = await createImageBitmap(new Blob([spreadBgData.imageBuffer], { type: spreadBgData.imageType === 'png' ? 'image/png' : 'image/jpeg' }));
              ctx.globalAlpha = spreadBg.opacity ?? 1.0;
              ctx.drawImage(image, 0, 0, fullSpreadCanvasWidthPx, fullSpreadCanvasHeightPx);
              ctx.globalAlpha = 1.0;
            }
          } catch (e) { console.error("Error rendering spread background for JPEG spread:", e); }
        }
        
        // 4. Render Image Placeholders - Full spread
        const template = templateMap[spreadToRender.templateId];
        if (template && template.id !== '__blank__') {
          const spreadAdjustedLayout = adjustTemplateForGap(template, imageGap, spreadWidthPt, pageHeightPt);
          for (const placement of spreadToRender.images) {
            if (!placement.imageId) continue;
            const imageFile = images.find(img => img.id === placement.imageId);
            if (!imageFile || !imageFile.naturalWidth || !imageFile.naturalHeight) continue;
            const adjustedPlaceholder = spreadAdjustedLayout.find(p => p.id === placement.placeholderId);
            if (!adjustedPlaceholder) continue;
            try {
              const { scale: userScale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain', rotation = 0 } = placement.transform;
              const phWidthPt = adjustedPlaceholder.adjustedWidth_pt;
              const phHeightPt = adjustedPlaceholder.adjustedHeight_pt;

              // Calculate interim and scaled dimensions for offset calculation
              const imgNaturalWidth_placeholder_spread = imageFile.naturalWidth || 1;
              const imgNaturalHeight_placeholder_spread = imageFile.naturalHeight || 1;
              const naturalImgRatio_placeholder_spread = imgNaturalWidth_placeholder_spread / imgNaturalHeight_placeholder_spread;
              const placeholderRatio_spread = phWidthPt / phHeightPt;
              let interimW_placeholder_spread, interimH_placeholder_spread;
              if (fit === 'cover') {
                if (naturalImgRatio_placeholder_spread > placeholderRatio_spread) { interimH_placeholder_spread = phHeightPt; interimW_placeholder_spread = interimH_placeholder_spread * naturalImgRatio_placeholder_spread; }
                else { interimW_placeholder_spread = phWidthPt; interimH_placeholder_spread = interimW_placeholder_spread / naturalImgRatio_placeholder_spread; }
              } else { // 'contain'
                if (naturalImgRatio_placeholder_spread > placeholderRatio_spread) { interimW_placeholder_spread = phWidthPt; interimH_placeholder_spread = interimW_placeholder_spread / naturalImgRatio_placeholder_spread; }
                else { interimH_placeholder_spread = phHeightPt; interimW_placeholder_spread = interimH_placeholder_spread * naturalImgRatio_placeholder_spread; }
              }
              const scaledW_placeholder_spread = interimW_placeholder_spread * userScale;
              const scaledH_placeholder_spread = interimH_placeholder_spread * userScale;
              const offsetX_placeholder_spread = (0.5 - focalX) * scaledW_placeholder_spread;
              const offsetY_placeholder_spread = (0.5 - focalY) * scaledH_placeholder_spread;
              
              const imgData = await window.electronAPI.loadImageDataForCanvas(imageFile.originalPath, {
                targetWidthPt: phWidthPt, targetHeightPt: phHeightPt,
                transform: { scale: userScale, offsetX: offsetX_placeholder_spread, offsetY: offsetY_placeholder_spread, fit: fit as 'cover' | 'contain', rotation: rotation },
                colorSpace: options.colorSpace,
              });
              if (imgData.success && imgData.imageBuffer && imgData.width && imgData.height) {
                const sourceImage = await createImageBitmap(new Blob([imgData.imageBuffer], { type: imgData.imageType === 'png' ? 'image/png' : 'image/jpeg' }));
                
                const placeholderXPx = (adjustedPlaceholder.adjustedX_pt / 72) * dpi;
                const placeholderYPx = (adjustedPlaceholder.adjustedY_pt / 72) * dpi;
                const placeholderWidthPx = (phWidthPt / 72) * dpi;
                const placeholderHeightPx = (phHeightPt / 72) * dpi;

                let dx = placeholderXPx, dy = placeholderYPx, dWidth = placeholderWidthPx, dHeight = placeholderHeightPx;
                if (fit === 'contain') {
                    const sourceAspectRatio = imgData.width / imgData.height;
                    const placeholderAspectRatioPx = placeholderWidthPx / placeholderHeightPx;
                    if (sourceAspectRatio > placeholderAspectRatioPx) {
                        dWidth = placeholderWidthPx; dHeight = dWidth / sourceAspectRatio;
                        dy = placeholderYPx + (placeholderHeightPx - dHeight) / 2;
                    } else {
                        dHeight = placeholderHeightPx; dWidth = dHeight * sourceAspectRatio;
                        dx = placeholderXPx + (placeholderWidthPx - dWidth) / 2;
                    }
                }
                ctx.drawImage(sourceImage, dx, dy, dWidth, dHeight);

                // Draw borders if applicable (New conditional logic for 'spreads' layout)
                const borderSizePx = Math.round((imageBorderSize / 72) * dpi);
                const borderColorCanvas = hexToRgbCanvas(imageBorderColor);

                if (borderSizePx > 0 && borderColorCanvas) {
                  ctx.fillStyle = borderColorCanvas;
                  const bleedIsActive = projectBleed > 0;
                  const edgeTolerancePx = (2.0 / 72) * dpi;

                  // Placeholder's position and dimensions on the full spread canvas
                  const phX_onSpread_canvas = placeholderXPx;
                  const phY_onSpread_canvas = placeholderYPx;
                  const phW_canvas_spread = placeholderWidthPx;
                  const phH_canvas_spread = placeholderHeightPx;

                  // Image content's position and dimensions (dx, dy, dWidth, dHeight are already calculated for the image content)
                  const imgContentX_inPh_canvas_spread = dx - phX_onSpread_canvas; // Relative to placeholder
                  const imgContentY_inPh_canvas_spread = dy - phY_onSpread_canvas; // Relative to placeholder
                  const imgContentW_canvas_spread = dWidth;
                  const imgContentH_canvas_spread = dHeight;

                  // Proximity checks (placeholder to full spread canvas edges)
                  const isTopEdgeOutside_spread = phY_onSpread_canvas < edgeTolerancePx;
                  const isBottomEdgeOutside_spread = (phY_onSpread_canvas + phH_canvas_spread) >= (fullSpreadCanvasHeightPx - edgeTolerancePx);
                  const isLeftEdgeOfSpread_canvas = phX_onSpread_canvas < edgeTolerancePx;
                  const isRightEdgeOfSpread_canvas = (phX_onSpread_canvas + phW_canvas_spread) >= (fullSpreadCanvasWidthPx - edgeTolerancePx);

                  let showTopBorder_canvas_spread = true, showBottomBorder_canvas_spread = true, showLeftBorder_canvas_spread = true, showRightBorder_canvas_spread = true;

                  if (fit === 'contain') {
                    const gapLeftImageToPlaceholderPx_spread = imgContentX_inPh_canvas_spread;
                    const gapRightImageToPlaceholderPx_spread = phW_canvas_spread - (imgContentX_inPh_canvas_spread + imgContentW_canvas_spread);
                    const gapTopImageToPlaceholderPx_spread = imgContentY_inPh_canvas_spread;
                    const gapBottomImageToPlaceholderPx_spread = phH_canvas_spread - (imgContentY_inPh_canvas_spread + imgContentH_canvas_spread);

                    const isImageNearTopPlaceholderEdge_spread = gapTopImageToPlaceholderPx_spread < edgeTolerancePx;
                    const isImageNearBottomPlaceholderEdge_spread = gapBottomImageToPlaceholderPx_spread < edgeTolerancePx;
                    const isImageNearLeftPlaceholderEdge_spread = gapLeftImageToPlaceholderPx_spread < edgeTolerancePx;
                    const isImageNearRightPlaceholderEdge_spread = gapRightImageToPlaceholderPx_spread < edgeTolerancePx;

                    showTopBorder_canvas_spread = !(isTopEdgeOutside_spread && isImageNearTopPlaceholderEdge_spread && bleedIsActive);
                    showBottomBorder_canvas_spread = !(isBottomEdgeOutside_spread && isImageNearBottomPlaceholderEdge_spread && bleedIsActive);
                    showLeftBorder_canvas_spread = !(isLeftEdgeOfSpread_canvas && isImageNearLeftPlaceholderEdge_spread && bleedIsActive);
                    showRightBorder_canvas_spread = !(isRightEdgeOfSpread_canvas && isImageNearRightPlaceholderEdge_spread && bleedIsActive);
                  } else { // fit === 'cover'
                    showTopBorder_canvas_spread = !(isTopEdgeOutside_spread && bleedIsActive);
                    showBottomBorder_canvas_spread = !(isBottomEdgeOutside_spread && bleedIsActive);
                    showLeftBorder_canvas_spread = !(isLeftEdgeOfSpread_canvas && bleedIsActive);
                    showRightBorder_canvas_spread = !(isRightEdgeOfSpread_canvas && bleedIsActive);
                  }

                  // Define the full placeholder rectangle on the spread canvas for border drawing
                  const borderDrawRectSpread: Rect = { x: phX_onSpread_canvas, y: phY_onSpread_canvas, width: phW_canvas_spread, height: phH_canvas_spread };
                  const spreadCanvasRect: Rect = { x: 0, y: 0, width: fullSpreadCanvasWidthPx, height: fullSpreadCanvasHeightPx };
                  const visibleBorderDrawRectSpread = intersectRects(borderDrawRectSpread, spreadCanvasRect);

                  if (visibleBorderDrawRectSpread) {
                    const vbdX_s = visibleBorderDrawRectSpread.x;
                    const vbdY_s = visibleBorderDrawRectSpread.y;
                    const vbdW_s = visibleBorderDrawRectSpread.width;
                    const vbdH_s = visibleBorderDrawRectSpread.height;

                    if (showTopBorder_canvas_spread) ctx.fillRect(vbdX_s, vbdY_s, vbdW_s, Math.min(borderSizePx, vbdH_s));
                    if (showBottomBorder_canvas_spread) ctx.fillRect(vbdX_s, vbdY_s + vbdH_s - Math.min(borderSizePx, vbdH_s), vbdW_s, Math.min(borderSizePx, vbdH_s));
                    if (showLeftBorder_canvas_spread) ctx.fillRect(vbdX_s, vbdY_s, Math.min(borderSizePx, vbdW_s), vbdH_s);
                    if (showRightBorder_canvas_spread) ctx.fillRect(vbdX_s + vbdW_s - Math.min(borderSizePx, vbdW_s), vbdY_s, Math.min(borderSizePx, vbdW_s), vbdH_s);
                  }
                }
              }
            } catch (e) { console.error(`Error rendering image ${imageFile.name} for JPEG spread:`, e); }
          }
        }

        // 5. Render Text Overlays - Full spread
        const spreadTextOverlays = textOverlays.filter(overlay => overlay.spreadId === spreadToRender.id);
        for (const overlay of spreadTextOverlays) {
          try {
            const style = overlay.style;
            const fontSizePx = (style.fontSize / 100) * fullSpreadCanvasHeightPx;
            ctx.font = `${style.bold ? 'bold ' : ''}${style.italic ? 'italic ' : ''}${fontSizePx}px ${style.fontFamily}`;
            ctx.fillStyle = style.color;
            ctx.globalAlpha = style.opacity;
            ctx.textAlign = style.textAlign as CanvasTextAlign;
            const textBlockWidthPx = (style.width / 100) * fullSpreadCanvasWidthPx;
            const textBlockCenterXpx = (style.x / 100) * fullSpreadCanvasWidthPx;
            let textDrawXpx;
            if (style.textAlign === 'center') { textDrawXpx = textBlockCenterXpx; }
            else if (style.textAlign === 'right') { textDrawXpx = textBlockCenterXpx + textBlockWidthPx / 2; }
            else { textDrawXpx = textBlockCenterXpx - textBlockWidthPx / 2; }
            const textDrawYpx = (style.y / 100) * fullSpreadCanvasHeightPx + (fontSizePx * 0.3);
            
            const words = overlay.content.split(' ');
            let line = ''; const lines = []; const lineHeight = fontSizePx * 1.2;
            for (let n = 0; n < words.length; n++) {
              const testLine = line + words[n] + ' '; const metrics = ctx.measureText(testLine); const testWidth = metrics.width;
              if (testWidth > textBlockWidthPx && n > 0) { lines.push(line); line = words[n] + ' '; } else { line = testLine; }
            }
            lines.push(line);
            let currentY = textDrawYpx - ((lines.length -1) * lineHeight / 2);
            for (const l of lines) { ctx.fillText(l.trim(), textDrawXpx, currentY); currentY += lineHeight; }
          } catch (e) { console.error(`Error rendering text overlay ${overlay.id} for JPEG spread:`, e); }
        }
        ctx.globalAlpha = 1.0;

        // 6. Export full spread to JPEG
        try {
          const pixelData = ctx.getImageData(0, 0, fullSpreadCanvasWidthPx, fullSpreadCanvasHeightPx);
          const targetColorSpaceForApi: ColorSpaceOption = options.colorSpace === 'passthrough' ? 'srgb' : options.colorSpace;
          if (!window.electronAPI?.convertAndGenerateJpeg) throw new Error("convertAndGenerateJpeg API not found");

          const jpegResult = await window.electronAPI.convertAndGenerateJpeg(
            { data: pixelData.data, width: pixelData.width, height: pixelData.height },
            { quality: 92, colorSpace: targetColorSpaceForApi }
          );
          if (!jpegResult.success || !jpegResult.dataUrl) throw new Error(jpegResult.error || 'Failed to generate JPEG spread via Electron API.');

          const filename = `spread-${String(spreadIndexForFilename + 1).padStart(3, '0')}.jpg`;
          const filePath = `${exportDirectoryPath}/${filename}`;
          if (exportCancelledRef.current) { toast.info(`JPEG export cancelled before saving spread ${filename}.`); return; }
          const success = await window.electronAPI.saveFile(filePath, jpegResult.dataUrl);
          if (success) exportSuccessCount++; else exportErrorCount++;
        } catch (e) {
          console.error("Error generating or saving JPEG spread:", e);
          exportErrorCount++;
        }
      } // End of 'spreads' layout logic
      if (exportCancelledRef.current) { toast.info("JPEG export cancelled during spread processing loop."); return; }
      await new Promise(resolve => setTimeout(resolve, 10)); // Small delay between processing each spread
    } // End of loop through targetSpreadsToProcess

    const totalExportsAttempted = exportSuccessCount + exportErrorCount;
    const spreadExportCount = totalExportsAttempted - (coverExportedSuccessfully || (coverExportSkippedOrFailed && options.coverExportOption === 'include' && canExportCoverActually) ? 1 : 0);

    if (exportSuccessCount > 0 && exportErrorCount === 0) {
      let successMessage = `Successfully exported ${exportSuccessCount} item(s)`;
      if (coverExportedSuccessfully && spreadExportCount > 0) successMessage = `Successfully exported cover and ${spreadExportCount} spread JPEG(s)`;
      else if (coverExportedSuccessfully) successMessage = `Successfully exported cover JPEG`;
      else if (spreadExportCount > 0) successMessage = `Successfully exported ${spreadExportCount} spread JPEG(s)`;
      toast.success(`${successMessage} to the selected folder.`, { id: 'jpeg-export-progress' });
      if (exportDirectoryPath && window.electronAPI?.openPath) window.electronAPI.openPath(exportDirectoryPath);
    } else if (exportSuccessCount > 0 && exportErrorCount > 0) {
      let warningMessage = `Exported ${exportSuccessCount} item(s) with ${exportErrorCount} error(s).`;
      // Add more specific messaging if needed based on cover vs spread success/failure
      toast.warning(warningMessage, { id: 'jpeg-export-progress' });
      if (exportDirectoryPath && window.electronAPI?.openPath) window.electronAPI.openPath(exportDirectoryPath);
    } else if (exportErrorCount > 0) {
      toast.error(`Failed to export ${exportErrorCount} item(s).`, { id: 'jpeg-export-progress' });
    } else if (!coverExportedSuccessfully && coverExportSkippedOrFailed && targetSpreadsToProcess.length === 0) {
        toast.info("Cover export was skipped or failed, and no spreads to export.", { id: 'jpeg-export-progress' });
    } else if (targetSpreadsToProcess.length === 0 && !(canExportCoverActually && options.coverExportOption === 'include')) {
      toast.info("No JPEGs were exported (no spreads and cover export not selected/possible).", { id: 'jpeg-export-progress' });
    } else if (exportSuccessCount === 0 && exportErrorCount === 0 && !(canExportCoverActually && options.coverExportOption === 'include' && !coverExportSkippedOrFailed) && targetSpreadsToProcess.length === 0) {
      // This case might occur if everything was skipped (e.g. no cover, no spreads)
      toast.info("No items were selected or available for JPEG export.", { id: 'jpeg-export-progress' });
    }

  }, [
    aspectRatio, spreads, currentSpreadId, backgroundColorRef,
    projectBackgroundImagePathRef, backgroundImageZoomRef, backgroundImagePanXRef, backgroundImagePanYRef, projectBackgroundImageOpacityRef,
    templateMap, imageGap, images, textOverlays, isCoverVisibleRef, hasCoverRef, coverImageRef, pdfGenerationArgsRef, // Added cover refs and pdfGenerationArgsRef
    imageBorderSize, imageBorderColor // Added image border settings
  ]);

  const executePdfGenerationWithCoverOption = useCallback(async (
    coverExportOption: CoverExportOption,
    _pdfFormat: PdfFormatOption, // Correctly use PdfFormatOption
    pdfLayout: PdfLayoutOption,
    coverLayout: CoverLayoutOption,
    colorSpace: ColorSpaceOption,
    exportEmptySpreadsAsIs: boolean // Added new parameter
  ) => {
    setIsExporting(true); // Set exporting state to true at start
    
    if (exportCancelledRef.current) {
      console.log('PDF export cancelled before starting.');
      // toast.info("PDF Export cancelled."); // Already handled by handleExportCancel
      setIsExporting(false); // Reset state if cancelled
      return;
    }
    // Handle JPEG export
    if ((_pdfFormat as string) === 'jpeg') {
      // Note: executeJpegExport will handle its own isExporting state
      setIsExporting(false); // Reset state since we're delegating to executeJpegExport
      executeJpegExport(_pdfFormat as string, {
        coverExportOption,
        pdfLayout,
        coverLayout,
        colorSpace,
        exportEmptySpreadsAsIs
      });
      return;
    }

    // Existing PDF generation logic
    if (!window.electronAPI || !currentProjectFilePath) {
      // This should ideally be caught before calling this function
      toast.error("Export prerequisites not met.");
      setIsExporting(false); // Reset state if prerequisites not met
      return;
    }
    if (exportCancelledRef.current) { 
      toast.info("PDF Export cancelled after initial checks."); 
      setIsExporting(false); // Reset state if cancelled
      return; 
    }

    // Retrieve spreadsForExport and capturedCoverState from the ref
    let {
      spreadsForExport, // Make it mutable
      coverPdfDoc: separateCoverPdfDoc,
      capturedCoverState
    } = pdfGenerationArgsRef.current;

    // Filter empty trailing spreads if exportEmptySpreadsAsIs is false
    if (!exportEmptySpreadsAsIs && spreadsForExport) {
      const trailingEmptyIds: string[] = [];
      for (let i = spreadsForExport.length - 1; i >= 0; i--) {
        const spread = spreadsForExport[i];
        if ((spread.templateId === '__blank__' || !spread.images.some(img => img.imageId))) {
          trailingEmptyIds.push(spread.id);
        } else {
          break;
        }
      }
      if (trailingEmptyIds.length > 0) {
        spreadsForExport = spreadsForExport.filter(s => !trailingEmptyIds.includes(s.id));
        toast.info(`Removed ${trailingEmptyIds.length} empty trailing spread(s) for export.`);
      }
    }
    
    const finalSpreadsForExport = spreadsForExport; // Use the potentially filtered list

    if (!finalSpreadsForExport || finalSpreadsForExport.length === 0) {
        // Check if only a cover is being exported
        if (!(hasCover && capturedCoverState && capturedCoverState.coverImage && coverExportOption !== 'none')) {
            toast.error("No spreads available to export.", { id: 'pdf-export-progress' });
            return;
        }
    }
    
    toast.info("Preparing PDF export...", { id: 'pdf-export-progress', duration: 30000 });

    let pageWidthPt = aspectRatio.widthPt;
    let pageHeightPt = aspectRatio.heightPt;

    if (!pageWidthPt || !pageHeightPt || pageWidthPt <= 0 || pageHeightPt <= 0) {
      // This validation should ideally happen before showing the ExportDialog
      // or be part of the pre-flight checks displayed in the dialog.
      // For now, re-validate here.
      console.warn("[executePdfGeneration] Point dimensions not available or invalid, attempting direct calculation.");
      if (aspectRatio?.dimensions) {
        try {
          const cleanedDimensions = aspectRatio.dimensions.replace(/"/g, '').replace(/×/g, 'x').replace(/\s+/g, '');
          const dims = cleanedDimensions.toLowerCase().split('x');
          if (dims.length === 2) {
            const widthInches = parseFloat(dims[0]); const heightInches = parseFloat(dims[1]);
            if (!isNaN(widthInches) && !isNaN(heightInches) && widthInches > 0 && heightInches > 0) {
              pageWidthPt = widthInches * 72; pageHeightPt = heightInches * 72;
              onUpdateAspectRatioPoints({ widthPt: pageWidthPt, heightPt: pageHeightPt });
            }
          }
        } catch (error) { /* error handling */ }
      }
    }
    if (!pageWidthPt || !pageHeightPt || pageWidthPt <= 0 || pageHeightPt <= 0) {
      toast.error("Invalid page dimensions detected. Cannot export PDF.", { id: 'pdf-export-progress' });
      return;
    }

    let mainPdfDoc: PDFDocument;
    try {
      mainPdfDoc = await PDFDocument.create();
      mainPdfDoc.setProducer('BookProofs Designer');
      mainPdfDoc.setCreator('BookProofs Designer');
    } catch (pdfCreateError) {
       console.error("PDF Creation Error (mainPdfDoc):", pdfCreateError);
       toast.error(`PDF Creation Failed: ${pdfCreateError.message}`, { id: 'pdf-export-progress' });
       return;
    }

    // Define hexToRgb function at a higher scope so it can be used for both main PDF and cover PDF
    // It now returns an RGB object from pdf-lib or null
    const hexToRgb = (hex: string): RGB | null => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? rgb(parseInt(result[1], 16) / 255, parseInt(result[2], 16) / 255, parseInt(result[3], 16) / 255) : null;
    };

    // --- Cover Handling ---
    let coverPdfBytes: Uint8Array | undefined;
    let coverSavePath: string | null | undefined = undefined; // Declare here
    if (hasCover && capturedCoverState && capturedCoverState.coverImage && coverExportOption !== 'none') {
        // Original editor concept of cover width (double page)
        // const editorCoverSpreadWidthPt = pageWidthPt * 2;
        // Actual width for the PDF page of the cover, depends on coverLayout
        const pdfCoverPageActualWidthPt = coverLayout === 'double' ? pageWidthPt * 2 : pageWidthPt;
        const pdfCoverPageActualHeightPt = pageHeightPt;
        const coverStateImage = capturedCoverState.coverImage;

        // Define the transform based on capturedCoverState
        // Assuming translateX/Y are pixel offsets and ProjectCover uses 'cover' fit.
        // If ProjectCover's fit mode can vary, it should be part of capturedCoverState.
        const imgNativeWidthPx = coverStateImage.naturalWidth || 1000; // Fallback, but should be present
        const imgNativeHeightPx = coverStateImage.naturalHeight || 1000; // Fallback

        // Target dimensions for the image content area in the PDF
        const pdfImageContentTargetWidthPt = pageWidthPt; // Cover image content area is always single page width for transform calculation
        const pdfImageContentTargetHeightPt = pageHeightPt;
        const currentCoverScale = capturedCoverState.scale;

        const imageRatioNative = imgNativeWidthPx / imgNativeHeightPx;
        const containerRatioPt = pdfImageContentTargetWidthPt / pdfImageContentTargetHeightPt;

        let baseRenderedW_pt_pdf: number, baseRenderedH_pt_pdf: number;

        // 'contain' fit logic for PDF container, to match ProjectCover's internal logic
        if (imageRatioNative > containerRatioPt) { // Image is wider than container (proportionally)
            baseRenderedW_pt_pdf = pdfImageContentTargetWidthPt; // Fit to width
            baseRenderedH_pt_pdf = pdfImageContentTargetWidthPt / imageRatioNative; // Height will be smaller
        } else { // Image is taller or same aspect as container
            baseRenderedH_pt_pdf = pdfImageContentTargetHeightPt; // Fit to height
            baseRenderedW_pt_pdf = pdfImageContentTargetHeightPt * imageRatioNative; // Width will be smaller
        }

        const scaledRenderedW_pdf_pt = baseRenderedW_pt_pdf * currentCoverScale;
        const scaledRenderedH_pdf_pt = baseRenderedH_pt_pdf * currentCoverScale;

        const pdfMaxPanX_pt = Math.max(0, (scaledRenderedW_pdf_pt - pdfImageContentTargetWidthPt) / 2);
        const pdfMaxPanY_pt = Math.max(0, (scaledRenderedH_pdf_pt - pdfImageContentTargetHeightPt) / 2);

        const editorTX_px = capturedCoverState.translateX || 0;
        const editorTY_px = capturedCoverState.translateY || 0;
        // editorMaxTranslateX/Y are from ProjectCover, calculated for its 'contain' view
        const editorMaxTX_px = capturedCoverState.editorMaxTranslateX;
        const editorMaxTY_px = capturedCoverState.editorMaxTranslateY;

        let finalPdfOffsetX_pt = 0;
        let finalPdfOffsetY_pt = 0;

        if (editorMaxTX_px && editorMaxTX_px > 0) {
            finalPdfOffsetX_pt = (editorTX_px / editorMaxTX_px) * pdfMaxPanX_pt;
        }
        if (editorMaxTY_px && editorMaxTY_px > 0) {
            finalPdfOffsetY_pt = (editorTY_px / editorMaxTY_px) * pdfMaxPanY_pt;
        }

        // Clamp final offsets
        finalPdfOffsetX_pt = Math.max(-pdfMaxPanX_pt, Math.min(pdfMaxPanX_pt, finalPdfOffsetX_pt));
        finalPdfOffsetY_pt = Math.max(-pdfMaxPanY_pt, Math.min(pdfMaxPanY_pt, finalPdfOffsetY_pt));
        
        const coverTransformForPdf: { scale: number; offsetX: number; offsetY: number; fit: "cover" | "contain" } = {
            scale: capturedCoverState.scale,
            offsetX: finalPdfOffsetX_pt,
            offsetY: finalPdfOffsetY_pt,
            fit: 'contain', // Match ProjectCover's internal 'contain'-then-scale logic for separate export
        };

        let coverImagePath = getOriginalPathFromUrl(coverStateImage.originalPath);
        if (window.bookProofsApp?.getUpdatedFilePath) {
            const updatedPath = window.bookProofsApp.getUpdatedFilePath(coverImagePath);
            if (updatedPath) coverImagePath = updatedPath;
        }

        if (coverExportOption === 'separate') {
            // Generate a separate PDF for the cover
            try {
                const tempCoverPdfDoc = await PDFDocument.create();
                tempCoverPdfDoc.setProducer('BookProofs Designer');
                tempCoverPdfDoc.setCreator('BookProofs Designer');
                
                // Create a page with dimensions based on coverLayout
                const coverPage = tempCoverPdfDoc.addPage([pdfCoverPageActualWidthPt, pdfCoverPageActualHeightPt]);
                
                // Always use white background for cover page
                const whiteColor = rgb(1, 1, 1); // White color in RGB (1,1,1)
                coverPage.drawRectangle({ x: 0, y: 0, width: pdfCoverPageActualWidthPt, height: pdfCoverPageActualHeightPt, color: whiteColor });
                
                // Load the cover image data, targeting single page width for the image itself
                // as capturedCoverState transforms are relative to that.
                const coverImageTargetWidthPt = pageWidthPt; // Image content is for one page
                const coverImageTargetHeightPt = pageHeightPt;

                // Mirror the transform calculation from the "include cover" section
                const userScale_sep = capturedCoverState.scale;
                const userFocalX_sep = capturedCoverState.focalX || 0.5;
                const userFocalY_sep = capturedCoverState.focalY || 0.5;

                // baseRenderedW_pt_pdf and baseRenderedH_pt_pdf are already calculated above using 'contain' logic
                // and pdfImageContentTargetWidthPt/HeightPt (which are equivalent to coverImageTargetWidthPt/HeightPt here)
                const finalScaledWidthForFocalCalc_sep = baseRenderedW_pt_pdf * userScale_sep;
                const finalScaledHeightForFocalCalc_sep = baseRenderedH_pt_pdf * userScale_sep;

                const offsetX_for_api_sep = (0.5 - userFocalX_sep) * finalScaledWidthForFocalCalc_sep;
                const offsetY_for_api_sep = (0.5 - userFocalY_sep) * finalScaledHeightForFocalCalc_sep;

                const focalCoverTransformForPdf_sep: { scale: number; offsetX: number; offsetY: number; fit: "cover" | "contain" } = {
                    scale: userScale_sep,
                    offsetX: offsetX_for_api_sep,
                    offsetY: offsetY_for_api_sep,
                    fit: 'contain',
                };

                const coverImageDataResult = await window.electronAPI.loadImageDataForPdf(coverImagePath, {
                    targetWidthPt: coverImageTargetWidthPt,
                    targetHeightPt: coverImageTargetHeightPt,
                    transform: focalCoverTransformForPdf_sep, // Use the explicitly created transform object
                    colorSpace: colorSpace // Pass colorSpace
                });
                
                if (coverImageDataResult?.success && coverImageDataResult.imageBuffer) {
                    // Embed the image in the PDF
                    let pdfCoverImage: PDFImage;
                    const { imageType: coverImageType, imageBuffer: coverImageBuffer, iccProfileBuffer: coverIccProfileBuffer } = coverImageDataResult;

                    if (coverImageType === 'png') {
                        let decompressedCoverIccProfile: Uint8Array | undefined = undefined;
                        if (coverIccProfileBuffer) {
                            try {
                                const compressedProfile = coverIccProfileBuffer instanceof Uint8Array
                                    ? coverIccProfileBuffer
                                    : new Uint8Array(coverIccProfileBuffer);
                                decompressedCoverIccProfile = pako.inflate(compressedProfile);
                            } catch (inflateError) {
                                console.error(`Failed to decompress PNG ICC profile for separate cover image:`, inflateError);
                                toast.warning(`Failed to decompress color profile for separate cover. Embedding without profile.`, { duration: 5000 });
                            }
                        }
                        pdfCoverImage = await tempCoverPdfDoc.embedPng(coverImageBuffer, { iccProfile: decompressedCoverIccProfile, iccProfileComponents: decompressedCoverIccProfile ? 3 : undefined });
                    } else { // JPEG
                        let rawCoverIccProfile: Uint8Array | undefined = undefined;
                        if (coverIccProfileBuffer) {
                           rawCoverIccProfile = coverIccProfileBuffer instanceof Uint8Array
                             ? coverIccProfileBuffer
                             : new Uint8Array(coverIccProfileBuffer);
                        }
                        pdfCoverImage = await tempCoverPdfDoc.embedJpg(coverImageBuffer, { iccProfile: rawCoverIccProfile, iccProfileComponents: rawCoverIccProfile ? 3 : undefined });
                    }
                    
                    // Draw the image
                    // For 'separate' export with 'double' layout, the image (which is processed as 'contain' relative to a single page)
                    // needs to be drawn onto the right half of the double-page PDF.
                    // The pdfCoverImage is already scaled and positioned by loadImageDataForPdf based on coverImageTargetWidthPt/HeightPt.
                    const drawX = coverLayout === 'double' ? pageWidthPt : 0;
                    
                    // Calculate the actual drawing dimensions to maintain aspect ratio within the target area
                    // The pdfCoverImage from loadImageDataForPdf is already transformed (scaled and panned)
                    // We just need to draw it into the correct slot on the PDF page.
                    // The coverImageTargetWidthPt/HeightPt are the dimensions of this slot.
                    coverPage.drawImage(pdfCoverImage, {
                        x: drawX, // Position on the right half if double layout
                        y: 0,
                        width: coverImageTargetWidthPt, // Draw at the target slot width
                        height: coverImageTargetHeightPt // Draw at the target slot height
                    });
                    
                    // Add any text overlays for the cover
                    const coverTextOverlays = textOverlays.filter(overlay => overlay.spreadId === 'cover');
                    if (coverTextOverlays.length > 0) {
                        for (const overlay of coverTextOverlays) {
                            try {
                                // Get font based on the overlay's font family
                                const pdfFontName = fontFamilyToPdfFontMap[overlay.style.fontFamily] || StandardFonts.Helvetica;
                                const font = await tempCoverPdfDoc.embedFont(pdfFontName);
                                
                                // Calculate text position and size
                                const fontSize = (overlay.style.fontSize / 100) * pdfCoverPageActualHeightPt;
                                const textContent = overlay.content;
                                const textBlockAbsoluteWidthPt = (overlay.style.width / 100) * (pageWidthPt * 2); // True width in points, from editor's double-page perspective
                                const textContentWidth = font.widthOfTextAtSize(textContent, fontSize);
                                
                                let textBlockCenterX_on_pdf: number;
                                if (coverLayout === 'single') {
                                    // Map editor's X (center of text block on double spread) to the single PDF page
                                    textBlockCenterX_on_pdf = ((overlay.style.x - 50) / 50) * pageWidthPt;
                                } else { // coverLayout === 'double'
                                    // Editor's X is already relative to the double spread width
                                    textBlockCenterX_on_pdf = (overlay.style.x / 100) * pdfCoverPageActualWidthPt;
                                }

                                let final_text_x_on_pdf: number;
                                if (overlay.style.textAlign === 'center') {
                                    final_text_x_on_pdf = textBlockCenterX_on_pdf - (textContentWidth / 2);
                                } else if (overlay.style.textAlign === 'right') {
                                    final_text_x_on_pdf = textBlockCenterX_on_pdf + (textBlockAbsoluteWidthPt / 2) - textContentWidth;
                                } else { // 'left'
                                    final_text_x_on_pdf = textBlockCenterX_on_pdf - (textBlockAbsoluteWidthPt / 2);
                                }
                                
                                const textBlockCenterY_on_pdf = pdfCoverPageActualHeightPt - ((overlay.style.y / 100) * pdfCoverPageActualHeightPt) - (fontSize * 0.3); // Approximation for baseline

                                const textOptions = {
                                    x: final_text_x_on_pdf,
                                    y: textBlockCenterY_on_pdf,
                                    font: font,
                                    size: fontSize,
                                    color: hexToRgb(overlay.style.color) || rgb(0, 0, 0),
                                    opacity: overlay.style.opacity,
                                    maxWidth: textBlockAbsoluteWidthPt, // Use the absolute width for maxWidth
                                    lineHeight: fontSize * 1.2,
                                };
                                
                                coverPage.drawText(textContent, textOptions);
                            } catch (textError) {
                                console.error("Error adding text overlay to cover PDF:", textError);
                            }
                        }
                    }
                    
                    // Save the PDF
                    coverPdfBytes = await tempCoverPdfDoc.save();
                    if (tempCoverPdfDoc.getPageCount() > 0) {
                        const firstPageSeparate = tempCoverPdfDoc.getPage(0);
                    }
                } else {
                    // console.error("[PDF DEBUG] Separate Cover: Failed to load image data for separate cover:", coverImageDataResult?.error);
                    toast.error("Failed to load image for separate cover.");
                }
            } catch (e) {
                console.error("Error generating separate cover PDF:", e);
                toast.error("Error generating separate cover.");
            }
        } else if (coverExportOption === 'include') {
            // Helper async function for cover generation to ensure all its awaits complete
            const generateIncludedCover = async () => {
              const coverPage = mainPdfDoc.insertPage(0, [pdfCoverPageActualWidthPt, pdfCoverPageActualHeightPt]);
              try {
                  // Load the cover image data, targeting single page width for the image itself
                  const coverImageTargetWidthPt = pageWidthPt;
                const coverImageTargetHeightPt = pageHeightPt;

                // --- Proportional Pan Calculation for Included Cover ---
                // This re-uses the logic from the 'separate cover' section, as it's identical.
                // Ensure coverStateImage and capturedCoverState are in scope and valid.
                const imgNativeWidthPx_incl = coverStateImage.naturalWidth || 1000;
                const imgNativeHeightPx_incl = coverStateImage.naturalHeight || 1000;
                const currentCoverScale_incl = capturedCoverState.scale;

                const imageRatioNative_incl = imgNativeWidthPx_incl / imgNativeHeightPx_incl;
                // For 'include' and 'double' coverLayout, the container for transform calculation is still a single page.
                // If coverLayout is 'single', it's also a single page.
                const containerRatioPt_incl = coverImageTargetWidthPt / coverImageTargetHeightPt;

                let baseRenderedW_pt_pdf_incl: number, baseRenderedH_pt_pdf_incl: number;

                // 'contain' fit logic for PDF container, to match ProjectCover's internal logic
                if (imageRatioNative_incl > containerRatioPt_incl) { // Image is wider than container (proportionally)
                    baseRenderedW_pt_pdf_incl = coverImageTargetWidthPt; // Fit to width
                    baseRenderedH_pt_pdf_incl = coverImageTargetWidthPt / imageRatioNative_incl; // Height will be smaller
                } else { // Image is taller or same aspect as container
                    baseRenderedH_pt_pdf_incl = coverImageTargetHeightPt; // Fit to height
                    baseRenderedW_pt_pdf_incl = coverImageTargetHeightPt * imageRatioNative_incl; // Width will be smaller
                }

                const scaledRenderedW_pdf_pt_incl = baseRenderedW_pt_pdf_incl * currentCoverScale_incl;
                const scaledRenderedH_pdf_pt_incl = baseRenderedH_pt_pdf_incl * currentCoverScale_incl;

                const pdfMaxPanX_pt_incl = Math.max(0, (scaledRenderedW_pdf_pt_incl - coverImageTargetWidthPt) / 2);
                const pdfMaxPanY_pt_incl = Math.max(0, (scaledRenderedH_pdf_pt_incl - coverImageTargetHeightPt) / 2);

                const editorTX_px_incl = capturedCoverState.translateX || 0;
                const editorTY_px_incl = capturedCoverState.translateY || 0;
                const editorMaxTX_px_incl = capturedCoverState.editorMaxTranslateX;
                const editorMaxTY_px_incl = capturedCoverState.editorMaxTranslateY;

                // Calculate offsetX and offsetY based on focalX, focalY, and scale,
                // similar to how spread images are handled.
                const userScale = capturedCoverState.scale;
                const userFocalX = capturedCoverState.focalX || 0.5;
                const userFocalY = capturedCoverState.focalY || 0.5;

                // baseRenderedW_pt_pdf_incl and baseRenderedH_pt_pdf_incl are the dimensions
                // of the image when it's made to 'cover' the coverImageTargetWidthPt x coverImageTargetHeightPt area,
                // before the user's 'userScale' is applied.
                const finalScaledWidthForFocalCalc = baseRenderedW_pt_pdf_incl * userScale;
                const finalScaledHeightForFocalCalc = baseRenderedH_pt_pdf_incl * userScale;

                const offsetX_for_api = (0.5 - userFocalX) * finalScaledWidthForFocalCalc;
                const offsetY_for_api = (0.5 - userFocalY) * finalScaledHeightForFocalCalc;

                const focalCoverTransformForPdf: { scale: number; offsetX: number; offsetY: number; fit: "cover" | "contain" } = {
                    scale: userScale,
                    offsetX: offsetX_for_api,
                    offsetY: offsetY_for_api,
                    fit: 'contain', // Match ProjectCover's internal 'contain'-then-scale logic
                };
                // --- End Focal Point Based Pan Calculation ---

                const coverImageDataResult = await window.electronAPI.loadImageDataForPdf(coverImagePath, {
                    targetWidthPt: coverImageTargetWidthPt, targetHeightPt: coverImageTargetHeightPt,
                    transform: focalCoverTransformForPdf, // Use the focal point based transform
                    colorSpace: colorSpace // Pass colorSpace
                });
                if (coverImageDataResult?.success && coverImageDataResult.imageBuffer) {
                    let pdfCoverImage: PDFImage;
                    const { imageType: coverImageType, imageBuffer: coverImageBuffer, iccProfileBuffer: coverIccProfileBuffer } = coverImageDataResult;

                    if (coverImageType === 'png') {
                        let decompressedCoverIccProfile: Uint8Array | undefined = undefined;
                        if (coverIccProfileBuffer) {
                            try {
                                const compressedProfile = coverIccProfileBuffer instanceof Uint8Array
                                    ? coverIccProfileBuffer
                                    : new Uint8Array(coverIccProfileBuffer);
                                decompressedCoverIccProfile = pako.inflate(compressedProfile);
                            } catch (inflateError) {
                                console.error(`Failed to decompress PNG ICC profile for included cover image:`, inflateError);
                                toast.warning(`Failed to decompress color profile for included cover. Embedding without profile.`, { duration: 5000 });
                            }
                        }
                        pdfCoverImage = await mainPdfDoc.embedPng(coverImageBuffer, { iccProfile: decompressedCoverIccProfile, iccProfileComponents: decompressedCoverIccProfile ? 3 : undefined });
                    } else { // JPEG
                        let rawCoverIccProfile: Uint8Array | undefined = undefined;
                        if (coverIccProfileBuffer) {
                           rawCoverIccProfile = coverIccProfileBuffer instanceof Uint8Array
                             ? coverIccProfileBuffer
                             : new Uint8Array(coverIccProfileBuffer);
                        }
                        pdfCoverImage = await mainPdfDoc.embedJpg(coverImageBuffer, { iccProfile: rawCoverIccProfile, iccProfileComponents: rawCoverIccProfile ? 3 : undefined });
                    }
                    // Draw image - Fix for double page layout
                    // For double page layout, we need to draw the full cover across the spread
                    if (coverLayout === 'double') {
                        // Always use white background color for the cover page
                        const whiteColor = rgb(1, 1, 1); // White color in RGB (1,1,1)
                        coverPage.drawRectangle({ x: 0, y: 0, width: pdfCoverPageActualWidthPt, height: pdfCoverPageActualHeightPt, color: whiteColor });
                        
                        // Draw the cover image on the right side of the spread
                        coverPage.drawImage(pdfCoverImage, {
                            x: pageWidthPt,
                            y: 0,
                            width: coverImageTargetWidthPt,
                            height: coverImageTargetHeightPt
                        });
                    } else {
                        // For single page layout, use white background and draw the cover image
                        const whiteColor = rgb(1, 1, 1); // White color in RGB (1,1,1)
                        coverPage.drawRectangle({ x: 0, y: 0, width: pdfCoverPageActualWidthPt, height: pdfCoverPageActualHeightPt, color: whiteColor });
                        
                        coverPage.drawImage(pdfCoverImage, {
                            x: 0,
                            y: 0,
                            width: coverImageTargetWidthPt,
                            height: coverImageTargetHeightPt
                        });
                    }
                    
                    // Add any text overlays for the cover
                    const coverTextOverlays = textOverlays.filter(overlay => overlay.spreadId === 'cover');
                    if (coverTextOverlays.length > 0) {
                        for (const overlay of coverTextOverlays) {
                            try {
                                // Get font based on the overlay's font family
                                const pdfFontName = fontFamilyToPdfFontMap[overlay.style.fontFamily] || StandardFonts.Helvetica;
                                const font = await mainPdfDoc.embedFont(pdfFontName);
                                
                                // Calculate text position and size
                                const fontSize = (overlay.style.fontSize / 100) * pdfCoverPageActualHeightPt;
                                const textContent = overlay.content;
                                const textBlockAbsoluteWidthPt = (overlay.style.width / 100) * (pageWidthPt * 2); // True width in points, from editor's double-page perspective
                                const textContentWidth = font.widthOfTextAtSize(textContent, fontSize);

                                let textBlockCenterX_on_pdf: number;
                                if (coverLayout === 'single') {
                                    textBlockCenterX_on_pdf = ((overlay.style.x - 50) / 50) * pageWidthPt;
                                } else { // coverLayout === 'double'
                                    textBlockCenterX_on_pdf = (overlay.style.x / 100) * pdfCoverPageActualWidthPt;
                                }

                                let final_text_x_on_pdf: number;
                                if (overlay.style.textAlign === 'center') {
                                    final_text_x_on_pdf = textBlockCenterX_on_pdf - (textContentWidth / 2);
                                } else if (overlay.style.textAlign === 'right') {
                                    final_text_x_on_pdf = textBlockCenterX_on_pdf + (textBlockAbsoluteWidthPt / 2) - textContentWidth;
                                } else { // 'left'
                                    final_text_x_on_pdf = textBlockCenterX_on_pdf - (textBlockAbsoluteWidthPt / 2);
                                }
                                
                                const textBlockCenterY_on_pdf = pdfCoverPageActualHeightPt - ((overlay.style.y / 100) * pdfCoverPageActualHeightPt) - (fontSize * 0.3); // Approximation for baseline
                                
                                const textOptions = {
                                    x: final_text_x_on_pdf,
                                    y: textBlockCenterY_on_pdf,
                                    font: font,
                                    size: fontSize,
                                    color: hexToRgb(overlay.style.color) || rgb(0, 0, 0),
                                    opacity: overlay.style.opacity,
                                    maxWidth: textBlockAbsoluteWidthPt, // Use the absolute width for maxWidth
                                    lineHeight: fontSize * 1.2,
                                };
                                
                                coverPage.drawText(textContent, textOptions);
                            } catch (textError) {
                                console.error("Error adding text overlay to included cover:", textError);
                            }
                        }
                    }
                } else {
                    console.error("Failed to load image data for included cover:", coverImageDataResult?.error);
                    toast.error("Failed to load image for included cover.");
                }
            } catch (e) {
                  console.error("Error drawing cover to main PDF:", e);
                    toast.error("Error drawing cover to main PDF.");
                }
                if (mainPdfDoc.getPageCount() > 0) {
                    try {
                        const firstPage = mainPdfDoc.getPage(0);
                    } catch (pageError) {
                        console.error(`Error getting first page:`, pageError);
                    }
                }
              }; // End of generateIncludedCover helper

              await generateIncludedCover(); // Await the helper function
        }
    }
    // --- End Cover Handling ---


    const totalSpreadsToExport = finalSpreadsForExport.length;
    if (totalSpreadsToExport === 0 && coverExportOption === 'none') {
        toast.error("No content (spreads or cover) to export.", { id: 'pdf-export-progress' });
        return;
    }

    const borderColorRgb = hexToRgb(imageBorderColor);
    const shouldDrawBorders = imageBorderSize > 0 && borderColorRgb;
    const bSize = imageBorderSize; // in points for PDF
    
    try {
      for (let i = 0; i < totalSpreadsToExport; i++) {
        if (exportCancelledRef.current) { toast.info(`PDF export cancelled before processing spread ${i + 1}.`); return; }
        const spread = finalSpreadsForExport[i];
        const template = templateMap[spread.templateId];
        toast.info(`Exporting spread ${i + 1} of ${totalSpreadsToExport}...`, { id: 'pdf-export-progress', duration: 30000 });

        if (pdfLayout === 'pages') {
          if (exportCancelledRef.current) { toast.info(`PDF export cancelled before adding pages for spread ${i + 1}.`); return; }
          const leftPage = mainPdfDoc.addPage([pageWidthPt, pageHeightPt]);
          const rightPage = mainPdfDoc.addPage([pageWidthPt, pageHeightPt]);
          const projectBgColorRgb = hexToRgb(backgroundColor);
          const bgColorToDraw = projectBgColorRgb ? projectBgColorRgb : rgb(1, 1, 1);
          leftPage.drawRectangle({ x: 0, y: 0, width: pageWidthPt, height: pageHeightPt, color: bgColorToDraw });
          rightPage.drawRectangle({ x: 0, y: 0, width: pageWidthPt, height: pageHeightPt, color: bgColorToDraw });

          // Draw project background image if it exists
          if (projectBackgroundImagePath) {
            try {
              let bgImagePath = projectBackgroundImagePath;
              if (window.bookProofsApp?.getUpdatedFilePath) {
                const updatedPath = window.bookProofsApp.getUpdatedFilePath(bgImagePath);
                if (updatedPath) bgImagePath = updatedPath;
              }

              // Get transform properties from BookEditor state
              const scale = backgroundImageZoom;
              const focalX = backgroundImagePanX / 100; // Convert 0-100% to 0-1
              const focalY = backgroundImagePanY / 100; // Convert 0-100% to 0-1
              const opacity = projectBackgroundImageOpacity !== undefined ? projectBackgroundImageOpacity : 1;

              // Find the image file to get natural dimensions
              let imgNaturalWidth: number | undefined;
              let imgNaturalHeight: number | undefined;
              const currentGlobalBgPath = projectBackgroundImagePathRef.current;

              if (currentGlobalBgPath && window.electronAPI?.getExifData) {
                try {
                  const exifResult = await window.electronAPI.getExifData(currentGlobalBgPath);
                  if (exifResult.success && exifResult.exifData?.dimensions) {
                    imgNaturalWidth = exifResult.exifData.dimensions.width;
                    imgNaturalHeight = exifResult.exifData.dimensions.height;
                  } else {
                    const imageFileEntry = imagesRef.current.find(img => img.originalPath === currentGlobalBgPath);
                    if (imageFileEntry && imageFileEntry.naturalWidth && imageFileEntry.naturalHeight) {
                      imgNaturalWidth = imageFileEntry.naturalWidth;
                      imgNaturalHeight = imageFileEntry.naturalHeight;
                    }
                  }
                } catch (exifError) {
                  console.error(`[PDF Export] Global BG: Error fetching EXIF for ${currentGlobalBgPath}:`, exifError);
                }
              } else if (currentGlobalBgPath) {
                const imageFileEntry = imagesRef.current.find(img => img.originalPath === currentGlobalBgPath);
                if (imageFileEntry && imageFileEntry.naturalWidth && imageFileEntry.naturalHeight) {
                  imgNaturalWidth = imageFileEntry.naturalWidth;
                  imgNaturalHeight = imageFileEntry.naturalHeight;
                }
              }

              if (!imgNaturalWidth || !imgNaturalHeight) {
                imgNaturalWidth = pageWidthPt * 2;
                imgNaturalHeight = pageHeightPt;
              }

              // Calculate dimensions and offsets for the background image
              const spreadRatio = (pageWidthPt * 2) / pageHeightPt;
              const imgRatio = imgNaturalWidth / imgNaturalHeight;
              
              let interimW: number, interimH: number;
              if (imgRatio > spreadRatio) {
                interimH = pageHeightPt;
                interimW = interimH * imgRatio;
              } else {
                interimW = pageWidthPt * 2;
                interimH = interimW / imgRatio;
              }
              
              const scaledW = interimW * scale;
              const scaledH = interimH * scale;
              
              const offsetX = (0.5 - focalX) * scaledW;
              const offsetY = (0.5 - focalY) * scaledH;

              // Load and draw the background image on both pages
              const bgImageDataResult = await window.electronAPI.loadImageDataForPdf(bgImagePath, {
                targetWidthPt: pageWidthPt * 2,
                targetHeightPt: pageHeightPt,
                transform: {
                  scale: scale,
                  fit: 'cover',
                  offsetX: offsetX,
                  offsetY: offsetY
                },
                colorSpace: colorSpace
              });

              if (bgImageDataResult && bgImageDataResult.success && bgImageDataResult.imageBuffer) {
                let pdfBgImage: PDFImage;
                const { imageType, imageBuffer, iccProfileBuffer: rawOrCompressedProfileBuffer } = bgImageDataResult;

                // ICC Profile handling
                if (imageType === 'png') {
                  let decompressedIccProfile: Uint8Array | undefined = undefined;
                  if (rawOrCompressedProfileBuffer) {
                    try {
                      const compressedProfile = rawOrCompressedProfileBuffer instanceof Uint8Array
                        ? rawOrCompressedProfileBuffer
                        : new Uint8Array(rawOrCompressedProfileBuffer);
                      decompressedIccProfile = pako.inflate(compressedProfile);
                    } catch (inflateError) {
                      console.error(`Failed to decompress PNG ICC profile for project background image:`, inflateError);
                    }
                  }
                  pdfBgImage = await mainPdfDoc.embedPng(imageBuffer, { iccProfile: decompressedIccProfile, iccProfileComponents: decompressedIccProfile ? 3 : undefined });
                } else { // JPEG
                  let rawIccProfile: Uint8Array | undefined = undefined;
                  if (rawOrCompressedProfileBuffer) {
                    rawIccProfile = rawOrCompressedProfileBuffer instanceof Uint8Array
                      ? rawOrCompressedProfileBuffer
                      : new Uint8Array(rawOrCompressedProfileBuffer);
                  }
                  pdfBgImage = await mainPdfDoc.embedJpg(imageBuffer, { iccProfile: rawIccProfile, iccProfileComponents: rawIccProfile ? 3 : undefined });
                }
                
                // Use clipping to draw left half of the background on left page
                leftPage.pushOperators(pushGraphicsState());
                leftPage.drawRectangle({
                  x: 0,
                  y: 0,
                  width: pageWidthPt,
                  height: pageHeightPt,
                  borderWidth: 0,
                  color: undefined,
                  borderColor: undefined,
                  opacity: 1,
                });
                leftPage.pushOperators(clip());
                leftPage.drawImage(pdfBgImage, {
                  x: 0,
                  y: 0,
                  width: pageWidthPt * 2, // Full spread width
                  height: pageHeightPt,
                  opacity: opacity
                });
                leftPage.pushOperators(popGraphicsState());
                
                // Use clipping to draw right half of the background on right page
                rightPage.pushOperators(pushGraphicsState());
                rightPage.drawRectangle({
                  x: 0,
                  y: 0,
                  width: pageWidthPt,
                  height: pageHeightPt,
                  borderWidth: 0,
                  color: undefined,
                  borderColor: undefined,
                  opacity: 1,
                });
                rightPage.pushOperators(clip());
                rightPage.drawImage(pdfBgImage, {
                  x: -pageWidthPt, // Offset to show right half
                  y: 0,
                  width: pageWidthPt * 2, // Full spread width
                  height: pageHeightPt,
                  opacity: opacity
                });
                rightPage.pushOperators(popGraphicsState());
              }
            } catch (bgImgError) {
              console.error("Error processing project background image for PDF:", bgImgError);
            }
          }
          
          // Draw spread background image if it exists
          if (spread.spreadBackgroundData?.imagePath) {
            try {
              let bgImagePath = spread.spreadBackgroundData.imagePath;
              if (window.bookProofsApp?.getUpdatedFilePath) {
                const updatedPath = window.bookProofsApp.getUpdatedFilePath(bgImagePath);
                if (updatedPath) bgImagePath = updatedPath;
              }

              // Get transform properties from the spread background data
              const { scale = 1, focalX = 0.5, focalY = 0.5 } = spread.spreadBackgroundData.transform;
              const opacity = spread.spreadBackgroundData.opacity ?? 1;

              // Calculate dimensions using 'cover' logic
              const spreadRatio = (pageWidthPt * 2) / pageHeightPt;
              
              // Get natural image dimensions from the spread background data
              const imgNaturalWidth = spread.spreadBackgroundData.naturalWidth || 1;
              const imgNaturalHeight = spread.spreadBackgroundData.naturalHeight || 1;
              const imgRatio = imgNaturalWidth / imgNaturalHeight;
              
              let interimW: number, interimH: number;
              if (imgRatio > spreadRatio) {
                interimH = pageHeightPt;
                interimW = interimH * imgRatio;
              } else {
                interimW = pageWidthPt * 2;
                interimH = interimW / imgRatio;
              }
              
              const scaledW = interimW * scale;
              const scaledH = interimH * scale;
              
              const offsetX = (0.5 - focalX) * scaledW;
              const offsetY = (0.5 - focalY) * scaledH;
              
              const bgImageDataResult = await window.electronAPI.loadImageDataForPdf(bgImagePath, {
                targetWidthPt: pageWidthPt * 2,
                targetHeightPt: pageHeightPt,
                transform: {
                  scale: scale,
                  fit: 'cover',
                  offsetX: offsetX,
                  offsetY: offsetY
                },
                colorSpace: colorSpace
              });

              if (bgImageDataResult && bgImageDataResult.success && bgImageDataResult.imageBuffer) {
                let pdfBgImage: PDFImage;
                const { imageType, imageBuffer, iccProfileBuffer: rawOrCompressedProfileBuffer } = bgImageDataResult;

                if (imageType === 'png') {
                  let decompressedIccProfile: Uint8Array | undefined = undefined;
                  if (rawOrCompressedProfileBuffer) {
                    try {
                      const compressedProfile = rawOrCompressedProfileBuffer instanceof Uint8Array
                        ? rawOrCompressedProfileBuffer
                        : new Uint8Array(rawOrCompressedProfileBuffer);
                      decompressedIccProfile = pako.inflate(compressedProfile);
                    } catch (inflateError) {
                      console.error(`Failed to decompress PNG ICC profile for spread background image:`, inflateError);
                    }
                  }
                  pdfBgImage = await mainPdfDoc.embedPng(imageBuffer, {
                    iccProfile: decompressedIccProfile,
                    iccProfileComponents: decompressedIccProfile ? 3 : undefined
                  });
                } else { // JPEG
                  let rawIccProfile: Uint8Array | undefined = undefined;
                  if (rawOrCompressedProfileBuffer) {
                    rawIccProfile = rawOrCompressedProfileBuffer instanceof Uint8Array
                      ? rawOrCompressedProfileBuffer
                      : new Uint8Array(rawOrCompressedProfileBuffer);
                  }
                  pdfBgImage = await mainPdfDoc.embedJpg(imageBuffer, {
                    iccProfile: rawIccProfile,
                    iccProfileComponents: rawIccProfile ? 3 : undefined
                  });
                }
                
                // Use clipping to draw left half of the background on left page
                leftPage.pushOperators(pushGraphicsState());
                leftPage.drawRectangle({
                  x: 0,
                  y: 0,
                  width: pageWidthPt,
                  height: pageHeightPt,
                  borderWidth: 0,
                  color: undefined,
                  borderColor: undefined,
                  opacity: 1,
                });
                leftPage.pushOperators(clip());
                leftPage.drawImage(pdfBgImage, {
                  x: 0,
                  y: 0,
                  width: pageWidthPt * 2, // Full spread width
                  height: pageHeightPt,
                  opacity: opacity
                });
                leftPage.pushOperators(popGraphicsState());
                
                // Use clipping to draw right half of the background on right page
                rightPage.pushOperators(pushGraphicsState());
                rightPage.drawRectangle({
                  x: 0,
                  y: 0,
                  width: pageWidthPt,
                  height: pageHeightPt,
                  borderWidth: 0,
                  color: undefined,
                  borderColor: undefined,
                  opacity: 1,
                });
                rightPage.pushOperators(clip());
                rightPage.drawImage(pdfBgImage, {
                  x: -pageWidthPt, // Offset to show right half
                  y: 0,
                  width: pageWidthPt * 2, // Full spread width
                  height: pageHeightPt,
                  opacity: opacity
                });
                rightPage.pushOperators(popGraphicsState());
              }
            } catch (bgImgError) {
              console.error(`Error processing spread background image:`, bgImgError);
            }
          }

          if (template && template.id !== '__blank__') {
            const spreadAdjustedLayout = adjustTemplateForGap(template, imageGap, pageWidthPt * 2, pageHeightPt);
            for (const placement of spread.images) {
              if (!placement.imageId) continue;
              const imageFile = images.find(img => img.id === placement.imageId);
              if (!imageFile) continue;
              const adjustedPlaceholder = spreadAdjustedLayout.find(p => p.id === placement.placeholderId);
              if (!adjustedPlaceholder || !Number.isFinite(adjustedPlaceholder.adjustedX_pt) || adjustedPlaceholder.adjustedWidth_pt <= 0) continue;
              try {
                const { scale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain', rotation = 0 } = placement.transform;
                const placeholderWidth = adjustedPlaceholder.adjustedWidth_pt;
                const placeholderHeight = adjustedPlaceholder.adjustedHeight_pt;
                const imgNaturalWidth = imageFile.naturalWidth || 1;
                const imgNaturalHeight = imageFile.naturalHeight || 1;
                const imgRatio = imgNaturalWidth / imgNaturalHeight;
                const placeholderRatio = placeholderWidth / placeholderHeight;
                let interimW, interimH;
                if (fit === 'cover') {
                  if (imgRatio > placeholderRatio) { interimH = placeholderHeight; interimW = interimH * imgRatio; }
                  else { interimW = placeholderWidth; interimH = interimW / imgRatio; }
                } else { // contain
                  if (imgRatio > placeholderRatio) { interimW = placeholderWidth; interimH = interimW / imgRatio; }
                  else { interimH = placeholderHeight; interimW = interimH * imgRatio; }
                }
                const scaledW = interimW * scale; const scaledH = interimH * scale;
                const offsetX = (0.5 - focalX) * scaledW; const offsetY = (0.5 - focalY) * scaledH;
                let imagePath = imageFile.originalPath;
                if (window.bookProofsApp?.getUpdatedFilePath) {
                  const updatedPath = window.bookProofsApp.getUpdatedFilePath(imageFile.originalPath);
                  if (updatedPath) imagePath = updatedPath;
                }
                const imageDataResult = await window.electronAPI.loadImageDataForPdf(imagePath, {
                  transform: { scale, offsetX, offsetY, fit, rotation },
                  targetWidthPt: placeholderWidth, targetHeightPt: placeholderHeight,
                  colorSpace: colorSpace
                });
                if (!imageDataResult?.success || !imageDataResult.imageBuffer) throw new Error(imageDataResult?.error || 'Failed to load image data');
                let pdfImage: PDFImage;
                const { imageType: fgImageType, imageBuffer: fgImageBuffer, iccProfileBuffer: fgIccProfileBuffer } = imageDataResult;
                if (fgImageType === 'png') {
                  let decompressedFgIccProfile: Uint8Array | undefined = undefined;
                  if (fgIccProfileBuffer) { try { const c = fgIccProfileBuffer instanceof Uint8Array ? fgIccProfileBuffer : new Uint8Array(fgIccProfileBuffer); decompressedFgIccProfile = pako.inflate(c); } catch (e) { console.error(e);}}
                  pdfImage = await mainPdfDoc.embedPng(fgImageBuffer, { iccProfile: decompressedFgIccProfile, iccProfileComponents: decompressedFgIccProfile ? 3 : undefined });
                } else {
                  let rawFgIccProfile: Uint8Array | undefined = undefined;
                  if (fgIccProfileBuffer) { rawFgIccProfile = fgIccProfileBuffer instanceof Uint8Array ? fgIccProfileBuffer : new Uint8Array(fgIccProfileBuffer); }
                  pdfImage = await mainPdfDoc.embedJpg(fgImageBuffer, { iccProfile: rawFgIccProfile, iccProfileComponents: rawFgIccProfile ? 3 : undefined });
                }
                const placeholderX_onSpread = adjustedPlaceholder.adjustedX_pt;
                const placeholderY_onSpread_pdf = pageHeightPt - adjustedPlaceholder.adjustedY_pt - placeholderHeight;
                let drawWidth, drawHeight, drawX_on_page, drawY_on_page;
                if (fit === 'contain') {
                  if (pdfImage.width / pdfImage.height > placeholderRatio) { drawWidth = placeholderWidth; drawHeight = drawWidth / (pdfImage.width / pdfImage.height); drawX_on_page = 0; drawY_on_page = (placeholderHeight - drawHeight) / 2; }
                  else { drawHeight = placeholderHeight; drawWidth = drawHeight * (pdfImage.width / pdfImage.height); drawY_on_page = 0; drawX_on_page = (placeholderWidth - drawWidth) / 2; }
                } else { drawWidth = placeholderWidth; drawHeight = placeholderHeight; drawX_on_page = 0; drawY_on_page = 0; }
                
                // Check if the image spans across the gutter (center of spread)
                const placeholderLeft = placeholderX_onSpread;
                const placeholderRight = placeholderX_onSpread + placeholderWidth;
                const spansGutter = placeholderLeft < pageWidthPt && placeholderRight > pageWidthPt;
                
                if (spansGutter) {
                  // Image spans the gutter - draw on both pages
                  
                  // Calculate how much of the image is on each page
                  const leftPageWidth = pageWidthPt - placeholderLeft;
                  const rightPageWidth = placeholderRight - pageWidthPt;
                  
                  // Calculate the proportion of the image width on each page
                  const leftProportion = leftPageWidth / placeholderWidth;
                  const rightProportion = rightPageWidth / placeholderWidth;
                  
                  // Draw left portion on left page
                  // Draw left portion on left page
                  leftPage.pushOperators(pushGraphicsState());
                  leftPage.drawRectangle({
                    x: placeholderLeft,
                    y: placeholderY_onSpread_pdf + drawY_on_page,
                    width: leftPageWidth,
                    height: drawHeight,
                    borderWidth: 0,
                    color: undefined,
                    borderColor: undefined,
                    opacity: 1,
                  });
                  leftPage.pushOperators(clip());
                  leftPage.drawImage(pdfImage, {
                    x: placeholderX_onSpread,
                    y: placeholderY_onSpread_pdf + drawY_on_page,
                    width: drawWidth,
                    height: drawHeight
                  });
                  leftPage.pushOperators(popGraphicsState());

                  if (shouldDrawBorders && borderColorRgb) {
                    const bleedIsActive = projectBleed > 0;
                    const edgeTolerancePt = 2.0;

                    // --- Borders for Left Page Portion ---
                    const isTopEdgeOutside_L = adjustedPlaceholder.adjustedY_pt < edgeTolerancePt;
                    const isBottomEdgeOutside_L = (adjustedPlaceholder.adjustedY_pt + placeholderHeight) >= (pageHeightPt - edgeTolerancePt);
                    const isLeftEdgeOfPage_L = placeholderX_onSpread < edgeTolerancePt;
                    const isRightEdgeOfPage_L = (placeholderX_onSpread + placeholderWidth) >= (pageWidthPt - edgeTolerancePt); // Gutter edge for left page

                    let showTopBorder_L = true, showBottomBorder_L = true, showLeftBorder_L = true, showRightBorder_L = true;

                    if (fit === 'contain') {
                      // For spanning 'contain', image is centered within the *full* placeholder first, then clipped.
                      // Gaps are relative to the full placeholder, then we see which part of image+border falls on this page.
                      const gapLeftImageToPlaceholderPt_pdf = drawX_on_page; // drawX_on_page is offset within full placeholder
                      const gapRightImageToPlaceholderPt_pdf = placeholderWidth - (drawX_on_page + drawWidth);
                      const gapTopImageToPlaceholderPt_pdf = placeholderHeight - (drawY_on_page + drawHeight);
                      const gapBottomImageToPlaceholderPt_pdf = drawY_on_page;

                      const isImageNearTopPlaceholderEdge_pdf = gapTopImageToPlaceholderPt_pdf < edgeTolerancePt;
                      const isImageNearBottomPlaceholderEdge_pdf = gapBottomImageToPlaceholderPt_pdf < edgeTolerancePt;
                      const isImageNearLeftPlaceholderEdge_pdf = gapLeftImageToPlaceholderPt_pdf < edgeTolerancePt;
                      const isImageNearRightPlaceholderEdge_pdf = gapRightImageToPlaceholderPt_pdf < edgeTolerancePt;

                      showTopBorder_L = !(isTopEdgeOutside_L && isImageNearTopPlaceholderEdge_pdf && bleedIsActive);
                      showBottomBorder_L = !(isBottomEdgeOutside_L && isImageNearBottomPlaceholderEdge_pdf && bleedIsActive);
                      showLeftBorder_L = !(isLeftEdgeOfPage_L && isImageNearLeftPlaceholderEdge_pdf && bleedIsActive);
                      showRightBorder_L = false; // Never show right border for left part of spanning image (it's at gutter)
                    } else { // fit === 'cover'
                      showTopBorder_L = !(isTopEdgeOutside_L && bleedIsActive);
                      showBottomBorder_L = !(isBottomEdgeOutside_L && bleedIsActive);
                      showLeftBorder_L = !(isLeftEdgeOfPage_L && bleedIsActive);
                      showRightBorder_L = false; // Never show right border for left part of spanning image
                    }

                    // Define the placeholder's bounding box on the spread for border drawing
                    const borderBoxX_Spread_L = placeholderX_onSpread;
                    const borderBoxY_Spread_L = placeholderY_onSpread_pdf;
                    const borderBoxW_Spread_L = placeholderWidth;
                    const borderBoxH_Spread_L = placeholderHeight;

                    // Clip border drawing to the left page portion
                    leftPage.pushOperators(pushGraphicsState());
                    leftPage.drawRectangle({ x: placeholderLeft, y: placeholderY_onSpread_pdf, width: leftPageWidth, height: placeholderHeight, color: undefined, borderWidth: 0 });
                    leftPage.pushOperators(clip());
                    if (showTopBorder_L) leftPage.drawRectangle({ x: borderBoxX_Spread_L, y: borderBoxY_Spread_L + borderBoxH_Spread_L - bSize, width: borderBoxW_Spread_L, height: bSize, color: borderColorRgb });
                    if (showBottomBorder_L) leftPage.drawRectangle({ x: borderBoxX_Spread_L, y: borderBoxY_Spread_L, width: borderBoxW_Spread_L, height: bSize, color: borderColorRgb });
                    if (showLeftBorder_L) leftPage.drawRectangle({ x: borderBoxX_Spread_L, y: borderBoxY_Spread_L, width: bSize, height: borderBoxH_Spread_L, color: borderColorRgb });
                    // No right border for left part of spanning image
                    leftPage.pushOperators(popGraphicsState());
                  }
                  
                  // Draw right portion on right page
                  rightPage.pushOperators(pushGraphicsState());
                  rightPage.drawRectangle({
                    x: 0, // Clip from the left edge of the right page
                    y: placeholderY_onSpread_pdf + drawY_on_page,
                    width: rightPageWidth,
                    height: drawHeight,
                    borderWidth: 0,
                    color: undefined,
                    borderColor: undefined,
                    opacity: 1,
                  });
                  rightPage.pushOperators(clip());
                  rightPage.drawImage(pdfImage, {
                    x: placeholderX_onSpread - pageWidthPt, // Adjust image position relative to the right page
                    y: placeholderY_onSpread_pdf + drawY_on_page,
                    width: drawWidth,
                    height: drawHeight
                  });
                  rightPage.pushOperators(popGraphicsState());

                  if (shouldDrawBorders && borderColorRgb) {
                    const bleedIsActive = projectBleed > 0;
                    const edgeTolerancePt = 2.0;

                    // --- Borders for Right Page Portion ---
                    const isTopEdgeOutside_R = adjustedPlaceholder.adjustedY_pt < edgeTolerancePt;
                    const isBottomEdgeOutside_R = (adjustedPlaceholder.adjustedY_pt + placeholderHeight) >= (pageHeightPt - edgeTolerancePt);
                    const isLeftEdgeOfPage_R = (placeholderX_onSpread - pageWidthPt) < edgeTolerancePt; // Gutter edge for right page
                    const isRightEdgeOfPage_R = ((placeholderX_onSpread - pageWidthPt) + placeholderWidth) >= (pageWidthPt - edgeTolerancePt);

                    let showTopBorder_R = true, showBottomBorder_R = true, showLeftBorder_R = true, showRightBorder_R = true;

                    if (fit === 'contain') {
                      const gapLeftImageToPlaceholderPt_pdf = drawX_on_page;
                      const gapRightImageToPlaceholderPt_pdf = placeholderWidth - (drawX_on_page + drawWidth);
                      const gapTopImageToPlaceholderPt_pdf = placeholderHeight - (drawY_on_page + drawHeight);
                      const gapBottomImageToPlaceholderPt_pdf = drawY_on_page;

                      const isImageNearTopPlaceholderEdge_pdf = gapTopImageToPlaceholderPt_pdf < edgeTolerancePt;
                      const isImageNearBottomPlaceholderEdge_pdf = gapBottomImageToPlaceholderPt_pdf < edgeTolerancePt;
                      const isImageNearLeftPlaceholderEdge_pdf = gapLeftImageToPlaceholderPt_pdf < edgeTolerancePt;
                      const isImageNearRightPlaceholderEdge_pdf = gapRightImageToPlaceholderPt_pdf < edgeTolerancePt;

                      showTopBorder_R = !(isTopEdgeOutside_R && isImageNearTopPlaceholderEdge_pdf && bleedIsActive);
                      showBottomBorder_R = !(isBottomEdgeOutside_R && isImageNearBottomPlaceholderEdge_pdf && bleedIsActive);
                      showLeftBorder_R = false; // Never show left border for right part of spanning image
                      showRightBorder_R = !(isRightEdgeOfPage_R && isImageNearRightPlaceholderEdge_pdf && bleedIsActive);
                    } else { // fit === 'cover'
                      showTopBorder_R = !(isTopEdgeOutside_R && bleedIsActive);
                      showBottomBorder_R = !(isBottomEdgeOutside_R && bleedIsActive);
                      showLeftBorder_R = false; // Never show left border for right part of spanning image
                      showRightBorder_R = !(isRightEdgeOfPage_R && bleedIsActive);
                    }

                    // Define the placeholder's bounding box on the spread for border drawing
                    const borderBoxX_Spread_R = placeholderX_onSpread;
                    const borderBoxY_Spread_R = placeholderY_onSpread_pdf;
                    const borderBoxW_Spread_R = placeholderWidth;
                    const borderBoxH_Spread_R = placeholderHeight;

                    // Clip border drawing to the right page portion
                    rightPage.pushOperators(pushGraphicsState());
                    rightPage.drawRectangle({ x: 0, y: placeholderY_onSpread_pdf, width: rightPageWidth, height: placeholderHeight, color: undefined, borderWidth: 0 });
                    rightPage.pushOperators(clip());

                    // Draw borders relative to the full placeholder, but offset for the right page view
                    if (showTopBorder_R) rightPage.drawRectangle({ x: borderBoxX_Spread_R - pageWidthPt, y: borderBoxY_Spread_R + borderBoxH_Spread_R - bSize, width: borderBoxW_Spread_R, height: bSize, color: borderColorRgb });
                    if (showBottomBorder_R) rightPage.drawRectangle({ x: borderBoxX_Spread_R - pageWidthPt, y: borderBoxY_Spread_R, width: borderBoxW_Spread_R, height: bSize, color: borderColorRgb });
                    // No left border for right part of spanning image (gutter edge)
                    if (showRightBorder_R) rightPage.drawRectangle({ x: borderBoxX_Spread_R - pageWidthPt + borderBoxW_Spread_R - bSize, y: borderBoxY_Spread_R, width: bSize, height: borderBoxH_Spread_R, color: borderColorRgb });
                    rightPage.pushOperators(popGraphicsState());
                  }
                } else {
                  // Image doesn't span the gutter - draw on a single page
                  const placeholderCenterXinSpread = placeholderX_onSpread + placeholderWidth / 2;
                  const targetPageForImage = placeholderCenterXinSpread < pageWidthPt ? leftPage : rightPage;
                  const xOffsetForPageDraw = placeholderCenterXinSpread < pageWidthPt ? placeholderX_onSpread : placeholderX_onSpread - pageWidthPt;
                  
                  const imgDrawX_abs = placeholderX_onSpread + drawX_on_page; // Absolute X on the full spread for the image content
                  const imgDrawY_abs_pdf = placeholderY_onSpread_pdf + drawY_on_page; // Absolute Y (PDF coords) for the image content
                  const imgDrawWidth_content = drawWidth;
                  const imgDrawHeight_content = drawHeight;

                  // targetPageForImage and xOffsetForPageDraw are already defined above for this block

                  targetPageForImage.drawImage(pdfImage, {
                    x: xOffsetForPageDraw + drawX_on_page, // drawX_on_page is offset within placeholder
                    y: placeholderY_onSpread_pdf + drawY_on_page, // placeholderY_onSpread_pdf is bottom of placeholder, drawY_on_page is offset within
                    width: imgDrawWidth_content,
                    height: imgDrawHeight_content
                  });

                  if (shouldDrawBorders && borderColorRgb) {
                    const bleedIsActive = projectBleed > 0;
                    const edgeTolerancePt = 2.0;

                    // const isCurrentPageLeft = targetPageForImage === leftPage; // Already determined by targetPageForImage
                    // const placeholderX_onThisPage = isCurrentPageLeft ? placeholderX_onSpread : placeholderX_onSpread - pageWidthPt; // xOffsetForPageDraw serves this purpose

                    const isTopEdgeOutside = adjustedPlaceholder.adjustedY_pt < edgeTolerancePt;
                    const isBottomEdgeOutside = (adjustedPlaceholder.adjustedY_pt + placeholderHeight) >= (pageHeightPt - edgeTolerancePt);
                    const isLeftEdgeOfPage = xOffsetForPageDraw < edgeTolerancePt; // Use xOffsetForPageDraw for relative X
                    const isRightEdgeOfPage = (xOffsetForPageDraw + placeholderWidth) >= (pageWidthPt - edgeTolerancePt);

                    let showTopBorder_pdf = true, showBottomBorder_pdf = true, showLeftBorder_pdf = true, showRightBorder_pdf = true;

                    if (fit === 'contain') {
                      const gapLeftImageToPlaceholderPt_pdf = drawX_on_page;
                      const gapRightImageToPlaceholderPt_pdf = placeholderWidth - (drawX_on_page + imgDrawWidth_content);
                      const gapTopImageToPlaceholderPt_pdf = placeholderHeight - (drawY_on_page + imgDrawHeight_content);
                      const gapBottomImageToPlaceholderPt_pdf = drawY_on_page;

                      const isImageNearTopPlaceholderEdge_pdf = gapTopImageToPlaceholderPt_pdf < edgeTolerancePt;
                      const isImageNearBottomPlaceholderEdge_pdf = gapBottomImageToPlaceholderPt_pdf < edgeTolerancePt;
                      const isImageNearLeftPlaceholderEdge_pdf = gapLeftImageToPlaceholderPt_pdf < edgeTolerancePt;
                      const isImageNearRightPlaceholderEdge_pdf = gapRightImageToPlaceholderPt_pdf < edgeTolerancePt;

                      showTopBorder_pdf = !(isTopEdgeOutside && isImageNearTopPlaceholderEdge_pdf && bleedIsActive);
                      showBottomBorder_pdf = !(isBottomEdgeOutside && isImageNearBottomPlaceholderEdge_pdf && bleedIsActive);
                      showLeftBorder_pdf = !(isLeftEdgeOfPage && isImageNearLeftPlaceholderEdge_pdf && bleedIsActive);
                      showRightBorder_pdf = !(isRightEdgeOfPage && isImageNearRightPlaceholderEdge_pdf && bleedIsActive);
                    } else { // fit === 'cover'
                      showTopBorder_pdf = !(isTopEdgeOutside && bleedIsActive);
                      showBottomBorder_pdf = !(isBottomEdgeOutside && bleedIsActive);
                      showLeftBorder_pdf = !(isLeftEdgeOfPage && bleedIsActive);
                      showRightBorder_pdf = !(isRightEdgeOfPage && bleedIsActive);
                    }

                    // Border drawing box always aligns with the placeholder's full extents on this page
                    const borderBoxX_onPage = xOffsetForPageDraw; // Placeholder's X relative to this page
                    const borderBoxY_onPage = placeholderY_onSpread_pdf; // Placeholder's Y (PDF coords) on spread
                    const borderBoxWidth_onPage = placeholderWidth;    // Full placeholder width
                    const borderBoxHeight_onPage = placeholderHeight;  // Full placeholder height

                    if (showTopBorder_pdf) targetPageForImage.drawRectangle({ x: borderBoxX_onPage, y: borderBoxY_onPage + borderBoxHeight_onPage - bSize, width: borderBoxWidth_onPage, height: bSize, color: borderColorRgb });
                    if (showBottomBorder_pdf) targetPageForImage.drawRectangle({ x: borderBoxX_onPage, y: borderBoxY_onPage, width: borderBoxWidth_onPage, height: bSize, color: borderColorRgb });
                    if (showLeftBorder_pdf) targetPageForImage.drawRectangle({ x: borderBoxX_onPage, y: borderBoxY_onPage, width: bSize, height: borderBoxHeight_onPage, color: borderColorRgb });
                    if (showRightBorder_pdf) targetPageForImage.drawRectangle({ x: borderBoxX_onPage + borderBoxWidth_onPage - bSize, y: borderBoxY_onPage, width: bSize, height: borderBoxHeight_onPage, color: borderColorRgb });
                  }
                }
              } catch (e) { console.error("Error processing image for PDF (pages layout):", e); }
            }

            const spreadTextOverlays = textOverlays.filter(overlay => overlay.spreadId === spread.id);
            if (spreadTextOverlays.length > 0) {
              for (const overlay of spreadTextOverlays) {
                try {
                  const pdfFontName = fontFamilyToPdfFontMap[overlay.style.fontFamily] || StandardFonts.Helvetica;
                  const font = await mainPdfDoc.embedFont(pdfFontName);
                  const spreadFullWidthPt = pageWidthPt * 2;
                  const fontSize = (overlay.style.fontSize / 100) * pageHeightPt;
                  const textContent = overlay.content;
                  const textBlockAbsoluteWidthPt = (overlay.style.width / 100) * spreadFullWidthPt;
                  const textContentWidth = font.widthOfTextAtSize(textContent, fontSize);
                  const textBlockCenterXOnSpread = (overlay.style.x / 100) * spreadFullWidthPt;
                  let final_text_x_on_page_coord: number;

                  if (overlay.style.textAlign === 'center') { final_text_x_on_page_coord = textBlockCenterXOnSpread - (textContentWidth / 2); }
                  else if (overlay.style.textAlign === 'right') { final_text_x_on_page_coord = textBlockCenterXOnSpread + (textBlockAbsoluteWidthPt / 2) - textContentWidth; }
                  else { final_text_x_on_page_coord = textBlockCenterXOnSpread - (textBlockAbsoluteWidthPt / 2); }
                  const textY_on_page_coord = pageHeightPt - ((overlay.style.y / 100) * pageHeightPt) - (fontSize * 0.3);

                  const textOptions = { font: font, size: fontSize, color: hexToRgb(overlay.style.color) || rgb(0,0,0), opacity: overlay.style.opacity, maxWidth: textBlockAbsoluteWidthPt, lineHeight: fontSize * 1.2 };
                  
                  const textBlockStartX_onSpread = textBlockCenterXOnSpread - (textBlockAbsoluteWidthPt / 2);
                  const textBlockEndX_onSpread = textBlockCenterXOnSpread + (textBlockAbsoluteWidthPt / 2);
                  const spansGutter = textBlockStartX_onSpread < pageWidthPt && textBlockEndX_onSpread > pageWidthPt;

                  if (spansGutter) {
                    // Text spans the gutter - draw on both pages with clipping

                    // --- Draw on Left Page (Clipped) ---
                    // Calculate the actual visible width of the text block on the left page
                    const leftVisibleTextWidth = Math.min(pageWidthPt, textBlockEndX_onSpread) - textBlockStartX_onSpread;

                    if (leftVisibleTextWidth > 0) {
                      leftPage.pushOperators(pushGraphicsState());
                      leftPage.drawRectangle({ // This rectangle defines the clipping area
                        x: textBlockStartX_onSpread,
                        y: 0, // Clip full page height for simplicity of text rendering
                        width: leftVisibleTextWidth,
                        height: pageHeightPt,
                        color: undefined, // Explicitly no fill
                        borderColor: undefined, // Explicitly no border
                        borderWidth: 0,
                        opacity: 0, // Make it fully transparent
                      });
                      leftPage.pushOperators(clip());
                      // Draw the full text, it will be clipped by the rectangle above
                      leftPage.drawText(textContent, { ...textOptions, x: final_text_x_on_page_coord, y: textY_on_page_coord });
                      leftPage.pushOperators(popGraphicsState());
                    }

                    // --- Draw on Right Page (Clipped) ---
                    // Calculate the actual visible width of the text block on the right page
                    const rightVisibleTextWidth = textBlockEndX_onSpread - Math.max(pageWidthPt, textBlockStartX_onSpread);

                    if (rightVisibleTextWidth > 0) {
                      rightPage.pushOperators(pushGraphicsState());
                      rightPage.drawRectangle({ // This rectangle defines the clipping area
                        x: 0, // Clipping starts from the left edge of the right page
                        y: 0, // Clip full page height
                        width: rightVisibleTextWidth,
                        height: pageHeightPt,
                        color: undefined, // Explicitly no fill
                        borderColor: undefined, // Explicitly no border
                        borderWidth: 0,
                        opacity: 0, // Make it fully transparent
                      });
                      rightPage.pushOperators(clip());
                      // Draw the full text, adjusted for the right page, it will be clipped
                      rightPage.drawText(textContent, { ...textOptions, x: final_text_x_on_page_coord - pageWidthPt, y: textY_on_page_coord });
                      rightPage.pushOperators(popGraphicsState());
                    }
                  } else {
                    // Text does not span the gutter - draw on a single page
                    if (textBlockEndX_onSpread <= pageWidthPt) { // Entirely on Left page
                      leftPage.drawText(textContent, {...textOptions, x: final_text_x_on_page_coord, y: textY_on_page_coord });
                    } else { // Entirely on Right page (or starts on right page)
                      rightPage.drawText(textContent, { ...textOptions, x: final_text_x_on_page_coord - pageWidthPt, y: textY_on_page_coord });
                    }
                  }
                } catch (textError) { console.error(`Error adding text overlay ${overlay.id} to spread ${spread.id} (pages layout):`, textError); }
              }
            }
          }
        } else if (pdfLayout === 'spreads') {
          if (exportCancelledRef.current) { toast.info(`PDF export cancelled before adding spread page ${i + 1}.`); return; }
          const page = mainPdfDoc.addPage([pageWidthPt * 2, pageHeightPt]);
          const projectBgColorRgb = hexToRgb(backgroundColor);
        page.drawRectangle({
          x: 0, y: 0, width: pageWidthPt * 2, height: pageHeightPt,
          color: projectBgColorRgb ? projectBgColorRgb : rgb(1, 1, 1), // Default to white if color is invalid
        });

        // Then, if a project background image exists, draw it on top with opacity and correct transforms
        if (projectBackgroundImagePath) { // Use projectBackgroundImagePath to ensure we have an original path
          try {
            let bgImagePath = projectBackgroundImagePath; // Start with the stored original path
            if (window.bookProofsApp?.getUpdatedFilePath) {
              const updatedPath = window.bookProofsApp.getUpdatedFilePath(bgImagePath);
              if (updatedPath) bgImagePath = updatedPath;
            }

            // Get transform properties from BookEditor state
            const scale = backgroundImageZoom; // backgroundImageZoom is the scale factor (e.g., 1, 1.5, 2)
            const focalX = backgroundImagePanX / 100; // Convert 0-100% to 0-1
            const focalY = backgroundImagePanY / 100; // Convert 0-100% to 0-1
            const opacity = projectBackgroundImageOpacity !== undefined ? projectBackgroundImageOpacity : 1;

            // Find the image file to get natural dimensions
            let imgNaturalWidth: number | undefined;
            let imgNaturalHeight: number | undefined;
            const currentGlobalBgPath = projectBackgroundImagePathRef.current; // Use ref for the most current path

            if (currentGlobalBgPath && window.electronAPI?.getExifData) {
                console.log(`[PDF Export] Global BG: Attempting to get EXIF for path: ${currentGlobalBgPath}`);
                try {
                    const exifResult = await window.electronAPI.getExifData(currentGlobalBgPath);
                    if (exifResult.success && exifResult.exifData?.dimensions) {
                        imgNaturalWidth = exifResult.exifData.dimensions.width;
                        imgNaturalHeight = exifResult.exifData.dimensions.height;
                        console.log(`[PDF Export] Global BG: Got EXIF dims: ${imgNaturalWidth}x${imgNaturalHeight}`);
                    } else {
                        console.warn(`[PDF Export] Global BG: Failed to get EXIF dims for ${currentGlobalBgPath} from getExifData:`, exifResult.error);
                        // Fallback to images array if EXIF failed for a known path
                        const imageFileEntry = imagesRef.current.find(img => img.originalPath === currentGlobalBgPath);
                        if (imageFileEntry && imageFileEntry.naturalWidth && imageFileEntry.naturalHeight) {
                            imgNaturalWidth = imageFileEntry.naturalWidth;
                            imgNaturalHeight = imageFileEntry.naturalHeight;
                            console.log(`[PDF Export] Global BG: Used dims from images state after EXIF fail: ${imgNaturalWidth}x${imgNaturalHeight}`);
                        }
                    }
                } catch (exifError) {
                    console.error(`[PDF Export] Global BG: Error fetching EXIF for ${currentGlobalBgPath}:`, exifError);
                }
            } else if (currentGlobalBgPath) {
                 // Path exists but no getExifData API (should not happen if electronAPI is defined)
                 // Or, if getExifData is not available, try from images state directly.
                const imageFileEntry = imagesRef.current.find(img => img.originalPath === currentGlobalBgPath);
                if (imageFileEntry && imageFileEntry.naturalWidth && imageFileEntry.naturalHeight) {
                    imgNaturalWidth = imageFileEntry.naturalWidth;
                    imgNaturalHeight = imageFileEntry.naturalHeight;
                    console.log(`[PDF Export] Global BG: Got dims from images state (getExifData not available/used): ${imgNaturalWidth}x${imgNaturalHeight}`);
                }
            }

            // If dimensions are still not found (e.g., path was null, or all attempts failed)
            if (!imgNaturalWidth || !imgNaturalHeight) {
                const pathInfo = currentGlobalBgPath || projectBackgroundImageRef.current || "Unknown Source"; // Use display URL if path was null
                console.warn(`[PDF Export] Global BG: Using fallback (page) dimensions for image from: ${pathInfo}. Pan may be incorrect if image aspect ratio differs from page.`);
                imgNaturalWidth = pageWidthPt * 2; // Fallback to full spread width
                imgNaturalHeight = pageHeightPt;   // Fallback to full spread height
            }
            
            // Use the EXACT SAME transform logic as per-spread backgrounds
            const spreadRatio = (pageWidthPt * 2) / pageHeightPt;
            const imgRatio = imgNaturalWidth / imgNaturalHeight;
            
            let interimW: number, interimH: number;
            if (imgRatio > spreadRatio) { // Image is wider than spread container (relative to height)
              interimH = pageHeightPt; // Fit to height
              interimW = interimH * imgRatio; // Calculate width based on image ratio
            } else { // Image is taller or same aspect ratio as spread container
              interimW = pageWidthPt * 2; // Fit to width (full spread width)
              interimH = interimW / imgRatio; // Calculate height based on image ratio
            }
            
            const scaledW = interimW * scale;
            const scaledH = interimH * scale;
            
            const offsetX = (0.5 - focalX) * scaledW;
            const offsetY = (0.5 - focalY) * scaledH;
            
            const bgImageDataResult = await window.electronAPI.loadImageDataForPdf(bgImagePath, {
              targetWidthPt: pageWidthPt * 2, // Full spread width
              targetHeightPt: pageHeightPt,   // Full spread height
              transform: {
                scale: scale,
                fit: 'cover', // Global background is always effectively 'cover'
                offsetX: offsetX,
                offsetY: offsetY
              },
              colorSpace: colorSpace // Pass colorSpace
            });

            if (bgImageDataResult && bgImageDataResult.success && bgImageDataResult.imageBuffer) {
              let pdfBgImage: PDFImage;
              const { imageType, imageBuffer, iccProfileBuffer: rawOrCompressedProfileBuffer } = bgImageDataResult;

              // ICC Profile handling (same as per-spread background)
              if (imageType === 'png') {
                let decompressedIccProfile: Uint8Array | undefined = undefined;
                if (rawOrCompressedProfileBuffer) {
                  try {
                    const compressedProfile = rawOrCompressedProfileBuffer instanceof Uint8Array
                      ? rawOrCompressedProfileBuffer
                      : new Uint8Array(rawOrCompressedProfileBuffer);
                    decompressedIccProfile = pako.inflate(compressedProfile);
                  } catch (inflateError) {
                    console.error(`Failed to decompress PNG ICC profile for project background image:`, inflateError);
                  }
                }
                pdfBgImage = await mainPdfDoc.embedPng(imageBuffer, { iccProfile: decompressedIccProfile, iccProfileComponents: decompressedIccProfile ? 3 : undefined });
              } else { // JPEG
                let rawIccProfile: Uint8Array | undefined = undefined;
                if (rawOrCompressedProfileBuffer) {
                   rawIccProfile = rawOrCompressedProfileBuffer instanceof Uint8Array
                     ? rawOrCompressedProfileBuffer
                     : new Uint8Array(rawOrCompressedProfileBuffer);
                }
                pdfBgImage = await mainPdfDoc.embedJpg(imageBuffer, { iccProfile: rawIccProfile, iccProfileComponents: rawIccProfile ? 3 : undefined });
              }
              
              page.drawImage(pdfBgImage, {
                x: 0, y: 0, width: pageWidthPt * 2, height: pageHeightPt,
                opacity: opacity,
              });
            } else {
              console.warn(`Failed to load project background image for PDF: ${bgImagePath}`, bgImageDataResult?.error);
            }
          } catch (bgImgError) {
            console.error("Error processing project background image for PDF:", bgImgError);
          }
        }
        
        if (!template || template.id === '__blank__') continue;

        let spreadBackgroundDrawn = false;
        if (spread.spreadBackgroundData?.imagePath) {
          try {
            let bgImagePath = spread.spreadBackgroundData.imagePath;
            if (window.bookProofsApp?.getUpdatedFilePath) {
              const updatedPath = window.bookProofsApp.getUpdatedFilePath(bgImagePath);
              if (updatedPath) bgImagePath = updatedPath;
            }

            // Get transform properties from the spread background data
            const { scale = 1, focalX = 0.5, focalY = 0.5 } = spread.spreadBackgroundData.transform;
            const opacity = spread.spreadBackgroundData.opacity ?? 1;

            // Use the EXACT SAME transform logic as regular image placements
            // First calculate the base dimensions using 'cover' logic
            const spreadRatio = (pageWidthPt * 2) / pageHeightPt;
            
            // Get natural image dimensions from the spread background data
            const imgNaturalWidth = spread.spreadBackgroundData.naturalWidth || 1;
            const imgNaturalHeight = spread.spreadBackgroundData.naturalHeight || 1;
            const imgRatio = imgNaturalWidth / imgNaturalHeight;
            
            // Calculate base dimensions using cover logic (same as regular image placements)
            let interimW: number, interimH: number;
            if (imgRatio > spreadRatio) {
              interimH = pageHeightPt;
              interimW = interimH * imgRatio;
            } else {
              interimW = pageWidthPt * 2;
              interimH = interimW / imgRatio;
            }
            
            // Apply scale factor to get scaled dimensions
            const scaledW = interimW * scale;
            const scaledH = interimH * scale;
            
            // Calculate offset based on focal point and scaled dimensions
            // EXACTLY the same calculation as used for regular image placements
            const offsetX = (0.5 - focalX) * scaledW;
            const offsetY = (0.5 - focalY) * scaledH;
            
            const bgImageDataResult = await window.electronAPI.loadImageDataForPdf(bgImagePath, {
              targetWidthPt: pageWidthPt * 2, // Full spread width
              targetHeightPt: pageHeightPt,
              transform: {
                scale: scale,
                fit: 'cover', // Background is always effectively 'cover'
                offsetX: offsetX, // Pass correctly calculated offset based on scaled dimensions
                offsetY: offsetY  // Pass correctly calculated offset based on scaled dimensions
              },
              colorSpace: colorSpace // Pass colorSpace
            });

            if (bgImageDataResult && bgImageDataResult.success && bgImageDataResult.imageBuffer) {
              let pdfBgImage: PDFImage;
              const { imageType, imageBuffer, iccProfileBuffer: rawOrCompressedProfileBuffer } = bgImageDataResult;

              if (imageType === 'png') {
                let decompressedIccProfile: Uint8Array | undefined = undefined;
                if (rawOrCompressedProfileBuffer) {
                  try {
                    // Ensure buffer is Uint8Array before decompression
                    const compressedProfile = rawOrCompressedProfileBuffer instanceof Uint8Array
                      ? rawOrCompressedProfileBuffer
                      : new Uint8Array(rawOrCompressedProfileBuffer);
                    decompressedIccProfile = pako.inflate(compressedProfile);
                  } catch (inflateError) {
                    console.error(`Failed to decompress PNG ICC profile for spread background image:`, inflateError);
                    toast.warning(`Failed to decompress color profile for spread background image. Embedding without profile.`, { duration: 5000 });
                  }
                }

                try {
                  // Embed PNG with decompressed profile (if available)
                  pdfBgImage = await mainPdfDoc.embedPng(imageBuffer, {
                    iccProfile: decompressedIccProfile,
                    // Assuming RGB for PNGs with profiles, adjust if needed
                    iccProfileComponents: decompressedIccProfile ? 3 : undefined
                  });
                } catch (embedError) {
                  console.error(`Failed to embed spread background PNG:`, embedError);
                  toast.error(`Failed to embed spread background image. Using background color.`, { duration: 5000 });
                  throw embedError; // Re-throw to trigger the catch block
                }
              } else { // Handle JPEG (and other types converted to JPEG)
                let rawIccProfile: Uint8Array | undefined = undefined;
                if (rawOrCompressedProfileBuffer) {
                   rawIccProfile = rawOrCompressedProfileBuffer instanceof Uint8Array
                     ? rawOrCompressedProfileBuffer
                     : new Uint8Array(rawOrCompressedProfileBuffer);
                }

                try {
                  // Embed JPEG with raw profile (if available)
                  pdfBgImage = await mainPdfDoc.embedJpg(imageBuffer, {
                    iccProfile: rawIccProfile,
                    // Assuming 3 components (RGB) for JPEGs with profiles
                    iccProfileComponents: rawIccProfile ? 3 : undefined
                  });
                } catch (embedError) {
                   console.error(`Failed to embed spread background JPEG:`, embedError);
                   toast.error(`Failed to embed spread background image. Using background color.`, { duration: 5000 });
                   throw embedError; // Re-throw to trigger the catch block
                }
              }

              // Draw the image with opacity
              page.drawImage(pdfBgImage, {
                x: 0,
                y: 0,
                width: pageWidthPt * 2,
                height: pageHeightPt,
                opacity: opacity // Apply the opacity setting from spread background data
              });
              spreadBackgroundDrawn = true;
            } else {
              console.warn(`Failed to load spread background image: ${bgImagePath}`, bgImageDataResult?.error);
              toast.warning(`Could not load spread background image. Using background color.`, { id: 'pdf-export-progress' });
            }
          } catch (bgImgError) {
            console.error(`Error processing spread background image ${spread.spreadBackgroundData.imagePath}:`, bgImgError);
            toast.warning(`Error loading spread background image. Using background color.`, { id: 'pdf-export-progress' });
          }
        }
        if (!spread.images.some(img => img.imageId) && !spreadBackgroundDrawn) {
          // For 'spreads' layout, draw the center line
           if (pdfLayout === 'spreads') { // This check is valid here as 'page' is defined in this scope
            const page = mainPdfDoc.getPage(mainPdfDoc.getPageCount() -1); // Get the last added page (which is the spread page)
            page.drawLine({ start: { x: pageWidthPt, y: 0 }, end: { x: pageWidthPt, y: pageHeightPt }, thickness: 0.5, color: rgb(0.78, 0.78, 0.78) });
          }
        }
        const spreadAdjustedLayout = adjustTemplateForGap(template, imageGap, pageWidthPt * 2, pageHeightPt);
        
        // Image Placement Loop
        for (const placement of spread.images) {
          if (!placement.imageId) continue;
          const imageFile = images.find(img => img.id === placement.imageId);
          if (!imageFile) continue;
          const adjustedPlaceholder = spreadAdjustedLayout.find(p => p.id === placement.placeholderId);
          if (!adjustedPlaceholder || !Number.isFinite(adjustedPlaceholder.adjustedX_pt) || adjustedPlaceholder.adjustedWidth_pt <= 0) continue;
          
          try {
            const { scale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain', rotation = 0 } = placement.transform;
            const placeholderWidth = adjustedPlaceholder.adjustedWidth_pt;
            const placeholderHeight = adjustedPlaceholder.adjustedHeight_pt;
            const imgNaturalWidth = imageFile.naturalWidth || 1;
            const imgNaturalHeight = imageFile.naturalHeight || 1;
            const imgRatio = imgNaturalWidth / imgNaturalHeight;
            const placeholderRatio = placeholderWidth / placeholderHeight;
            let interimW, interimH;
            if (fit === 'cover') {
              if (imgRatio > placeholderRatio) { interimH = placeholderHeight; interimW = interimH * imgRatio; }
              else { interimW = placeholderWidth; interimH = interimW / imgRatio; }
            } else { // contain
              if (imgRatio > placeholderRatio) { interimW = placeholderWidth; interimH = interimW / imgRatio; }
              else { interimH = placeholderHeight; interimW = interimH * imgRatio; }
            }
            const scaledW = interimW * scale; const scaledH = interimH * scale;
            const offsetX = (0.5 - focalX) * scaledW; const offsetY = (0.5 - focalY) * scaledH;

            let imagePath = imageFile.originalPath;
            if (window.bookProofsApp?.getUpdatedFilePath) {
              const updatedPath = window.bookProofsApp.getUpdatedFilePath(imageFile.originalPath);
              if (updatedPath) imagePath = updatedPath;
            }
            const imageDataResult = await window.electronAPI.loadImageDataForPdf(imagePath, {
              transform: { scale, offsetX, offsetY, fit, rotation },
              targetWidthPt: placeholderWidth, targetHeightPt: placeholderHeight,
              colorSpace: colorSpace
            });
            if (!imageDataResult?.success || !imageDataResult.imageBuffer) throw new Error(imageDataResult?.error || 'Failed to load image data');

            let pdfImage: PDFImage;
            const { imageType: fgImageType, imageBuffer: fgImageBuffer, iccProfileBuffer: fgIccProfileBuffer } = imageDataResult;
            if (fgImageType === 'png') {
              let decompressedFgIccProfile: Uint8Array | undefined = undefined;
              if (fgIccProfileBuffer) {
                try {
                  const compressedProfile = fgIccProfileBuffer instanceof Uint8Array ? fgIccProfileBuffer : new Uint8Array(fgIccProfileBuffer);
                  decompressedFgIccProfile = pako.inflate(compressedProfile);
                } catch (inflateError) { console.error(`PNG ICC Decompression Error:`, inflateError); }
              }
              pdfImage = await mainPdfDoc.embedPng(fgImageBuffer, { iccProfile: decompressedFgIccProfile, iccProfileComponents: decompressedFgIccProfile ? 3 : undefined });
            } else { // JPEG
              let rawFgIccProfile: Uint8Array | undefined = undefined;
              if (fgIccProfileBuffer) rawFgIccProfile = fgIccProfileBuffer instanceof Uint8Array ? fgIccProfileBuffer : new Uint8Array(fgIccProfileBuffer);
              pdfImage = await mainPdfDoc.embedJpg(fgImageBuffer, { iccProfile: rawFgIccProfile, iccProfileComponents: rawFgIccProfile ? 3 : undefined });
            }
            
            const placeholderX_onSpread = adjustedPlaceholder.adjustedX_pt;
            const placeholderY_onSpread_pdf = pageHeightPt - adjustedPlaceholder.adjustedY_pt - placeholderHeight;
            let drawWidth, drawHeight, drawX_on_page, drawY_on_page;

            if (fit === 'contain') {
              if (pdfImage.width / pdfImage.height > placeholderRatio) {
                drawWidth = placeholderWidth; drawHeight = drawWidth / (pdfImage.width / pdfImage.height);
                drawX_on_page = 0; drawY_on_page = (placeholderHeight - drawHeight) / 2;
              } else {
                drawHeight = placeholderHeight; drawWidth = drawHeight * (pdfImage.width / pdfImage.height);
                drawY_on_page = 0; drawX_on_page = (placeholderWidth - drawWidth) / 2;
              }
            } else { // cover
              drawWidth = placeholderWidth; drawHeight = placeholderHeight;
              drawX_on_page = 0; drawY_on_page = 0;
            }
            const overlap = 0; // Set to 0 to avoid bleeding into adjacent pages when split

            if (pdfLayout === 'pages' as PdfLayoutOption) {
              const placeholderCenterXinSpread = placeholderX_onSpread + placeholderWidth / 2;
              const targetPageForImage = placeholderCenterXinSpread < pageWidthPt ? mainPdfDoc.getPage(mainPdfDoc.getPageCount() - 2) : mainPdfDoc.getPage(mainPdfDoc.getPageCount() - 1);
              const xOffsetForPage = placeholderCenterXinSpread < pageWidthPt ? placeholderX_onSpread : placeholderX_onSpread - pageWidthPt;
              
              targetPageForImage.drawImage(pdfImage, {
                x: xOffsetForPage + drawX_on_page - overlap / 2,
                y: placeholderY_onSpread_pdf + drawY_on_page - overlap / 2,
                width: drawWidth + overlap,
                height: drawHeight + overlap
              });
            } else { // pdfLayout === 'spreads'
              const page = mainPdfDoc.getPage(mainPdfDoc.getPageCount() -1);
              page.drawImage(pdfImage, {
                x: placeholderX_onSpread + drawX_on_page - overlap / 2,
                y: placeholderY_onSpread_pdf + drawY_on_page - overlap / 2,
                width: drawWidth + overlap,
                height: drawHeight + overlap
              });

              if (shouldDrawBorders && borderColorRgb) {
                const bleedIsActive = projectBleed > 0; // projectBleed is in inches
                const edgeTolerancePt = 2.0;
                const spreadPdfWidth = pageWidthPt * 2;

                // Placeholder vs. Page Edge Checks (using original Y-down for top/bottom, Y-up for PDF drawing)
                const isTopEdgeOutside = adjustedPlaceholder.adjustedY_pt < edgeTolerancePt;
                const isBottomEdgeOutside = (adjustedPlaceholder.adjustedY_pt + placeholderHeight) >= (pageHeightPt - edgeTolerancePt);
                const isLeftEdgeOfSpread = placeholderX_onSpread < edgeTolerancePt;
                const isRightEdgeOfSpread = (placeholderX_onSpread + placeholderWidth) >= (spreadPdfWidth - edgeTolerancePt);

                let showTopBorder_pdf = true, showBottomBorder_pdf = true, showLeftBorder_pdf = true, showRightBorder_pdf = true;

                if (fit === 'contain') {
                  const imageRenderedWidthPt_pdf = drawWidth;
                  const imageRenderedHeightPt_pdf = drawHeight;
                  // drawX_on_page and drawY_on_page are offsets of the image within the placeholder
                  const gapLeftImageToPlaceholderPt_pdf = drawX_on_page;
                  const gapRightImageToPlaceholderPt_pdf = placeholderWidth - (drawX_on_page + imageRenderedWidthPt_pdf);
                  const gapTopImageToPlaceholderPt_pdf = placeholderHeight - (drawY_on_page + imageRenderedHeightPt_pdf); // Y-up context for drawY_on_page
                  const gapBottomImageToPlaceholderPt_pdf = drawY_on_page;

                  const isImageNearTopPlaceholderEdge_pdf = gapTopImageToPlaceholderPt_pdf < edgeTolerancePt;
                  const isImageNearBottomPlaceholderEdge_pdf = gapBottomImageToPlaceholderPt_pdf < edgeTolerancePt;
                  const isImageNearLeftPlaceholderEdge_pdf = gapLeftImageToPlaceholderPt_pdf < edgeTolerancePt;
                  const isImageNearRightPlaceholderEdge_pdf = gapRightImageToPlaceholderPt_pdf < edgeTolerancePt;

                  showTopBorder_pdf = !(isTopEdgeOutside && isImageNearTopPlaceholderEdge_pdf && bleedIsActive);
                  showBottomBorder_pdf = !(isBottomEdgeOutside && isImageNearBottomPlaceholderEdge_pdf && bleedIsActive);
                  showLeftBorder_pdf = !(isLeftEdgeOfSpread && isImageNearLeftPlaceholderEdge_pdf && bleedIsActive);
                  showRightBorder_pdf = !(isRightEdgeOfSpread && isImageNearRightPlaceholderEdge_pdf && bleedIsActive);
                } else { // fit === 'cover'
                  showTopBorder_pdf = !(isTopEdgeOutside && bleedIsActive);
                  showBottomBorder_pdf = !(isBottomEdgeOutside && bleedIsActive);
                  showLeftBorder_pdf = !(isLeftEdgeOfSpread && bleedIsActive);
                  showRightBorder_pdf = !(isRightEdgeOfSpread && bleedIsActive);
                }

                // Draw borders based on visibility flags
                const imgDrawX = placeholderX_onSpread + drawX_on_page - overlap / 2; // Absolute X of the image content
                const imgDrawY = placeholderY_onSpread_pdf + drawY_on_page - overlap / 2; // Absolute Y of the image content
                const imgDrawWidth = drawWidth + overlap;
                const imgDrawHeight = drawHeight + overlap;

                // For 'cover', borders align with placeholder; for 'contain', with image content.
                const borderX = fit === 'cover' ? placeholderX_onSpread : imgDrawX;
                const borderY = fit === 'cover' ? placeholderY_onSpread_pdf : imgDrawY;
                const borderWidth = fit === 'cover' ? placeholderWidth : imgDrawWidth;
                const borderHeight = fit === 'cover' ? placeholderHeight : imgDrawHeight;

                if (showTopBorder_pdf) page.drawRectangle({ x: borderX, y: borderY + borderHeight - bSize, width: borderWidth, height: bSize, color: borderColorRgb });
                if (showBottomBorder_pdf) page.drawRectangle({ x: borderX, y: borderY, width: borderWidth, height: bSize, color: borderColorRgb });
                if (showLeftBorder_pdf) page.drawRectangle({ x: borderX, y: borderY, width: bSize, height: borderHeight, color: borderColorRgb });
                if (showRightBorder_pdf) page.drawRectangle({ x: borderX + borderWidth - bSize, y: borderY, width: bSize, height: borderHeight, color: borderColorRgb });
              }
            }
          } catch (e) { console.error("Error processing image for PDF:", e); }
        }

        // Text Overlay Loop
        const spreadTextOverlays = textOverlays.filter(overlay => overlay.spreadId === spread.id);
        if (spreadTextOverlays.length > 0) {
          for (const overlay of spreadTextOverlays) {
            try {
              const pdfFontName = fontFamilyToPdfFontMap[overlay.style.fontFamily] || StandardFonts.Helvetica;
              const font = await mainPdfDoc.embedFont(pdfFontName);
              const spreadFullWidthPt = pageWidthPt * 2;
              const fontSize = (overlay.style.fontSize / 100) * pageHeightPt;
              const textContent = overlay.content;
              const textBlockAbsoluteWidthPt = (overlay.style.width / 100) * spreadFullWidthPt;
              const textContentWidth = font.widthOfTextAtSize(textContent, fontSize);
              const textBlockCenterXOnSpread = (overlay.style.x / 100) * spreadFullWidthPt;
              let final_text_x_on_page: number;

              if (overlay.style.textAlign === 'center') {
                final_text_x_on_page = textBlockCenterXOnSpread - (textContentWidth / 2);
              } else if (overlay.style.textAlign === 'right') {
                final_text_x_on_page = textBlockCenterXOnSpread + (textBlockAbsoluteWidthPt / 2) - textContentWidth;
              } else { // 'left'
                final_text_x_on_page = textBlockCenterXOnSpread - (textBlockAbsoluteWidthPt / 2);
              }
              const textY_on_page = pageHeightPt - ((overlay.style.y / 100) * pageHeightPt) - (fontSize * 0.3);

              const textOptions = {
                font: font, size: fontSize,
                color: hexToRgb(overlay.style.color) || rgb(0, 0, 0),
                opacity: overlay.style.opacity,
                maxWidth: textBlockAbsoluteWidthPt,
                lineHeight: fontSize * 1.2,
              };

              if (pdfLayout === 'pages' as PdfLayoutOption) {
                const textBlockEffectiveCenterXinSpread = final_text_x_on_page + (font.widthOfTextAtSize(textContent, fontSize) / 2); // More accurate center for splitting
                if (textBlockEffectiveCenterXinSpread < pageWidthPt) { // Primarily on Left page
                    mainPdfDoc.getPage(mainPdfDoc.getPageCount() - 2).drawText(textContent, {...textOptions, x: final_text_x_on_page, y: textY_on_page });
                } else { // Primarily on Right page
                    mainPdfDoc.getPage(mainPdfDoc.getPageCount() - 1).drawText(textContent, { ...textOptions, x: final_text_x_on_page - pageWidthPt, y: textY_on_page });
                }
              } else { // pdfLayout === 'spreads'
                 const page = mainPdfDoc.getPage(mainPdfDoc.getPageCount() -1);
                 page.drawText(textContent, {...textOptions, x: final_text_x_on_page, y: textY_on_page});
              }
            } catch (textError) { console.error(`Error adding text overlay ${overlay.id} to spread ${spread.id}:`, textError); }
          }
        }
       } // End of main 'if (pdfLayout === 'pages')' vs 'else if (pdfLayout === 'spreads')'
        if (exportCancelledRef.current) { toast.info("PDF export cancelled during spread processing loop."); return; }
        await new Promise(resolve => setTimeout(resolve, 10));
      } // End of for loop iterating through finalSpreadsForExport

      if (exportCancelledRef.current) { toast.info("PDF export cancelled before saving main PDF."); return; }
      const mainPdfBytes = await mainPdfDoc.save();
      const projectFilenameBase = (currentProjectFilePath.split(/[\\/]/).pop() || 'bookproofs-project').replace(/\.[^/.]+$/, "");
      
      // Handle separate cover PDF if it was generated
      if (coverPdfBytes && coverExportOption === 'separate') {
        if (exportCancelledRef.current) { toast.info("PDF export cancelled before saving separate cover PDF."); return; }
        toast.info("Preparing to save cover PDF...", { id: 'cover-pdf-export' });
        
        coverSavePath = await window.electronAPI.showSaveDialog({ // Assign to the higher-scoped variable
          title: 'Save Cover PDF',
          defaultPath: `${projectFilenameBase}-Cover.pdf`,
          filters: [{ name: 'PDF Documents', extensions: ['pdf'] }]
        });
        
        if (coverSavePath) {
          if (exportCancelledRef.current) { toast.info("PDF export cancelled before saving separate cover PDF file."); return; }
          const coverSuccess = await window.electronAPI.savePdfFile(coverSavePath, coverPdfBytes);
          if (coverSuccess) {
            toast.success(`Cover PDF exported to ${coverSavePath}`, { id: 'cover-pdf-export' });
            // Optionally open the cover PDF
            window.electronAPI.openPath(coverSavePath);
          } else {
            toast.error("Failed to save cover PDF.", { id: 'cover-pdf-export' });
          }
        } else {
          toast.warning("Cover PDF export cancelled.", { id: 'cover-pdf-export' });
        }
      }

      if (totalSpreadsToExport > 0 || (coverExportOption === 'include' && hasCover && coverImage)) {
        if (exportCancelledRef.current) { toast.info("PDF export cancelled before saving main book PDF."); return; }
        const mainPdfSavePath = await window.electronAPI.showSaveDialog({
          title: 'Save Book PDF', defaultPath: `${projectFilenameBase}.pdf`,
          filters: [{ name: 'PDF Documents', extensions: ['pdf'] }]
        });
        if (mainPdfSavePath) {
          if (exportCancelledRef.current) { toast.info("PDF export cancelled before saving main book PDF file."); return; }
          const mainSuccess = await window.electronAPI.savePdfFile(mainPdfSavePath, mainPdfBytes);
          if (mainSuccess) {
            let successMessage = `Book PDF exported to ${mainPdfSavePath}`;
            if (coverExportOption === 'include' && hasCover && coverImage) {
              successMessage += " (with cover included).";
            } else if (coverExportOption === 'separate' && coverPdfBytes) {
              successMessage += " (cover exported separately).";
            } else {
              successMessage += ".";
            }
            toast.success(successMessage, { id: 'pdf-export-progress' });
            window.electronAPI.openPath(mainPdfSavePath);
          } else {
            toast.error("Failed to save book PDF.", { id: 'pdf-export-progress' });
          }
        } else {
          let cancelMessage = "Book PDF export cancelled.";
          if (coverExportOption === 'separate' && coverPdfBytes && !coverSavePath) {
            // This case means cover was generated but its save was cancelled.
            // The main book PDF export was also cancelled.
          } else if (coverExportOption === 'separate' && !coverPdfBytes && hasCover && coverImage) {
            cancelMessage = "Book PDF export cancelled (cover failed to generate).";
          }
          toast.warning(cancelMessage, { id: 'pdf-export-progress' });
        }
      } else if (!coverPdfBytes && coverExportOption !== 'none' && hasCover && coverImage) {
        // This case means a cover was expected (either separate or included) but failed to generate,
        // and there were no main spreads to export either.
        toast.error("Cover PDF generation failed and no book spreads to export.", { id: 'pdf-export-progress' });
      } else if (!coverPdfBytes) { // No spreads and no cover to export (or cover was 'none')
         toast.info("Nothing to export.", { id: 'pdf-export-progress' });
      }

    } catch (error) {
      console.error("PDF Export Error:", error);
      toast.error(`PDF Export Failed: ${error.message}`, { id: 'pdf-export-progress' });
    } finally {
      setIsExporting(false); // Reset exporting state
      if (!exportCancelledRef.current) { // Only close dialog if not cancelled, as cancel already closes it
        setShowExportDialog(false); // Close the dialog once export attempt is finished
      }
    }
  }, [
    spreads, images, templateMap, aspectRatio, currentProjectFilePath, imageGap, backgroundColor, projectBackgroundImage, textOverlays,
    onUpdateAspectRatioPoints, saveStateForUndo, setIsDirty, setSpreads, setCurrentSpreadId, currentSpreadId,
    hasCover, coverImage, // For cover logic
    // Dependencies for PDF generation logic (ensure all used states/props are here)
    checkFilesAvailability, setShowMissingFilesDialog, fileAvailability, // For missing files check
    dpiWarningThresholdPercent, // For quality check
    setDialogQualityIssueDetails, // For storing quality details for review
    setIsExporting, // For managing export state
    executeJpegExport, // Add executeJpegExport to dependencies
    // Added refs for background image properties as they are used directly in PDF generation
    projectBackgroundImagePathRef, backgroundImageZoomRef, backgroundImagePanXRef, backgroundImagePanYRef, projectBackgroundImageOpacityRef,
    imagesRef, // For accessing image dimensions
    pdfGenerationArgsRef, // For accessing pre-captured cover state
    exportCancelledRef, // For checking cancellation status
    imageBorderSize, imageBorderColor // Added image border settings
  ]);


  const initiateExportProcess = useCallback(async () => {
    if (!window.electronAPI) {
      toast.error("PDF export is only available in the desktop app.");
      return;
    }
    if (!currentProjectFilePath) {
      toast.error("Please save the project before exporting.");
      return;
    }

    const allFilesOk = await checkFilesAvailability(true);
    let currentMissingFilesCount = 0;
    if (!allFilesOk) {
      // If files are missing, MissingFilesDialog will be shown by useFileAvailability hook or direct call.
      // For now, we'll just count them for the ExportDialog.
      // The actual MissingFilesDialog will still block if it's configured to do so.
      // We might want to show ExportDialog *after* MissingFilesDialog is resolved.
      currentMissingFilesCount = fileAvailability.missingFiles.length;
      // To prevent proceeding if files are missing and dialog is shown:
      if (currentMissingFilesCount > 0 && !showMissingFilesDialog) { // Check if dialog isn't already up
         setShowMissingFilesDialog(true); // Trigger it if not
         // return; // Optionally return here to force user to resolve missing files first
      }
    }

    exportCancelledRef.current = false; // Reset cancellation flag before starting export dialog
    
    let currentSpreadsForExport = [...spreadsRef.current]; // Use ref for current spreads
    // Check for ANY empty spreads
    let currentEmptySpreadsCount = 0;
    for (const spread of currentSpreadsForExport) {
      if (spread.templateId === '__blank__' || !spread.images.some(img => img.imageId)) {
        currentEmptySpreadsCount++;
      }
    }
    
    // We'll pass this count to the ExportDialog instead of showing a separate dialog
    // Capture cover state before showing dialog
    
    let capturedCoverState = null;
    let originalIsCoverVisible = isCoverVisible; // Store original visibility
    let coverVisibilityChangedForExport = false;

    if (hasCover && coverImage) {
      if (!projectCoverRef.current && !isCoverVisible) {
        // If cover exists, but ProjectCover component is not rendered (likely isCoverVisible is false)
        // we need to temporarily make it visible to capture its state.
        setIsCoverVisible(true);
        coverVisibilityChangedForExport = true;
        // Wait for the state update to apply and the ref to be populated
        await new Promise(resolve => setTimeout(resolve, 100)); // Adjust delay as needed, 1-2 render cycles
      } else if (!projectCoverRef.current && isCoverVisible) {
        // isCoverVisible is true, but ref is still not set. This indicates a timing issue with ref assignment.
        await new Promise(resolve => setTimeout(resolve, 100)); // Wait for ref
      }

      // After potential visibility change and wait, try to capture
      if (projectCoverRef.current) {
        capturedCoverState = projectCoverRef.current.captureCurrentCoverState();
      } else {
        // Fallback if ref is still not available
        // Ensure focalX and focalY are included in this fallback
        if (coverImageRef.current) {
            capturedCoverState = {
                coverImage: coverImageRef.current,
                scale: coverScaleRef.current, // Use current scale from ref
                focalX: coverFocalXRef.current, // Use current focalX from ref
                focalY: coverFocalYRef.current, // Use current focalY from ref
                translateX: 0, // Default translateX if not calculable
                translateY: 0, // Default translateY if not calculable
            };
        } else {
            // coverImageRef.current is also null, capturedCoverState remains null
        }
      }
    } else {
      // No cover to capture (hasCover is false or coverImage is null)
    }

    pdfGenerationArgsRef.current = {
      spreadsForExport: currentSpreadsForExport,
      capturedCoverState: capturedCoverState, // Use the potentially fallback state
    };

    // Restore original cover visibility if it was changed for export
    if (coverVisibilityChangedForExport && isCoverVisible !== originalIsCoverVisible) {
      setIsCoverVisible(originalIsCoverVisible);
      // Optionally wait for this to apply if it affects subsequent UI before dialog
      // await new Promise(resolve => setTimeout(resolve, 50));
    }


    // Calculate low-resolution image count and details
    let currentLowResImagesCount = 0;
    const qualityDetails: WarningSpreadDetail[] = [];
    currentSpreadsForExport.forEach((spread, index) => {
      const template = templateMap[spread.templateId];
      if (!template || template.id === '__blank__' || !aspectRatio.widthPt || !aspectRatio.heightPt) return;
      const adjPlaceholders = adjustTemplateForGap(template, imageGap, aspectRatio.widthPt * 2, aspectRatio.heightPt);
      const phDims: Record<string, { width: number; height: number }> = {};
      adjPlaceholders.forEach(p => { phDims[p.id] = { width: p.adjustedWidth_pt, height: p.adjustedHeight_pt }; });
      const issues = checkSpreadImagesQuality(imagesRef.current, spread.images, phDims, dpiWarningThresholdPercent);
      if (Object.keys(issues).length > 0) {
        qualityDetails.push({ spreadId: spread.id, displayIndex: index + 1, issues });
      }
    });
    currentLowResImagesCount = qualityDetails.reduce((sum, detail) => sum + Object.keys(detail.issues).length, 0);

    // Store quality details for potential review from ExportDialog
    if (currentLowResImagesCount > 0) {
      setDialogQualityIssueDetails(qualityDetails);
    }
    
    // If all checks passed or were handled, show the ExportDialog
    setExportDialogCounts({
      missingFiles: currentMissingFilesCount, // Show count even if MissingFilesDialog was shown
      lowResImages: currentLowResImagesCount, // Always show the actual count
      emptySpreads: currentEmptySpreadsCount, // Pass the empty spreads count to the dialog
    });
    setShowExportDialog(true);
  }, [
    currentProjectFilePath, checkFilesAvailability, fileAvailability.missingFiles.length, spreads, templateMap, aspectRatio, imageGap, images, dpiWarningThresholdPercent,
    saveStateForUndo, setIsDirty, setSpreads, currentSpreadId, setCurrentSpreadId, setShowMissingFilesDialog, showMissingFilesDialog,
    setDialogQualityIssueDetails, // For storing quality details for review
    hasCover, coverImage, isCoverVisible, setIsCoverVisible, // Added cover related state and setters
    executeJpegExport // Add executeJpegExport to dependencies
    // projectCoverRef and coverImageRef are refs.
    // Note: hasCover, coverImage, etc. are used in this callback to determine capturedCoverState.
  ]);

  const handleExportCancel = useCallback(() => {
    exportCancelledRef.current = true;
    setIsExporting(false); // Reset exporting state when cancelled
    toast.info("Export process cancelled."); // This is the new toast confirming cancellation
    // Dismiss the progress toasts that might be lingering
    toast.dismiss('jpeg-export-progress');
    toast.dismiss('pdf-export-progress');
    toast.dismiss('cover-pdf-export'); // For separate cover PDF export progress
    // setShowExportDialog(false); // Dialog will close itself via onOpenChange
  }, []);


  // Remove the separate handlers for proceeding with export despite quality warnings
  // as this is now handled directly in the ExportDialog

  // --- Effects ---

  const handleLocateFilesFromDialog = useCallback(() => {
    setShowExportDialog(false);
    setShowMissingFilesDialog(true);
  }, [setShowExportDialog, setShowMissingFilesDialog]);

  const handleReviewImagesFromDialog = useCallback(() => {
    // This is now a fallback if there are no quality details available
    // The main functionality is handled by the popover in ExportDialog
    if (dialogQualityIssueDetails.length > 0) {
      toast.info(`There are ${dialogQualityIssueDetails.reduce((sum, detail) => sum + Object.keys(detail.issues).length, 0)} low resolution images.`);
    }
  }, [dialogQualityIssueDetails]);

  const handleNavigateToSpreadFromExport = useCallback((spreadId: string) => {
    // Navigate to the specified spread
    if (isCoverVisible) {
      setIsCoverVisible(false);
    }
    setCurrentSpreadId(spreadId);
    toast.info(`Navigated to spread with low resolution images.`);
  }, [isCoverVisible, setIsCoverVisible, setCurrentSpreadId]);

  const handleDeleteEmptySpreadsFromDialog = useCallback(() => {
    const allCurrentSpreads = spreadsRef.current; // Get the latest spreads from ref
    const emptySpreadIds: string[] = [];
    allCurrentSpreads.forEach(spread => {
      if (spread.templateId === '__blank__' || !spread.images.some(img => img.imageId)) {
        emptySpreadIds.push(spread.id);
      }
    });

    if (emptySpreadIds.length > 0) {
      saveStateForUndo();
      setIsDirty(true);
      
      let filteredSpreads = allCurrentSpreads.filter(s => !emptySpreadIds.includes(s.id));
      let newCurrentSpreadId = currentSpreadId; // Use state currentSpreadId

      if (filteredSpreads.length === 0 && allCurrentSpreads.length > 0) {
        // All spreads were empty and got deleted, add a new blank one
        const newBlankSpreadId = `spread-${Date.now()}`;
        const newBlankSpread: Spread = {
          id: newBlankSpreadId,
          templateId: '__blank__',
          images: [],
          spreadBackgroundData: defaultSpreadBackgroundRef.current ? {...defaultSpreadBackgroundRef.current} : null
        };
        filteredSpreads = [newBlankSpread];
        newCurrentSpreadId = newBlankSpreadId;
        toast.success(`${emptySpreadIds.length} empty spread(s) deleted. Added a new blank spread as all were empty.`);
      } else {
        // Some spreads remain, or some non-empty spreads were already there
        if (emptySpreadIds.includes(currentSpreadId)) {
          // If the current spread was deleted, try to select the one before it, or the first one
          const originalIndex = allCurrentSpreads.findIndex(s => s.id === currentSpreadId);
          let targetIndex = -1;
          // Find the closest non-deleted spread before the deleted one
          for (let i = originalIndex - 1; i >= 0; i--) {
            if (!emptySpreadIds.includes(allCurrentSpreads[i].id)) {
              const foundIndexInFiltered = filteredSpreads.findIndex(fs => fs.id === allCurrentSpreads[i].id);
              if (foundIndexInFiltered !== -1) {
                targetIndex = foundIndexInFiltered;
                break;
              }
            }
          }
          if (targetIndex === -1 && filteredSpreads.length > 0) { // Fallback to first spread if no prior found
            targetIndex = 0;
          }
          newCurrentSpreadId = filteredSpreads[targetIndex]?.id || '';
        }
        toast.success(`${emptySpreadIds.length} empty spread(s) deleted.`);
      }
      
      setSpreads(filteredSpreads);
      if (newCurrentSpreadId !== currentSpreadId && newCurrentSpreadId !== '') {
        setCurrentSpreadId(newCurrentSpreadId);
      } else if (filteredSpreads.length > 0 && newCurrentSpreadId === '' && !emptySpreadIds.includes(currentSpreadId)) {
        // This case handles if currentSpreadId was not deleted, but newCurrentSpreadId somehow became empty.
        // It should retain currentSpreadId if it's still valid in filteredSpreads.
        // Or, if currentSpreadId is no longer valid (e.g. only one spread left and it was the current one), set to the first.
        const currentStillExists = filteredSpreads.some(s => s.id === currentSpreadId);
        if (!currentStillExists && filteredSpreads.length > 0) {
            setCurrentSpreadId(filteredSpreads[0].id);
        } else if (currentStillExists) {
            // currentSpreadId is still valid, no change needed for setCurrentSpreadId
        }
      }


      setExportDialogCounts(prevCounts => ({
        ...prevCounts,
        emptySpreads: 0,
      }));
    } else {
      toast.info("No empty spreads to delete.");
    }
  }, [
    saveStateForUndo, setIsDirty, setSpreads, setCurrentSpreadId,
    spreadsRef, currentSpreadId,
    setExportDialogCounts, defaultSpreadBackgroundRef
  ]);


  // Text overlay handlers - MOVED EARLIER TO RESOLVE DECLARATION ORDER ISSUE
  const handleAddTextOverlay = useCallback((textOverlay: TextOverlay) => {
    saveStateForUndo();
    setIsDirty(true);
    setTextOverlays(prev => [...prev, textOverlay]);
  }, [saveStateForUndo, setIsDirty]);

  const handleUpdateTextOverlay = useCallback((updatedOverlay: TextOverlay) => {
    saveStateForUndo();
    setIsDirty(true);

    // SIMPLIFIED: Just use the updated overlay directly to preserve all properties
    // This ensures that all style properties are preserved when updating text
    setTextOverlays(prev => {
      return prev.map(overlay => {
        if (overlay.id === updatedOverlay.id) {
          // Always use the complete updated overlay to ensure all properties are preserved
          return updatedOverlay;
        }
        return overlay;
      });
    });

    // Also update selectedTextOverlayData to ensure UI consistency
    if (selectedTextOverlayData && selectedTextOverlayData.id === updatedOverlay.id) {
      setSelectedTextOverlayData(updatedOverlay);
    }
  }, [saveStateForUndo, setIsDirty, selectedTextOverlayData]);

  const handleDeleteTextOverlay = useCallback((textOverlayId: string) => {
    saveStateForUndo();
    setIsDirty(true);
    setTextOverlays(prev => prev.filter(overlay => overlay.id !== textOverlayId));
    setSelectedTextOverlayData(null); // Deselect after deleting
  }, [saveStateForUndo, setIsDirty, setTextOverlays, setSelectedTextOverlayData]);


  // Update refs immediately when state changes
  useEffect(() => {
    aspectRatioRef.current = aspectRatio;
    imagesRef.current = images;
    spreadsRef.current = spreads;
    imageGapRef.current = imageGap;
    backgroundColorRef.current = backgroundColor;
    projectBackgroundImageRef.current = projectBackgroundImage;
    projectBackgroundImagePathRef.current = projectBackgroundImagePath;
    projectBackgroundImageOriginalDimensionsRef.current = projectBackgroundImageOriginalDimensions;
    // ADDED: Update background image transform refs
    backgroundImageZoomRef.current = backgroundImageZoom;
    backgroundImagePanXRef.current = backgroundImagePanX;
    backgroundImagePanYRef.current = backgroundImagePanY;
    projectBackgroundImageOpacityRef.current = projectBackgroundImageOpacity; // Update opacity ref
    textOverlaysRef.current = textOverlays;
    // enableProjectCoverRef.current = enableProjectCover; // ADDED: Update enableProjectCoverRef // Replaced by useUserSettings
    hasCoverRef.current = hasCover; // Update hasCoverRef
    isCoverVisibleRef.current = isCoverVisible; // Update isCoverVisibleRef
    coverImageRef.current = coverImage;
    coverScaleRef.current = coverScale;
    coverFocalXRef.current = coverFocalX; // Update coverFocalXRef
    coverFocalYRef.current = coverFocalY; // Update coverFocalYRef
    projectBleedRef.current = projectBleed; // Update projectBleedRef
    projectSafetyMarginRef.current = projectSafetyMargin; // Update projectSafetyMarginRef
    imageBorderSizeRef.current = imageBorderSize; // Update imageBorderSizeRef
    imageBorderColorRef.current = imageBorderColor; // Update imageBorderColorRef
    customTemplatesRef.current = customTemplates; // Update customTemplatesRef
  }, [
    aspectRatio, images, spreads, imageGap, backgroundColor,
    projectBackgroundImage, projectBackgroundImagePath, projectBackgroundImageOpacity, // Add opacity
    backgroundImageZoom, backgroundImagePanX, backgroundImagePanY, // ADDED dependencies
    textOverlays,
    isEditingBackgroundForSpreadId, // Add background editing state dependency
    hasCover, // ADDED: hasCover dependency
    isCoverVisible,
    coverImage,
    coverScale, coverFocalX, coverFocalY, // Updated to focal dependencies
    projectBleed, projectSafetyMargin, // ADDED: bleed and safety margin state dependencies
    imageBorderSize, imageBorderColor, customTemplates // ADDED: image border state dependencies, custom templates
  ]);

  // Effect to manage cover visibility based on hasCover state
  useEffect(() => {
    if (hasCover) {
      // When cover is enabled, default to showing the cover.
      // This handles the initial state when the toggle is turned on.
      setIsCoverVisible(true);
    } else {
      // When cover is disabled, ensure it's not visible.
      setIsCoverVisible(false);
    }
  }, [hasCover, setIsCoverVisible]); // Only react to changes in hasCover

  // Effect to capture cover state when navigating away from cover
  useEffect(() => {
    // Capture state when cover becomes invisible (but only if it was previously visible)
    if (hasCover && !isCoverVisible && projectCoverRef.current) {
      const currentState = projectCoverRef.current.captureCurrentCoverState();
      if (currentState) {
        lastCoverState.current = currentState;
      }
    }
  }, [hasCover, isCoverVisible]); // React to cover visibility changes

  // Effect to restore cover state when cover becomes visible
  useEffect(() => {
    if (hasCover && isCoverVisible && projectCoverRef.current && lastCoverState.current) {
      // Restore the last known cover state when the cover becomes visible again
      projectCoverRef.current.applyRestoredState(lastCoverState.current);
    }
  }, [hasCover, isCoverVisible]); // React to cover visibility changes

  // Effect to measure container size AND enforce boundary-first sizing
  useEffect(() => {
    const containerElement = spreadContainerRef.current;
    if (!containerElement) return;

    const calculateBoundaryConstrainedSize = () => {
      const containerRect = containerElement.getBoundingClientRect();
      const availableWidth = containerRect.width;
      const availableHeight = containerRect.height;
      
      if (!aspectRatio?.widthPt || !aspectRatio?.heightPt || availableWidth <= 0 || availableHeight <= 0) {
        setSpreadContainerSize({ width: availableWidth, height: availableHeight });
        return;
      }

      // BOUNDARY-FIRST CALCULATION
      // For SpreadCanvas, we need SPREAD aspect ratio (two pages side by side)
      const spreadAspectRatio = (aspectRatio.widthPt * 2) / aspectRatio.heightPt;
      const containerAspectRatio = availableWidth / availableHeight;

      let constrainedWidth: number;
      let constrainedHeight: number;

      if (spreadAspectRatio > containerAspectRatio) {
        // Spread is wider relative to container - constrain by width, calculate height
        constrainedWidth = availableWidth;
        constrainedHeight = availableWidth / spreadAspectRatio;
      } else {
        // Spread is taller relative to container - constrain by height, calculate width  
        constrainedHeight = availableHeight;
        constrainedWidth = availableHeight * spreadAspectRatio;
      }

      // ENSURE BOUNDARIES ARE NEVER EXCEEDED
      const finalWidth = Math.min(constrainedWidth, availableWidth);
      const finalHeight = Math.min(constrainedHeight, availableHeight);


      setSpreadContainerSize({ width: finalWidth, height: finalHeight });
    };

    const resizeObserver = new ResizeObserver(() => {
      calculateBoundaryConstrainedSize();
    });

    resizeObserver.observe(containerElement);
    calculateBoundaryConstrainedSize(); // Initial calculation

    return () => resizeObserver.disconnect();
  }, [aspectRatio]);

  // --- Project Loading/Saving ---

  // Effect to update active template ID for tray when current spread changes
  useEffect(() => {
    const currentSpread = spreads.find(s => s.id === currentSpreadId);
    const template = currentSpread ? templateMap[currentSpread.templateId] : null;
    if (template && template.id !== '__blank__') {
      const imageCount = template.images.length;
      setFilterToTrigger(imageCount);
      setActiveTemplateIdForTray(template.id);
    } else {
      setFilterToTrigger(null);
      setActiveTemplateIdForTray(null);
    }
  }, [currentSpreadId, spreads, templateMap]);

  // Setup Undo/Redo IPC Listeners
  useEffect(() => {
    let removeUndoListener: (() => void) | undefined;
    let removeRedoListener: (() => void) | undefined;
    if (window.electronAPI) {
      removeUndoListener = window.electronAPI.onTriggerUndo(handleUndo);
      removeRedoListener = window.electronAPI.onTriggerRedo(handleRedo);
    } else {
      console.warn("Electron API not found, cannot set up undo/redo listeners.");
    }
    return () => {
      if (removeUndoListener) removeUndoListener();
      if (removeRedoListener) removeRedoListener();
    };
  }, [handleUndo, handleRedo]);

  // Focus Mode Shortcut
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'f') {
        event.preventDefault();
        toggleFocusMode();
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [toggleFocusMode]); // Dependency is now defined above

  // Combined Global Key Handler (Arrows, Delete, Undo/Redo, Save)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const targetElement = event.target as HTMLElement;
      const isInputFocused = targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA' || targetElement.isContentEditable;

      // Handle Escape key to close text editor bar
      if (event.key === 'Escape' && selectedTextOverlayData) {
        event.preventDefault();
        setSelectedTextOverlayData(null);
        return;
      }

      if (isInputFocused) return; // Ignore if typing in inputs

      // Handle Undo/Redo/Save first as they use modifiers
      if (event.metaKey || event.ctrlKey) {
        if (event.key === 'z') {
          event.preventDefault();
          if (event.shiftKey) { handleRedo(); } else { handleUndo(); }
          return;
        } else if (event.key === 'y') {
          event.preventDefault(); handleRedo(); return;
        } else if (event.key === 's') {
          event.preventDefault(); handleSaveProject(); return;
        }
        // If other modifier combos are pressed, ignore for nav/delete
        // Allow specific combos like Cmd+. for focus mode if needed elsewhere
        if (event.key !== '.') { // Allow Cmd+. through
             return;
        }
      }

      // Handle Arrow Keys (No modifiers allowed based on check above)
      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        if (document.activeElement instanceof HTMLElement) { document.activeElement.blur(); }
        handleNavigateSpread('prev');
    } else if (event.key === 'ArrowRight') {
      event.preventDefault();
      if (document.activeElement instanceof HTMLElement) { (document.activeElement as HTMLElement).blur(); } // Blur current focus

      if (isCoverVisible) { // If cover is currently visible
        saveStateForUndo(); // Save state before changing currentSpreadId and visibility
        setIsCoverVisible(false); // Hide the cover
        if (spreads.length > 0 && spreads[0]?.id) {
          const firstSpreadId = spreads[0].id;
          setCurrentSpreadId(firstSpreadId); // Set current spread to the first one
          setSelectedSpreadIdsState([firstSpreadId]); // Highlight in tray
          // Focus the spreads tray for consistency with other navigation
          setTimeout(() => {
            spreadsTrayRef.current?.focus({ preventScroll: true });
          }, 0);
        }
      } else {
        handleNavigateSpread('next'); // Existing logic for navigating to the next spread
      }
    }
    // Handle Delete/Backspace (No modifiers allowed)
      else if (event.key === 'Delete' || event.key === 'Backspace') {
        // Log state *just before* checking conditions with more detail

        // Priority 0: Selected Text Overlay
        if (selectedTextOverlayData) {
          event.preventDefault();
          handleDeleteTextOverlay(selectedTextOverlayData.id);
          return;
        }

        // Priority 1: Canvas Hover (Focus NOT required)
        if (hoveredCanvasPlaceholderId) {
          event.preventDefault();
          spreadCanvasRef.current?.deleteHoveredImage();
          return;
        }

        // Priority 2: Canvas Focus + Selection
        if (isCanvasFocused && selectedCanvasImagesCount > 0) {
          event.preventDefault();
          spreadCanvasRef.current?.deleteHoveredImage(); // This now handles multiple selected images
          return;
        }

        // Priority 3: Image Tray Focus + Selection
        if (isImageTrayFocused && selectedImageIdsState.length > 0) {
          event.preventDefault();
          handleRemoveImagesRequest(selectedImageIdsState);
          return;
        }

        // Priority 3: Spreads Tray Focus + Selection
        if (isSpreadsTrayFocused && selectedSpreadIdsState.length > 0) {
          event.preventDefault();
          handleDeleteSpreadsRequest(selectedSpreadIdsState);
          return;
        } else {
        }
      }
      // Handle 'g' key for toggling gutter
      else if (event.key === 'g') {
        event.preventDefault();
        setShowBookGutter(prev => {
          const nextState = !prev;
          toast.info(`Book gutter: ${nextState ? 'Shown' : 'Hidden'}`);
          return nextState;
        });
      }
      // Handle 'f' key for toggling filenames
      else if (event.key === 'f') {
        event.preventDefault();
        setShowFilenames(prev => !prev);
      }
      // Handle 'e' key for toggling end of filename
      else if (event.key === 'e') {
        event.preventDefault();
        // Only toggle if filenames are currently shown
        if (showFilenames) {
          setShowEndOfFilename(prev => !prev);
        }
      }
      // Handle 'n' key for toggling image numbering
      else if (event.key === 'n') {
        event.preventDefault();
        // Determine the next state based on the current state *before* toggling
        const nextState = !showImageNumbers;
        toggleImageNumbers();
        toast.info(`Image numbering: ${nextState ? 'Shown' : 'Hidden'}`);
      }
      // Handle 'd' key for toggling default drop mode - COMMENTED OUT: Always use cover mode
      /*
      else if (event.key === 'd') {
        event.preventDefault();
        // Determine the *next* state before calling the setter
        const nextModeIsCover = !defaultDropModeIsCover;
        setDefaultDropModeIsCover(nextModeIsCover);
        // Show toast with the new mode
        toast.info(`Default fit mode: ${nextModeIsCover ? 'Cover Frame' : 'Contain Inside Frame'}`);
      }
      */
      // Handle '\' key for toggling fit mode all
      else if (event.key === '\\') {
        event.preventDefault();
        spreadCanvasRef.current?.toggleFitModeAll();
      }
      // Handle 'q' key for toggling image quality indicators
      else if (event.key === 'q') {
        event.preventDefault();
        // Determine the next state before toggling
        const nextState = !showQualityIndicators;
        setShowQualityIndicators(nextState);
        toast.info(`Image Quality Indicators: ${nextState ? 'Shown' : 'Hidden'}`);
      }
      // Handle 't' key for toggling bleed lines
      else if (event.key.toLowerCase() === 't') {
        event.preventDefault();
        userSetShowBleedLines(prev => {
          const nextState = !prev;
          toast.info(`Trim lines: ${nextState ? 'Shown' : 'Hidden'}`);
          return nextState;
        });
      }
      // Handle 'b' key for cycling bleed area modes
      else if (event.key.toLowerCase() === 'b') {
        event.preventDefault();
        setBleedAreaMode(prev => {
          let nextState: 'hide' | 'indicate' | 'ignore';
          let toastMessage: string;
          if (prev === 'hide') {
            nextState = 'indicate';
            setShowBleedArea(true); // For backward compatibility
            toastMessage = "Bleed Area: Indicated";
          } else if (prev === 'indicate') {
            nextState = 'ignore';
            setShowBleedArea(false); // For backward compatibility
            toastMessage = "Bleed Area: Showing Full Canvas";
          } else { // prev === 'ignore'
            nextState = 'hide';
            setShowBleedArea(false); // For backward compatibility
            toastMessage = "Bleed Area: Hidden";
          }
          toast.info(toastMessage);
          return nextState;
        });
      }
      // Handle 'm' key for toggling safety margin
      else if (event.key.toLowerCase() === 'm') {
        event.preventDefault();
        userSetShowSafetyMargin(prev => { // Use the setter from useUserSettings
          const nextState = !prev;
          toast.info(`Safety Margin: ${nextState ? 'Shown' : 'Hidden'}`);
          return nextState;
        });
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [
    // Navigation dependency
    handleNavigateSpread,
    // Delete dependencies
    selectedTextOverlayData,
    handleDeleteTextOverlay,
    hoveredCanvasPlaceholderId,
    isImageTrayFocused,
    selectedImageIdsState,
    isSpreadsTrayFocused,
    selectedSpreadIdsState,
    handleRemoveImagesRequest,
    handleDeleteSpreadsRequest,
    // Undo/Redo/Save dependencies
    handleUndo,
    handleRedo,
    handleSaveProject,
    // Add setShowBookGutter dependency
    setShowBookGutter,
    // Add setShowFilenames dependency
    setShowFilenames,
    // Add showFilenames and setShowEndOfFilename dependencies
    showFilenames,
    setShowEndOfFilename,
    // Add setDefaultDropModeIsCover dependency - COMMENTED OUT: 'D' key handler removed
    // setDefaultDropModeIsCover,
    // Add defaultDropModeIsCover dependency (needed to calculate next state for toast) - COMMENTED OUT
    // defaultDropModeIsCover,
    // Add spreadCanvasRef dependency
    // Add toggleImageNumbers and showImageNumbers dependencies for 'n' shortcut
    toggleImageNumbers,
    showImageNumbers,
    // Add setShowQualityIndicators dependency
    setShowQualityIndicators,
    // Add showQualityIndicators dependency (needed for toast message)
    showQualityIndicators,
    // Dependencies for cover navigation with ArrowRight
    isCoverVisible,
    setIsCoverVisible,
    spreads,
    setCurrentSpreadId,
    saveStateForUndo,
    setSelectedSpreadIdsState,
    // Add dependencies for new shortcuts
    userSetShowBleedLines,
    setBleedAreaMode,
    setShowBleedArea, // From BookEditor state, used for 'b' key backward compatibility
    userSetShowSafetyMargin, // From useUserSettings hook for 'm' key
  ]);

  // Effect for 'z' key to toggle Auto-Zoom Near Edge setting
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ignore if typing in inputs
      const targetElement = event.target as HTMLElement;
      const isInputFocused = targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA' || targetElement.isContentEditable;
      if (isInputFocused) return;

      // Check for 'z' key (case-insensitive) without modifiers
      if (event.key.toLowerCase() === 'z' && !event.metaKey && !event.ctrlKey && !event.altKey && !event.shiftKey) {
        event.preventDefault();
        setAutoCoverNearEdges(prev => {
          const nextState = !prev;
          toast.info(`Auto-Zoom Near Edge: ${nextState ? 'Enabled' : 'Disabled'}`);
          return nextState;
        });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [autoCoverNearEdges, setAutoCoverNearEdges]); // Depend on the state and its setter

  // Effect for 'c' key to toggle project cover
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ignore if typing in inputs
      const targetElement = event.target as HTMLElement;
      const isInputFocused = targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA' || targetElement.isContentEditable;
      if (isInputFocused) return;

      // Check for 'c' key (case-insensitive) without modifiers
      if (event.key.toLowerCase() === 'c' && !event.metaKey && !event.ctrlKey && !event.altKey && !event.shiftKey) {
        event.preventDefault();
        setHasCover(prev => {
          const nextState = !prev;
          toast.info(`Project Cover: ${nextState ? 'Enabled' : 'Disabled'}`);
          return nextState;
        });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [setHasCover]); // Depend on the state setter

  // DEBUG: Log boundary calculations when SpreadsTray height changes
  useEffect(() => {
  }, [spreadsTrayHeight, isFocusMode]);


  // Handler for image tray width changes with optimized performance
  const handleImageTrayWidthChange = useCallback((newWidth: number) => {
    setImageTrayWidth(newWidth);
    setIsResizingTray(true);

    // Debounce the end of resizing for performance
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }

    resizeTimeoutRef.current = setTimeout(() => {
      setIsResizingTray(false);
    }, 150); // Small delay after resize stops
  }, []);

  // Handler for templates tray width changes with optimized performance
  const handleTemplatesTrayWidthChange = useCallback((newWidth: number) => {
    setTemplatesTrayWidth(newWidth);
    setIsResizingTemplatesTray(true);

    // Debounce the end of resizing for performance
    if (templatesResizeTimeoutRef.current) {
      clearTimeout(templatesResizeTimeoutRef.current);
    }

    templatesResizeTimeoutRef.current = setTimeout(() => {
      setIsResizingTemplatesTray(false);
    }, 150); // Small delay after resize stops
  }, []);

  // Start templates tray resize function
  const handleTemplatesTrayResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizingTemplatesTray(true);
    
    // If currently collapsed, expand to minimum width first
    if (isTemplatesTrayCollapsed) {
      setIsTemplatesTrayCollapsed(false);
      setTemplatesTrayWidth(260);
    }

    let startX = e.clientX;
    let lastX = e.clientX;
    let lastTime = Date.now();
    let dragDistance = 0;
    const collapseThreshold = 60; // Distance in pixels to trigger collapse
    const snapZone = 30; // Resistance zone near minimum width
    const minWidth = 260; // Minimum width before collapse

    const handleMouseMove = (e: MouseEvent) => {
      // Calculate from right edge of window
      const windowWidth = window.innerWidth;
      const rawWidth = windowWidth - e.clientX;
      
      // Track drag distance and velocity
      const currentTime = Date.now();
      const deltaX = e.clientX - lastX;
      const deltaTime = currentTime - lastTime;
      dragDistance = Math.abs(e.clientX - startX);
      
      // Calculate velocity (pixels per millisecond)
      const velocity = deltaTime > 0 ? Math.abs(deltaX) / deltaTime : 0;
      
      if (rawWidth < minWidth) {
        // In potential collapse zone - show visual feedback
        setIsInCollapseZone(true);
        
        // Only collapse if user has dragged far enough or with enough velocity
        const shouldCollapse = dragDistance > collapseThreshold || velocity > 0.5;
        
        if (shouldCollapse) {
          setIsTemplatesTrayCollapsed(true);
          setTemplatesTrayWidth(0);
          setIsInCollapseZone(false);
        } else {
          // Snap back to minimum if not enough drag distance/velocity
          setIsTemplatesTrayCollapsed(false);
          setTemplatesTrayWidth(minWidth);
        }
      } else if (rawWidth < minWidth + snapZone) {
        // In snap zone - add resistance by snapping to minimum
        setIsInCollapseZone(false);
        setIsTemplatesTrayCollapsed(false);
        setTemplatesTrayWidth(minWidth);
      } else {
        // Normal resize within bounds
        setIsInCollapseZone(false);
        setIsTemplatesTrayCollapsed(false);
        const newWidth = Math.max(minWidth, Math.min(480, rawWidth));
        setTemplatesTrayWidth(newWidth);
      }
      
      lastX = e.clientX;
      lastTime = currentTime;
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.classList.remove('resizing');

      // Clear collapse zone indicator
      setIsInCollapseZone(false);

      // If we're close to minimum but not collapsed, snap to minimum
      const windowWidth = window.innerWidth;
      const currentWidth = windowWidth - lastX;
      if (!isTemplatesTrayCollapsed && currentWidth < minWidth + snapZone && currentWidth >= minWidth) {
        setTemplatesTrayWidth(minWidth);
      }

      // Set small delay before disabling resize state (for transition)
      if (templatesResizeTimeoutRef.current) {
        clearTimeout(templatesResizeTimeoutRef.current);
      }

      templatesResizeTimeoutRef.current = setTimeout(() => {
        setIsResizingTemplatesTray(false);
      }, 150);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.classList.add('resizing');
  }, [isTemplatesTrayCollapsed]);

  // Start image tray resize function
  const handleImageTrayResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizingTray(true);
    
    const isLeft = imageTrayPosition === 'left';
    
    // If currently collapsed, expand to minimum size first
    if (isImageTrayCollapsed) {
      setIsImageTrayCollapsed(false);
      if (isLeft) {
        setImageTrayWidth(240);
      } else {
        setImageTrayHeight(240);
      }
    }

    const startPos = isLeft ? e.clientX : e.clientY;
    let lastPos = startPos;
    let lastTime = Date.now();
    let dragDistance = 0;
    const collapseThreshold = 60; // Distance in pixels to trigger collapse
    const snapZone = 30; // Resistance zone near minimum size
    const minSize = isLeft ? 190 : 190; // Minimum width/height before collapse - set to 240px for bottom with compact mode
    const maxSize = isLeft ? 480 : 400; // Maximum width/height

    const handleMouseMove = (e: MouseEvent) => {
      const currentPos = isLeft ? e.clientX : e.clientY;
      let rawSize;
      
      if (isLeft) {
        // Calculate from left edge of window for left side tray
        rawSize = currentPos;
      } else {
        // Calculate from bottom of window for bottom tray, accounting for spreadtray height
        const spreadTraySpace = isSpreadsTrayCollapsed ? 16 : spreadsTrayHeight;
        rawSize = window.innerHeight - currentPos - spreadTraySpace;
      }
      
      // Track drag distance and velocity
      const currentTime = Date.now();
      const deltaPos = currentPos - lastPos;
      const deltaTime = currentTime - lastTime;
      dragDistance = Math.abs(currentPos - startPos);
      
      // Calculate velocity (pixels per millisecond)
      const velocity = deltaTime > 0 ? Math.abs(deltaPos) / deltaTime : 0;
      
      if (rawSize < minSize) {
        // In potential collapse zone - show visual feedback
        setIsInCollapseZone(true);
        
        // Only collapse if user has dragged far enough or with enough velocity
        const shouldCollapse = dragDistance > collapseThreshold || velocity > 0.5;
        
        if (shouldCollapse) {
          setIsImageTrayCollapsed(true);
          if (isLeft) {
            setImageTrayWidth(0);
          } else {
            setImageTrayHeight(0);
          }
          setIsInCollapseZone(false);
        } else {
          // Snap back to minimum if not enough drag distance/velocity
          setIsImageTrayCollapsed(false);
          if (isLeft) {
            setImageTrayWidth(minSize);
          } else {
            setImageTrayHeight(minSize);
          }
        }
      } else {
        // Normal resize within bounds
        setIsInCollapseZone(false);
        setIsImageTrayCollapsed(false);
        const newSize = Math.max(minSize, Math.min(maxSize, rawSize));
        if (isLeft) {
          setImageTrayWidth(newSize);
        } else {
          setImageTrayHeight(newSize);
        }
      }
      
      lastPos = currentPos;
      lastTime = currentTime;
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.classList.remove('resizing');

      // Clear collapse zone indicator
      setIsInCollapseZone(false);

      // If we're close to minimum but not collapsed, snap to minimum
      const currentPos = isLeft ? lastPos : (window.innerHeight - lastPos);
      if (!isImageTrayCollapsed && currentPos < minSize + snapZone && currentPos >= minSize) {
        if (isLeft) {
          setImageTrayWidth(minSize);
        } else {
          setImageTrayHeight(minSize);
        }
      }

      // Set small delay before disabling resize state (for transition)
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      resizeTimeoutRef.current = setTimeout(() => {
        setIsResizingTray(false);
      }, 150);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.classList.add('resizing');
  }, [isImageTrayCollapsed, imageTrayPosition]);

  // Start spreads tray resize function
  const handleSpreadsTrayResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizingSpreadsTray(true);
    
    // If currently collapsed, expand to minimum height first
    if (isSpreadsTrayCollapsed) {
      setIsSpreadsTrayCollapsed(false);
      setSpreadsTrayHeight(150); // Expand to default height, not minimum
    }

    let startY = e.clientY;
    let lastY = e.clientY;
    let lastTime = Date.now();
    let dragDistance = 0;
    const collapseThreshold = 60; // Distance in pixels to trigger collapse
    const snapZone = 30; // Resistance zone near minimum height
    const minHeight = 96; // Minimum height before collapse
    const maxHeight = Math.floor(window.innerHeight / 3); // Maximum height (1/3 of viewport)

    const handleMouseMove = (e: MouseEvent) => {
      // Calculate from bottom of window (since spreads tray is at bottom)
      const windowHeight = window.innerHeight;
      const rawHeight = windowHeight - e.clientY;
      
      // Track drag distance and velocity
      const currentTime = Date.now();
      const deltaY = e.clientY - lastY;
      const deltaTime = currentTime - lastTime;
      dragDistance = Math.abs(e.clientY - startY);
      
      // Calculate velocity (pixels per millisecond)
      const velocity = deltaTime > 0 ? Math.abs(deltaY) / deltaTime : 0;
      
      if (rawHeight < minHeight) {
        // In potential collapse zone - show visual feedback
        setIsInCollapseZone(true);
        
        // Only collapse if user has dragged far enough or with enough velocity
        const shouldCollapse = dragDistance > collapseThreshold || velocity > 0.5;
        
        if (shouldCollapse) {
          setIsSpreadsTrayCollapsed(true);
          setSpreadsTrayHeight(0);
          setIsInCollapseZone(false);
        } else {
          // Snap back to minimum if not enough drag distance/velocity
          setIsSpreadsTrayCollapsed(false);
          setSpreadsTrayHeight(minHeight);
        }
      } else if (rawHeight < minHeight + snapZone) {
        // In snap zone - add resistance by snapping to minimum
        setIsInCollapseZone(false);
        setIsSpreadsTrayCollapsed(false);
        setSpreadsTrayHeight(minHeight);
      } else {
        // Normal resize within bounds
        setIsInCollapseZone(false);
        setIsSpreadsTrayCollapsed(false);
        const newHeight = Math.max(minHeight, Math.min(maxHeight, rawHeight));
        setSpreadsTrayHeight(newHeight);
      }
      
      lastY = e.clientY;
      lastTime = currentTime;
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.classList.remove('resizing');

      // Clear collapse zone indicator
      setIsInCollapseZone(false);

      // If we're close to minimum but not collapsed, snap to minimum
      const windowHeight = window.innerHeight;
      const currentHeight = windowHeight - lastY;
      if (!isSpreadsTrayCollapsed && currentHeight < minHeight + snapZone && currentHeight >= minHeight) {
        setSpreadsTrayHeight(minHeight);
      }

      // Set small delay before disabling resize state (for transition)
      if (spreadsResizeTimeoutRef.current) {
        clearTimeout(spreadsResizeTimeoutRef.current);
      }

      spreadsResizeTimeoutRef.current = setTimeout(() => {
        setIsResizingSpreadsTray(false);
      }, 150);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.classList.add('resizing');
  }, [isSpreadsTrayCollapsed]);

  // Cleanup resize timeouts on unmount
  useEffect(() => {
    return () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
      if (templatesResizeTimeoutRef.current) {
        clearTimeout(templatesResizeTimeoutRef.current);
      }
    };
  }, []);

  // Handle spreads tray height change notification
  const handleSpreadsTrayHeightChange = useCallback((newHeight: number) => {
    setSpreadsTrayHeight(newHeight);
    setIsResizingSpreadsTray(true);

    // Clear any existing timeout
    if (spreadsResizeTimeoutRef.current) {
      clearTimeout(spreadsResizeTimeoutRef.current);
    }

    // Set a new timeout to update the state after resizing stops
    spreadsResizeTimeoutRef.current = setTimeout(() => {
      setIsResizingSpreadsTray(false);
      spreadsResizeTimeoutRef.current = null;
    }, 200); // 200ms after resizing stops
  }, []);

  // --- Listen for file updates from external edits (e.g., Photoshop round trip) ---
  useEffect(() => {
    if (window.electronAPI?.onFileUpdated) {
      const removeListener = window.electronAPI.onFileUpdated((data) => {
        console.log('[ROUNDTRIP DEBUG onFileUpdated] Received:', JSON.stringify(data));
        const { originalPath, thumbnailUrl, previewUrl: rawPreviewUrl } = data;

        const cacheBuster = `?t=${Date.now()}`;
        const generateBustedUrl = (url: string) => {
          let normalized = normalizeBackgroundImageUrl(url);
          return normalized.includes('?') ? `${normalized}&${cacheBuster.substring(1)}` : `${normalized}${cacheBuster}`;
        };

        const newBustedPreviewUrl = generateBustedUrl(rawPreviewUrl);
        // const newBustedThumbnailUrl = generateBustedUrl(thumbnailUrl); // If thumbnails also need aggressive cache busting

        let wasProjectBgUpdatedThisTime = false;
        let wasDefaultSpreadBgUpdatedThisTime = false;
        let wasAnyIndividualSpreadBgUpdatedThisTime = false;
        let wasImageInMainArrayUpdated = false;
        let useBustedUrlForImagesArray = false;

        // --- Check spread backgrounds FIRST (before checking regular images) ---
        // Check individual spreads for spread background matches
        for (const spread of spreads) {
          if (spread.spreadBackgroundData?.imagePath) {
            const originalSpreadBgPath = getOriginalPathFromUrl(spread.spreadBackgroundData.imagePath);
            const storedOriginalPath = spread.spreadBackgroundData?.originalPath;
            console.log(`[ROUNDTRIP DEBUG onFileUpdated] Checking spread ${spread.id}: imagePath="${spread.spreadBackgroundData.imagePath}", extracted originalPath="${originalSpreadBgPath}", stored originalPath="${storedOriginalPath}", comparing with incoming="${originalPath}"`);
            if (originalSpreadBgPath === originalPath || storedOriginalPath === originalPath) {
              wasAnyIndividualSpreadBgUpdatedThisTime = true;
              useBustedUrlForImagesArray = true;
              console.log(`[ROUNDTRIP DEBUG onFileUpdated] Found spread background match for spread ID: ${spread.id}`);
              break;
            }
          }
        }

        // --- Determine if the updated image is used as any type of background ---
        if (projectBackgroundImagePathRef.current && originalPath === projectBackgroundImagePathRef.current) {
          wasProjectBgUpdatedThisTime = true;
          useBustedUrlForImagesArray = true;
        }

        if (defaultSpreadBackgroundRef.current?.imagePath === originalPath) {
          wasDefaultSpreadBgUpdatedThisTime = true;
          useBustedUrlForImagesArray = true;
        }

        const previewUrlForSetImages = useBustedUrlForImagesArray ? newBustedPreviewUrl : rawPreviewUrl;

        // --- Apply State Updates ---

        // 1. Handle spread background updates FIRST
        if (wasAnyIndividualSpreadBgUpdatedThisTime) {
          console.log('[ROUNDTRIP DEBUG onFileUpdated] Matched individual spread background. Updating spreads state.');
          
          window.electronAPI.regenerateThumbnails(originalPath)
            .then(refreshedImageData => {
              if (refreshedImageData) {
                console.log('[ROUNDTRIP DEBUG onFileUpdated] Spread background thumbnail regeneration successful, new dims:', refreshedImageData.naturalWidth, 'x', refreshedImageData.naturalHeight);
                
                setSpreads(currentSpreadsInUpdater =>
                  currentSpreadsInUpdater.map(spread => {
                    const spreadOriginalDiskPath = spread.spreadBackgroundData?.originalPath || getOriginalPathFromUrl(spread.spreadBackgroundData?.imagePath || '');
                    if (spreadOriginalDiskPath === originalPath) {
                      console.log(`[ROUNDTRIP DEBUG onFileUpdated] Updating individual spread BG for spread ID: ${spread.id} with cache-busted URL: ${newBustedPreviewUrl}`);
                      
                      // Get original image dimensions from images array, not from thumbnail regeneration
                      const originalImage = images.find(img => img.originalPath === originalPath);
                      const originalWidth = originalImage?.naturalWidth || spread.spreadBackgroundData?.naturalWidth;
                      const originalHeight = originalImage?.naturalHeight || spread.spreadBackgroundData?.naturalHeight;
                      
                      console.log(`[ROUNDTRIP DEBUG onFileUpdated] Using original dimensions: ${originalWidth}x${originalHeight} (from images array), not thumbnail dims: ${refreshedImageData.naturalWidth}x${refreshedImageData.naturalHeight}`);
                      
                      return {
                        ...spread,
                        spreadBackgroundData: {
                          ...(spread.spreadBackgroundData || {}), // Ensure existing data is spread first
                          imagePath: newBustedPreviewUrl,      // Use cache-busted URL for display
                          originalPath: originalPath,          // Preserve actual disk path
                          naturalWidth: originalWidth,
                          naturalHeight: originalHeight,
                          // Preserve other properties like transform, opacity
                          transform: spread.spreadBackgroundData?.transform || { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'cover' },
                          opacity: spread.spreadBackgroundData?.opacity
                        },
                      };
                    }
                    return spread;
                  })
                );

                // Also update the main images array if this image is present there,
                // ensuring its naturalWidth/Height are updated from regeneration.
                setImages(currentImages_inner => { // Use different name to avoid conflict with outer closure
                    let mainImageArrayEntryUpdated = false;
                    const newImagesArray = currentImages_inner.map(img => {
                        if (img.originalPath === originalPath) {
                            mainImageArrayEntryUpdated = true;
                            return {
                                ...img,
                                // previewUrl will be set by the general logic below if useBustedUrlForImagesArray is true
                                naturalWidth: refreshedImageData.naturalWidth || img.naturalWidth,
                                naturalHeight: refreshedImageData.naturalHeight || img.naturalHeight,
                            };
                        }
                        return img;
                    });
                    if (mainImageArrayEntryUpdated) {
                        console.log('[ROUNDTRIP DEBUG onFileUpdated] Updated natural dimensions in main images array for:', originalPath);
                        return newImagesArray;
                    }
                    return currentImages_inner;
                });

              } else {
                console.warn('[ROUNDTRIP DEBUG onFileUpdated] Spread background thumbnail regeneration failed or no data returned. Dimensions might be stale. Applying cache-bust anyway.');
                setSpreads(currentSpreadsInUpdater =>
                  currentSpreadsInUpdater.map(spread => {
                    const spreadOriginalDiskPath = spread.spreadBackgroundData?.originalPath || getOriginalPathFromUrl(spread.spreadBackgroundData?.imagePath || '');
                    if (spreadOriginalDiskPath === originalPath) {
                      const imageFromMainArray = images.find(img => img.originalPath === originalPath); // Use closure `images`
                      return {
                        ...spread,
                        spreadBackgroundData: {
                          ...(spread.spreadBackgroundData || {}),
                          imagePath: newBustedPreviewUrl,
                          originalPath: originalPath,
                          naturalWidth: imageFromMainArray?.naturalWidth || spread.spreadBackgroundData?.naturalWidth,
                          naturalHeight: imageFromMainArray?.naturalHeight || spread.spreadBackgroundData?.naturalHeight,
                          transform: spread.spreadBackgroundData?.transform || { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'cover' },
                          opacity: spread.spreadBackgroundData?.opacity
                        },
                      };
                    }
                    return spread;
                  })
                );
              }
            })
            .catch(error => {
              console.error('[ROUNDTRIP DEBUG onFileUpdated] Error regenerating spread background thumbnails:', error);
               setSpreads(currentSpreadsInUpdater =>
                  currentSpreadsInUpdater.map(spread => {
                    const spreadOriginalDiskPath = spread.spreadBackgroundData?.originalPath || getOriginalPathFromUrl(spread.spreadBackgroundData?.imagePath || '');
                    if (spreadOriginalDiskPath === originalPath) {
                       const imageFromMainArray = images.find(img => img.originalPath === originalPath); // Use closure `images`
                      return {
                        ...spread,
                        spreadBackgroundData: {
                          ...(spread.spreadBackgroundData || {}),
                          imagePath: newBustedPreviewUrl,
                          originalPath: originalPath,
                          naturalWidth: imageFromMainArray?.naturalWidth || spread.spreadBackgroundData?.naturalWidth,
                          naturalHeight: imageFromMainArray?.naturalHeight || spread.spreadBackgroundData?.naturalHeight,
                          transform: spread.spreadBackgroundData?.transform || { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'cover' },
                          opacity: spread.spreadBackgroundData?.opacity
                        },
                      };
                    }
                    return spread;
                  })
                );
            });
        }

        // 2. Update the main `images` array.
        // This needs to run if it's not an individual spread background OR if it IS an individual spread background
        // AND useBustedUrlForImagesArray is true (meaning it's also a project/default BG or we decided to bust it anyway).
        if (useBustedUrlForImagesArray || !wasAnyIndividualSpreadBgUpdatedThisTime) {
          setImages(currentImages => {
            const updatedIdsForTrayHighlight: string[] = [];
            const newImagesArray = currentImages.map(img => {
              if (img.originalPath === originalPath) {
                wasImageInMainArrayUpdated = true;
                updatedIdsForTrayHighlight.push(img.id);
                return {
                  ...img,
                  thumbnailUrl: thumbnailUrl, // Or newBustedThumbnailUrl
                  previewUrl: previewUrlForSetImages,
                };
              }
              return img;
            });

            if (wasImageInMainArrayUpdated) {
              console.log('[ROUNDTRIP DEBUG onFileUpdated] setImages: Images array was updated.');
              if (updatedIdsForTrayHighlight.length > 0) {
                setUpdatedImageIds(updatedIdsForTrayHighlight);
                setTimeout(() => setUpdatedImageIds([]), 2000);
              }
              return newImagesArray;
            }
            console.log('[ROUNDTRIP DEBUG onFileUpdated] setImages: Images array NOT updated.');
            return currentImages;
          });
        }

        // 3. Update default spread background
        if (wasDefaultSpreadBgUpdatedThisTime) {
          console.log('[ROUNDTRIP DEBUG onFileUpdated] Updating default spread background.');
          setDefaultSpreadBackground(prev => {
            // Ensure prev is not null and imagePath matches before updating
            if (prev && prev.imagePath === originalPath) {
              return {
                ...prev,
                imagePath: newBustedPreviewUrl,
              };
            }
            return prev; // Should not happen if flag is true, but good practice
          });
        }

        // 4. Update project-wide background
        if (wasProjectBgUpdatedThisTime) {
          console.log('[ROUNDTRIP DEBUG onFileUpdated] Updating project-wide background with:', newBustedPreviewUrl);
          setProjectBackgroundImage(newBustedPreviewUrl);
        }

        // --- Handle side effects (undo, dirty, toasts) ---
        if (wasProjectBgUpdatedThisTime || wasDefaultSpreadBgUpdatedThisTime || wasAnyIndividualSpreadBgUpdatedThisTime || wasImageInMainArrayUpdated) {
          saveStateForUndo();
          setIsDirty(true);
          console.log('[ROUNDTRIP DEBUG onFileUpdated] Setting isDirty=true.');

          // Updated toast messages to account for both images and backgrounds
          let toastMessage = '';
          const updateCount = (wasProjectBgUpdatedThisTime ? 1 : 0) +
                            (wasDefaultSpreadBgUpdatedThisTime ? 1 : 0) +
                            (wasAnyIndividualSpreadBgUpdatedThisTime ? 1 : 0) +
                            (wasImageInMainArrayUpdated ? 1 : 0);

          if (wasProjectBgUpdatedThisTime) {
            toastMessage = "Project background image updated.";
          } else if (wasDefaultSpreadBgUpdatedThisTime) {
            toastMessage = "Default spread background image updated.";
          } else if (wasAnyIndividualSpreadBgUpdatedThisTime) {
            toastMessage = "Spread background image updated.";
          } else if (wasImageInMainArrayUpdated) {
            toastMessage = "Image updated.";
          }

          if (updateCount > 1) {
            toastMessage = `${updateCount} items updated.`;
          }

          if (toastMessage) {
            toast.success(toastMessage);
          }
        }
        console.log('[ROUNDTRIP DEBUG onFileUpdated] End of handler.');
      });

      // Cleanup function to remove the listener
      return () => {
        removeListener();
      };
    } else {
    }
  }, [
      images, // For reading to check if image is in main array (though setImages updater is better)
      spreads, // For iterating to check individual spread backgrounds
      defaultSpreadBackground, // For checking default spread background path
      projectBackgroundImagePath, // For checking project background path
      setImages,
      setIsDirty,
      setProjectBackgroundImage,
      saveStateForUndo,
      setUpdatedImageIds,
      setSpreads,
      setDefaultSpreadBackground,
      normalizeBackgroundImageUrl, // Stable utility function
      handleUpdateSpreadBackground // Add the spread background update callback
  ]);

  // --- Render ---

  // Calculate dominant fit mode for the current spread
  const dominantFitMode = useMemo(() => {
    const currentSpreadData = spreads.find(s => s.id === currentSpreadId);
    if (!currentSpreadData || !currentSpreadData.images || currentSpreadData.images.length === 0) {
      return 'contain'; // Default if no spread or images
    }
    let coverCount = 0;
    let containCount = 0;
    let imageCount = 0;
    currentSpreadData.images.forEach(placement => {
      if (placement.imageId) {
        imageCount++;
        if (placement.transform?.fit === 'cover') {
          coverCount++;
        } else { // Default to contain if undefined or explicitly contain
          containCount++;
        }
      }
    });
    if (imageCount === 0) return 'contain'; // Default if no images placed
    return coverCount > containCount ? 'cover' : 'contain';
  }, [spreads, currentSpreadId]);

  // console.log(`[BookEditor RENDER] onlyShowPoorQuality from hook: ${onlyShowPoorQuality}`); // DEBUG LOG REMOVED

  // Update project to save function to include text overlays
  const getProjectToSave = useCallback(() => {
    const currentAspectRatio = {
      id: aspectRatioRef.current.id,
      title: aspectRatioRef.current.title,
      ratio: aspectRatioRef.current.ratio,
      dimensions: aspectRatioRef.current.dimensions,
      widthPt: aspectRatioRef.current.widthPt,
      heightPt: aspectRatioRef.current.heightPt,
      bleed: aspectRatioRef.current.bleed,
    };

    return {
      version: 5,
      aspectRatio: currentAspectRatio,
      imageGap: imageGapRef.current,
      spreads: spreadsRef.current,
      images: imagesRef.current,
      backgroundColor: backgroundColorRef.current,
      projectBackgroundImage: projectBackgroundImagePathRef.current,
      projectBackgroundImageNormalizedUrl: projectBackgroundImageRef.current,
      projectBackgroundImageOpacity: projectBackgroundImageOpacityRef.current,
      // favoriteTemplateIds: Array.from(localFavoriteTemplateIds), // Favorites are global, not saved with project
      textOverlays: textOverlaysRef.current,
      hasCover: hasCoverRef.current,
      isCoverVisible: isCoverVisibleRef.current,
      coverImage: coverImageRef.current,
      coverScale: coverScaleRef.current,
      coverTranslateX: coverFocalXRef.current, // Save coverFocalX as coverTranslateX
      coverTranslateY: coverFocalYRef.current, // Save coverFocalY as coverTranslateY
      projectBleed: projectBleedRef.current,
      projectSafetyMargin: projectSafetyMarginRef.current,
    };
  }, []); // Removed localFavoriteTemplateIds from dependency as it's not saved with project

  // Parse project data including text overlays (handled by useProjectPersistence)
  // const loadProjectData = useCallback((projectData: ProjectPersistenceData) => {
  //   // ... existing project loading logic ...

  //   // Handle text overlays
  //   if (projectData.textOverlays) {
  //     setTextOverlays(projectData.textOverlays);
  //   }
  //   // ... rest of existing loading logic ...
  // }, [/* existing dependencies */]);

  // MOVED EARLIER: handleAddTextOverlay, handleUpdateTextOverlay, handleDeleteTextOverlay

  // Function to toggle the text controls bar on and off
  const handleOpenTextControls = useCallback(() => {
    // If the controls are already open, close them
    if (selectedTextOverlayData) {
      setSelectedTextOverlayData(null);
      return;
    }

    // Otherwise, create a temporary text overlay to open the controls
    // This won't be added to the spread
    const dummyTextOverlay: TextOverlay = {
      id: 'temp-text-controls',
      spreadId: currentSpreadId || '',
      content: '',
      style: {
        fontFamily: fontFamilies[0].value, // Use the first font from fontFamilies (Helvetica)
        fontSize: 8,
        color: '#000000',
        bold: false,
        italic: false,
        underline: false,
        textAlign: 'center',
        x: 50,
        y: 50,
        width: 30,
        opacity: 1,
      }
    };

    // Open the controls without adding to the spread
    setSelectedTextOverlayData(dummyTextOverlay);
  }, [currentSpreadId, selectedTextOverlayData, setSelectedTextOverlayData]);

  // Function to add a new text element to the current spread or cover
  const handleAddNewTextElement = useCallback(() => {
    if (handleAddTextOverlay) {
      // Create a unique ID for the new text element
      const newTextId = `text-${Date.now()}`;
      
      // Determine where to add the text based on whether the cover is visible
      const targetSpreadId = isCoverVisible ? 'cover' : currentSpreadId;
      
      if (targetSpreadId) {
        const newTextOverlayData: TextOverlay = {
          id: newTextId,
          spreadId: targetSpreadId,
          content: 'Double-click to edit text',
          style: {
            fontFamily: fontFamilies[0].value, // Use the first font from fontFamilies (Helvetica)
            fontSize: 15, // Default to 15% of container height
            color: '#FFFFFF', // White
            bold: false,
            italic: false,
            underline: false,
            textAlign: 'center',
            // Position in the middle of the right half (75%) when on cover, otherwise center of spread (50%)
            x: isCoverVisible ? 75 : 50,
            y: 50, // Center vertically
            width: 30, // 30% of spread/cover width
            opacity: 0.7, // 70% opacity
          }
        };
        handleAddTextOverlay(newTextOverlayData);
        setSelectedTextOverlayData(newTextOverlayData); // Set the new text overlay as selected
        
        // Show appropriate success message based on where the text was added
        if (isCoverVisible) {
          toast.success("Text box added to cover.");
        } else {
          toast.success("Text box added to current spread.");
        }
      }
    }
  }, [handleAddTextOverlay, currentSpreadId, isCoverVisible, saveStateForUndo, setIsDirty, setSelectedTextOverlayData]); // handleAddTextOverlay is defined above

  const handleSelectTextOverlayFromCanvas = useCallback((overlay: TextOverlay | null) => {
    setSelectedTextOverlayData(overlay);
  }, [setSelectedTextOverlayData]);

  // Font families are now imported from TextEditor.tsx

  const getFontDisplayName = (fontValue: string): string => {
    const font = fontFamilies.find(f => f.value === fontValue);
    return font ? font.name : fontValue.split(',')[0].replace(/["']/g, '');
  };

  // Helper function to calculate text width based on content and style
  const calculateTextWidth = (text: string, style: TextOverlay['style'], containerHeight: number): number => {
    if (!spreadContainerRef.current) return style.width; // Return current width if container not available

    // Create a temporary span to measure text width
    const tempSpan = document.createElement('span');
    tempSpan.style.visibility = 'hidden';
    tempSpan.style.position = 'absolute';
    tempSpan.style.whiteSpace = 'nowrap';
    tempSpan.style.fontFamily = style.fontFamily;
    tempSpan.style.fontSize = `${(style.fontSize / 100) * containerHeight}px`;
    tempSpan.style.fontWeight = style.bold ? 'bold' : 'normal';
    tempSpan.style.fontStyle = style.italic ? 'italic' : 'normal';
    tempSpan.style.textDecoration = style.underline ? 'underline' : 'none';
    tempSpan.innerText = text || ' '; // Use space for empty text to maintain minimum width

    document.body.appendChild(tempSpan);
    const textWidth = tempSpan.getBoundingClientRect().width;
    document.body.removeChild(tempSpan);

    // Add padding to the measured width (20px for padding)
    const paddedWidth = textWidth + 20;

    // Calculate width as percentage of container width
    const containerWidth = spreadContainerRef.current.getBoundingClientRect().width;
    const widthPercentage = (paddedWidth / containerWidth) * 100;
    return Math.max(widthPercentage, 5); // Ensure minimum width of 5%
  };

  // Text Controls Handlers (adapted from TextEditor.tsx)
  const handleTextControlChange = (styleChanges: Partial<TextOverlay['style']>) => {
    if (selectedTextOverlayData) {
      // Find the current text overlay in the actual textOverlays array
      // This is important because the selectedTextOverlayData might not have the latest editingContent
      const currentOverlay = textOverlays.find(overlay => overlay.id === selectedTextOverlayData.id);

      // Create the updated overlay with the new style changes
      const updatedFullOverlay = {
        ...selectedTextOverlayData,
        // Preserve the editingContent if it exists in the current overlay
        ...(currentOverlay?.editingContent !== undefined ? { editingContent: currentOverlay.editingContent } : {}),
        style: {
          ...selectedTextOverlayData.style,
          ...styleChanges,
        },
      };

      // Always recalculate the width when any style property changes
      // This ensures the text box width is always appropriate for the content
      const containerHeight = spreadContainerRef.current?.getBoundingClientRect().height || 0;
      if (containerHeight > 0) {
        const textContent = currentOverlay?.editingContent !== undefined
          ? currentOverlay.editingContent
          : updatedFullOverlay.content;

        // Calculate new width based on the updated style
        const newWidth = calculateTextWidth(
          textContent,
          updatedFullOverlay.style,
          containerHeight
        );

        // Update the width in the style changes
        updatedFullOverlay.style.width = newWidth;
      }

      // Update the text overlay
      handleUpdateTextOverlay(updatedFullOverlay);
      setSelectedTextOverlayData(updatedFullOverlay); // Ensure immediate feedback for controls
    }
  };

  // Removed dragging logic for text controls (handleMouseDownOnTextControls and related useEffect)

  // Handler for updating the cover image
  const handleUpdateCoverImage = useCallback((image: ImageFile | null) => {
    saveStateForUndo();
    setIsDirty(true);
    setCoverImage(image);
  }, [saveStateForUndo, setIsDirty]);

  // Cover transform wrapper functions that properly set isDirty state
  const handleCoverScaleUpdate = useCallback((newScale: number) => {
    setCoverScale(newScale);
    if (!window.bookProofsApp?.isRestoringState) {
      setIsDirty(true);
    }
  }, [setCoverScale, setIsDirty]);

  const handleCoverFocalXUpdate = useCallback((newFocalX: number) => {
    setCoverFocalX(newFocalX);
    if (!window.bookProofsApp?.isRestoringState) {
      setIsDirty(true);
    }
  }, [setCoverFocalX, setIsDirty]);

  const handleCoverFocalYUpdate = useCallback((newFocalY: number) => {
    setCoverFocalY(newFocalY);
    if (!window.bookProofsApp?.isRestoringState) {
      setIsDirty(true);
    }
  }, [setCoverFocalY, setIsDirty]);
  
  // Effect to update coverImage when preview is generated
  useEffect(() => {
    if (!coverImage) return;
    
    // Find the matching image in the images array that might have an updated preview
    const matchingImage = images.find(img => img.id === coverImage.id);
    
    // If we found a matching image and it has a preview URL that's different from the current coverImage
    if (matchingImage &&
        matchingImage.previewUrl &&
        matchingImage.previewUrl !== coverImage.previewUrl) {
      
      
      // Update the coverImage with the new preview URL
      setCoverImage({
        ...coverImage,
        previewUrl: matchingImage.previewUrl
      });
    }
  }, [images, coverImage]);

  const handleNavigateForwardFromCover = useCallback(() => {
    setIsCoverVisible(false);
  }, [setIsCoverVisible]);

  const handleNavigateToCover = useCallback(() => {
    setIsCoverVisible(true);
  }, [setIsCoverVisible]);

  const handleLoadWithSaveCheck = async () => {
    if (isDirty) {
      if (!window.electronAPI?.confirmDialog) {
        toast.error("Confirmation dialog is not available.");
        // Fallback or decide how to proceed if confirmDialog is missing
        if (onTriggerLoadAndNavigate) {
          await onTriggerLoadAndNavigate();
        } else {
          console.error("onTriggerLoadAndNavigate is not defined, cannot proceed with load.");
          toast.error("Load functionality is currently unavailable.");
        }
        return;
      }

      const result = await window.electronAPI.confirmDialog({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Do you want to save before loading a new project?',
        buttons: ['Save', "Don't Save", 'Cancel'],
        defaultId: 0, // Default to "Save"
        cancelId: 2,  // "Cancel" is the third button (index 2)
      });

      if (result === 0) { // Save
        await handleSaveProject(); // Assumes handleSaveProject returns a promise or is awaited correctly
        // Proceed to load after saving
        if (onTriggerLoadAndNavigate) {
          await onTriggerLoadAndNavigate();
        } else {
          console.error("onTriggerLoadAndNavigate is not defined, cannot proceed with load after save.");
          toast.error("Load functionality is currently unavailable after save.");
        }
      } else if (result === 1) { // Don't Save
        if (onTriggerLoadAndNavigate) {
          await onTriggerLoadAndNavigate();
        } else {
          console.error("onTriggerLoadAndNavigate is not defined, cannot proceed with load.");
          toast.error("Load functionality is currently unavailable.");
        }
      }
      // If result is 2 (Cancel) or dialog was dismissed, do nothing.
    } else {
      // No unsaved changes, proceed directly
      if (onTriggerLoadAndNavigate) {
        await onTriggerLoadAndNavigate();
      } else {
        console.error("onTriggerLoadAndNavigate is not defined, cannot proceed with load.");
        toast.error("Load functionality is currently unavailable.");
      }
    }
  };

  // IPC Listeners Effect (moved after all handler definitions)
  useEffect(() => {
    let removeSaveListener: (() => void) | undefined;
    let removeSaveAsListener: (() => void) | undefined;
    let removeImportListener: (() => void) | undefined;
    let removeExportListener: (() => void) | undefined;
    let removeLoadListener: (() => void) | undefined;

    if (window.electronAPI) {
      if (window.electronAPI.onTriggerSave) {
        removeSaveListener = window.electronAPI.onTriggerSave(handleSaveProject);
      }
      if (window.electronAPI.onTriggerSaveAs) {
        removeSaveAsListener = window.electronAPI.onTriggerSaveAs(handleSaveProjectAs);
      }
      if (window.electronAPI.onTriggerImportImages) {
        removeImportListener = window.electronAPI.onTriggerImportImages(() => {
          if (imageTrayRef.current) {
           imageTrayRef.current.triggerFileSelect();
          } else {
           toast.error("Image tray not available to trigger import.");
          }
        });
      }
      if (window.electronAPI.onTriggerExportDialog) {
        removeExportListener = window.electronAPI.onTriggerExportDialog(initiateExportProcess);
      }
      if (window.electronAPI.onTriggerLoadProject) {
        removeLoadListener = window.electronAPI.onTriggerLoadProject(handleLoadWithSaveCheck);
      }
    }

    return () => {
      if (removeSaveListener) removeSaveListener();
      if (removeSaveAsListener) removeSaveAsListener();
      if (removeImportListener) removeImportListener();
      if (removeExportListener) removeExportListener();
      if (removeLoadListener) removeLoadListener();
    };
  }, [handleSaveProject, handleSaveProjectAs, handleAddImages, initiateExportProcess, handleLoadWithSaveCheck]);

  // Helper function to render ImageTray component
  const renderImageTray = () => (
    <ImageTray
      ref={imageTrayRef}
      images={images}
      onAddImages={handleAddImages}
      usedImageIds={usedImageIds}
      showFilenames={showFilenames}
      showEndOfFilename={showEndOfFilename}
      showRatingFilter={showRatingFilter}
      isTemplatesTrayFocused={isTemplatesTrayFocused}
      onRemoveImages={handleRemoveImagesRequest}
      hoveredCanvasPlaceholderId={hoveredCanvasPlaceholderId}
      isSpreadThumbnailHovered={isSpreadThumbnailHovered}
      onFocusChange={handleImageTrayFocusChange}
      onSelectionChange={handleImageSelectionChange}
      onWidthChange={handleImageTrayWidthChange}
      photoLibraryDisplayMode={photoLibraryDisplayMode}
      onFilesProcessedFromDrop={handleFilesDroppedOnTrayPlaceholder}
      onAutoBuild={handleAutoBuildFromImageTray}
      theme={theme}
      position={imageTrayPosition}
      height={imageTrayPosition === 'bottom' ? imageTrayHeight : undefined}
      isMultiSelectActive={isMultiSelectActive}
      imageSelectionCount={imageSelectionCount}
      onMultiSelectStateChange={handleMultiSelectStateChange}
      onPositionChange={handleImageTrayPositionChange}
    />
  );

  return (
    <div className={`h-screen flex flex-col bg-gray-100 transition-all duration-300 ease-in-out ${isFocusMode ? 'p-0' : ''}`}>
      <BookProofsHeader
        currentSpreadNumber={currentSpreadDisplayIndex}
        totalSpreads={spreads.length}
        dimensions={aspectRatio.dimensions}
        commonSize={aspectRatio.title}
        onUndo={handleUndo}
        onRedo={handleRedo}
        canUndo={canUndo}
        canRedo={canRedo}
        onSaveProject={handleSaveProject} // Use function from useProjectPersistence
        onSaveProjectAs={handleSaveProjectAs} // Use function from useProjectPersistence
        isDirty={isDirty} // Use state from useProjectPersistence
        onNewProject={handleNewProjectRequest} // Use function from useProjectPersistence
        onLoadProject={handleLoadWithSaveCheck} // Use new save check handler
        onExportPdf={initiateExportProcess} // UPDATED to call the new initiator function
        isCoverVisible={isCoverVisible} // Pass cover visibility state
        theme={theme} // Pass the theme prop
        onResetTrays={handleResetTrays} // Pass the reset trays handler
      />

      <div className={`flex flex-1 overflow-hidden transition-all duration-300 ease-in-out ${isFocusMode ? 'gap-0' : 'gap-0'}`}>
        {/* Image Tray - Left Position */}
        {imageTrayPosition === 'left' && (
        <div className={`relative h-full ${
          isFocusMode ? 'w-0 opacity-0 pointer-events-none' : ''
        } ${isImageTrayCollapsed ? `w-4 ${theme === 'dark' ? 'bg-black' : ''}` : ''}`}>
          {/* Always visible resize handle, positioned at viewport edge when collapsed */}
          <div
            className={`absolute top-0 bottom-0 w-4 flex items-center justify-center cursor-ew-resize image-tray-resize-handle z-20 ${
              isImageTrayCollapsed ? 'right-0' : 'right-[-4px]'
            }`}
            onMouseDown={handleImageTrayResizeStart}
            onDoubleClick={() => {
              if (isImageTrayCollapsed) {
                setIsImageTrayCollapsed(false);
                setImageTrayWidth(256); // Expand to default width
              } else {
                setIsImageTrayCollapsed(true);
                setImageTrayWidth(0); // Collapse
              }
            }}
            title={
              isImageTrayCollapsed 
                ? "Drag to expand • Double-click to expand" 
                : "Drag to resize • Double-click to collapse"
            }
            data-resize-handle="true"
            aria-label="Resize image tray"
            role="separator"
            style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
          >
            <div className="resize-handle-line">
              <div className="resize-handle-dot"></div>
            </div>
          </div>
          
          {/* Tray content */}
          <div className={`bg-white flex flex-col h-full ${
            !isFocusMode && !isImageTrayCollapsed ? 'border-r' : ''
          } ${theme === 'dark' ? 'border-neutral-800' : 'border-gray-200'} ${
            isResizingTray ? '' : 'transition-all duration-300 ease-in-out'
          } ${isImageTrayCollapsed ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
          style={{ 
            width: isFocusMode ? 0 : (isImageTrayCollapsed ? 0 : imageTrayWidth), 
            filter: 'none'
          }}
          >
          {renderImageTray()}
          </div>
        </div>
        )}
        {/* End Image Tray - Left Position */}

        {/* Central Area */}
        <div
          ref={centralAreaRef}
          className={cn(
            `relative flex-1 flex flex-col overflow-hidden ${isFocusMode ? 'p-0' : ''} ${isResizingTray ? '' : 'transition-all duration-300 ease-in-out'}`,
            theme === 'dark' ? 'bg-neutral-800' : 'bg-gray-100' // Apply theme-based background
          )}
        >
          {/* Text Controls - Rendered within Central Area */}
          {selectedTextOverlayData && (
            <div
              ref={textControlsBarRef}
              className="absolute bg-white rounded-none px-2 py-1.5 z-50 flex items-center flex-wrap gap-x-2 gap-y-1.5 pointer-events-auto w-full relative"
              style={{
                left: 0,
                right: 0,
                bottom: isSpreadsTrayCollapsed ? 16 : spreadsTrayHeight, // 16px for collapsed handle height
                position: 'absolute',
                display: 'flex',
                flexWrap: 'wrap',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
              onMouseDown={e => e.stopPropagation()}
              // onMouseDown removed as controls are no longer draggable
            >
              {/* Group 1: Add Text Button */}
              <div className="flex-shrink-0 order-1">
                <Button
                  variant="secondary"
                  size="sm"
                  className="h-7 px-2 flex items-center gap-1"
                  onClick={handleAddNewTextElement}
                >
                  <Type className="h-3.5 w-3.5" />
                  <span className="text-xs">Add</span>
                </Button>
              </div>

              {/* Group 2: Font Family */}
              <div className="flex-grow order-2 min-w-[120px] max-w-[200px] md:max-w-[250px]">
                <Select
                  value={selectedTextOverlayData.style.fontFamily}
                  onValueChange={(value) => handleTextControlChange({ fontFamily: value })}
                >
                  <SelectTrigger className="w-full h-8">
                    <SelectValue>
                      {getFontDisplayName(selectedTextOverlayData.style.fontFamily)}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent className="max-h-[300px]">
                    {/* Standard Fonts Group */}
                    <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">Standard Fonts</div>
                    {fontFamilies.slice(0, 8).map(font => (
                      <SelectItem
                        key={font.name}
                        value={font.value}
                        style={{ fontFamily: font.value }}
                        className="text-base"
                      >
                        {font.name}
                      </SelectItem>
                    ))}

                    {/* Script Fonts Group */}
                    <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground mt-2">Script Fonts</div>
                    {fontFamilies.slice(8).map(font => (
                      <SelectItem
                        key={font.name}
                        value={font.value}
                        style={{ fontFamily: font.value }}
                        className="text-base"
                      >
                        {font.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Group 3: Font Size */}
              <div className="flex items-center gap-1 flex-grow order-3 min-w-[120px] max-w-[200px] md:max-w-[250px]">
                <span className="text-xs flex-shrink-0">Size</span>
                <Slider
                  value={[selectedTextOverlayData.style.fontSize]}
                  min={1} max={100} step={0.5} // Percentages: 1% to 100%, step 0.5%
                  onValueChange={([val]) => handleTextControlChange({ fontSize: val })}
                  className="w-full flex-1 h-7" // Allow slider track to grow
                />
                <span className="text-xs w-12 flex-shrink-0 text-right">
                  {selectedTextOverlayData.style.fontSize >= 10
                    ? Math.round(selectedTextOverlayData.style.fontSize)
                    : selectedTextOverlayData.style.fontSize.toFixed(1)}%
                </span>
              </div>

              {/* Group 4: Text Formatting (combined style and alignment) */}
              <div className="flex gap-1 flex-shrink-0 order-4 flex-nowrap">
                {/* Font Style Buttons */}
                <div className="flex gap-0.5 flex-shrink-0">
                  <Button variant={selectedTextOverlayData.style.bold ? "secondary" : "outline"} size="icon" onClick={() => handleTextControlChange({ bold: !selectedTextOverlayData.style.bold })} className="h-7 w-7"><Bold className="h-3.5 w-3.5" /></Button>
                  <Button variant={selectedTextOverlayData.style.italic ? "secondary" : "outline"} size="icon" onClick={() => handleTextControlChange({ italic: !selectedTextOverlayData.style.italic })} className="h-7 w-7"><Italic className="h-3.5 w-3.5" /></Button>
                  <Button variant={selectedTextOverlayData.style.underline ? "secondary" : "outline"} size="icon" onClick={() => handleTextControlChange({ underline: !selectedTextOverlayData.style.underline })} className="h-7 w-7"><Underline className="h-3.5 w-3.5" /></Button>
                </div>

                {/* Text Alignment */}
                <div className="flex gap-0.5 flex-shrink-0">
                  <Button variant={selectedTextOverlayData.style.textAlign === "left" ? "secondary" : "outline"} size="icon" onClick={() => handleTextControlChange({ textAlign: 'left' })} className="h-7 w-7"><AlignLeft className="h-3.5 w-3.5" /></Button>
                  <Button variant={selectedTextOverlayData.style.textAlign === "center" ? "secondary" : "outline"} size="icon" onClick={() => handleTextControlChange({ textAlign: 'center' })} className="h-7 w-7"><AlignCenter className="h-3.5 w-3.5" /></Button>
                  <Button variant={selectedTextOverlayData.style.textAlign === "right" ? "secondary" : "outline"} size="icon" onClick={() => handleTextControlChange({ textAlign: 'right' })} className="h-7 w-7"><AlignRight className="h-3.5 w-3.5" /></Button>
                </div>

                {/* Color Picker */}
                <div className="flex-shrink-0 flex items-center">
                  <Popover open={isTextControlsColorPickerOpen} onOpenChange={setIsTextControlsColorPickerOpen}>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="h-7 w-7 p-0 flex items-center justify-center">
                        <div className="w-5 h-5 rounded" style={{ backgroundColor: selectedTextOverlayData.style.color }} />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 z-[100]" onClick={(e) => e.stopPropagation()} onMouseDown={(e) => e.stopPropagation()}>
                      <div
                        className="color-picker-container"
                        style={{
                          userSelect: 'none',
                          touchAction: 'none' // Prevent touch actions from propagating
                        }}
                        onMouseDown={(e) => {
                          // Set a flag to indicate we're dragging within the color picker
                          const pickerElement = e.currentTarget;
                          let isInsidePicker = true;

                          const onMouseMove = (moveEvent) => {
                            // Check if mouse is outside the picker bounds
                            const rect = pickerElement.getBoundingClientRect();
                            const { clientX, clientY } = moveEvent;

                            isInsidePicker = (
                              clientX >= rect.left &&
                              clientX <= rect.right &&
                              clientY >= rect.top &&
                              clientY <= rect.bottom
                            );

                            // If mouse is outside the picker, prevent default behavior
                            if (!isInsidePicker) {
                              moveEvent.stopPropagation();
                              moveEvent.preventDefault();
                            }
                          };

                          const onMouseUp = () => {
                            document.removeEventListener('mousemove', onMouseMove, true);
                            document.removeEventListener('mouseup', onMouseUp, true);
                          };

                          // Add capture phase listeners
                          document.addEventListener('mousemove', onMouseMove, true);
                          document.addEventListener('mouseup', onMouseUp, true);
                        }}
                      >
                        <ChromePicker
                          color={selectedTextOverlayData.style.color}
                          onChange={(color) => handleTextControlChange({ color: color.hex })}
                          disableAlpha
                        />
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Group 5: Opacity */}
              <div className="flex items-center gap-1 flex-grow order-5 min-w-[120px] max-w-[200px] md:max-w-[250px]">
                <span className="text-xs flex-shrink-0">Opacity</span>
                <Slider
                  value={[selectedTextOverlayData.style.opacity * 100]}
                  min={0} max={100} step={1}
                  onValueChange={([val]) => handleTextControlChange({ opacity: val / 100 })}
                  className="w-full flex-1 h-7" // Allow slider track to grow
                />
                <span className="text-xs w-9 flex-shrink-0 text-right">{Math.round(selectedTextOverlayData.style.opacity * 100)}%</span>
              </div>

              {/* Group 6: Delete and Close Buttons */}
              <div className="flex-shrink-0 order-6 ml-auto">
                <Button
                  variant="default"
                  size="icon"
                  onClick={() => handleDeleteTextOverlay(selectedTextOverlayData.id)}
                  className="h-7 w-7 bg-gray-900 hover:bg-gray-800 focus:ring-0 focus-visible:ring-0 focus:ring-offset-0 focus-visible:outline-none"
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
                {/* Close Button */}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSelectedTextOverlayData(null)}
                  className="h-7 w-7 ml-1 focus:ring-0 focus-visible:ring-0 focus:ring-offset-0 focus-visible:outline-none"
                  aria-label="Close text editor"
                >
                  <X className="h-3.5 w-3.5" />
                </Button>
              </div>
            </div>
          )}

          {/* Removed separate Image Quality Warning Dialog as it's now handled in ExportDialog */}

          {/* Missing Files Dialog */}
          <MissingFilesDialog
            open={showMissingFilesDialog}
            onOpenChange={setShowMissingFilesDialog}
            missingFiles={fileAvailability.missingFiles}
            totalFiles={fileAvailability.totalFiles}
            onUpdateBasePath={updateBasePath}
            onUpdateIndividualPath={updateIndividualPath}
            onCheckFilesAvailability={checkFilesAvailability}
            setIgnoreAllMissingFiles={setIgnoreAllMissingFiles} // Pass the new function
           onDialogClose={() => {
             // When MissingFilesDialog is closed (either by success, ignore, or 'x'),
             // re-initiate the export process if the ExportDialog was the entry point.
             // This ensures the ExportDialog reflects any changes.
             if (showExportDialog) { // Check if ExportDialog was open or intended to be open
               initiateExportProcess();
             }
           }}
          />

          {/* Top Left Buttons Container */}
          {/* Moved Preview Progress Indicator inside Canvas Wrapper below */}
          {/* Invisible Wrapper Container - contains buttons and SpreadCanvas constraint area */}
          <div className="relative flex-1 overflow-hidden">
            
            
            {/* Floating Buttons - positioned absolutely within wrapper */}
            <div
              className="absolute top-4 left-4 z-20 flex flex-row space-x-1"
              style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
            >
              {/* Toggle Fit Mode All Button */}
              <Button
                variant="ghost" size="icon"
                className="ml-1 w-7 h-7 bg-white/50 hover:bg-white/80 focus:outline-none focus-visible:outline-none focus:ring-0 focus-visible:ring-0"
                tabIndex={-1}
                onClick={() => spreadCanvasRef.current?.toggleFitModeAll()}
                title={dominantFitMode === 'contain' ? "Set All to Cover. Use \\ to toggle." : "Set All to Contain. Use \\ to toggle."}
              >
                {dominantFitMode === 'contain' ? <Expand className="w-4 h-4" /> : <Shrink className="w-4 h-4" />}
              </Button>
            </div>
            
            <div
              className="absolute top-4 right-4 z-20 flex flex-row space-x-1"
              style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
            >
              {/* Spread Background Edit Button - Always Visible */}
              {toggleBackgroundEditMode && (
              <Button
                variant="ghost" size="icon"
                className={`w-7 h-7 rounded-md focus:outline-none focus-visible:outline-none focus:ring-0 focus-visible:ring-0 ${
                  isEditingBackgroundForSpreadId === currentSpreadId 
                    ? 'bg-gray-600/50 hover:bg-gray-600/70 ring-1 ring-gray-500' 
                    : 'bg-white/50 hover:bg-white/80'
                }`}
                tabIndex={-1}
                title={isEditingBackgroundForSpreadId === currentSpreadId ? "Exit Background Edit Mode (S)" : "Edit Spread Background (S)"}
                onClick={() => toggleBackgroundEditMode(currentSpreadId)}
              >
                <Layers className="h-4 w-4" />
              </Button>
              )}
              
              {/* Text Editor Button */}
              <Button
                variant="ghost" size="icon"
                className="w-7 h-7 bg-white/50 hover:bg-white/80 rounded-md focus:outline-none focus-visible:outline-none focus:ring-0 focus-visible:ring-0"
                tabIndex={-1}
                title={selectedTextOverlayData ? "Close text edit controls" : "Open text edit controls"}
                onClick={handleOpenTextControls}
              >
                <Type className="h-4 w-4" />
              </Button>
              {/* Focus Mode Button */}
              <Button
                variant="ghost" size="icon"
                className="w-7 h-7 bg-white/50 hover:bg-white/80 rounded-md focus:outline-none focus-visible:outline-none focus:ring-0 focus-visible:ring-0"
                tabIndex={-1}
                title={isFocusMode ? "Exit Focus Mode (Ctrl+F)" : "Enter Focus Mode (Ctrl+F)"}
                onClick={toggleFocusMode}
              >
                {isFocusMode ? <Minimize2 className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
              </Button>
            </div>

            {/* SpreadCanvas Constraint Container - GREEN BORDER for testing */}
            <div 
              className="absolute inset-x-0 top-12 bottom-0 overflow-hidden flex items-center justify-center"
              style={{
                // Ensure this container stays above background elements
                zIndex: 20,
              }}
            >
              {/* Canvas Wrapper - Available Space Measurement */}
              <div
                ref={spreadContainerRef}
                className="w-full h-full flex items-center justify-center overflow-hidden relative"
                style={{
                  // HARD BOUNDARIES: Available space between red and blue lines
                  height: isFocusMode ? '85vh' : `calc(100vh - 48px - ${isSpreadsTrayCollapsed ? 16 : spreadsTrayHeight}px - ${imageTrayPosition === 'bottom' && !isImageTrayCollapsed ? imageTrayHeight + 30 : 0}px - 20px)`,
                  maxHeight: isFocusMode ? '85vh' : `calc(100vh - 48px - ${isSpreadsTrayCollapsed ? 16 : spreadsTrayHeight}px - ${imageTrayPosition === 'bottom' && !isImageTrayCollapsed ? imageTrayHeight + 30 : 0}px - 20px)`,
                  minHeight: '200px',
                  transition: isResizingTray ? 'none' : 'all 0.3s ease-in-out',
                  zIndex: 25
                }}
              >
            {/* Preview Progress Indicator (Moved Here) */}
            {useMemo(() => {
              // Show progress for either import-time preview generation or on-demand preview generation
              const missingPreviewCount = images.filter(img => !img.previewUrl).length;
              
              // Check for import-time preview generation (original behavior)
              if (importProgress?.stage === 'generating_previews' && missingPreviewCount > 0) {
                return (
                  <div className="absolute bottom-6 left-4 bg-gray-800 text-white text-xs px-3 py-1.5 rounded-md shadow-lg z-40 flex items-center gap-2">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span>Generating Previews ({importProgress.current}/{importProgress.total})... ({missingPreviewCount} remaining)</span>
                  </div>
                );
              }
              
              // Check for on-demand preview generation (new behavior)
              if (previewProgress.inProgress && previewProgress.pendingPaths.length > 0) {
                return (
                  <div className="absolute bottom-6 left-4 bg-gray-800 text-white text-xs px-3 py-1.5 rounded-md shadow-lg z-40 flex items-center gap-2">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span>Generating Previews ({previewProgress.current}/{previewProgress.total})... ({previewProgress.pendingPaths.length} remaining)</span>
                  </div>
                );
              }
              
              return null;
            }, [images, importProgress, previewProgress])}
                {/* SpreadCanvas Size Enforcer - FORCES exact calculated dimensions */}
                <div
                  className="flex items-center justify-center overflow-hidden"
                  style={{
                    // FORCE SpreadCanvas to exact calculated dimensions
                    width: `${spreadContainerSize.width}px`,
                    height: `${spreadContainerSize.height}px`,
                    maxWidth: `${spreadContainerSize.width}px`,
                    maxHeight: `${spreadContainerSize.height}px`,
                    minWidth: `${spreadContainerSize.width}px`,
                    minHeight: `${spreadContainerSize.height}px`,
                    // Prevent any overflow beyond calculated bounds
                    position: 'relative',
                    flexShrink: 0,
                    flexGrow: 0
                  }}
                >

            {hasCover && isCoverVisible ? (
              <ProjectCover
                ref={projectCoverRef} // Pass the ref
                projectWidth={aspectRatio.widthPt || 0} // Single page width
                projectHeight={aspectRatio.heightPt || 0}
                fullSpreadWidth={(aspectRatio.widthPt || 0) * 2} // Pass full spread width
                fullSpreadHeight={aspectRatio.heightPt || 0}    // Pass full spread height
                onNavigateForward={handleNavigateForwardFromCover}
                coverImage={coverImage}
                onUpdateCoverImage={handleUpdateCoverImage}
                trackPreviewRequest={handleTrackPreviewRequest}
                showQualityIndicators={showQualityIndicators} // Pass quality indicators setting
                dpiWarningThresholdPercent={dpiWarningThresholdPercent} // Pass DPI threshold
                getImageDimensions={getImageDimensions} // Pass the image dimensions function
                textOverlays={textOverlays} // Pass text overlays
                onUpdateTextOverlay={handleUpdateTextOverlay} // Pass text overlay update handler
                onSelectTextOverlay={handleSelectTextOverlayFromCanvas} // Pass text overlay select handler
                textControlsBarRef={textControlsBarRef} // Pass text controls bar ref
                // Cover transform props
                coverScale={coverScale}
                coverFocalX={coverFocalX}
                coverFocalY={coverFocalY}
                onUpdateCoverScale={handleCoverScaleUpdate}
                onUpdateCoverFocalX={handleCoverFocalXUpdate}
                onUpdateCoverFocalY={handleCoverFocalYUpdate}
                isRestoringProject={window.bookProofsApp?.isRestoringState || false} // Pass restoration state
                theme={theme} // Pass the theme prop
              />
            ) : (
              <SpreadCanvas
                ref={spreadCanvasRef}
                onFocusChange={handleCanvasFocusChange}
                trackPreviewRequest={handleTrackPreviewRequest}
                renderedSpreads={spreads}
                currentSpreadId={currentSpreadId}
                currentRenderedIndex={currentSpreadIndex}
                images={images}
                imageGap={gapForEditor}
                allSpreadLayouts={allSpreadLayouts}
                spreadDimensionsPt={{ width: aspectRatio.widthPt || 0, height: aspectRatio.heightPt || 0 }}
                bleedValue={projectBleed * 72} // Always pass the actual bleed value in points
                displayTrimLines={userShowBleedLines} // Pass the separate prop for trim line visibility
                onUpdateSpreadImages={handleUpdateSpreadImages}
                currentSpreadNumber={currentSpreadDisplayIndex}
                totalSpreads={spreads.length}
                onNavigateSpread={handleNavigateSpread}
                onAddSpread={handleAddSpread}
                addSpreadOnArrow={addSpreadOnArrow}
                onSelectTemplate={handleSelectTemplateById}
                allTemplates={allTemplates}
                usedImageIds={usedImageIds}
                onUpdateImageTransform={handleUpdateImageTransform}
                defaultDropModeIsCover={defaultDropModeIsCover}
                isTemplatesTrayFocused={isTemplatesTrayFocused}
                onActivateTemplateFilter={activateTemplateFilter}
                isFocusMode={isFocusMode}
                onToggleFocusMode={toggleFocusMode}
                showVisualDropChoice={showVisualDropChoice}
                showDragIcon={showDragIcon}
                autoSwitchTemplateOnRemove={autoSwitchTemplateOnRemove}
                onRemoveImageAndSwitchTemplate={handleRemoveImageAndSwitchTemplate}
                onRemoveMultipleImagesAndSwitchTemplate={handleRemoveMultipleImagesAndSwitchTemplate}
                onHoverChange={handleCanvasHoverChange}
                findExactTemplateForImageCount={findExactTemplateForImageCount}
                containerWidthPx={spreadContainerSize.width}
                containerHeightPx={spreadContainerSize.height}
                getImageDimensions={getImageDimensions}
                onUpdateThumbnailSnapshot={handleUpdateThumbnailSnapshot} // Pass the handler
                onClearImageSelection={handleClearImageSelection} // Pass the clear selection callback
                onSelectionChange={handleCanvasSelectionChange} // Pass the selection change callback
                showBookGutter={showBookGutter} // Pass book gutter setting to SpreadCanvas
                autoCoverNearEdges={autoCoverNearEdges} // Pass the auto-cover setting
                showQualityIndicators={showQualityIndicators} // Pass the quality indicators setting
                dpiWarningThresholdPercent={dpiWarningThresholdPercent} // Pass DPI threshold
                backgroundColor={backgroundColor} // Pass background color
                projectBackgroundImage={projectBackgroundImage} // Pass project background image
                backgroundImageZoom={backgroundImageZoom} // Pass zoom level
                backgroundImagePanX={backgroundImagePanX} // Pass pan X position
                backgroundImagePanY={backgroundImagePanY} // Pass pan Y position
                projectBackgroundImageOpacity={projectBackgroundImageOpacity} // Pass opacity
                projectBackgroundImagePath={projectBackgroundImagePath} // Pass the actual file path if available
                onAutoBuildFromDrop={handleAutoBuildFromCanvasDrop} // Pass the existing handler for direct autobuild
                onOfferAutobuildRequest={handleOfferAutobuildRequest} // Pass the NEW handler to request the dialog
                // Text overlay props
                textOverlays={textOverlays}
                onUpdateTextOverlay={handleUpdateTextOverlay}
                onSelectTextOverlay={handleSelectTextOverlayFromCanvas} // Added to SpreadCanvas
                textControlsBarRef={textControlsBarRef} // Pass the ref to SpreadCanvas
                isTextEditorActive={!!selectedTextOverlayData} // Pass text editor active state
                isEditingBackgroundForSpreadId={isEditingBackgroundForSpreadId} // Pass current editing ID
                toggleBackgroundEditMode={toggleBackgroundEditMode} // Pass toggle function for SpreadCanvas's own button
                onToggleBackgroundEditMode={toggleBackgroundEditMode} // Pass toggle function for SpreadBackgroundManager
                // Spread Background Callbacks
                onUpdateSpreadBackground={handleUpdateSpreadBackground}
                onSetSpreadBackgroundFromImageFile={handleSetSpreadBackgroundFromImageFile}
                onApplyBackgroundToAll={handleApplyBackgroundToAll} // Pass the apply handler
                onClearAllBackgrounds={handleClearAllBackgrounds} // Pass the clear all handler
                // Cover navigation
                hasCover={hasCover}
                onNavigateToCover={handleNavigateToCover}
                showBleedArea={showBleedArea} // Pass bleed area visibility
                bleedAreaMode={bleedAreaMode} // Pass bleed area mode
                showSafetyMargin={userShowSafetyMargin} // Pass safety margin visibility from hook
                safetyMarginValuePt={projectSafetyMargin * 72} // Pass per-project safety margin value in points
                theme={theme} // Pass the theme prop
                // Border props
                imageBorderSize={imageBorderSize}
                imageBorderColor={imageBorderColor}
                // Custom template props
                customTemplates={customTemplates}
                onUpdateCustomTemplate={handleUpdateCustomTemplate}
              />
            )}
                </div>
                {/* END: SpreadCanvas Size Enforcer */}
            </div>
            </div>
          </div>
          
          {/* Image Tray - Bottom Position */}
          {imageTrayPosition === 'bottom' && (
          <div className={`relative w-full ${
            isFocusMode ? 'h-0 opacity-0 pointer-events-none' : ''
          } ${isImageTrayCollapsed ? 'h-4' : ''}`}>
            {/* Always visible resize handle, positioned at viewport edge when collapsed */}
            <div
              className={`absolute left-0 right-0 h-4 flex items-center justify-center cursor-ns-resize spread-tray-resize-handle z-20 ${
                isImageTrayCollapsed ? 'bottom-0' : 'top-[-4px]'
              }`}
              onMouseDown={handleImageTrayResizeStart}
              onDoubleClick={() => {
                if (isImageTrayCollapsed) {
                  setIsImageTrayCollapsed(false);
                  setImageTrayHeight(240); // Expand to minimum height with compact mode
                } else {
                  setIsImageTrayCollapsed(true);
                  setImageTrayHeight(0); // Collapse
                }
              }}
              title={
                isImageTrayCollapsed 
                  ? "Drag to expand • Double-click to expand" 
                  : "Drag to resize • Double-click to collapse"
              }
              data-resize-handle="true"
              aria-label="Resize image tray"
              role="separator"
              style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
            >
              <div className="resize-handle-line horizontal">
                <div className="resize-handle-dot"></div>
              </div>
            </div>
            
            {/* Tray content */}
            <div className={`bg-white w-full ${
              !isFocusMode && !isImageTrayCollapsed ? 'border-t' : ''
            } ${theme === 'dark' ? 'border-neutral-800' : 'border-gray-200'} ${
              isResizingTray ? '' : 'transition-all duration-300 ease-in-out'
            } ${isImageTrayCollapsed ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
            style={{ 
              height: isFocusMode ? 0 : (isImageTrayCollapsed ? 0 : `${imageTrayHeight}px`), 
              filter: 'none'
            }}
            >
            {renderImageTray()}
            </div>
          </div>
          )}
          {/* End Image Tray - Bottom Position */}
          
          {/* Spreads Tray */}
          <div className={`relative w-full ${
            isFocusMode ? 'h-0 opacity-0 pointer-events-none' : ''
          } ${isSpreadsTrayCollapsed ? 'h-4' : ''}`}>
            {/* Always visible resize handle, positioned at viewport edge when collapsed */}
            <div
              className={`absolute left-0 right-0 h-4 flex items-center justify-center cursor-ns-resize spread-tray-resize-handle z-20 ${
                isSpreadsTrayCollapsed ? 'bottom-0' : 'top-[-4px]'
              }`}
              onMouseDown={handleSpreadsTrayResizeStart}
              onDoubleClick={() => {
                if (isSpreadsTrayCollapsed) {
                  setIsSpreadsTrayCollapsed(false);
                  setSpreadsTrayHeight(150); // Expand to default height
                } else {
                  setIsSpreadsTrayCollapsed(true);
                  setSpreadsTrayHeight(0); // Collapse
                }
              }}
              title={
                isSpreadsTrayCollapsed 
                  ? "Drag to expand • Double-click to expand" 
                  : "Drag to resize • Double-click to collapse"
              }
              data-resize-handle="true"
              aria-label="Resize spreads tray"
              role="separator"
              style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
            >
              <div className="resize-handle-line horizontal">
                <div className="resize-handle-dot"></div>
              </div>
            </div>
            
            {/* Tray content */}
            <div className={`bg-white w-full ${
              !isFocusMode && !isSpreadsTrayCollapsed ? 'border-t' : ''
            } ${theme === 'dark' ? 'border-neutral-800' : 'border-gray-200'} ${
              isResizingSpreadsTray ? '' : 'transition-all duration-300 ease-in-out'
            } ${isSpreadsTrayCollapsed ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
            style={{ 
              height: isFocusMode ? 0 : (isSpreadsTrayCollapsed ? 0 : `${spreadsTrayHeight}px`), 
              filter: 'none'
            }}
            >
            <SpreadsTray
              ref={spreadsTrayRef}
              spreads={spreads}
              currentSpreadId={currentSpreadId}
              onSelectSpread={handleSelectSpread}
              onAddSpread={handleAddSpread}
              onDeleteMultipleSpreads={handleDeleteMultipleSpreads}
              onDeleteAllSpreadsAndAddBlank={handleDeleteAllSpreadsAndAddBlank} // Add the new prop
              onFocusChange={handleSpreadsTrayFocusChange}
              onSelectionChange={handleSpreadSelectionChange}
              onReorderSpreads={handleReorderSpreads}
              onAddSpreadFromImages={handleAddSpreadFromImages}
              templateMap={templateMap}
              allImages={images}
              onHoverChange={handleSpreadThumbnailHover}
              usedImageIds={usedImageIds}
              onHeightChange={handleSpreadsTrayHeightChange}
              trayHeight={isSpreadsTrayCollapsed ? 0 : spreadsTrayHeight}
              spreadDimensionsPt={{ width: aspectRatio.widthPt || 0, height: aspectRatio.heightPt || 0 }}
              defaultDropModeIsCover={defaultDropModeIsCover} // Keep passing this
              spreadBackgroundImage={projectBackgroundImage} // Pass the project background image from BookEditor state
              bleedValue={projectBleed * 72} // Convert inches to points
              imageBorderSize={imageBorderSize}
              imageBorderColor={imageBorderColor}
              projectBackgroundImageOpacity={projectBackgroundImageOpacity} // Pass opacity
              projectBackgroundImageZoom={backgroundImageZoom}
              projectBackgroundImagePanX={backgroundImagePanX}
              projectBackgroundImagePanY={backgroundImagePanY}
              projectBackgroundImagePath={projectBackgroundImagePath} // Pass original path of project background
              projectBackgroundImageNaturalWidth={images.find(img => img.originalPath === projectBackgroundImagePath)?.naturalWidth}
              projectBackgroundImageNaturalHeight={images.find(img => img.originalPath === projectBackgroundImagePath)?.naturalHeight}
              canvasBackgroundColor={backgroundColor} // Pass the canvas background color from settings
              imageGap={gapForEditor} // Pass multiplied gap values for consistency with SpreadCanvas
              updatedImageIds={updatedImageIds} // Pass the IDs of images updated via Photoshop roundtrip
              onAddImageToExistingSpread={handleAddImageToExistingSpread} // Pass the new callback
              onRemoveImagesFromSource={handleRemoveImagesFromSource} // Pass the source cleanup callback
              maxTemplateSize={maxTemplateSize} // Pass max template size
              onOfferAutobuildRequest={handleOfferAutobuildRequest} // Pass autobuild offer handler
              trackPreviewRequest={handleTrackPreviewRequest} // Pass preview request handler
              // dominantFitModeForDrop={dominantFitMode} // REMOVED prop pass
              defaultSpreadBackground={defaultSpreadBackground} // Pass default spread background
              theme={theme} // Pass the theme prop
              isFocusMode={isFocusMode} // Pass isFocusMode down to SpreadsTray
              customTemplates={customTemplates} // Pass custom templates
            />
            </div>
          </div>
        </div>

        {/* Right Tray (Templates/Settings) */}
        <div className={`relative h-full ${
          isFocusMode ? 'w-0 opacity-0 pointer-events-none' : ''
        } ${isTemplatesTrayCollapsed ? `w-4 ${theme === 'dark' ? 'bg-black' : ''}` : ''}`}>
          {/* Always visible resize handle, positioned at viewport edge when collapsed */}
          <div
            className={`absolute top-0 bottom-0 w-4 flex items-center justify-center cursor-ew-resize image-tray-resize-handle z-20 ${
              isTemplatesTrayCollapsed ? 'left-0' : 'left-[-4px]'
            }`}
            onMouseDown={handleTemplatesTrayResizeStart}
            onDoubleClick={() => {
              if (isTemplatesTrayCollapsed) {
                setIsTemplatesTrayCollapsed(false);
                setTemplatesTrayWidth(320); // Expand to default width
              } else {
                setIsTemplatesTrayCollapsed(true);
                setTemplatesTrayWidth(0); // Collapse
              }
            }}
            title={
              isTemplatesTrayCollapsed 
                ? "Drag to expand • Double-click to expand" 
                : "Drag to resize • Double-click to collapse"
            }
            data-resize-handle="true"
            aria-label="Resize templates tray"
            role="separator"
            style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
          >
            <div className="resize-handle-line">
              <div className="resize-handle-dot"></div>
            </div>
          </div>
          
          {/* Tray content */}
          <div className={`bg-white flex flex-col h-full ${
            !isFocusMode && !isTemplatesTrayCollapsed ? 'border-l border-gray-200' : ''
          } ${isResizingTemplatesTray ? '' : 'transition-all duration-300 ease-in-out'} ${
            isTemplatesTrayCollapsed ? 'opacity-0 pointer-events-none' : 'opacity-100'
          }`}
          style={{ 
            width: isFocusMode ? 0 : (isTemplatesTrayCollapsed ? 0 : templatesTrayWidth), 
            filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' 
          }}
          >
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <div className="flex">
              <button
                className={`flex-1 py-4 text-center font-medium focus:outline-none ${selectedTab === 'templates' ? 'text-gray-900 border-b-2 border-gray-900' : 'text-gray-500 hover:text-gray-700'}`}
                onClick={() => {
                  console.log('\ud83d\udce6 Templates tab clicked, switching from', selectedTab, 'to templates');
                  setSelectedTab('templates');
                  // Focus the templates tray when switching to templates tab
                  setTimeout(() => {
                    console.log('\ud83d\udd0d Calling focusTray() after tab switch');
                    templatesTrayRef.current?.focusTray();
                  }, 0);
                }}
              >Templates</button>
              <button
                className={`flex-1 py-4 text-center font-medium focus:outline-none ${selectedTab === 'settings' ? 'text-gray-900 border-b-2 border-gray-900' : 'text-gray-500 hover:text-gray-700'}`}
                onClick={() => setSelectedTab('settings')}
              >Settings</button>
            </div>
          </div>
          {/* Content */}
          {selectedTab === 'templates' && (
            <TemplatesTray
              ref={templatesTrayRef}
              triggerFilterCount={filterToTrigger}
              triggerSelectFirst={triggerSelectFirst}
              templateIdToSkip={templateIdToSkip}
              onSelectTemplate={handleSelectTemplate}
              onPreviewTemplate={setPreviewTemplateId}
              onFocus={handleTemplatesTrayFocus}
              onBlur={handleTemplatesTrayBlur}
              favoriteTemplateIds={localFavoriteTemplateIds}
              onToggleFavorite={handleToggleFavoriteTemplate}
              onEscape={handleEscapeFromTemplates}
              onNavigateSpread={handleNavigateSpread}
              activeTemplateId={activeTemplateIdForTray}
              currentSpreadPlacedImageCount={currentSpreadPlacedImageCount}
              theme={theme} // Pass the theme prop
              projectDimension={getProjectDimension(aspectRatio)} // Add project dimension filtering
              spreadAspectRatio={aspectRatio.widthPt && aspectRatio.heightPt ? (aspectRatio.widthPt * 2) / aspectRatio.heightPt : undefined} // Calculate spread aspect ratio (width * 2 / height)
              onFiltersChange={handleTemplateFiltersChange} // Add filters change callback
            />
          )}
          {selectedTab === 'settings' && (
            <SettingsPanel
              aspectRatio={aspectRatio}
              imageGap={imageGap}
              handleImageGapChange={handleImageGapChange}
              backgroundColor={backgroundColor} // Pass background color
              handleBackgroundColorChange={handleBackgroundColorChange} // Pass handler
              showFilenames={showFilenames}
              setShowFilenames={setShowFilenames} // Pass setter directly
              showEndOfFilename={showEndOfFilename}
              setShowEndOfFilename={setShowEndOfFilename} // Pass setter directly
              showRatingFilter={showRatingFilter}
              setShowRatingFilter={setShowRatingFilter} // Pass setter directly
              defaultDropModeIsCover={defaultDropModeIsCover}
              setDefaultDropModeIsCover={setDefaultDropModeIsCover} // Pass setter directly
              addSpreadOnArrow={addSpreadOnArrow}
              setAddSpreadOnArrow={setAddSpreadOnArrow} // Pass setter directly
              showVisualDropChoice={showVisualDropChoice}
              setShowVisualDropChoice={setShowVisualDropChoice} // Pass setter directly
              showDragIcon={showDragIcon}
              setShowDragIcon={setShowDragIcon} // Pass setter directly
              autoSwitchTemplateOnRemove={autoSwitchTemplateOnRemove}
              setAutoSwitchTemplateOnRemove={setAutoSwitchTemplateOnRemove} // Pass setter directly
              autosaveOnSpreadTurn={autosaveOnSpreadTurn} // Pass new prop
              setAutosaveOnSpreadTurn={setAutosaveOnSpreadTurn} // Pass new prop setter
              resetImageTransformsOnTemplateSwitch={resetImageTransformsOnTemplateSwitch} // Pass new prop
              setResetImageTransformsOnTemplateSwitch={setResetImageTransformsOnTemplateSwitch} // Pass new prop setter
              showBookGutter={showBookGutter} // Pass book gutter setting
              setShowBookGutter={setShowBookGutter} // Pass book gutter setter
              photoLibraryDisplayMode={photoLibraryDisplayMode}
              setPhotoLibraryDisplayMode={setPhotoLibraryDisplayMode}
              autoCoverNearEdges={autoCoverNearEdges} // Pass auto-cover setting
              setAutoCoverNearEdges={setAutoCoverNearEdges} // Pass auto-cover setter
              showQualityIndicators={showQualityIndicators}
              setShowQualityIndicators={setShowQualityIndicators} // Pass quality indicators setter
              dpiWarningThresholdPercent={dpiWarningThresholdPercent} // Pass DPI threshold setting
              setDpiWarningThresholdPercent={setDpiWarningThresholdPercent} // Pass DPI threshold setter
              hasCover={hasCover} // Pass new toggle state
              setHasCover={setHasCover} // Pass new toggle setter
              projectBackgroundImage={projectBackgroundImage} // Pass project background image URL
              setProjectBackgroundImage={handleSetProjectBackground} // Pass combined setter
              projectBackgroundImageOriginalDimensions={projectBackgroundImageOriginalDimensions} // Pass original dimensions
              setProjectBackgroundImageOriginalDimensions={setProjectBackgroundImageOriginalDimensions} // Pass original dimensions setter
              backgroundImageZoom={backgroundImageZoom}
              setBackgroundImageZoom={handleBackgroundImageZoomChange}
              backgroundImagePanX={backgroundImagePanX}
              setBackgroundImagePanX={handleBackgroundImagePanXChange}
              backgroundImagePanY={backgroundImagePanY}
              setBackgroundImagePanY={handleBackgroundImagePanYChange}
              projectBackgroundImageOpacity={projectBackgroundImageOpacity}
              setProjectBackgroundImageOpacity={setProjectBackgroundImageOpacity}
              onCheckFilesAvailability={() => {
                checkFilesAvailability().then(allFilesAvailable => {
                  if (!allFilesAvailable) {
                    setShowMissingFilesDialog(true);
                  } else {
                    toast.success('All image files are available.');
                  }
                });
              }}
              // Bleed settings
              showBleedLines={userShowBleedLines}
              setShowBleedLines={userSetShowBleedLines}
              projectBleed={projectBleed}
              setProjectBleed={setProjectBleed}
              projectSafetyMargin={projectSafetyMargin}
              setProjectSafetyMargin={setProjectSafetyMargin}
              showBleedArea={showBleedArea} // Pass bleed area visibility
              setShowBleedArea={setShowBleedArea} // Pass bleed area visibility setter
              bleedAreaMode={bleedAreaMode} // Pass bleed area mode
              setBleedAreaMode={setBleedAreaMode} // Pass bleed area mode setter
              showSafetyMargin={userShowSafetyMargin}
              setShowSafetyMargin={userSetShowSafetyMargin}
              // Image Border Settings
              imageBorderSize={imageBorderSize}
              setImageBorderSize={setImageBorderSize}
              imageBorderColor={imageBorderColor}
              setImageBorderColor={setImageBorderColor}
              // ImageTray position settings
              imageTrayPosition={imageTrayPosition}
              setImageTrayPosition={setImageTrayPosition}
              // Project state management
              setIsDirty={setIsDirty}
              // Theme settings
              theme={theme}
              setTheme={setTheme}
              // Focus mode state
              isFocusMode={isFocusMode}
              // Panel collapse state
              isTemplatesTrayCollapsed={isTemplatesTrayCollapsed}
            />
          )}
          
          {/* ContextualStatusTray - positioned below templates/settings content */}
          <ContextualStatusTray
            isMultiSelectActive={isMultiSelectActive}
            imageSelectionCount={imageSelectionCount}
            isSpreadThumbnailHovered={isSpreadThumbnailHovered}
            theme={theme}
          />
          
          </div>
        </div>
      </div>

      {/* Import Progress Overlay */}
      {/* Show overlay if importProgress exists, has a total, and is NOT generating previews, complete, or error */}
      {importProgress && importProgress.total > 0 && importProgress.stage !== 'generating_previews' && importProgress.stage !== 'complete' && importProgress.stage !== 'error' && (
        <div className="absolute inset-0 bg-gray-900 bg-opacity-75 flex flex-col items-center justify-center z-50">
          <Loader2 className="h-10 w-10 text-white animate-spin mb-4" />
          {/* Display the message generated by the hook */}
          <p className="text-white text-lg mb-2">{importProgress?.message || 'Processing...'}</p>
          {/* Progress bar remains the same */}
          {importProgress?.total > 0 && (
             <div className="w-64 mt-2">
               <Progress
                 value={(importProgress.current / importProgress.total) * 100}
                 className="h-2 bg-gray-700 [&>div]:bg-white"
               />
             </div>
           )}
        </div>
      )}

      {/* Image Deletion Dialog */}
      <AlertDialog open={isImageDeleteAlertOpen} onOpenChange={setIsImageDeleteAlertOpen}>
        <AlertDialogContent onKeyDown={(e) => { if (e.key === 'Enter') { e.preventDefault(); confirmImageDeletion(); } }}>
          <AlertDialogHeader>
            <AlertDialogTitle>{`Remove Image${imageIdsToDelete.length > 1 ? 's' : ''}?`}</AlertDialogTitle>
            <AlertDialogDescription>
              {imageIdsToDelete.length > 1
                ? "Are you sure you want to remove the selected images from this project?"
                : `Are you sure you want to remove ${imageIdsToDelete.length} image from this project?`}
              <br />
              This action cannot be undone. The image file(s) will NOT be deleted from your computer.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => { setIsImageDeleteAlertOpen(false); setImageIdsToDelete([]); }}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmImageDeletion} className="bg-red-600 hover:bg-red-700">Remove</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Alert Dialog for Spreads Deletion */}
      <AlertDialog open={isSpreadDeleteAlertOpen} onOpenChange={setIsSpreadDeleteAlertOpen}>
        <AlertDialogContent onKeyDown={(e) => { if (e.key === 'Enter') { e.preventDefault(); confirmSpreadDeletion(); } }}>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {spreadIdsToDeleteState.length === spreads.length
                ? 'Delete All Spreads?'
                : spreadIdsToDeleteState.length > 1
                  ? `Delete ${spreadIdsToDeleteState.length} Spreads?`
                  : 'Delete Spread?'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {spreadIdsToDeleteState.length === spreads.length
                ? 'This will delete all spreads and add a new blank spread.'
                : `This will delete the selected ${spreadIdsToDeleteState.length > 1 ? `${spreadIdsToDeleteState.length} spreads` : '1 spread'}.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmSpreadDeletion} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* No Template Available / Auto-build Dialog */}
      <AlertDialog open={isNoTemplateDialogVisible} onOpenChange={setIsNoTemplateDialogVisible}>
        <AlertDialogContent onKeyDown={(e) => {
          // Allow Enter key to trigger Auto-build if it's a valid option
          if (e.key === 'Enter' && noTemplateDialogData && noTemplateDialogData.buildInstructions.length > 0) {
            e.preventDefault();
            // Simulate clicking the Auto-build action button
            const actionButton = (e.currentTarget as HTMLElement).querySelector<HTMLButtonElement>('[data-alert-dialog-action="autobuild"]');
            actionButton?.click();
          }
        }}>
          <AlertDialogHeader>
            <AlertDialogTitle>No Template Available</AlertDialogTitle>
            <AlertDialogDescription>
              There is no template available for {noTemplateDialogData?.imageCount || 'the selected number of'} image(s).
              <br />
              Would you like to automatically build spreads?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setIsNoTemplateDialogVisible(false);
              setNoTemplateDialogData(null);
            }}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              data-alert-dialog-action="autobuild" // Add data attribute for Enter key handling
              onClick={() => {
                if (noTemplateDialogData) {
                  const { imageCount: count, droppedImages: dImages, targetIndex: tIndex, initialFit: iFit, buildInstructions: bInstructions } = noTemplateDialogData;

                  if (bInstructions.length === 0) {
                    toast.error("Auto-build failed: No instructions generated.");
                    setIsNoTemplateDialogVisible(false);
                    setNoTemplateDialogData(null); // Clear data on failure too
                    return;
                  }

                  saveStateForUndo();
                  setIsDirty(true);

                  const newAutoSpreads: Spread[] = [];
                  let imagesConsumedCount = 0;
                  let firstNewSpreadId = '';

                  bInstructions.forEach((instruction, index) => {
                    const autoSpreadId = `spread-auto-${Date.now()}-${index}`;
                    if (index === 0) firstNewSpreadId = autoSpreadId;
                    const templateForSpread = instruction.template;
                    const imagesForThisSpread = dImages.slice(imagesConsumedCount, imagesConsumedCount + templateForSpread.images.length);

                    const placements: ImagePlacement[] = templateForSpread.images.map((ph, pIdx) => ({
                      placeholderId: ph.id,
                      imageId: imagesForThisSpread[pIdx]?.id,
                      transform: { scale: 1, focalX: 0.5, focalY: 0.5, fit: iFit }
                    }));

                    newAutoSpreads.push({
                      id: autoSpreadId,
                      templateId: templateForSpread.id,
                      images: placements
                    });
                    imagesConsumedCount += imagesForThisSpread.length;
                  });

                  setSpreads(prevSpreads => {
                    const updatedSpreads = [...prevSpreads];
                    updatedSpreads.splice(tIndex, 0, ...newAutoSpreads);
                    return updatedSpreads;
                  });

                  if (firstNewSpreadId) {
                    setCurrentSpreadId(firstNewSpreadId);
                  }

                  toast.success(`Auto-built ${newAutoSpreads.length} spread(s) for ${count} images.`);

                  // Trigger preview generation after state update
                  newAutoSpreads.forEach(spread => {
                    spread.images.forEach(placement => {
                      if (placement.imageId) {
                        const image = imagesRef.current.find(img => img.id === placement.imageId); // Use ref for latest images state
                        if (image && !image.previewUrl && !requestedPreviewPathsRef.current.has(image.originalPath)) {
                          let pathForPreview = image.originalPath;
                          if (window.bookProofsApp?.getUpdatedFilePath) {
                            const updatedPath = window.bookProofsApp.getUpdatedFilePath(image.originalPath);
                            if (updatedPath) pathForPreview = updatedPath;
                          }
                          requestedPreviewPathsRef.current.add(image.originalPath);
                          window.electronAPI.regenerateThumbnails(pathForPreview)
                            .then(result => { if (!result) requestedPreviewPathsRef.current.delete(image.originalPath); })
                            .catch(error => {
                              console.error(`[BookEditor Autobuild Dialog] Error requesting preview for ${pathForPreview}:`, error);
                              toast.error(`Failed to generate preview for ${image.name}.`);
                              requestedPreviewPathsRef.current.delete(image.originalPath);
                            });
                        }
                      }
                    });
                  });

                  imageTrayRef.current?.clearSelection();
                  setIsNoTemplateDialogVisible(false);
                  setNoTemplateDialogData(null); // Clear data after successful action
                }
              }}
              disabled={!noTemplateDialogData || noTemplateDialogData.buildInstructions.length === 0}
            >
              Auto-build ({noTemplateDialogData?.buildInstructions?.length || 0} spread{noTemplateDialogData?.buildInstructions?.length === 1 ? '' : 's'})
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Offer Autobuild Dialog (for SpreadsTray drop) */}
      <AlertDialog open={isOfferAutobuildDialogVisible} onOpenChange={setIsOfferAutobuildDialogVisible}>
        <AlertDialogContent onKeyDown={(e) => {
          // Allow Enter key to trigger Auto-build
          if (e.key === 'Enter') {
            e.preventDefault();
            const actionButton = (e.currentTarget as HTMLElement).querySelector<HTMLButtonElement>('[data-alert-dialog-action="autobuild-offer"]');
            actionButton?.click();
          }
        }}>
          <AlertDialogHeader>
            <AlertDialogTitle>Too Many Images</AlertDialogTitle>
            <AlertDialogDescription>
              {offerAutobuildDialogData ? (
                `You dropped ${offerAutobuildDialogData.droppedImages.length} image(s). The current spread has ${offerAutobuildDialogData.existingImageCount} image(s) placed, and the largest template available holds ${offerAutobuildDialogData.maxTemplateSize} images.`
              ) : (
                "The number of dropped images exceeds the capacity of the largest available template."
              )}
              <br />
              Would you like to automatically build new spreads for the dropped images?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setIsOfferAutobuildDialogVisible(false);
              setOfferAutobuildDialogData(null);
            }}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              data-alert-dialog-action="autobuild-offer" // Unique data attribute
              onClick={() => {
                if (offerAutobuildDialogData) {
                  // Check if we need to use batch processing (more than 100 images)
                  if (offerAutobuildDialogData.droppedImages.length > 100) {
                    // Use the batch processing function for large image sets
                    handleAutoBuildInBatches(
                      offerAutobuildDialogData.droppedImages,
                      offerAutobuildDialogData.currentSpreadId
                    );
                  } else {
                    // Use the original handler for smaller image sets
                    handleAutoBuildFromCanvasDrop(
                      offerAutobuildDialogData.droppedImages,
                      offerAutobuildDialogData.currentSpreadId
                    );
                  }
                  setIsOfferAutobuildDialogVisible(false);
                  setOfferAutobuildDialogData(null);
                }
              }}
              disabled={!offerAutobuildDialogData}
            >
              Auto-build Spreads
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Autobuild Duplicate Image Warning Dialog */}
      <AlertDialog open={showAutobuildDuplicateWarning} onOpenChange={setShowAutobuildDuplicateWarning}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Duplicate Images in Autobuild Selection</AlertDialogTitle>
            <AlertDialogDescription>
              {autobuildDuplicateWarningData && (
                <>
                  {autobuildDuplicateWarningData.duplicateImages.length === 1 ? (
                    <>
                      The image "{autobuildDuplicateWarningData.duplicateImages[0].name}" has already been used in this project.
                    </>
                  ) : (
                    <>
                      {autobuildDuplicateWarningData.duplicateImages.length} of the {autobuildDuplicateWarningData.selectedImages.length} selected images have already been used in this project.
                    </>
                  )}
                  <br /><br />
                  Would you like to proceed with autobuild anyway? This will create spreads with duplicate images.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleAutobuildDuplicateCancel}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleAutobuildDuplicateConfirm}>
              Proceed with Autobuild
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* New Export Dialog */}
      <ExportDialog
        isOpen={showExportDialog}
        onOpenChange={setShowExportDialog}
        onExport={executePdfGenerationWithCoverOption}
        hasCover={hasCover}
        hasCoverImage={!!coverImage}
        missingFilesCount={exportDialogCounts.missingFiles}
        lowResImagesCount={exportDialogCounts.lowResImages}
        emptySpreadsCount={exportDialogCounts.emptySpreads}
        onLocateFilesClick={handleLocateFilesFromDialog}
        onReviewImagesClick={handleReviewImagesFromDialog}
        onDeleteEmptySpreads={handleDeleteEmptySpreadsFromDialog} // Changed prop
        qualityIssueDetails={dialogQualityIssueDetails}
        onNavigateToSpread={handleNavigateToSpreadFromExport}
        onExportCancel={handleExportCancel} // Pass the cancel handler
        isExporting={isExporting} // Pass the exporting state
      />

    </div>
  );

}; // <<< END OF BookEditor COMPONENT FUNCTION

export default BookEditor;


