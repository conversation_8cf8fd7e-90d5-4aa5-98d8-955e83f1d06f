
import React from 'react';
import { ImageIcon } from 'lucide-react';
import { TemplateImage } from '@/lib/templates/interfaces'; // Import from new location

// Removed local TemplateImage interface definition
// Removed leftover brace from deleted interface

interface TemplateProps {
  id: string;
  name: string;
  images: TemplateImage[];
  onClick: () => void;
  selected?: boolean;
  spreadAspectRatio?: number; // Spread aspect ratio for letterboxing/pillarboxing
}

const Template = ({ id, name, images, onClick, selected = false, spreadAspectRatio }: TemplateProps) => {
  // Calculate container dimensions based on spread aspect ratio
  // Default to 4:3 aspect ratio if no spread aspect ratio is provided
  const targetAspectRatio = spreadAspectRatio || (4/3);
  
  // Calculate letterboxing/pillarboxing
  const containerAspectRatio = 4/3; // Fixed container aspect ratio (1.333...)
  const needsLetterboxing = targetAspectRatio > containerAspectRatio; // Spread is wider than container
  const needsPillarboxing = targetAspectRatio < containerAspectRatio; // Spread is taller than container
  
  let templateWidth = 100;
  let templateHeight = 100;
  let templateTop = 0;
  let templateLeft = 0;
  
  if (needsLetterboxing) {
    // Add horizontal bars (letterboxing) - template should be shorter to fit wider aspect ratio
    templateHeight = (containerAspectRatio / targetAspectRatio) * 100;
    templateTop = (100 - templateHeight) / 2;
  } else if (needsPillarboxing) {
    // Add vertical bars (pillarboxing) - template should be narrower to fit taller aspect ratio
    templateWidth = (containerAspectRatio / targetAspectRatio) * 100;
    templateLeft = (100 - templateWidth) / 2;
  }


  return (
    <div
      className={`w-full flex flex-col items-center cursor-pointer transition-all ${
        selected ? 'scale-[1.02]' : 'hover:scale-[1.01]'
      }`}
      onClick={onClick}
    >
      <div 
        className={`relative w-full border rounded-md overflow-hidden ${
          selected 
            ? 'border-primary ring-2 ring-primary ring-opacity-50 shadow-md' 
            : 'border-gray-200 hover:border-gray-300'
        }`}
        style={{ 
          paddingTop: '75%', // 4:3 aspect ratio container
          backgroundColor: needsLetterboxing || needsPillarboxing ? '#fafafa' : '#fafafa' // Show lighter gray background
        }}
      >
        {/* Template content area with letterboxing/pillarboxing */}
        <div
          className="absolute bg-white"
          style={{
            top: `${templateTop}%`,
            left: `${templateLeft}%`,
            width: `${templateWidth}%`,
            height: `${templateHeight}%`,
          }}
        >
          {images.map((image) => (
            <div
              key={image.id}
              className="absolute bg-gray-100 border border-white"
              style={{
                left: `${image.x * 100}%`,
                top: `${image.y * 100}%`,
                width: `${image.width * 100}%`,
                height: `${image.height * 100}%`,
                boxShadow: 'inset 0 0 0 1px #d9d9d9',
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Template;
