import { useMemo, useState, useRef, useCallback, useEffect } from 'react'; // Add useState, useRef, useCallback, useEffect
import React from 'react';
import { createPortal } from 'react-dom';
import throttle from 'lodash/throttle'; // Import throttle
// RgbaStringColorPicker is now in RgbaInputColorPicker.tsx
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"; // Added for color picker popover
import RgbaInputColorPicker from './RgbaInputColorPicker'; // Import the new component
import { normalizeBackgroundImageUrl } from '@/utils/imageUtils';
import { getCachedDataUrl, setCachedDataUrl, getNormalizedPathKey, fetchDataUrl, isTimestampProcessed, markTimestampProcessed, clearCachedDataUrl } from '@/utils/dataUrlCache';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AspectRatio } from "@/components/ui/aspect-ratio"; // Import AspectRatio
import { Slider } from "@/components/ui/slider"; // <<< ADDED Slider import
import { Button } from '@/components/ui/button'; // Added Button import
import { FolderSearch } from 'lucide-react'; // Added icon import
import ImageGapControl from './ImageGapControl'; // Assuming ImageGapControl is in the same directory
import { ProofingControls } from './ProofingComponents'; // Import ProofingControls
import { ImageQualityInfo, ImageQualityStatus, TARGET_DPI, calculateEffectiveDpi } from '@/lib/imageQuality'; // Import for DPI warning
import BackgroundImageQualityWarning from './BackgroundImageQualityWarning'; // Import the warning component

interface SettingsPanelProps {
  // Theme Settings
  theme: 'light' | 'dark';
  setTheme: React.Dispatch<React.SetStateAction<'light' | 'dark'>>;

  // Project Settings
  aspectRatio: {
    id: string; // Added id to aspectRatio prop
    widthPt?: number;
    heightPt?: number;
    bleed?: string; // Already added in previous step, ensuring it's here
  };
  imageGap: number;
  handleImageGapChange: (newValue: number) => void;
  backgroundColor: string;
  handleBackgroundColorChange: (newColor: string) => void;

  // User Settings (from useUserSettings hook)
  showFilenames: boolean;
  setShowFilenames: React.Dispatch<React.SetStateAction<boolean>>;
  showEndOfFilename: boolean;
  setShowEndOfFilename: React.Dispatch<React.SetStateAction<boolean>>;
  showRatingFilter: boolean;
  setShowRatingFilter: React.Dispatch<React.SetStateAction<boolean>>;
  defaultDropModeIsCover: boolean;
  setDefaultDropModeIsCover: React.Dispatch<React.SetStateAction<boolean>>;
  addSpreadOnArrow: boolean;
  setAddSpreadOnArrow: React.Dispatch<React.SetStateAction<boolean>>;
  autoSwitchTemplateOnRemove: boolean;
  setAutoSwitchTemplateOnRemove: React.Dispatch<React.SetStateAction<boolean>>;
  showVisualDropChoice: boolean;
  setShowVisualDropChoice: React.Dispatch<React.SetStateAction<boolean>>;
  showDragIcon: boolean;
  setShowDragIcon: React.Dispatch<React.SetStateAction<boolean>>;
  autosaveOnSpreadTurn: boolean; // New prop
  setAutosaveOnSpreadTurn: React.Dispatch<React.SetStateAction<boolean>>; // New prop setter
  resetImageTransformsOnTemplateSwitch: boolean; // New prop
  setResetImageTransformsOnTemplateSwitch: React.Dispatch<React.SetStateAction<boolean>>; // New prop setter
  showBookGutter: boolean; // Setting to show/hide book gutter
  setShowBookGutter: React.Dispatch<React.SetStateAction<boolean>>; // Setter for book gutter
  photoLibraryDisplayMode: string; // Setting for photo library display mode
  setPhotoLibraryDisplayMode: React.Dispatch<React.SetStateAction<string>>; // Setter for photo library display mode
  autoCoverNearEdges: boolean; // New prop for auto-cover setting
  setAutoCoverNearEdges: React.Dispatch<React.SetStateAction<boolean>>; // New prop setter for auto-cover
  showQualityIndicators: boolean; // Setting for image quality indicators
  setShowQualityIndicators: React.Dispatch<React.SetStateAction<boolean>>; // Setter for quality indicators
  dpiWarningThresholdPercent: number; // DPI threshold percentage (50-70) for warnings
  setDpiWarningThresholdPercent: React.Dispatch<React.SetStateAction<number>>; // Setter for DPI threshold
  hasCover: boolean; // Prop for project cover state
  setHasCover: React.Dispatch<React.SetStateAction<boolean>>; // Prop setter for project cover state
  projectBackgroundImage: string | null; // New prop for project background image URL
  setProjectBackgroundImage: (imageUrl: string | null, imagePath: string | null) => void; // Corrected type for combined setter
  projectBackgroundImageOriginalDimensions: { width: number; height: number } | null; // Original dimensions for DPI calculation
  setProjectBackgroundImageOriginalDimensions: React.Dispatch<React.SetStateAction<{ width: number; height: number } | null>>; // Setter for original dimensions
  // Background image zoom and pan
  backgroundImageZoom: number; // Zoom level for background image
  setBackgroundImageZoom: (zoom: number) => void; // Setter for zoom level
  backgroundImagePanX: number; // Pan X position for background image (0-100%)
  setBackgroundImagePanX: (panX: number) => void; // Setter for pan X
  backgroundImagePanY: number; // Pan Y position for background image (0-100%)
  setBackgroundImagePanY: (panY: number) => void; // Setter for pan Y
  projectBackgroundImageOpacity: number; // Opacity for background image (0-1)
  setProjectBackgroundImageOpacity: (opacity: number) => void; // Setter for opacity
  // File availability checking
  onCheckFilesAvailability: () => void; // Function to trigger file availability check

  // Focus mode state
  isFocusMode?: boolean; // Whether the app is in focus mode (hides portal)
  
  // Panel collapse state
  isTemplatesTrayCollapsed?: boolean; // Whether the templates/settings panel is collapsed (hides portal)

  // Bleed settings
  showBleedLines: boolean;
  setShowBleedLines: React.Dispatch<React.SetStateAction<boolean>>;
  projectBleed: number; // Bleed value in inches
  setProjectBleed: (bleed: number) => void; // Setter for bleed value
  
  // Safety margin settings
  projectSafetyMargin: number; // Safety margin value in inches
  setProjectSafetyMargin: (safetyMargin: number) => void; // Setter for safety margin value

  // Bleed area visibility
  showBleedArea: boolean;
  setShowBleedArea: React.Dispatch<React.SetStateAction<boolean>>;
  bleedAreaMode: 'hide' | 'indicate' | 'ignore';
  setBleedAreaMode: React.Dispatch<React.SetStateAction<'hide' | 'indicate' | 'ignore'>>;
  // Safety Margin settings
  showSafetyMargin: boolean;
  setShowSafetyMargin: React.Dispatch<React.SetStateAction<boolean>>;
  // Image Border Settings
  imageBorderSize: number;
  setImageBorderSize: React.Dispatch<React.SetStateAction<number>>;
  imageBorderColor: string;
  setImageBorderColor: React.Dispatch<React.SetStateAction<string>>;
  // ImageTray position settings
  imageTrayPosition: 'left' | 'bottom';
  setImageTrayPosition: React.Dispatch<React.SetStateAction<'left' | 'bottom'>>;
  // Project state management
  setIsDirty: React.Dispatch<React.SetStateAction<boolean>>; // Setter for dirty state
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  theme,
  setTheme,
  aspectRatio, // Destructure aspectRatio
  imageGap,
  handleImageGapChange,
  backgroundColor,
  handleBackgroundColorChange,
  showFilenames,
  setShowFilenames,
  showEndOfFilename,
  setShowEndOfFilename,
  showRatingFilter,
  setShowRatingFilter,
  defaultDropModeIsCover,
  setDefaultDropModeIsCover,
  addSpreadOnArrow,
  setAddSpreadOnArrow,
  showVisualDropChoice,
  setShowVisualDropChoice,
  showDragIcon,
  setShowDragIcon,
  autoSwitchTemplateOnRemove,
  setAutoSwitchTemplateOnRemove,
  autosaveOnSpreadTurn, // Destructure new prop
  setAutosaveOnSpreadTurn, // Destructure new prop setter
  resetImageTransformsOnTemplateSwitch, // Destructure new prop
  setResetImageTransformsOnTemplateSwitch, // Destructure new prop setter
  showBookGutter, // Destructure book gutter setting
  setShowBookGutter, // Destructure book gutter setter
  photoLibraryDisplayMode, // Destructure photo library display mode
  setPhotoLibraryDisplayMode, // Destructure photo library display mode setter
  autoCoverNearEdges, // Destructure auto-cover setting
  setAutoCoverNearEdges, // Destructure auto-cover setter
  showQualityIndicators, // Destructure quality indicators setting
  setShowQualityIndicators, // Destructure quality indicators setter
  dpiWarningThresholdPercent, // Destructure DPI threshold setting
  setDpiWarningThresholdPercent, // Destructure DPI threshold setter
  hasCover, // Destructure project cover prop
  setHasCover, // Destructure project cover prop setter
  projectBackgroundImage, // Destructure project background image
  setProjectBackgroundImage, // Destructure project background image setter
  projectBackgroundImageOriginalDimensions, // Destructure original dimensions
  setProjectBackgroundImageOriginalDimensions, // Destructure original dimensions setter
  backgroundImageZoom, // Destructure background image zoom
  setBackgroundImageZoom, // Destructure background image zoom setter
  backgroundImagePanX, // Destructure background image pan X
  setBackgroundImagePanX, // Destructure background image pan X setter
  backgroundImagePanY, // Destructure background image pan Y
  setBackgroundImagePanY, // Destructure background image pan Y setter
  projectBackgroundImageOpacity, // Destructure background image opacity
  setProjectBackgroundImageOpacity, // Destructure background image opacity setter
  onCheckFilesAvailability, // Destructure file availability check function
  isFocusMode = false, // Destructure focus mode state
  isTemplatesTrayCollapsed = false, // Destructure panel collapse state
  // Destructure bleed settings
  showBleedLines,
  setShowBleedLines,
  projectBleed,
  setProjectBleed,
  // Destructure safety margin settings
  projectSafetyMargin,
  setProjectSafetyMargin,
  // Destructure bleed area visibility
  showBleedArea,
  setShowBleedArea,
  bleedAreaMode,
  setBleedAreaMode,
  // Destructure safety margin settings
  showSafetyMargin,
  setShowSafetyMargin,
  // Destructure image border settings
  imageBorderSize,
  setImageBorderSize,
  imageBorderColor,
  setImageBorderColor,
  // Destructure imageTray position settings
  imageTrayPosition,
  setImageTrayPosition,
  // Destructure project state management
  setIsDirty,
}) => {
  // State for background image interaction
  const [isBackgroundHovered, setIsBackgroundHovered] = useState(false);
  const [isPanningBackground, setIsPanningBackground] = useState(false);
  const [panStartCoords, setPanStartCoords] = useState<{ x: number; y: number; startPanX: number; startPanY: number } | null>(null);
  const backgroundImageContainerRef = useRef<HTMLDivElement | null>(null); // Ref for the image container
  const [containerDims, setContainerDims] = useState<{ width: number; height: number } | null>(null); // Store container dimensions
  const [naturalImageDimensions, setNaturalImageDimensions] = useState<{ width: number; height: number } | null>(null); // Store natural image dimensions
  const [displayableBackgroundImageSrc, setDisplayableBackgroundImageSrc] = useState<string | null>(null); // For Data URL
  
  // State for background image quality check
  const [backgroundImageQualityInfo, setBackgroundImageQualityInfo] = useState<ImageQualityInfo | null>(null);
  const [isBackgroundImageLoading, setIsBackgroundImageLoading] = useState<boolean>(false);
  
  // Use project-level original dimensions (passed as props)
  // Local state for tracking the path to detect changes
  const [originalImagePath, setOriginalImagePath] = useState<string | null>(null);
  
  // State for color portal overlay
  const [colorButtonRect, setColorButtonRect] = useState<DOMRect | null>(null);
  const [isScrolling, setIsScrolling] = useState<boolean>(false);
  const isScrollingRef = useRef<boolean>(false);
  const colorButtonRef = useRef<HTMLButtonElement>(null);

  // Effect to detect path changes and preserve original dimensions during photoshop round trips
  useEffect(() => {
    if (!projectBackgroundImage) {
      // Clear everything when no background image
      setProjectBackgroundImageOriginalDimensions(null);
      setOriginalImagePath(null);
      return;
    }
    
    const currentPath = getNormalizedPathKey(projectBackgroundImage);
    const isCache = currentPath.includes('preview_cache') || currentPath.includes('thumbnail_cache');
    
    // If we have stored original dimensions and the new path is a cache path,
    // this is likely a photoshop round trip - preserve the original dimensions
    if (projectBackgroundImageOriginalDimensions && originalImagePath && isCache && !originalImagePath.includes('cache')) {
      // Keep originalImageDimensions but update the path to the new cache path
      setOriginalImagePath(currentPath);
    }
    // If the path changed to a non-cache path and we're not going from cache to original,
    // this might be a different image - clear stored dimensions
    else if (originalImagePath && currentPath !== originalImagePath && !isCache && !originalImagePath.includes('cache')) {
      setProjectBackgroundImageOriginalDimensions(null);
      setOriginalImagePath(null);
    }
  }, [projectBackgroundImage, projectBackgroundImageOriginalDimensions, originalImagePath, setProjectBackgroundImageOriginalDimensions]);

  // Effect to load projectBackgroundImage as a Data URL
  useEffect(() => {
    if (projectBackgroundImage && window.electronAPI?.getImageDataUrl) {
      setIsBackgroundImageLoading(true);
      setDisplayableBackgroundImageSrc(null); // Clear previous image while loading new one
      
      
      fetchDataUrl(projectBackgroundImage)
        .then(dataUrl => {
          setIsBackgroundImageLoading(false);
          if (dataUrl) {
            setDisplayableBackgroundImageSrc(dataUrl);
          } else {
            setDisplayableBackgroundImageSrc(null); // Fallback or clear
          }
        })
        .catch(error => {
          setIsBackgroundImageLoading(false);
          setDisplayableBackgroundImageSrc(null); // Fallback or clear
        });
    } else if (!projectBackgroundImage) {
      setDisplayableBackgroundImageSrc(null); // Clear if projectBackgroundImage is null
      setIsBackgroundImageLoading(false);
    }
  }, [projectBackgroundImage]);

  // Effect to detect project background file updates (photoshop round trips) and refresh Data URL
  useEffect(() => {
    if (!projectBackgroundImage) return;
    
    // Extract timestamp from cache-busted URL (e.g., "?t=1748452216307")
    const timestampMatch = projectBackgroundImage.match(/[?&]t=(\d+)/);
    const timestamp = timestampMatch ? timestampMatch[1] : null;
    
    // Only refresh Data URL if this is a photoshop round trip (has timestamp) and we haven't processed this timestamp yet
    if (timestamp && displayableBackgroundImageSrc && !isTimestampProcessed(timestamp)) {
      
      // Mark this timestamp as processed globally
      markTimestampProcessed(timestamp);
      
      // Clear cache for this file since it was updated
      clearCachedDataUrl(projectBackgroundImage);
      
      setIsBackgroundImageLoading(true);
      
      // Fetch fresh Data URL
      fetchDataUrl(projectBackgroundImage)
        .then(dataUrl => {
          setIsBackgroundImageLoading(false);
          if (dataUrl) {
            setDisplayableBackgroundImageSrc(dataUrl);
          } else {
            setDisplayableBackgroundImageSrc(null);
          }
        })
        .catch(error => {
          setIsBackgroundImageLoading(false);
          setDisplayableBackgroundImageSrc(null);
        });
    } else if (timestamp && isTimestampProcessed(timestamp)) {
    }
  }, [projectBackgroundImage, displayableBackgroundImageSrc]); // Trigger when projectBackgroundImage changes and we have existing Data URL

  // Throttled update for panning
  const throttledSetPan = useCallback(
    throttle((x: number, y: number) => {
      setBackgroundImagePanX(x);
      setBackgroundImagePanY(y);
    }, 50), // Throttle interval (ms)
    [setBackgroundImagePanX, setBackgroundImagePanY] // Dependencies
  );

  // Effect to measure container dimensions using ResizeObserver
  useEffect(() => {
    const element = backgroundImageContainerRef.current;
    if (!element) return;
  
    const resizeObserver = new ResizeObserver((entries: ResizeObserverEntry[]) => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0) {
          setContainerDims({ width, height });
        }
      }
    });
  
    resizeObserver.observe(element);
  
    // Initial measurement
    const { offsetWidth, offsetHeight } = element;
    if (offsetWidth > 0 && offsetHeight > 0) {
      setContainerDims({ width: offsetWidth, height: offsetHeight });
    }
  
  
    return () => {
      resizeObserver.unobserve(element);
    };
  }, [projectBackgroundImage]); // Re-observe if the background image changes (container might reappear)

  const handleBackgroundMouseEnter = () => setIsBackgroundHovered(true);
  const handleBackgroundMouseLeave = () => {
      setIsBackgroundHovered(false);
      // Stop panning if mouse leaves while panning
      if (isPanningBackground) {
          setIsPanningBackground(false);
          setPanStartCoords(null);
          document.body.style.cursor = 'default'; // Reset cursor
          // Optionally trigger final throttled update if needed
          throttledSetPan.flush();
      }
  };
  
  const handleBackgroundMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.button !== 0) return; // Only left click
    e.preventDefault();
    e.stopPropagation();
  
    setIsPanningBackground(true);
    setPanStartCoords({
      x: e.clientX,
      y: e.clientY,
      startPanX: backgroundImagePanX, // Store starting pan percentage
      startPanY: backgroundImagePanY, // Store starting pan percentage
    });
    document.body.style.cursor = 'grabbing'; // Change cursor for visual feedback
  };
  
  const handleBackgroundMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isPanningBackground || !panStartCoords || !containerDims || !naturalImageDimensions) return;

    const scale = backgroundImageZoom;
    const currentFocalX = panStartCoords.startPanX / 100; // Convert start pan (0-100) to focal (0-1)
    const currentFocalY = panStartCoords.startPanY / 100; // Convert start pan (0-100) to focal (0-1)

    const dx = e.clientX - panStartCoords.x;
    const dy = e.clientY - panStartCoords.y;

    // Calculate rendered image dimensions within the container
    let renderedWidthPx: number;
    let renderedHeightPx: number;
    const containerRatio = containerDims.width / containerDims.height;
    const imageRatio = naturalImageDimensions.width / naturalImageDimensions.height;

    let baseRenderedW: number;
    let baseRenderedH: number;
    // 'cover' logic for global background
    if (imageRatio > containerRatio) { // Image is wider than container (relative to its height)
      baseRenderedH = containerDims.height; // Fit to height
      baseRenderedW = baseRenderedH * imageRatio; // Calculate width based on image ratio
    } else { // Image is taller or same aspect ratio as container
      baseRenderedW = containerDims.width; // Fit to width
      baseRenderedH = baseRenderedW / imageRatio; // Calculate height based on image ratio
    }
    renderedWidthPx = baseRenderedW * scale;
    renderedHeightPx = baseRenderedH * scale;

    if (renderedWidthPx <= 0 || renderedHeightPx <= 0) return;

    const focalShiftX = -(dx / renderedWidthPx);
    const focalShiftY = -(dy / renderedHeightPx);

    let newFocalX = currentFocalX + focalShiftX;
    let newFocalY = currentFocalY + focalShiftY;

    // Constrain focal points to prevent showing whitespace (similar to SpreadBackgroundManager)
    const visiblePortionX = containerDims.width / renderedWidthPx;
    const visiblePortionY = containerDims.height / renderedHeightPx;
    const maxDeviationX = renderedWidthPx > containerDims.width ? (1 - visiblePortionX) / 2 : 0;
    const maxDeviationY = renderedHeightPx > containerDims.height ? (1 - visiblePortionY) / 2 : 0;

    newFocalX = Math.max(0.5 - maxDeviationX, Math.min(0.5 + maxDeviationX, newFocalX));
    newFocalY = Math.max(0.5 - maxDeviationY, Math.min(0.5 + maxDeviationY, newFocalY));
    
    // Ensure focal points are strictly within 0-1
    newFocalX = Math.max(0, Math.min(1, newFocalX));
    newFocalY = Math.max(0, Math.min(1, newFocalY));

    // Convert constrained focal points (0-1) back to pan percentages (0-100) for the setters
    throttledSetPan(newFocalX * 100, newFocalY * 100);
  };
  
  const handleBackgroundMouseUp = () => {
    if (isPanningBackground) {
      setIsPanningBackground(false);
      setPanStartCoords(null);
      document.body.style.cursor = 'default'; // Reset cursor
      // Flush any remaining throttled updates
      throttledSetPan.flush();
    }
  };
  
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault(); // Necessary to allow dropping
  };

  // Calculate numerical aspect ratio for the spread (width * 2 / height)
  const numericalAspectRatio = useMemo(() => {
    const width = aspectRatio?.widthPt;
    const height = aspectRatio?.heightPt;
    if (width && height && height > 0) {
      return (width * 2) / height;
    }
    return 2; // Default to a 2:1 ratio if calculation fails
  }, [aspectRatio]);

  // Function to check background image quality
  const checkBackgroundImageQuality = useCallback((imageWidth: number, imageHeight: number) => {
    if (!containerDims || !aspectRatio.widthPt || !aspectRatio.heightPt) return;
  
    // Use original dimensions if available, otherwise use the provided dimensions
    const finalWidth = projectBackgroundImageOriginalDimensions?.width || imageWidth;
    const finalHeight = projectBackgroundImageOriginalDimensions?.height || imageHeight;
    
  
    // Create a mock ImageFile object with the dimensions
    const mockImage = {
      id: 'background-image',
      naturalWidth: finalWidth,
      naturalHeight: finalHeight,
      // Other required fields that aren't used in the calculation
      name: 'background-image',
      originalPath: projectBackgroundImage || '',
      thumbnailUrl: projectBackgroundImage || '',
    };
  
    // Create a mock ImagePlacement object with the current transform
    const mockPlacement = {
      placeholderId: 'background-image-placeholder',
      imageId: 'background-image',
      transform: {
        scale: backgroundImageZoom,
        focalX: backgroundImagePanX / 100, // Convert from percentage to 0-1 range
        focalY: backgroundImagePanY / 100, // Convert from percentage to 0-1 range
        fit: 'cover' as const, // Background image is always cover
      }
    };
  
    // Calculate the physical dimensions in points (72 points = 1 inch)
    // For background image, we use the full spread width (width * 2) and height
    const placeholderWidthPt = aspectRatio.widthPt * 2;
    const placeholderHeightPt = aspectRatio.heightPt;
  
    // Create quality info object
    const qualityInfo: ImageQualityInfo = {
      status: ImageQualityStatus.GOOD, // Default, will be updated in calculation
      actualDpi: 0, // Will be calculated
      message: '', // Will be set based on calculation
      placeholderId: 'background-image-placeholder',
      imageId: 'background-image'
    };
  
    // Calculate effective DPI
    const effectiveDpi = calculateEffectiveDpi(mockImage, mockPlacement, placeholderWidthPt, placeholderHeightPt);
  
    if (effectiveDpi === null) {
      setBackgroundImageQualityInfo(null);
      return;
    }
  
    // Update quality info with calculated DPI
    qualityInfo.actualDpi = effectiveDpi;
  
    // Calculate thresholds
    const warningThresholdDpi = TARGET_DPI * (dpiWarningThresholdPercent / 100);
    const poorThresholdDpi = TARGET_DPI * 0.5; // 150 DPI
  
    // Determine status based on thresholds
    if (effectiveDpi >= warningThresholdDpi) {
      qualityInfo.status = ImageQualityStatus.GOOD;
      if (effectiveDpi >= TARGET_DPI) {
        qualityInfo.message = `Good quality: ${Math.round(effectiveDpi)} Effective DPI (meets target ${TARGET_DPI} DPI)`;
      } else {
        qualityInfo.message = `Acceptable quality: ${Math.round(effectiveDpi)} Effective DPI (meets threshold ${Math.round(warningThresholdDpi)} DPI)`;
      }
    } else if (effectiveDpi >= poorThresholdDpi) {
      qualityInfo.status = ImageQualityStatus.WARNING;
      qualityInfo.message = `Warning: ${Math.round(effectiveDpi)} Effective DPI (below ${Math.round(warningThresholdDpi)} DPI threshold)`;
    } else {
      qualityInfo.status = ImageQualityStatus.POOR;
      qualityInfo.message = `Poor quality: ${Math.round(effectiveDpi)} Effective DPI (below minimum ${Math.round(poorThresholdDpi)} DPI)`;
    }
  
    // Update state with quality info
    setBackgroundImageQualityInfo(qualityInfo);
  }, [containerDims, aspectRatio.widthPt, aspectRatio.heightPt, projectBackgroundImage,
      backgroundImageZoom, backgroundImagePanX, backgroundImagePanY, dpiWarningThresholdPercent, projectBackgroundImageOriginalDimensions]);
  
  // Function to load background image and check quality
  const loadBackgroundImageAndCheckQuality = useCallback(() => {
    if (!projectBackgroundImage) {
      setBackgroundImageQualityInfo(null);
      setNaturalImageDimensions(null); // Clear dimensions
      setProjectBackgroundImageOriginalDimensions(null); // Clear original dimensions
      setOriginalImagePath(null); // Clear original path
      return;
    }
    
    // Don't clear originalImageDimensions during normal operation
    // They should only be cleared when a new image is explicitly dropped or when projectBackgroundImage is null
  
    // Create a new image element to load the image and get its dimensions
    const img = new Image();
    img.onload = () => {
      const loadedWidth = img.naturalWidth;
      const loadedHeight = img.naturalHeight;
      
      setNaturalImageDimensions({ width: loadedWidth, height: loadedHeight }); // Set dimensions
      
      // If we don't have originalImageDimensions stored, and these look like original dimensions
      // (not preview dimensions), store them for future use
      const currentPath = getNormalizedPathKey(projectBackgroundImage);
      const isCache = currentPath.includes('preview_cache') || currentPath.includes('thumbnail_cache');
      
      if (!projectBackgroundImageOriginalDimensions && originalImagePath !== currentPath) {
        // Check if these dimensions look like original dimensions (typically larger)
        // We'll store any dimensions > 2000px as likely original dimensions
        if (loadedWidth > 2000 || loadedHeight > 2000) {
          setProjectBackgroundImageOriginalDimensions({ width: loadedWidth, height: loadedHeight });
          setOriginalImagePath(currentPath);
        }
      }
      
      // CRITICAL: If this is a cache path but we're getting preview dimensions (2000x1333),
      // and we have original dimensions stored, don't let the preview dimensions overwrite our stored dimensions
      if (isCache && projectBackgroundImageOriginalDimensions && (loadedWidth <= 2000 || loadedHeight <= 1333)) {
        // Don't update originalImageDimensions - keep the stored ones
      }
      
      checkBackgroundImageQuality(loadedWidth, loadedHeight);
    };
    img.onerror = () => {
      console.error('Failed to load background image for quality check');
      setBackgroundImageQualityInfo(null);
      setNaturalImageDimensions(null); // Clear dimensions
    };
  
    // CRITICAL: Always use the original file path (not Data URL) to get true dimensions
    // Data URLs contain preview dimensions, not original dimensions
    const originalPath = normalizeBackgroundImageUrl(projectBackgroundImage) || '';
    img.src = originalPath;
  }, [projectBackgroundImage, checkBackgroundImageQuality, setNaturalImageDimensions, projectBackgroundImageOriginalDimensions, originalImagePath, setProjectBackgroundImageOriginalDimensions]);

  // Effect to check quality when background image, zoom, or pan changes
  useEffect(() => {
    loadBackgroundImageAndCheckQuality();
  }, [loadBackgroundImageAndCheckQuality]);

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  
    // Check if this is an image from SpreadCanvas (special format)
    if (event.dataTransfer.types.includes('application/x-bookproofs-canvas-image')) {
      try {
        const canvasImageData = event.dataTransfer.getData('application/x-bookproofs-canvas-image');
        const imageData = JSON.parse(canvasImageData);
  
        // Always use the original path for the background image when available
        if (imageData && imageData.originalPath) {
          setProjectBackgroundImage(imageData.originalPath, imageData.originalPath);
        }
      } catch (error) {
        console.error("Failed to parse SpreadCanvas image data:", error);
      }
    }
    // Check if this is an image from ImageTray (JSON data)
    else if (event.dataTransfer.types.includes('application/json')) {
      try {
        const jsonData = event.dataTransfer.getData('application/json');
        const droppedData = JSON.parse(jsonData);
  
        // Handle both single image and array of images (take first one)
        const imageFile = Array.isArray(droppedData) ? droppedData[0] : droppedData;
  
        if (imageFile && imageFile.originalPath) {
          // UPDATED: Always use the original path for the background image
          
          // Check if this is a different image than the current one
          if (originalImagePath !== imageFile.originalPath) {
            // Clear previous dimensions since this is a new image
            setProjectBackgroundImageOriginalDimensions(null);
            setOriginalImagePath(null);
          }
          
          // Pass the original path as both parameters to ensure it's used directly
          setProjectBackgroundImage(imageFile.originalPath, imageFile.originalPath);
          
          // Store original dimensions for accurate DPI calculation
          if (imageFile.naturalWidth && imageFile.naturalHeight) {
            setProjectBackgroundImageOriginalDimensions({ width: imageFile.naturalWidth, height: imageFile.naturalHeight });
            setOriginalImagePath(imageFile.originalPath);
          }
        }
      } catch (error) {
        console.error("Failed to parse dropped image data:", error);
      }
    } else {
      // Fallback to plain text for direct URLs or base64 strings
      const imageSrc = event.dataTransfer.getData('text/plain');
      if (imageSrc) {
        // For text/plain data, check if it looks like a file path
        // This helps distinguish between file paths and preview/thumbnail URLs
        const isLikelyFilePath = !imageSrc.startsWith('data:') &&
                                !imageSrc.startsWith('http') &&
                                !imageSrc.startsWith('blob:');
  
        if (isLikelyFilePath) {
          setProjectBackgroundImage(imageSrc, imageSrc);
        } else {
          // When setting from a direct URL/base64, use it as both the URL and path
          // This handles cases where images are dragged from browser or other sources
          setProjectBackgroundImage(imageSrc, imageSrc);
        }
      }
    }
  };
  
  const handleClearBackgroundImage = () => {
    // Clear both the URL and the path
    setProjectBackgroundImage(null, null);
    // Clear stored original dimensions and path
    setProjectBackgroundImageOriginalDimensions(null);
    setOriginalImagePath(null);
  };

  // Effect to track color button position for portal overlay
  useEffect(() => {
    let rafId: number | null = null;
    let scrollTimeout: NodeJS.Timeout | null = null;
    let resizeObserver: ResizeObserver | null = null;

    const updateButtonRect = () => {
      if (colorButtonRef.current) {
        // Check if button is actually visible
        const buttonRect = colorButtonRef.current.getBoundingClientRect();
        if (buttonRect.width === 0 || buttonRect.height === 0) {
          // Button is not visible (focus mode or collapsed)
          setColorButtonRect(null);
          return;
        }
        setColorButtonRect(buttonRect);
        setIsScrolling(false);
      } else {
        setColorButtonRect(null);
      }
    };

    const scheduleUpdate = () => {
      if (rafId !== null) {
        cancelAnimationFrame(rafId);
      }
      rafId = requestAnimationFrame(updateButtonRect);
    };

    const handleScroll = () => {
      // Hide portal immediately using ref and state
      isScrollingRef.current = true;
      setIsScrolling(true);
      
      // Clear previous timeout
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      
      // Show portal again after scroll stops
      scrollTimeout = setTimeout(() => {
        isScrollingRef.current = false;
        scheduleUpdate();
      }, 100); // Wait 100ms after scroll stops
    };

    updateButtonRect();

    // Find the ScrollArea viewport element and the resizable panel container
    const scrollAreaViewport = colorButtonRef.current?.closest('[data-radix-scroll-area-viewport]');
    const resizablePanel = colorButtonRef.current?.closest('[data-panel-id]') || 
                          colorButtonRef.current?.closest('[data-panel]') ||
                          colorButtonRef.current?.closest('.react-resizable');

    const handleResize = scheduleUpdate;
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);

    // Listen for scroll events on both window and ScrollArea viewport
    window.addEventListener('scroll', handleScroll);
    if (scrollAreaViewport) {
      scrollAreaViewport.addEventListener('scroll', handleScroll);
    }

    // Set up ResizeObserver to detect panel resize
    if (resizablePanel) {
      resizeObserver = new ResizeObserver(() => {
        scheduleUpdate();
      });
      resizeObserver.observe(resizablePanel);
    }

    // Also observe the color button itself for visibility changes
    if (colorButtonRef.current) {
      if (!resizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          scheduleUpdate();
        });
      }
      resizeObserver.observe(colorButtonRef.current);
    }

    return () => {
      if (rafId !== null) {
        cancelAnimationFrame(rafId);
      }
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
      if (scrollAreaViewport) {
        scrollAreaViewport.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  return (
    <>
    {/* Wrap settings content in ScrollArea */}
    {/* Added flex-1 and overflow-hidden to allow ScrollArea to fill space */}
    <ScrollArea className="flex-1 overflow-hidden">
      <div
        className="p-6 space-y-8"
        style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}
      > {/* Keep padding inside scrollable area */}
        {/* Project Settings Section */}
        <h4 className="font-semibold text-lg mb-3 border-b pb-2" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>Project Settings</h4>
        <div style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
          <ImageGapControl value={imageGap} onChange={handleImageGapChange} />
        </div>

        {/* Background Color Setting */}
        <div
          className="space-y-3 pt-4 border-t border-gray-300"
          style={theme === 'dark' ? { borderTopColor: '#1f2937' } : {}}
        >
          <Label htmlFor="background-color-trigger" className="text-base" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            Project Background Color
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <button
                ref={colorButtonRef}
                id="background-color-trigger"
                className="h-10 w-full p-1 border border-gray-300 rounded-md cursor-pointer focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                style={{ backgroundColor: backgroundColor }}
                aria-label="Open color picker"
              />
            </PopoverTrigger>
            <PopoverContent className="w-auto p-2 border-none z-[9999]">
               {/* RgbaInputColorPicker handles its own theme inversion for inputs */}
               <RgbaInputColorPicker color={backgroundColor} onChange={handleBackgroundColorChange} theme={theme} />
            </PopoverContent>
          </Popover>
          <p className="text-xs text-muted-foreground" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            Set a background color for all spreads. Click the bar to pick a color.
          </p>
        </div>

        {/* Project Background Image Setting - Conditionally Rendered (Now represents cover background) */}
        {/* {hasCover && ( */}
          <div
            className="flex flex-col space-y-3 pt-4 border-t border-gray-300"
            style={theme === 'dark' ? { borderTopColor: '#1f2937' } : {}}
          >
            <Label className="text-base" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>Project Background Image</Label>
            {projectBackgroundImage ? (
            <>
              <AspectRatio ratio={numericalAspectRatio} className="bg-muted rounded-md">
                <div
                  ref={backgroundImageContainerRef}
                  className="relative w-full h-full overflow-hidden rounded-md group cursor-grab"
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onMouseEnter={handleBackgroundMouseEnter}
                  onMouseLeave={handleBackgroundMouseLeave}
                  onMouseDown={handleBackgroundMouseDown}
                  onMouseMove={handleBackgroundMouseMove}
                  onMouseUp={handleBackgroundMouseUp}
                  style={{ cursor: isPanningBackground ? 'grabbing' : 'grab' }}
                >
                  {isBackgroundImageLoading ? (
                    <div className="w-full h-full flex items-center justify-center text-sm text-gray-500" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
                      Loading preview...
                    </div>
                  ) : displayableBackgroundImageSrc ? (
                    <img
                      key={projectBackgroundImage} // Keyed by the original prop to trigger Data URL refetch
                      src={displayableBackgroundImageSrc} // Use Data URL
                      alt="Background Preview"
                      className="absolute max-w-none max-h-none pointer-events-none" // Use absolute positioning classes
                      style={(() => {
                        // Filter is now handled by the panel-level inversion.
                        // This style function just calculates dimensions and opacity.
                        const baseStyle: React.CSSProperties = {
                          position: 'absolute',
                          willChange: 'width, height, top, left, opacity',
                          opacity: projectBackgroundImageOpacity !== undefined ? projectBackgroundImageOpacity : 1,
                        };

                        if (!containerDims || !naturalImageDimensions || containerDims.width <= 0 || containerDims.height <= 0 || naturalImageDimensions.width <= 0 || naturalImageDimensions.height <= 0) {
                          return { ...baseStyle, width: '100%', height: '100%', top: '0', left: '0', objectFit: 'cover' };
                        }
                        const scale = backgroundImageZoom;
                        const focalX = backgroundImagePanX / 100; // Convert 0-100 to 0-1
                        const focalY = backgroundImagePanY / 100; // Convert 0-100 to 0-1
                        
                        const containerW = containerDims.width;
                        const containerH = containerDims.height;
                        const imageW = naturalImageDimensions.width;
                        const imageH = naturalImageDimensions.height;
                        const imageRatio = imageW / imageH;
                        const containerRatio = containerW / containerH;
                        
                        let baseRenderedW: number;
                        let baseRenderedH: number;
                        // 'cover' logic
                        if (imageRatio > containerRatio) { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; }
                        else { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; }
                        
                        const scaledW = baseRenderedW * scale;
                        const scaledH = baseRenderedH * scale;
                        
                        const offsetX = (containerW / 2) - (scaledW * focalX);
                        const offsetY = (containerH / 2) - (scaledH * focalY);
                        
                        return {
                          ...baseStyle,
                          width: `${scaledW}px`, height: `${scaledH}px`,
                          left: `${offsetX}px`, top: `${offsetY}px`,
                        };
                      })()}
                      draggable={false}
                    />
                  ) : null /* Or some placeholder if projectBackgroundImage is set but src is still null and not loading */ }
                  {backgroundImageQualityInfo &&
                   backgroundImageQualityInfo.status !== ImageQualityStatus.GOOD &&
                   showQualityIndicators && (
                    <BackgroundImageQualityWarning qualityInfo={backgroundImageQualityInfo} />
                  )}
                  {isBackgroundHovered && (
                    <div className="absolute bottom-2 left-1 right-1 z-20 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-auto flex items-center justify-between space-x-1 bg-black/30 p-1 rounded">
                      <Slider
                        min={1}
                        max={3}
                        step={0.02}
                        value={[backgroundImageZoom]}
                        onValueChange={([value]) => setBackgroundImageZoom(value)}
                        onValueCommit={() => {}}
                        className="flex-1 h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-white/70 [&>span:first-child>span]:h-2"
                        aria-label="Set background image zoom level"
                      />
                      <span className="text-white text-xs font-medium w-10 text-right pr-1">
                        {Math.round(backgroundImageZoom * 100)}%
                      </span>
                    </div>
                  )}
                </div>
              </AspectRatio>
              <div className="space-y-2 pt-3" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
                <div className="flex items-center justify-between">
                  <Label htmlFor="background-opacity-slider" className="text-sm">Background Opacity</Label>
                  <span className="text-xs font-medium text-gray-700">
                    {projectBackgroundImageOpacity !== undefined ? Math.round(projectBackgroundImageOpacity * 100) : 100}%
                  </span>
                </div>
                <Slider
                  id="background-opacity-slider"
                  min={0}
                  max={1}
                  step={0.01}
                  value={[projectBackgroundImageOpacity !== undefined ? projectBackgroundImageOpacity : 1]}
                  onValueChange={([value]) => setProjectBackgroundImageOpacity(value)}
                  className="h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-primary/70 [&>span:first-child>span]:h-2"
                  aria-label="Set background image opacity"
                />
              </div>
            </>
          ) : (
            <div
              className="h-24 border-2 border-dashed rounded-md flex items-center justify-center border-gray-300"
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <div className="text-center text-gray-500">
                <p>Drag and drop</p>
                <p className="text-xs">Drop an Image from the Photo Library or Working Spread Here.</p>
              </div>
            </div>
          )}
          <p className="text-xs text-muted-foreground pt-2" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            Use for main project background image OR use as secondary project background image in combination with a spread background image's decreased opacity for effect.
          </p>

          {projectBackgroundImage && (
            <div className="space-y-2 pt-2">
              <Button
                size="sm"
                onClick={handleClearBackgroundImage}
                className="w-full bg-gray-900 text-white hover:bg-gray-800 focus:ring-0 focus-visible:ring-0 focus:ring-offset-0 focus-visible:outline-none"
                style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}
              >
                Clear Background Image
              </Button>
            </div>
          )}
        </div>
        {/* )} */}

        {/* Image Borders Setting */}
        <div
          className="space-y-3 pt-4 border-t border-gray-300"
          style={theme === 'dark' ? { borderTopColor: '#1f2937' } : {}}
        >
          <Label className="text-base" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>Image Borders</Label>
          {/* Wrapper for "Border Size" section to apply re-inversion */}
          <div style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="image-border-size-slider" className="text-sm">
                  Border Size
                </Label>
                <span className="text-xs font-medium text-gray-700">
                  {imageBorderSize} pt
                </span>
              </div>
              <Slider
                id="image-border-size-slider"
                min={0}
                max={15} // Max 15pt border
                step={1}
                value={[imageBorderSize]}
                onValueChange={([value]) => {
                  // Update immediately as the slider moves
                  setImageBorderSize(value);
                  
                  // Mark project as dirty when border size changes
                  setIsDirty(true);
                  
                  // Dispatch a custom event to notify all components that use this setting
                  const event = new CustomEvent('bookproofsSettingsChange', {
                    detail: { setting: 'imageBorderSize', value }
                  });
                  window.dispatchEvent(event);
                }}
                className="h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-primary/70 [&>span:first-child>span]:h-2"
                aria-label="Set image border size"
              />
            </div>
          </div>
          {/* "Border Color" section - Label re-inverted, input (swatch) is not */}
          <div className="space-y-2">
            <Label htmlFor="image-border-color-trigger" className="text-sm" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
              Border Color
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <button
                  id="image-border-color-trigger"
                  className="h-10 w-full p-1 border border-gray-300 rounded-md cursor-pointer focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  style={{ backgroundColor: imageBorderColor }}
                  aria-label="Open image border color picker"
                />
              </PopoverTrigger>
             <PopoverContent className="w-auto p-2 border-none z-[9999]">
                 {/* RgbaInputColorPicker handles its own theme inversion for inputs */}
                <RgbaInputColorPicker
                  color={imageBorderColor}
                  onChange={(newColor) => {
                    setImageBorderColor(newColor);
                    setIsDirty(true);
                    const event = new CustomEvent('bookproofsSettingsChange', {
                      detail: { setting: 'imageBorderColor', value: newColor }
                    });
                    window.dispatchEvent(event);
                  }}
                  theme={theme}
                />
              </PopoverContent>
            </Popover>
          </div>
          <p className="text-xs text-muted-foreground" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            Apply an inside border to all images. The border will reduce the visible image area.
          </p>
        </div>

        {/* Bleed Area Visibility - 3-way Toggle */}
        <div
          className="space-y-3 pt-4 border-t border-gray-300"
          style={theme === 'dark' ? { borderTopColor: '#1f2937' } : {}}
        >
          <div style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}> {/* Wrapper for content */}
            <Label className="text-base mb-2 block">
              Bleed Area Visibility (B)
            </Label>
            <ToggleGroup
              type="single"
              value={bleedAreaMode}
              onValueChange={(value) => {
                if (value) {
                  setBleedAreaMode(value as 'hide' | 'indicate' | 'ignore');
                  
                  // Update showBleedArea for backward compatibility
                  if (value === 'indicate') {
                    setShowBleedArea(true);
                  } else {
                    setShowBleedArea(false);
                  }
                }
              }}
              className="flex w-full"
            >
              <ToggleGroupItem value="hide" className="flex-1 text-xs">
                Hide Bleed
              </ToggleGroupItem>
              <ToggleGroupItem value="indicate" className="flex-1 text-xs">
                Indicate Bleed
              </ToggleGroupItem>
              <ToggleGroupItem value="ignore" className="flex-1 text-xs">
                Show Full Canvas
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
          <p className="text-xs text-muted-foreground pt-1" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}> {/* This p is outside the new wrapper */}
            Bleed area outside the trim line will be cut off after printing and it's important to extend your design into it.
          </p>
        </div>

        {/* Photo Library Position */}
        <div
          className="space-y-3 pt-4 border-t border-gray-300"
          style={theme === 'dark' ? { borderTopColor: '#1f2937' } : {}}
        >
          <div style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            <Label className="text-base mb-2 block">
              Photo Library Position
            </Label>
            <ToggleGroup
              type="single"
              value={imageTrayPosition}
              onValueChange={(value) => {
                if (value) {
                  setImageTrayPosition(value as 'left' | 'bottom');
                  setIsDirty(true);
                }
              }}
              className="flex w-full"
            >
              <ToggleGroupItem value="left" className="flex-1 text-xs">
                Left Side
              </ToggleGroupItem>
              <ToggleGroupItem value="bottom" className="flex-1 text-xs">
                Bottom
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
          <p className="text-xs text-muted-foreground pt-1" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            Choose where to dock the Photo Library.
          </p>
        </div>

        {/* Image Bleed Setting */}
        <div
          className="space-y-3 pt-4 border-t border-gray-300"
          style={theme === 'dark' ? { borderTopColor: '#1f2937' } : {}}
        >
          <div className="flex items-center justify-between" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            <Label htmlFor="show-bleed-lines-toggle" className="text-base">
              Show Trim Line (T)
            </Label>
            <Switch
              id="show-bleed-lines-toggle"
              checked={showBleedLines}
              onCheckedChange={setShowBleedLines}
              aria-label="Toggle bleed line visibility"
            />
          </div>
          {showBleedLines && (
            <div className="space-y-2 pl-2">
              <div className="flex items-center justify-between" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
                <Label htmlFor="bleed-slider" className="text-sm">
                  Bleed Amount
                </Label>
                <span className="text-xs font-medium text-gray-700">
                  {projectBleed.toFixed(3)} inches
                </span>
              </div>
              <Slider
                id="bleed-slider"
                min={0.0625} // 1/16th inch
                max={0.25}   // 1/4th inch
                step={0.001} // Fine control
                value={[projectBleed]}
                onValueChange={([value]) => setProjectBleed(value)}
                disabled={aspectRatio.id !== 'custom' && !aspectRatio.id.startsWith('custom-')} // Disable if not custom
                className="h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-primary/70 [&>span:first-child>span]:h-2"
                aria-label="Set bleed amount"
              />
              { (aspectRatio.id !== 'custom' && !aspectRatio.id.startsWith('custom-')) && (
                <p className="text-xs text-muted-foreground pt-1" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
                  Bleed for vendor-specific sizes is preset and cannot be changed.
                </p>
              )}
               <p className="text-xs text-muted-foreground pt-1" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
                Adjust the bleed amount (typically 0.125 inches) for custom book sizes. The area outside the trim line will be trimmed off after printing; ensure your design extends into it.
              </p>
            </div>
          )}
        </div>

        {/* Safety Margin Setting */}
        <div
          className="space-y-3 pt-4 border-t border-gray-300"
          style={theme === 'dark' ? { borderTopColor: '#1f2937' } : {}}
        >
          <div className="flex items-center justify-between" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            <Label htmlFor="show-safety-margin-toggle" className="text-base">
              Show Safety Margin (M)
            </Label>
            <Switch
              id="show-safety-margin-toggle"
              checked={showSafetyMargin}
              onCheckedChange={setShowSafetyMargin}
              aria-label="Toggle safety margin visibility"
            />
          </div>
          {showSafetyMargin && (
            <div className="space-y-2 pl-2">
              <div className="flex items-center justify-between" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
                <Label htmlFor="safety-margin-slider" className="text-sm">
                  Safety Margin Amount
                </Label>
                <span className="text-xs font-medium text-gray-700">
                  {projectSafetyMargin.toFixed(3)} inches
                </span>
              </div>
              <Slider
                id="safety-margin-slider"
                min={0.0625} // 1/16th inch
                max={0.75}   // 3/4 inch (Graphitudio default is 0.60)
                step={0.001} // Fine control
                value={[projectSafetyMargin]}
                onValueChange={([value]) => setProjectSafetyMargin(value)}
                disabled={aspectRatio.id !== 'custom' && !aspectRatio.id.startsWith('custom-')} // Disable if not custom
                className="h-2 [&>span:first-child]:h-full [&>span:first-child>span]:bg-primary/70 [&>span:first-child>span]:h-2" // Changed to primary color
                aria-label="Set safety margin amount"
              />
              { (aspectRatio.id !== 'custom' && !aspectRatio.id.startsWith('custom-')) && (
                <p className="text-xs text-muted-foreground pt-1" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
                  Margin for vendor-specific sizes is preset and cannot be changed.
                </p>
              )}
              <p className="text-xs text-muted-foreground pt-1" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
                Define the safety margin (e.g., 0.25 to 0.60 inches) inside the trim line. Critical content should stay within this blue dotted line to avoid being cut off when too close the edge.
              </p>
            </div>
          )}
        </div>

        {/* Proofing Settings Section */}
        <h4 className="font-semibold text-lg mb-3 pb-2 pt-6" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>Proofing Settings</h4>
        <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
          <Label className="flex flex-col space-y-1 cursor-pointer">
            <span>Image Numbering (N)</span>
            <span className="font-normal leading-snug text-muted-foreground">
              Show/hide proofing numbers
            </span>
          </Label>
          <ProofingControls />
        </div>

        {/* Add Project Cover Toggle */}
        <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
          <Label htmlFor="enable-project-cover-toggle" className="flex flex-col space-y-1 cursor-pointer">
            <span>Use Project Cover (C)</span>
            <span className="font-normal leading-snug text-muted-foreground">
              Add a separate cover page for client proofing.
            </span>
          </Label>
          <Switch
            id="enable-project-cover-toggle"
            checked={hasCover}
            onCheckedChange={setHasCover}
            aria-label="Toggle project cover"
          />
        </div>

        {/* User Settings Section */}
        <h4 className="font-semibold text-lg mb-3 pb-2 pt-6" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>User Settings</h4>
        {/* Theme Toggle */}
        <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
          <Label htmlFor="theme-toggle" className="flex flex-col space-y-1 cursor-pointer">
            <span>Theme (Cmd/Ctrl+T)</span>
            <span className="font-normal leading-snug text-muted-foreground">
              Toggle on to enable dark theme.
            </span>
          </Label>
          <Switch
            id="theme-toggle"
            checked={theme === 'dark'}
            onCheckedChange={async (checked) => {
              const newTheme = checked ? 'dark' : 'light';
              setTheme(newTheme);
              
              // Update the OS-level title bar theme
              if (window.electronAPI?.setTitleBarTheme) {
                try {
                  const result = await window.electronAPI.setTitleBarTheme(newTheme);
                  if (!result.success) {
                  }
                } catch (error) {
                }
              }
            }}
            aria-label="Toggle theme"
          />
        </div>
        {/* Show Filenames Toggle */}
        <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
          <Label htmlFor="show-filenames-toggle" className="flex flex-col space-y-1 cursor-pointer">
            <span>Show Filenames (F)</span>
            <span className="font-normal leading-snug text-muted-foreground">
              Display image filenames below thumbnails in the library.
            </span>
          </Label>
          <Switch
            id="show-filenames-toggle"
            checked={showFilenames}
            onCheckedChange={setShowFilenames}
            aria-label="Toggle filename visibility"
          />
        </div>
        {/* Add Show End of Filename Toggle */}
        <div className={`flex items-center justify-between space-x-2 pt-4 border-t border-gray-300 ${!showFilenames ? 'opacity-50' : ''}`} style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
          <Label htmlFor="show-end-filenames-toggle" className={`flex flex-col space-y-1 ${!showFilenames ? 'cursor-not-allowed' : 'cursor-pointer'}`}>
            <span>Show End of Filename (E)</span>
            <span className="font-normal leading-snug text-muted-foreground">
              Display the end part of truncated filenames instead of the start.
            </span>
          </Label>
          <Switch
            id="show-end-filenames-toggle"
            checked={showEndOfFilename}
            onCheckedChange={setShowEndOfFilename}
            disabled={!showFilenames}
            aria-label="Toggle showing end of filename"
          />
        </div>
        {/* Default Drop Mode Setting (Using RadioGroup) - COMMENTED OUT: Always use cover mode */}
        {/* 
        <div className="space-y-3 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
           <Label className="text-base">Default Image Fit (D)</Label>
           <RadioGroup
             value={defaultDropModeIsCover ? 'cover' : 'fit'} // Map boolean to string value
             onValueChange={(value: 'cover' | 'fit') => setDefaultDropModeIsCover(value === 'cover')} // Map string value back to boolean setter
             className="grid grid-cols-1 gap-3"
           >
             <Label htmlFor="drop-cover" className="flex flex-col space-y-1 rounded-md border border-gray-200 p-4 hover:border-gray-400 cursor-pointer [&:has([data-state=checked])]:border-gray-900">
               <div className="flex items-center space-x-3">
                 <RadioGroupItem value="cover" id="drop-cover" />
                 <span>Cover Frame</span>
               </div>
               <span className="block pl-7 font-normal text-xs leading-snug text-muted-foreground">
                 Images fill the frame. Shift+Drop contains native aspect ratio images inside frame.
               </span>
             </Label>
             <Label htmlFor="drop-fit" className="flex flex-col space-y-1 rounded-md border border-gray-200 p-4 hover:border-gray-400 cursor-pointer [&:has([data-state=checked])]:border-gray-900">
               <div className="flex items-center space-x-3">
                 <RadioGroupItem value="fit" id="drop-fit" />
                 <span>Contain Inside Frame</span>
               </div>
               <span className="block pl-7 font-normal text-xs leading-snug text-muted-foreground">
                 Native aspect ratio images fit inside the frame. Shift+Drop covers the frame.
               </span>
             </Label>
           </RadioGroup>
         </div>
         */}
         {/* Add Show Visual Drop Choice Toggle */}
         <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
           <Label htmlFor="visual-drop-choice-toggle" className="flex flex-col space-y-1 cursor-pointer">
             <span>Show Single Image Drop Choice</span>
             <span className="font-normal leading-snug text-muted-foreground">
               When dropping single images on placeholders, show split overlay for replace or add to spread. When off single image drops replace existing images.
             </span>
           </Label>
           <Switch
             id="visual-drop-choice-toggle"
             checked={showVisualDropChoice}
             onCheckedChange={setShowVisualDropChoice}
           />
         </div>
         {/* Add Visual Drag Icon Toggle */}
         <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
           <Label htmlFor="drag-icon-toggle" className="flex flex-col space-y-1 cursor-pointer">
             <span>Show Visual Drag Overlay</span>
             <span className="font-normal leading-snug text-muted-foreground">
               Display grip icon overlay in the top 1/4 of image placeholders. When disabled, top 1/4 remains draggable.
             </span>
           </Label>
           <Switch
             id="drag-icon-toggle"
             checked={showDragIcon}
             onCheckedChange={setShowDragIcon}
           />
         </div>
         {/* Add Auto-Switch Template on Remove Toggle */}
         <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
           <Label htmlFor="auto-switch-template-toggle" className="flex flex-col space-y-1 cursor-pointer">
             <span>Auto-Switch Template on Remove</span>
             <span className="font-normal leading-snug text-muted-foreground">
               When enabled, removing an image automatically switches to a template with one fewer slot (if available).
             </span>
           </Label>
           <Switch
             id="auto-switch-template-toggle"
             checked={autoSwitchTemplateOnRemove}
             onCheckedChange={setAutoSwitchTemplateOnRemove}
             aria-label="Toggle auto-switching template on image removal"
           />
         </div>
      {/* Add Auto Cover Near Edges Toggle */}
      <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
        <Label htmlFor="auto-cover-near-edges-toggle" className="flex flex-col space-y-1 cursor-pointer">
          <span>Auto-Zoom Contained Images Near Edge (Z)</span>
          <span className="font-normal leading-snug text-muted-foreground">
            Automatically zoom 'contain' placed images if any edge is close to the frame border to prevent thin gaps in print.
          </span>
        </Label>
        <Switch
          id="auto-cover-near-edges-toggle"
          checked={autoCoverNearEdges}
          onCheckedChange={setAutoCoverNearEdges}
          aria-label="Toggle auto-fill frame near edges"
        />
      </div>
      {/* Add Add Spread on Arrow/Chevron Toggle */}
      <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
        <Label htmlFor="add-spread-arrow-toggle" className="flex flex-col space-y-1 cursor-pointer">
          <span>Add Spread on Last Page Navigation</span>
          <span className="font-normal leading-snug text-muted-foreground">
            When enabled, pressing the right arrow key or clicking the right chevron on the last spread adds a new spread.
          </span>
        </Label>
        <Switch
          id="add-spread-arrow-toggle"
          checked={addSpreadOnArrow}
          onCheckedChange={setAddSpreadOnArrow}
          aria-label="Toggle adding spread via keyboard arrow or on-screen chevron"
        />
      </div>
         {/* Add Autosave on Spread Turn Toggle */}
         <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
           <Label htmlFor="autosave-spread-turn-toggle" className="flex flex-col space-y-1 cursor-pointer">
             <span>Autosave on Spread Turn</span>
             <span className="font-normal leading-snug text-muted-foreground">
               Automatically save the project when navigating between spreads if changes have been made.
             </span>
           </Label>
           <Switch
             id="autosave-spread-turn-toggle"
             checked={autosaveOnSpreadTurn}
             onCheckedChange={setAutosaveOnSpreadTurn}
             aria-label="Toggle autosave on spread turn"
           />
         </div>
         {/* Add Reset Image Transforms on Template Switch Toggle */}
         <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
           <Label htmlFor="reset-image-transforms-toggle" className="flex flex-col space-y-1 cursor-pointer">
             <span>Reset Zoom & Pan on Template Switch</span>
             <span className="font-normal leading-snug text-muted-foreground">
               When enabled, switching templates will reset zoom and pan for all images while preserving their fit style (cover or contain).
             </span>
           </Label>
           <Switch
             id="reset-image-transforms-toggle"
             checked={resetImageTransformsOnTemplateSwitch}
             onCheckedChange={setResetImageTransformsOnTemplateSwitch}
             aria-label="Toggle reset image transforms on template switch"
           />
         </div>
         {/* Add Show Book Gutter Toggle */}
         <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
           <Label htmlFor="show-book-gutter-toggle" className="flex flex-col space-y-1 cursor-pointer">
             <span>Show Book Gutter (G)</span>
             <span className="font-normal leading-snug text-muted-foreground">
               When enabled, a white line is displayed at the center of spreads with images to represent the book gutter.
             </span>
           </Label>
           <Switch
             id="show-book-gutter-toggle"
             checked={showBookGutter}
             onCheckedChange={setShowBookGutter}
             aria-label="Toggle book gutter visibility"
           />
         </div>
       {/* Add Show Rating Filter Toggle (Moved to End) */}
       <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
         <Label htmlFor="show-rating-filter-toggle" className="flex flex-col space-y-1 cursor-pointer">
           <span>Show Rating Filter</span>
           <span className="font-normal leading-snug text-muted-foreground">
             Show/hide the star rating filter controls in the library.
           </span>
         </Label>
         <Switch
           id="show-rating-filter-toggle"
           checked={showRatingFilter}
           onCheckedChange={setShowRatingFilter}
           aria-label="Toggle rating filter visibility"
         />
       </div>
       {/* Add Image Quality Indicators Toggle */}
       <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
         <Label htmlFor="quality-indicators-toggle" className="flex flex-col space-y-1 cursor-pointer">
           <span>Show Quality Indicators (Q)</span>
           <span className="font-normal leading-snug text-muted-foreground">
             Show warning indicators for images that don't meet effective 300 DPI quality requirements
           </span>
         </Label>
         <Switch
           id="quality-indicators-toggle"
           checked={showQualityIndicators}
           onCheckedChange={setShowQualityIndicators}
           aria-label="Toggle image quality indicators"
         />
       </div>

       {/* DPI Warning Threshold Slider - Always shown, disabled via className */}
       <div
         className={`space-y-3 pt-4 border-t border-gray-300 ${!showQualityIndicators ? 'opacity-50 pointer-events-none' : ''}`}
         style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}
       >
          <div className="flex items-center justify-between">
           {/* Removed text-base class for consistency */}
           <Label htmlFor="dpi-threshold-slider">
             Image Quality Warning Threshold
          </Label>
          <span className="text-sm font-medium text-gray-700">
            {dpiWarningThresholdPercent}% (Effective {Math.round(300 * (dpiWarningThresholdPercent / 100))} DPI)
          </span>
        </div>
        <Slider
          id="dpi-threshold-slider"
          min={50}
          max={70}
          step={1}
          value={[dpiWarningThresholdPercent]}
          onValueChange={([value]) => setDpiWarningThresholdPercent(value)}
          disabled={!showQualityIndicators} // Disable slider input
          aria-label="Set DPI warning threshold percentage"
        />
        <p className="text-xs text-muted-foreground" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
          Set the minimum DPI (as % of 300) for an image to show no warning.
          Images below this but above 150 DPI show a yellow warning.
          Images below 150 DPI show a red warning.
        </p>
      </div>

       {/* Add Photo Library Display Mode Toggle */}
       <div className="flex items-center justify-between space-x-2 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
         <Label htmlFor="photo-library-display-mode-toggle" className="flex flex-col space-y-1 cursor-pointer">
           <span>Default Photo Library View</span>
           <span className="font-normal leading-snug text-muted-foreground">
             Change default photo library view from all to unused
           </span>
          </Label>
          <Switch
            id="photo-library-display-mode-toggle"
            checked={photoLibraryDisplayMode === "unused"}
            onCheckedChange={(checked) => setPhotoLibraryDisplayMode(checked ? "unused" : "all")}
            aria-label="Toggle photo library display mode"
          />
        </div>

        {/* File Availability Check */}
        <div className="flex flex-col space-y-3 pt-4 border-t border-gray-300" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
          <Label className="flex flex-col space-y-1">
            <span>Image File Availability</span>
            <span className="font-normal leading-snug text-muted-foreground">
              Check if all image files in your project are still accessible
            </span>
          </Label>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={onCheckFilesAvailability}
            style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}
          >
            <FolderSearch className="h-4 w-4" />
            Check for Missing Files
          </Button>
          <p className="text-xs text-muted-foreground" style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}>
            Use this if you've moved image files or are working on a different computer.
          </p>
        </div>
       {/* Add more settings here in the future and they will be scrollable */}
      </div>
    </ScrollArea>
    
    {/* Portal overlay for true color display */}
    {colorButtonRect && !isScrolling && !isScrollingRef.current && !isFocusMode && !isTemplatesTrayCollapsed && createPortal(
      <div
        style={{
          position: 'fixed',
          top: colorButtonRect.top + 4,
          left: colorButtonRect.left + 4,
          width: colorButtonRect.width - 8,
          height: colorButtonRect.height - 8,
          backgroundColor: backgroundColor,
          borderRadius: '4px',
          pointerEvents: 'none',
          zIndex: 9999,
          mixBlendMode: 'normal'
        }}
      />,
      document.body
    )}
    </>
  );
};

export default SettingsPanel;