import React from 'react';
import { useStatusBar } from '@/contexts/StatusBarContext'; // Import the context hook

interface ContextualStatusBarProps {
  // hoveredCanvasPlaceholderId?: string | null; // No longer needed directly, comes from context
  isMultiSelectActive?: boolean;
  imageSelectionCount?: number; // Added prop for selected image count
  isSpreadThumbnailHovered?: boolean; // Renamed prop for consistency
  // Add other relevant state props here as needed, e.g.:
  // hoveredSpreadId?: string | null;
  // activeTool?: string | null;
}

const ContextualStatusBar: React.FC<ContextualStatusBarProps> = ({
  // hoveredCanvasPlaceholderId, // Removed from destructuring
  isMultiSelectActive,
  imageSelectionCount = 0, // Destructure and default to 0
  isSpreadThumbnailHovered = false, // Renamed prop for consistency
  // Destructure other props here
}) => {
  const { canvasHoverInfo, isOptionKeyPressed: globalOptionKeyPressed } = useStatusBar(); // Consume the context
  const { placeholderId: hoveredCanvasPlaceholderId, isZoomed: isHoveringZoomedImage } = canvasHoverInfo; // Destructure info
  
  // Note: Option key logic removed since visual drop choice overlay handles this now

  let message: React.ReactNode = null;

  // Determine the message based on priority (most specific first)
  if (isHoveringZoomedImage) { // NEW: Highest priority message when hovering zoomed image
    message = <span>Top: Drag to Swap | Bottom: Pan</span>;
  } else if (hoveredCanvasPlaceholderId === 'template-hover') { // Special ID for template hover
    message = <span>Use Up and Down Arrow Keys to Change Templates.</span>;
  } else if (hoveredCanvasPlaceholderId) { // Check for hover on non-zoomed image next
    message = <span>Top: Drag to Swap | Bottom: Pan | Space: Preview | Delete: Remove | Cmd (Mac) or Ctrl (Win) + click to select multiple images</span>;
  } else if (isSpreadThumbnailHovered) { // Check spread tray hover
    message = <span>Select and Drag to Reorder Spreads. Left & Right Arrow Keys to Navigate.</span>;
  } else if (imageSelectionCount > 0) { // Check image selection next
    if (imageSelectionCount === 1) {
      message = <span>1 image selected. Drag to canvas, spread tray or press delete to remove.</span>;
    } else {
      message = <span>{imageSelectionCount} images selected. Drag to canvas, spread tray or press delete to remove.</span>;
    }
  } else if (isMultiSelectActive) {
    // This might become less relevant if selection count covers the multi-select case,
    // but keep it for now in case multi-select is active *before* a selection is made.
    message = <span className="font-medium text-primary">Multi-select mode active</span>;
  } else {
    // Default message or other conditions can go here
    message = <span>Hold Cmd (Mac) or Ctrl (Win) to select multiple images or spreads. Shift to select groups of images in order.</span>;
  }

  // Add more conditions here for other states as the app grows
  // Example:
  // else if (hoveredSpreadId) {
  //   message = <span>Double-click to edit spread '{hoveredSpreadId}'</span>;
  // }
  // else if (selectionCount && selectionCount > 0) {
  //   message = <span>{selectionCount} item(s) selected</span>;
  // }

  // If no specific message is determined, maybe show a default or nothing
  if (!message) {
     // Optionally return null or a default placeholder message
     // return null;
     message = <span className="text-gray-400">Ready</span>; // Example default
  }

  return (
    <div className="px-4 py-2 text-xs text-gray-500 border-t border-gray-200 min-h-[3rem] flex items-center"> 
      {/* Changed fixed h-12 to min-h-[3rem] to allow expansion */}
      <div className="break-words hyphens-auto w-full"> 
        {/* Changed from truncate to break-words to allow proper wrapping and added hyphens-auto for better text breaks */}
        {message}
      </div>
    </div>
  );
};

export default ContextualStatusBar;