import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ImageFile } from './ImageTray';
import { Folder, AlertTriangle, Info } from 'lucide-react';

interface MissingFilesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  missingFiles: ImageFile[];
  totalFiles: number;
  onUpdateBasePath: (newBasePath: string) => Promise<boolean>;
  onUpdateIndividualPath: (originalPath: string, newPath: string) => Promise<boolean>;
  onCheckFilesAvailability: () => Promise<boolean>;
  setIgnoreAllMissingFiles: () => void; // Changed from onIgnore
  onDialogClose?: () => void; // New prop
}

/**
 * Dialog component that shows when image files are missing and allows the user
 * to update the folder location
 */
const MissingFilesDialog = ({
  open,
  onOpenChange,
  missingFiles,
  totalFiles,
  onUpdateBasePath,
  onUpdateIndividualPath,
  onCheckFilesAvailability,
  setIgnoreAllMissingFiles, // Changed from onIgnore
  onDialogClose
}: MissingFilesDialogProps) => {
  const [isUpdating, setIsUpdating] = useState(false);

  // Handle folder selection
  const handleSelectFolder = async () => {
    if (!window.electronAPI?.selectDirectory) {
      toast.error("This feature requires the desktop app.");
      return;
    }

    setIsUpdating(true);
    try {
      // Open folder selection dialog
      const newBasePath = await window.electronAPI.selectDirectory({
        title: "Select New Image Folder Location"
      });

      if (!newBasePath) {
        // User cancelled the dialog
        setIsUpdating(false);
        return;
      }

      // Update paths with new base folder
      const success = await onUpdateBasePath(newBasePath);
      
      if (success) {
        // Close dialog on success
        onOpenChange(false);
        if (onDialogClose) onDialogClose();
      }
    } catch (error: any) {
      console.error("Error selecting folder:", error);
      toast.error(`Error selecting folder: ${error.message || 'Unknown error'}`);
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle ignore button click
  const handleIgnore = () => {
    setIgnoreAllMissingFiles(); // Call the new function
    onOpenChange(false);
    if (onDialogClose) onDialogClose();
  };

  // State to track files with their most up-to-date paths
  const [displayFiles, setDisplayFiles] = useState<Array<{file: ImageFile, displayPath: string}>>([]); 
  
  // Update display paths when missingFiles changes
  useEffect(() => {
    // Check for updated paths in the global cache
    const updatedDisplayFiles = missingFiles.map(file => {
      let displayPath = file.originalPath;
      
      // If we have an updated path in our cache, use that instead
      if (window.bookProofsApp?.getUpdatedFilePath) {
        const updatedPath = window.bookProofsApp.getUpdatedFilePath(file.originalPath);
        if (updatedPath) {
          displayPath = updatedPath;
        }
      }
      
      return { file, displayPath };
    });
    
    setDisplayFiles(updatedDisplayFiles);
  }, [missingFiles]);

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      onOpenChange(isOpen);
      if (!isOpen && onDialogClose) { // Also call onDialogClose if closed via 'x' or overlay click
        onDialogClose();
      }
    }}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Missing Image Files
          </DialogTitle>
          <DialogDescription>
            {missingFiles.length} of {totalFiles} image files could not be found at their expected locations.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex items-start gap-3 text-sm">
            <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div>
              <p className="mb-2">
                This usually happens when:
              </p>
              <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                <li>Files have been moved to a different folder</li>
                <li>The project is being opened on a different computer</li>
                <li>External drives containing the images are not connected</li>
              </ul>
            </div>
          </div>

          {displayFiles.length > 0 && (
            <div className="border rounded-md p-3 bg-muted/30">
              <p className="text-sm font-medium mb-1">Missing files:</p>
              <div className="space-y-2 max-h-[300px] overflow-y-auto pr-1">
                {displayFiles.map(({file, displayPath}, index) => (
                  <div key={index} className="flex items-start gap-2 group">
                    <div className="flex-1">
                      <p className="text-xs text-muted-foreground">
                        {displayPath}
                      </p>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="h-6 px-2 text-xs"
                      onClick={async () => {
                        // Handle individual file update
                        console.log(`Selecting replacement for: ${displayPath}`);
                        const newPath = await window.electronAPI?.selectFile({
                          title: "Select replacement file",
                          defaultPath: displayPath, // Use the most up-to-date path as default
                          filters: [{ name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'tif', 'tiff'] }]
                        });
                        
                        console.log(`Selected new path: ${newPath}`);
                        
                        if (newPath) {
                          setIsUpdating(true);
                          toast.info(`Updating file path from ${displayPath} to ${newPath}...`);
                          
                          try {
                            // Always use the original path as the key for updating
                            const success = await onUpdateIndividualPath(file.originalPath, newPath);
                            console.log(`Update result: ${success ? 'success' : 'failed'}`);
                            
                            if (success) {
                              toast.success(`Updated file path successfully`);
                              
                              // Remove this file from the local list
                              const updatedMissingFiles = missingFiles.filter(f => f.originalPath !== file.originalPath);
                              
                              // Force a refresh by closing the dialog
                              onOpenChange(false);
                              if (onDialogClose) onDialogClose();
                              
                              // Check if all files are now available
                              // The parent component will handle checking file availability after the dialog closes.
                            } else {
                              toast.error(`Failed to update file path`);
                            }
                          } catch (error) {
                            console.error('Error updating file path:', error);
                            toast.error(`Error updating file path: ${error.message}`);
                          } finally {
                            setIsUpdating(false);
                          }
                        }
                      }}
                    >
                      Browse...
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex sm:justify-between">
          <Button 
            variant="outline" 
            onClick={handleIgnore}
            disabled={isUpdating}
          >
            Ignore for Now
          </Button>
          <Button 
            onClick={handleSelectFolder} 
            disabled={isUpdating}
            className="flex items-center gap-2"
          >
            <Folder className="h-4 w-4" />
            {isUpdating ? "Updating..." : "Update Folder Location"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default MissingFilesDialog;
