import React from 'react';
import { AlertTriangle } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ImageQualityInfo, ImageQualityStatus } from '@/lib/imageQuality';

interface ImageQualityWarningProps {
  qualityInfo: ImageQualityInfo;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  bleedAreaMode?: 'hide' | 'indicate' | 'ignore'; // Added bleedAreaMode prop
  imageBorderSize?: number; // Added imageBorderSize prop
}

/**
 * Component to display a warning icon when an image has quality issues
 */
const ImageQualityWarning: React.FC<ImageQualityWarningProps> = ({
  qualityInfo,
  position = 'top-right',
  bleedAreaMode = 'indicate', // Default to 'indicate' if not provided
  imageBorderSize = 0 // Default to 0 if not provided
}) => {
  // Determine icon color based on quality status
  const getIconColor = () => {
    switch (qualityInfo.status) {
      case ImageQualityStatus.WARNING:
        return 'text-amber-500';
      case ImageQualityStatus.POOR:
        return 'text-red-500';
      default:
        return 'text-green-500';
    }
  };

  // Always position in top-left regardless of position prop (as per updated requirements)
  // Position moved down to avoid overlap with ImageNumberOverlay
  const getPositionClasses = () => {
    // Conditional positioning based on bleedAreaMode or imageBorderSize
    if ((bleedAreaMode === 'hide' || bleedAreaMode === 'indicate') || (imageBorderSize >= 8)) {
      return 'top-3 left-3'; // Position further inward
    }
    return 'top-1 left-1'; // Original position
  };

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div 
            className={`absolute ${getPositionClasses()} z-50 cursor-pointer`}
            aria-label="Image quality warning"
          >
            <div className="bg-transparent rounded-full p-0.5">
              <AlertTriangle 
                className={`h-5 w-5 ${getIconColor()} drop-shadow-md`} 
                strokeWidth={2.5} 
              />
            </div>
          </div>
        </TooltipTrigger>
        {/* Added z-[60] to ensure tooltip appears above other elements */}
        <TooltipContent side="bottom" align="start" className="max-w-xs z-[60]">
          <div className="text-sm">
            <p className="font-medium">{qualityInfo.message}</p>
            <p className="text-xs mt-1 text-gray-500">
              This image may not print well at 300 DPI. Consider using a higher resolution image or reducing the zoom level.
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default ImageQualityWarning;
