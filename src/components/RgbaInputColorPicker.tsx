import React, { useState, useEffect, useCallback } from 'react';
import { RgbaStringColorPicker } from 'react-colorful';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface RgbaColor {
  r: number;
  g: number;
  b: number;
  a: number;
}

interface RgbaInputColorPickerProps {
  color: string; // Expects an rgba string e.g., "rgba(255, 0, 0, 0.5)"
  onChange: (newColor: string) => void;
  theme?: 'light' | 'dark'; // Optional theme prop
}

const parseRgbaString = (rgbaString: string): RgbaColor | null => {
  const match = rgbaString.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)$/i);
  if (match) {
    return {
      r: parseInt(match[1], 10),
      g: parseInt(match[2], 10),
      b: parseInt(match[3], 10),
      a: match[4] !== undefined ? parseFloat(match[4]) : 1,
    };
  }
  // Handle hex or other formats if necessary, or return a default
  // For now, if it's not a valid rgba string, we might return a default or null
  // Let's try to parse hex as a fallback
  if (rgbaString.startsWith('#')) {
    const hex = rgbaString.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    if (!isNaN(r) && !isNaN(g) && !isNaN(b)) {
      return { r, g, b, a: 1 };
    }
  }
  return { r: 0, g: 0, b: 0, a: 1 }; // Default to black if parsing fails
};

const RgbaInputColorPicker: React.FC<RgbaInputColorPickerProps> = ({ color, onChange, theme }) => {
  const [rgba, setRgba] = useState<RgbaColor>(parseRgbaString(color) || { r: 0, g: 0, b: 0, a: 1 });

  useEffect(() => {
    setRgba(parseRgbaString(color) || { r: 0, g: 0, b: 0, a: 1 });
  }, [color]);

  const handleColorPickerChange = useCallback((newColor: string) => {
    onChange(newColor); // This will trigger the useEffect above to parse and update inputs
  }, [onChange]);

  const handleInputChange = (component: keyof RgbaColor, value: string) => {
    let numValue = component === 'a' ? parseFloat(value) : parseInt(value, 10);

    if (isNaN(numValue)) {
      // Allow empty input for clearing, but don't propagate NaN
      // Or set to a default like 0 if preferred
      numValue = 0; 
    }

    let newRgba: RgbaColor;

    if (component === 'a') {
      numValue = Math.max(0, Math.min(1, numValue));
      newRgba = { ...rgba, [component]: numValue };
    } else {
      numValue = Math.max(0, Math.min(255, numValue));
      newRgba = { ...rgba, [component]: numValue };
    }
    
    setRgba(newRgba); // Update local state for inputs immediately
    onChange(`rgba(${newRgba.r}, ${newRgba.g}, ${newRgba.b}, ${newRgba.a.toFixed(2)})`);
  };
  
  const inputWrapperStyle = theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {};

  return (
    <div className="space-y-3"> {/* This provides overall spacing for the component's children */}
      <RgbaStringColorPicker color={color} onChange={handleColorPickerChange} className="mb-3 w-full" /> {/* Added mb-3 and w-full */}
      <div className="grid grid-cols-2 gap-3 pt-1" style={inputWrapperStyle}> {/* Adjusted gap and pt */}
        {(['r', 'g', 'b', 'a'] as const).map((comp) => (
          <div key={comp} className="space-y-1.5"> {/* Adjusted space-y */}
            <Label htmlFor={`rgba-${comp}`} className="text-xs uppercase font-medium"> {/* Added font-medium */}
              {comp}
            </Label>
            <Input
              type="number"
              id={`rgba-${comp}`}
              value={rgba[comp]}
              onChange={(e) => handleInputChange(comp, e.target.value)}
              min={comp === 'a' ? 0 : 0}
              max={comp === 'a' ? 1 : 255}
              step={comp === 'a' ? 0.01 : 1}
              className="w-full h-9 text-sm px-2 py-1"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default RgbaInputColorPicker;