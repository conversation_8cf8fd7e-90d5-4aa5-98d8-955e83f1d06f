import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { RefreshCcw } from 'lucide-react';
import { toast } from 'sonner';
import { Slider } from "@/components/ui/slider";
import { Button } from '@/components/ui/button';
import { ImagePlus } from 'lucide-react'; // Import ImagePlus
import { normalizeBackgroundImageUrl } from '@/utils/imageUtils'; // Assuming this utility is still useful
import { ImageFile } from '@/components/ImageTray'; // Correct import path
import { ImageTransform } from './SpreadsTray'; // Correct import path for ImageTransform
import { ImageQualityInfo, ImageQualityStatus, TARGET_DPI, calculateEffectiveDpi } from '@/lib/imageQuality'; // Import for DPI warning
import BackgroundImageQualityWarning from './BackgroundImageQualityWarning'; // Import warning component
import { getCachedDataUrl, setCachedDataUrl, clearCachedDataUrl, getNormalizedPathKey, isTimestampProcessed, markTimestampProcessed } from '@/utils/dataUrlCache';

// Define distinct types for spread background properties if needed, or reuse existing ones carefully
export interface SpreadBackgroundData {
  imagePath: string | null;
  originalPath?: string; // Store the original file path for matching during regeneration
  transform: ImageTransform; // Reusing ImageTransform for zoom/pan/fit
  naturalWidth?: number; // Add optional dimensions
  naturalHeight?: number; // Add optional dimensions
  opacity?: number; // Add opacity property for background image
}

interface SpreadBackgroundManagerProps {
    spreadId: string; // To identify the spread
    backgroundData: SpreadBackgroundData | null; // Data for the current spread's background
    aspectRatioWidthPt?: number; // Spread dimensions needed for positioning/scaling
    aspectRatioHeightPt?: number; // Spread dimensions needed for positioning/scaling
    onUpdateBackground: (spreadId: string, data: Partial<SpreadBackgroundData>) => void; // Function to update this spread's background
    onSetBackgroundFromImageFile: (spreadId: string, imageFile: ImageFile) => void; // Function to set background from a dropped image
    onApplyBackgroundToAll?: (spreadId: string) => void; // Optional: Function to apply this spread's background to all others
    onClearAllBackgrounds?: () => void; // Optional: Function to clear all spread backgrounds
    onToggleBackgroundEditMode?: (spreadId: string) => void; // Optional: Function to toggle background edit mode
    isBackgroundEditingActive?: boolean; // New prop to indicate if background editing is active
    showQualityIndicators?: boolean; // Whether to show image quality indicators
    dpiWarningThresholdPercent?: number; // DPI threshold percentage for warnings
    onRegenerateBackgroundPreview?: (spreadId: string, imagePath: string) => void; // New prop for regenerating preview
    images?: ImageFile[]; // Array of images for looking up original dimensions
}

const SpreadBackgroundManager: React.FC<SpreadBackgroundManagerProps> = ({
    spreadId,
    backgroundData,
    aspectRatioWidthPt,
    aspectRatioHeightPt,
    onUpdateBackground,
    onSetBackgroundFromImageFile,
    onApplyBackgroundToAll,
    onClearAllBackgrounds,
    onToggleBackgroundEditMode,
    isBackgroundEditingActive,
    showQualityIndicators = true,
    dpiWarningThresholdPercent = 70,
    onRegenerateBackgroundPreview,
    images,
}) => {
    const [isHovered, setIsHovered] = useState(false);
    const [isPanning, setIsPanning] = useState(false);
    const [panStartCoords, setPanStartCoords] = useState<{ x: number; y: number; startPanX: number; startPanY: number } | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const [containerDims, setContainerDims] = useState<{ width: number; height: number } | null>(null);
    const containerDimsRef = useRef<{ width: number; height: number } | null>(null);
    const [isDraggingOver, setIsDraggingOver] = useState(false); // New state for drag over visual
    const [imageDims, setImageDims] = useState<{ width: number; height: number } | null>(null); // State for natural image dimensions
    const [backgroundImageQualityInfo, setBackgroundImageQualityInfo] = useState<ImageQualityInfo | null>(null); // State for image quality info
    const [displayableSpreadBackgroundSrc, setDisplayableSpreadBackgroundSrc] = useState<string | null>(null); // For Data URL

    // Extract originalPath to avoid object reference issues
    const currentOriginalPath = backgroundData?.originalPath;
    
    // Ref to track the last loaded path to avoid unnecessary cache lookups
    const lastLoadedPathRef = useRef<string | null>(null);

    // Effect to load spread background image as a Data URL
    useEffect(() => {
        if (currentOriginalPath && window.electronAPI?.getImageDataUrl) {
            // Skip if we already loaded this exact path in this component instance
            if (lastLoadedPathRef.current === currentOriginalPath) {
                return;
            }

            // Check cache first using shared cache
            const cachedDataUrl = getCachedDataUrl(currentOriginalPath);
            if (cachedDataUrl) {
                setDisplayableSpreadBackgroundSrc(cachedDataUrl);
                lastLoadedPathRef.current = currentOriginalPath;
                return;
            }

            const osPath = getNormalizedPathKey(currentOriginalPath);
            window.electronAPI.getImageDataUrl(osPath)
                .then(result => {
                    if (result.success && result.dataUrl) {
                        // Cache the Data URL using shared cache
                        setCachedDataUrl(currentOriginalPath, result.dataUrl);
                        setDisplayableSpreadBackgroundSrc(result.dataUrl);
                        lastLoadedPathRef.current = currentOriginalPath;
                    } else {
                        setDisplayableSpreadBackgroundSrc(null);
                    }
                })
                .catch(error => {
                    setDisplayableSpreadBackgroundSrc(null);
                });
        } else if (!currentOriginalPath) {
            setDisplayableSpreadBackgroundSrc(null); // Clear if originalPath is null
            lastLoadedPathRef.current = null;
        }
    }, [currentOriginalPath]); // Re-run only when the originalPath value changes

    // Extract imagePath to avoid object reference issues
    const currentImagePath = backgroundData?.imagePath;

    // Effect to detect file updates (photoshop round trips) and refresh Data URL
    useEffect(() => {
        
        if (!currentOriginalPath || !currentImagePath) return;
        
        // Extract timestamp from cache-busted URL (e.g., "?t=1748452216307")
        const timestampMatch = currentImagePath.match(/[?&]t=(\d+)/);
        const timestamp = timestampMatch ? timestampMatch[1] : null;
        
        // Only refresh Data URL if this is a photoshop round trip (has timestamp) and we haven't processed this timestamp yet
        if (timestamp && displayableSpreadBackgroundSrc && !isTimestampProcessed(timestamp)) {
            
            // Mark this timestamp as processed globally
            markTimestampProcessed(timestamp);
            
            // Clear cache for this file since it was updated
            clearCachedDataUrl(currentOriginalPath);
            
            // Reset the ref so the main effect will reload the image
            lastLoadedPathRef.current = null;
            
            const osPath = getNormalizedPathKey(currentOriginalPath);
            window.electronAPI?.getImageDataUrl(osPath)
                .then(result => {
                    if (result.success && result.dataUrl) {
                        // Cache the new Data URL
                        setCachedDataUrl(currentOriginalPath, result.dataUrl);
                        setDisplayableSpreadBackgroundSrc(result.dataUrl);
                        lastLoadedPathRef.current = currentOriginalPath;
                    } else {
                    }
                })
                .catch(error => {
                });
        } else if (timestamp && isTimestampProcessed(timestamp)) {
        }
    }, [currentImagePath, displayableSpreadBackgroundSrc]); // Trigger when imagePath changes and we have existing Data URL

    // --- Placeholder functions - Implement logic based on requirements ---

    const handleSpreadBackgroundZoom = (newZoom: number) => {
        // newZoom is the value from the slider, which is already >= 1.0
        if (backgroundData) {
            onUpdateBackground(spreadId, {
                transform: { ...backgroundData.transform, scale: newZoom }
            });
        }
    };
    
    // Handle opacity change for background image
    const handleSpreadBackgroundOpacity = (newOpacity: number) => {
        if (backgroundData) {
            onUpdateBackground(spreadId, {
                opacity: newOpacity
            });
        }
    };

    const handleSpreadBackgroundPan = (newPanX: number, newPanY: number) => {
        // Update state via onUpdateBackground
         if (backgroundData) {
            onUpdateBackground(spreadId, {
                transform: { ...backgroundData.transform, focalX: newPanX / 100, focalY: newPanY / 100 } // Assuming focal points are 0-1
            });
        }
    };

     const handleMouseEnter = () => setIsHovered(true);
     const handleMouseLeave = () => {
         setIsHovered(false);
         if (isPanning) {
             setIsPanning(false);
             setPanStartCoords(null);
             document.body.style.cursor = 'default';
             // throttledSetPan.flush(); // If using throttle
         }
     };

     
          const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
              // Allow panning if explicitly in edit mode OR if hovering (for quick adjustments)
              if (e.button !== 0 || !backgroundData?.imagePath || (!isBackgroundEditingActive && !isHovered)) return;
              
              e.preventDefault();
              e.stopPropagation();
              setIsPanning(true);
              setPanStartCoords({
                  x: e.clientX,
                  y: e.clientY,
                  startPanX: (backgroundData.transform.focalX ?? 0.5) * 100, // Convert 0-1 to 0-100
                  startPanY: (backgroundData.transform.focalY ?? 0.5) * 100, // Convert 0-1 to 0-100
              });
              document.body.style.cursor = 'grabbing';
      };

      const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
          if (!isPanning || !panStartCoords || !containerDims || !backgroundData?.imagePath || !imageDims) return;

          // --- Use Constrained Panning Logic (from SpreadCanvas) ---
          const currentTransform = backgroundData.transform;
          const scale = currentTransform?.scale ?? 1;
          // const fitMode = currentTransform?.fit ?? 'cover'; // Background is always 'cover' effectively

          const dx = e.clientX - panStartCoords.x;
          const dy = e.clientY - panStartCoords.y;

          // Calculate rendered image dimensions
          let renderedWidthPx: number;
          let renderedHeightPx: number;
          const containerRatio = containerDims.width / containerDims.height;
          const imageRatio = imageDims.width / imageDims.height;

          // Calculate base rendered size (before scale) - Assume 'cover'
          let baseRenderedW: number;
          let baseRenderedH: number;
          if (imageRatio > containerRatio) { // Fit height, cover width
              baseRenderedH = containerDims.height;
              baseRenderedW = containerDims.height * imageRatio;
          } else { // Fit width, cover height
              baseRenderedW = containerDims.width;
              baseRenderedH = containerDims.width / imageRatio;
          }
          renderedWidthPx = baseRenderedW * scale;
          renderedHeightPx = baseRenderedH * scale;

          if (renderedWidthPx <= 0 || renderedHeightPx <= 0) return;

          // Calculate focal point shift
          const focalShiftX = -(dx / renderedWidthPx);
          const focalShiftY = -(dy / renderedHeightPx);

          // Calculate new focal point (before constraint)
          const startFocalX = panStartCoords.startPanX / 100; // Convert back to 0-1
          const startFocalY = panStartCoords.startPanY / 100; // Convert back to 0-1
          let newFocalX = startFocalX + focalShiftX;
          let newFocalY = startFocalY + focalShiftY;

          // Calculate max deviation and clamp
          const visiblePortionX = containerDims.width / renderedWidthPx;
          const visiblePortionY = containerDims.height / renderedHeightPx;
          const maxDeviationX = renderedWidthPx > containerDims.width ? (1 - visiblePortionX) / 2 : 0;
          const maxDeviationY = renderedHeightPx > containerDims.height ? (1 - visiblePortionY) / 2 : 0;

          newFocalX = Math.max(0.5 - maxDeviationX, Math.min(0.5 + maxDeviationX, newFocalX));
          newFocalY = Math.max(0.5 - maxDeviationY, Math.min(0.5 + maxDeviationY, newFocalY));

          // Update using the original 0-1 focal point values
          onUpdateBackground(spreadId, {
              transform: { ...currentTransform, focalX: Math.max(0, Math.min(1, newFocalX)), focalY: Math.max(0, Math.min(1, newFocalY)) }
          });
      };

      const handleMouseUp = () => {
         if (isPanning) {
             setIsPanning(false);
             setPanStartCoords(null);
             document.body.style.cursor = 'default';
             // throttledSetPan.flush(); // If using throttle
         }
     };

    // Effect to measure container dimensions using ResizeObserver with debouncing
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        // Debounced function to update container dimensions
        let resizeTimeout: NodeJS.Timeout | null = null;
        const debouncedUpdateDims = (width: number, height: number) => {
            if (resizeTimeout) clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                const newDims = { width, height };
                containerDimsRef.current = newDims;
                setContainerDims(newDims);
            }, 100); // 100ms debounce for background positioning (faster than SpreadCanvas)
        };

        // Initial dimensions
        const initialRect = container.getBoundingClientRect();
        const initialDims = { width: initialRect.width, height: initialRect.height };
        containerDimsRef.current = initialDims;
        setContainerDims(initialDims);

        // Set up ResizeObserver
        const resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                const { width, height } = entry.contentRect;
                if (width > 0 && height > 0) {
                    // Check if dimensions actually changed to avoid unnecessary updates
                    const currentDims = containerDimsRef.current;
                    if (!currentDims || 
                        Math.abs(currentDims.width - width) > 1 || 
                        Math.abs(currentDims.height - height) > 1) {
                        debouncedUpdateDims(width, height);
                    }
                }
            }
        });

        // Start observing
        resizeObserver.observe(container);

        // Cleanup
        return () => {
            resizeObserver.disconnect();
            if (resizeTimeout) clearTimeout(resizeTimeout);
        };
    }, []); // Remove containerDims dependency to avoid infinite loops

    // Calculate background image quality when relevant data changes
    useEffect(() => {
        // Only calculate quality when editing is active and quality indicators are enabled
        if (!isBackgroundEditingActive || !showQualityIndicators || !backgroundData?.imagePath) {
            setBackgroundImageQualityInfo(null);
            return;
        }
        
        // Skip if we don't have all the necessary data
        if (!aspectRatioWidthPt || !aspectRatioHeightPt || !backgroundData.naturalWidth || !backgroundData.naturalHeight) {
            setBackgroundImageQualityInfo(null);
            return;
        }
        
        // Look up original image dimensions from images array to ensure accurate DPI calculation
        const originalPath = backgroundData.originalPath || backgroundData.imagePath;
        const originalImage = images?.find(img => img.originalPath === originalPath);
        
        // Use original image dimensions if available, fall back to backgroundData dimensions
        const naturalWidth = originalImage?.naturalWidth || backgroundData.naturalWidth;
        const naturalHeight = originalImage?.naturalHeight || backgroundData.naturalHeight;
        
        // Create mock image and placement objects to use with the existing DPI calculation function
        const mockImage: ImageFile = {
            id: 'background-image',
            name: 'Background Image',
            originalPath: originalPath, // Prefer originalPath
            thumbnailUrl: backgroundData.imagePath, // imagePath is likely cache-busted
            naturalWidth: naturalWidth,
            naturalHeight: naturalHeight,
            rating: 0
        };
        
        // Create mock placement with the background transform data
        const mockPlacement = {
            placeholderId: 'background',
            imageId: 'background-image',
            transform: {
                scale: backgroundData.transform.scale,
                fit: 'cover', // Background images use cover fit mode
                focalX: backgroundData.transform.focalX,
                focalY: backgroundData.transform.focalY
            } as const
        };
        
        // Calculate effective DPI using the same function used for regular images
        const effectiveDpi = calculateEffectiveDpi(mockImage, mockPlacement, aspectRatioWidthPt, aspectRatioHeightPt);
        
        if (effectiveDpi === null) {
            setBackgroundImageQualityInfo(null);
            return;
        }
        
        // Create quality info object
        const qualityInfo: ImageQualityInfo = {
            status: ImageQualityStatus.GOOD, // Default status, will be updated below
            actualDpi: effectiveDpi,
            message: '',
            placeholderId: 'background',
            imageId: 'background-image'
        };
        
        // Calculate thresholds based on the same logic used for regular images
        const warningThresholdDpi = TARGET_DPI * (dpiWarningThresholdPercent / 100);
        const poorThresholdDpi = TARGET_DPI * 0.5; // 150 DPI
        
        // Determine status based on thresholds
        if (effectiveDpi >= warningThresholdDpi) {
            // Image meets or exceeds the user-defined warning threshold
            qualityInfo.status = ImageQualityStatus.GOOD;
            
            // Customize message based on whether it meets the absolute target
            if (effectiveDpi >= TARGET_DPI) {
                qualityInfo.message = `Good quality: ${Math.round(effectiveDpi)} Effective DPI (meets target ${TARGET_DPI} DPI)`;
            } else {
                qualityInfo.message = `Acceptable quality: ${Math.round(effectiveDpi)} Effective DPI (meets threshold ${Math.round(warningThresholdDpi)} DPI)`;
            }
        } else if (effectiveDpi >= poorThresholdDpi) {
            // Image is below warning threshold but above poor threshold
            qualityInfo.status = ImageQualityStatus.WARNING;
            qualityInfo.message = `Warning: ${Math.round(effectiveDpi)} Effective DPI (below threshold ${Math.round(warningThresholdDpi)} DPI)`;
        } else {
            // Image is below poor threshold - this is a severe quality issue
            qualityInfo.status = ImageQualityStatus.POOR;
            qualityInfo.message = `Poor quality: ${Math.round(effectiveDpi)} Effective DPI (below minimum ${Math.round(poorThresholdDpi)} DPI)`;
            
        }
        
        setBackgroundImageQualityInfo(qualityInfo);
    }, [backgroundData, aspectRatioWidthPt, aspectRatioHeightPt, isBackgroundEditingActive, showQualityIndicators, dpiWarningThresholdPercent]);

    // Effect to update image dimensions from backgroundData prop
    useEffect(() => {
        if (backgroundData?.naturalWidth && backgroundData?.naturalHeight) {
            setImageDims({ width: backgroundData.naturalWidth, height: backgroundData.naturalHeight });
        } else {
            setImageDims(null); // Clear if natural dimensions are not available
        }
    }, [backgroundData?.naturalWidth, backgroundData?.naturalHeight]);

    // Effect to handle Escape key press to exit edit mode
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && isBackgroundEditingActive && onToggleBackgroundEditMode) {
                onToggleBackgroundEditMode(spreadId);
            }
        };

        if (isBackgroundEditingActive) {
            document.addEventListener('keydown', handleKeyDown);
        } else {
            document.removeEventListener('keydown', handleKeyDown);
        }

        // Cleanup function
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [isBackgroundEditingActive, onToggleBackgroundEditMode, spreadId]); // Dependencies

    // --- Drag and Drop Handling ---
     const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        // Check if the drag contains image files from ImageTray
        if (event.dataTransfer.types.includes('application/json')) {
            setIsDraggingOver(true);
        }
     };

     const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault(); // Necessary to allow dropping
        if (event.dataTransfer.types.includes('application/json')) {
            event.dataTransfer.dropEffect = 'copy';
            if (!isDraggingOver) setIsDraggingOver(true); // Ensure it's set if missed by dragEnter
        } else {
            event.dataTransfer.dropEffect = 'none';
        }
     };

     const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        // Check if the mouse is truly leaving the component
        if (containerRef.current && !containerRef.current.contains(event.relatedTarget as Node)) {
            setIsDraggingOver(false);
        }
     };

     const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDraggingOver(false); // Reset dragging over state on drop

        // Check for ImageTray data
        if (event.dataTransfer.types.includes('application/json')) {
            try {
                const jsonData = event.dataTransfer.getData('application/json');
                const droppedData = JSON.parse(jsonData);
                const imageFile = Array.isArray(droppedData) ? droppedData[0] : droppedData;

                if (imageFile && imageFile.originalPath && imageFile.id) {
                    onSetBackgroundFromImageFile(spreadId, imageFile);
                } else {
                }
            } catch (error) {
            }
        } else {
        }
     };

    // --- Rendering Logic ---
    // If no background image path (via displayableSrc or original imagePath), render nothing.
    if (!displayableSpreadBackgroundSrc && !backgroundData?.imagePath) {
        return null;
    }

    // const imageUrl = normalizeBackgroundImageUrl(backgroundData.imagePath); // No longer directly used for src
    const zoom = backgroundData?.transform.scale ?? 1;
    // const panX = (backgroundData.transform.focalX ?? 0.5) * 100; // No longer used for style
    // const panY = (backgroundData.transform.focalY ?? 0.5) * 100; // No longer used for style

    return (
        <ContextMenu>
            <ContextMenuTrigger
                disabled={!isBackgroundEditingActive || (!backgroundData?.imagePath && !displayableSpreadBackgroundSrc)}
                asChild // Important to make the div the trigger itself
            >
                <div
                    ref={containerRef}
                    className={`absolute inset-0 overflow-hidden group ${isBackgroundEditingActive ? 'z-10' : 'z-0'}`} // Increase z-index when editing
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    style={{ cursor: (isPanning || (isBackgroundEditingActive && (backgroundData?.imagePath || displayableSpreadBackgroundSrc))) ? 'grabbing' : (isHovered && (backgroundData?.imagePath || displayableSpreadBackgroundSrc) ? 'grab' : 'default') }}
                >
                    {/* Use absolute positioning style calculation */}
                    <img
                        key={backgroundData?.imagePath || backgroundData?.originalPath} // Keyed by imagePath (cache-busted) or originalPath
                        src={displayableSpreadBackgroundSrc || undefined} // Use Data URL; undefined src if null
                        alt="Spread Background"
                        className="absolute max-w-none max-h-none pointer-events-none" // Use absolute positioning classes
                        draggable={false}
                        style={(() => {
                            // --- Calculate Style like SpreadCanvas ---
                            if (!containerDims || !imageDims || containerDims.width <= 0 || containerDims.height <= 0 || imageDims.width <= 0 || imageDims.height <= 0 || !backgroundData) {
                                return { position: 'absolute', width: '100%', height: '100%', top: '0', left: '0', objectFit: 'contain', opacity: backgroundData?.opacity ?? 1 } as React.CSSProperties; // Fallback
                            }
                            const { scale = 1, focalX = 0.5, focalY = 0.5 } = backgroundData.transform;
                            // const fit = 'cover'; // Background is always effectively 'cover'
                            const containerW = containerDims.width;
                            const containerH = containerDims.height;
                            const imageW = imageDims.width;
                            const imageH = imageDims.height;
                            const imageRatio = imageW / imageH;
                            const containerRatio = containerW / containerH;
                            let baseRenderedW: number;
                            let baseRenderedH: number;
                            // Cover logic
                            if (imageRatio > containerRatio) { baseRenderedH = containerH; baseRenderedW = containerH * imageRatio; } else { baseRenderedW = containerW; baseRenderedH = containerW / imageRatio; }
                            const scaledW = baseRenderedW * scale;
                            const scaledH = baseRenderedH * scale;
                            const offsetX = (containerW / 2) - (scaledW * focalX); // Centered focal point
                            const offsetY = (containerH / 2) - (scaledH * focalY); // Centered focal point
                            return {
                                position: 'absolute', width: `${scaledW}px`, height: `${scaledH}px`,
                                left: `${offsetX}px`, top: `${offsetY}px`,
                                willChange: 'width, height, top, left',
                                opacity: backgroundData?.opacity ?? 1, // Apply opacity from background data
                            } as React.CSSProperties;
                        })()}
                    />

            {/* Display quality warning only in edit mode */}
            {isBackgroundEditingActive && showQualityIndicators && backgroundImageQualityInfo && backgroundImageQualityInfo.status !== ImageQualityStatus.GOOD && (
                <BackgroundImageQualityWarning qualityInfo={backgroundImageQualityInfo} />
            )}
            
            {/* Controls Overlay - Appears on hover OR if background editing is active */}
            <div className={`absolute bottom-2 left-2 right-2 z-20 transition-opacity pointer-events-auto flex items-center justify-between space-x-2 bg-black/40 p-1 rounded ${ (isBackgroundEditingActive || isHovered || isDraggingOver) ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
                    {/* Left Aligned Buttons */}
                    <div className="flex items-center space-x-2">
                        <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onUpdateBackground(spreadId, { imagePath: null })}
                            title="Remove background from this spread"
                            className="text-xs h-6 bg-gray-700 text-white hover:bg-gray-600 hover:text-white"
                        >
                            Remove
                        </Button>
                        {onClearAllBackgrounds && (
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={onClearAllBackgrounds}
                                title="Remove background from all spreads"
                                className="text-xs h-6 bg-gray-700 text-white hover:bg-gray-600 hover:text-white"
                            >
                                Remove All
                            </Button>
                        )}
                    </div>

                    {/* Sliders Container - Centered */}
                    <div className="flex-1 flex items-center space-x-1 px-2 h-6">
                        {/* Opacity Slider - Left 1/3 */}
                        <div className="flex items-center space-x-1" style={{ width: '33%' }}>
                            <span className="text-white text-xs font-medium whitespace-nowrap">Opacity:</span>
                            <Slider
                                min={0.1}
                                max={1}
                                step={0.01}
                                value={[backgroundData?.opacity ?? 1]}
                                onValueChange={([value]) => handleSpreadBackgroundOpacity(value)}
                                className="flex-1 h-1.5 [&>span:first-child]:h-full [&>span:first-child>span]:bg-white/70 [&>span:first-child>span]:h-1.5"
                                aria-label="Set background image opacity"
                            />
                            <span className="text-white text-xs font-medium w-8 text-right">
                                {Math.round((backgroundData?.opacity ?? 1) * 100)}%
                            </span>
                        </div>
                        
                        {/* Zoom Slider - Right 2/3 */}
                        <div className="flex items-center space-x-1" style={{ width: '67%' }}>
                            <span className="text-white text-xs font-medium">Zoom:</span>
                            <Slider
                                min={1}
                                max={3}
                                step={0.02}
                                value={[zoom]}
                                onValueChange={([value]) => handleSpreadBackgroundZoom(value)}
                                className="flex-1 h-1.5 [&>span:first-child]:h-full [&>span:first-child>span]:bg-white/70 [&>span:first-child>span]:h-1.5"
                                aria-label="Set background image zoom level"
                            />
                            <span className="text-white text-xs font-medium w-10 text-right">
                                {Math.round(zoom * 100)}%
                            </span>
                        </div>
                    </div>

                    {/* Right Aligned Buttons */}
                    <div className="flex items-center space-x-2">
                        {onToggleBackgroundEditMode && (
                             <Button
                                size="sm"
                                variant="secondary"
                                onClick={() => onToggleBackgroundEditMode(spreadId)}
                                title="Apply changes and exit edit mode"
                                className="text-xs h-6"
                            >
                                Apply
                            </Button>
                        )}
                        {onApplyBackgroundToAll && (
                            <Button
                                size="sm"
                                variant="secondary"
                                onClick={() => {
                                    onApplyBackgroundToAll(spreadId);
                                    // Note: toggleBackgroundEditMode is now handled in the BookEditor component
                                }}
                                title="Apply this background to all spreads and exit edit mode"
                                className="text-xs h-6"
                            >
                                Apply to All
                            </Button>
                        )}
                    </div>
                </div>
            </div>
            </ContextMenuTrigger>
            <ContextMenuContent className="w-56"> {/* Increased width for better text fit */}
                <ContextMenuItem
                    onSelect={() => {
                        if (backgroundData?.imagePath && onRegenerateBackgroundPreview) {
                            onRegenerateBackgroundPreview(spreadId, backgroundData.imagePath);
                            toast.info("Requesting background preview regeneration...");
                        } else {
                            toast.error("Cannot regenerate preview: Information missing or action not available.");
                        }
                    }}
                    disabled={!onRegenerateBackgroundPreview || !backgroundData?.imagePath} // Item disabled if handler or path is missing
                >
                    <RefreshCcw className="mr-2 h-4 w-4" />
                    Regenerate Background Preview
                </ContextMenuItem>
            </ContextMenuContent>
        </ContextMenu>
    );
};

export default SpreadBackgroundManager;