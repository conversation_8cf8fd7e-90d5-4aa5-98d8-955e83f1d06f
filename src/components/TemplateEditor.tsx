import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ChevronLeft, ChevronRight, Filter, RotateCcw, Magnet } from 'lucide-react';
import { allBookTemplates } from '@/lib/templates';
import { adjustTemplateForGap, AdjustedPlaceholderPt } from '@/lib/templates';
import { TemplateImage } from '@/lib/templates/interfaces';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner'; // Import toast for user notifications

type ProjectDimension = 'square' | 'horizontal' | 'vertical';

interface DimensionConfig {
  width: number;
  height: number;
  label: string;
}

type DragMode = 'none' | 'move' | 'resize-n' | 'resize-s' | 'resize-e' | 'resize-w' | 'resize-nw' | 'resize-ne' | 'resize-sw' | 'resize-se';

interface DragState {
  mode: DragMode;
  placeholderIndex: number;
  startX: number;
  startY: number;
  startPlaceholder: TemplateImage;
}

interface CanvasTransform {
  scale: number;
  offsetX: number;
  offsetY: number;
  spreadWidthPt: number;
  spreadHeightPt: number;
}

const DIMENSION_CONFIGS: Record<ProjectDimension, DimensionConfig> = {
  square: { width: 24, height: 12, label: 'Square Spread (12×12 pages)' },
  horizontal: { width: 24, height: 8, label: 'Horizontal Spread (12×8 pages)' },
  vertical: { width: 16, height: 12, label: 'Vertical Spread (8×12 pages)' }
};

const HANDLE_SIZE = 8;
const MIN_SIZE = 0.05; // Minimum placeholder size (5% of spread)
const SNAP_TOLERANCE = 0.015; // Primary snap tolerance (increased from 0.012)
const CENTER_SNAP_TOLERANCE = 0.015; // Center-to-center snapping tolerance (increased from 0.008)
const SYMMETRY_TOLERANCE = 0.015; // Tolerance for symmetrical positioning (increased from 0.010)
const DISTANCE_SNAP_TOLERANCE = 0.015; // Tolerance for equal distance snapping (increased from 0.006)
const SPACING_SNAP_TOLERANCE = 0.015; // Tolerance for spacing-aware snapping (increased from 0.008)

interface TemplateEditorProps {
  initialDimension?: ProjectDimension;
  initialTemplateId?: string;
  initialImageCountFilter?: string;
  initialClassificationFilter?: string;
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({
  initialDimension = 'square',
  initialTemplateId,
  initialImageCountFilter = 'all',
  initialClassificationFilter = 'all'
}) => {
  const navigate = useNavigate();
  const [selectedDimension, setSelectedDimension] = useState<ProjectDimension>(initialDimension);
  const [currentTemplateIndex, setCurrentTemplateIndex] = useState(0);
  const [imageCountFilter, setImageCountFilter] = useState<string>(initialImageCountFilter);
  const [classificationFilter, setClassificationFilter] = useState<string>(initialClassificationFilter);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // Dragging state
  const [dragState, setDragState] = useState<DragState | null>(null);
  const [hoveredPlaceholder, setHoveredPlaceholder] = useState<number>(-1);
  const [hoveredHandle, setHoveredHandle] = useState<DragMode>('none');
  
  // Modified template state
  const [modifiedTemplate, setModifiedTemplate] = useState<TemplateImage[] | null>(null);
  
  // Snap guides state
  const [snapGuides, setSnapGuides] = useState<{x: number[], y: number[], symmetricX: number[], marginRefX: number[], marginRefY: number[]}>({x: [], y: [], symmetricX: [], marginRefX: [], marginRefY: []});
  
  // Snapping toggle state
  const [snappingEnabled, setSnappingEnabled] = useState<boolean>(true);
  
  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    placeholderIndex: number;
  } | null>(null);

  // Copy/paste state for placeholder dimensions (persists across template changes)
  const [copiedDimensions, setCopiedDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);

  // Focused placeholder state for keyboard shortcuts
  const [focusedPlaceholder, setFocusedPlaceholder] = useState<number>(-1);
  
  // Editable JSON state for modified template
  const [editableTemplateJson, setEditableTemplateJson] = useState<string>('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  // Filter templates by image count and classification
  const filteredTemplates = useMemo(() => {
    let templates = allBookTemplates;
    
    // Filter by image count
    if (imageCountFilter !== 'all') {
      const count = parseInt(imageCountFilter);
      templates = templates.filter(template => template.images.length === count);
    }
    
    // Filter by classification
    if (classificationFilter !== 'all') {
      templates = templates.filter(template => {
        // Handle both array and single string classifications for backward compatibility
        const templateClassifications = Array.isArray(template.classification) 
          ? template.classification 
          : [template.classification];
        return templateClassifications.includes(classificationFilter as any);
      });
    }
    
    return templates;
  }, [imageCountFilter, classificationFilter]);

  // Get unique image counts for filter dropdown
  const availableImageCounts = useMemo(() => {
    const counts = [...new Set(allBookTemplates.map(t => t.images.length))].sort((a, b) => a - b);
    return counts;
  }, []);

  // Get unique classifications for filter dropdown
  const availableClassifications = useMemo(() => {
    const classifications = new Set<string>();
    allBookTemplates.forEach(template => {
      // Handle both array and single string classifications for backward compatibility
      const templateClassifications = Array.isArray(template.classification) 
        ? template.classification 
        : [template.classification];
      
      templateClassifications.forEach(classification => {
        classifications.add(classification);
      });
    });
    return Array.from(classifications).sort();
  }, []);

  const currentTemplate = filteredTemplates[currentTemplateIndex];
  const dimensionConfig = DIMENSION_CONFIGS[selectedDimension];

  // Get current template images (modified or original)
  const currentTemplateImages = modifiedTemplate || currentTemplate?.images || [];
  
  // Calculate responsive canvas size
  const calculateCanvasSize = () => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const padding = 120;
    
    const availableWidth = viewportWidth - padding;
    const availableHeight = viewportHeight - padding;
    
    const aspectRatio = dimensionConfig.width / dimensionConfig.height;
    
    let canvasWidth, canvasHeight;
    
    if (availableWidth / availableHeight > aspectRatio) {
      canvasHeight = Math.min(availableHeight, 600);
      canvasWidth = canvasHeight * aspectRatio;
    } else {
      canvasWidth = Math.min(availableWidth, 800);
      canvasHeight = canvasWidth / aspectRatio;
    }
    
    return { width: canvasWidth, height: canvasHeight };
  };

  const [canvasSize, setCanvasSize] = useState(calculateCanvasSize());

  // Calculate canvas transform for coordinate conversion
  const canvasTransform = useMemo((): CanvasTransform => {
    const spreadWidthPt = dimensionConfig.width * 72;
    const spreadHeightPt = dimensionConfig.height * 72;
    const scaleX = canvasSize.width / spreadWidthPt;
    const scaleY = canvasSize.height / spreadHeightPt;
    const scale = Math.min(scaleX, scaleY);
    const offsetX = (canvasSize.width - (spreadWidthPt * scale)) / 2;
    const offsetY = (canvasSize.height - (spreadHeightPt * scale)) / 2;
    
    return { scale, offsetX, offsetY, spreadWidthPt, spreadHeightPt };
  }, [canvasSize, dimensionConfig]);

  // Convert canvas coordinates to template coordinates (0-1)
  const canvasToTemplate = useCallback((canvasX: number, canvasY: number) => {
    const { scale, offsetX, offsetY, spreadWidthPt, spreadHeightPt } = canvasTransform;
    const ptX = (canvasX - offsetX) / scale;
    const ptY = (canvasY - offsetY) / scale;
    return {
      x: Math.max(0, Math.min(1, ptX / spreadWidthPt)),
      y: Math.max(0, Math.min(1, ptY / spreadHeightPt))
    };
  }, [canvasTransform]);

  // Convert template coordinates to canvas coordinates
  const templateToCanvas = useCallback((templateX: number, templateY: number) => {
    const { scale, offsetX, offsetY, spreadWidthPt, spreadHeightPt } = canvasTransform;
    return {
      x: (templateX * spreadWidthPt * scale) + offsetX,
      y: (templateY * spreadHeightPt * scale) + offsetY
    };
  }, [canvasTransform]);

  // Get mouse position relative to canvas
  const getMousePos = useCallback((e: MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  }, []);

  // Hit test for placeholder
  const hitTestPlaceholder = useCallback((mouseX: number, mouseY: number, placeholder: TemplateImage): boolean => {
    const { x: canvasX, y: canvasY } = templateToCanvas(placeholder.x, placeholder.y);
    const { scale, spreadWidthPt, spreadHeightPt } = canvasTransform;
    const width = placeholder.width * spreadWidthPt * scale;
    const height = placeholder.height * spreadHeightPt * scale;
    
    return mouseX >= canvasX && mouseX <= canvasX + width &&
           mouseY >= canvasY && mouseY <= canvasY + height;
  }, [templateToCanvas, canvasTransform]);

  // Hit test for resize handles
  const hitTestHandle = useCallback((mouseX: number, mouseY: number, placeholder: TemplateImage): DragMode => {
    const { x: canvasX, y: canvasY } = templateToCanvas(placeholder.x, placeholder.y);
    const { scale, spreadWidthPt, spreadHeightPt } = canvasTransform;
    const width = placeholder.width * spreadWidthPt * scale;
    const height = placeholder.height * spreadHeightPt * scale;
    
    const handles = [
      { mode: 'resize-nw' as DragMode, x: canvasX, y: canvasY },
      { mode: 'resize-n' as DragMode, x: canvasX + width/2, y: canvasY },
      { mode: 'resize-ne' as DragMode, x: canvasX + width, y: canvasY },
      { mode: 'resize-e' as DragMode, x: canvasX + width, y: canvasY + height/2 },
      { mode: 'resize-se' as DragMode, x: canvasX + width, y: canvasY + height },
      { mode: 'resize-s' as DragMode, x: canvasX + width/2, y: canvasY + height },
      { mode: 'resize-sw' as DragMode, x: canvasX, y: canvasY + height },
      { mode: 'resize-w' as DragMode, x: canvasX, y: canvasY + height/2 }
    ];
    
    for (const handle of handles) {
      if (Math.abs(mouseX - handle.x) <= HANDLE_SIZE/2 && Math.abs(mouseY - handle.y) <= HANDLE_SIZE/2) {
        return handle.mode;
      }
    }
    
    return 'none';
  }, [templateToCanvas, canvasTransform]);

  // Calculate snap targets with clear priority ordering
  const calculateSnapTargets = useCallback((excludeIndex: number) => {
    const snapTargets = { 
      x: [] as number[], 
      y: [] as number[], 
      centerX: [] as number[], 
      centerY: [] as number[], 
      symmetricX: [] as number[],
      distanceX: [] as number[], // Equal distance from gutter
      distanceY: [] as number[], // Equal distance from vertical midpoint
      spacingX: [] as number[], // Spacing-aware snap targets
      spacingY: [] as number[]  // Spacing-aware snap targets
    };
    
    // Priority 1: Canvas bounds and key layout lines
    snapTargets.x.push(0, 1, 0.5, 0.25, 0.75); // Left edge, right edge, gutter, left page center, right page center
    snapTargets.y.push(0, 1, 0.5); // Top edge, bottom edge, horizontal center
    
    // Priority 2: Page centers for center-to-center snapping
    snapTargets.centerX.push(0.25, 0.5, 0.75); // Left page center, gutter, right page center
    snapTargets.centerY.push(0.5); // Vertical center (horizontal line across spread)
    
    // Priority 2.5: Canvas edge margin snapping (equal margins from canvas edges)
    // This needs to work with the CURRENT placeholder being dragged, not other placeholders
    const currentPlaceholder = currentTemplateImages[excludeIndex];
    if (currentPlaceholder) {
      const leftEdge = currentPlaceholder.x;
      const rightEdge = currentPlaceholder.x + currentPlaceholder.width;
      const topEdge = currentPlaceholder.y;
      const bottomEdge = currentPlaceholder.y + currentPlaceholder.height;
      
      // Horizontal canvas edge margin snapping
      const leftMarginFromCanvas = leftEdge - 0; // Distance from left canvas edge
      const rightMarginFromCanvas = 1 - rightEdge; // Distance from right canvas edge
      
      // Only suggest equal margin snapping if margins are currently unequal
      if (Math.abs(leftMarginFromCanvas - rightMarginFromCanvas) > 0.02) {
        const averageMargin = (leftMarginFromCanvas + rightMarginFromCanvas) / 2;
        const equalLeftEdge = averageMargin;
        const equalRightEdge = 1 - averageMargin;
        
        // Only add if the resulting position would be valid
        if (equalLeftEdge >= 0 && equalRightEdge <= 1 && (equalRightEdge - equalLeftEdge) >= (rightEdge - leftEdge)) {
          snapTargets.distanceX.push(equalLeftEdge, equalRightEdge);
        }
      }
      
      // Vertical canvas edge margin snapping
      const topMarginFromCanvas = topEdge - 0; // Distance from top canvas edge
      const bottomMarginFromCanvas = 1 - bottomEdge; // Distance from bottom canvas edge
      
      // Only suggest equal margin snapping if margins are currently unequal
      if (Math.abs(topMarginFromCanvas - bottomMarginFromCanvas) > 0.02) {
        const averageMargin = (topMarginFromCanvas + bottomMarginFromCanvas) / 2;
        const equalTopEdge = averageMargin;
        const equalBottomEdge = 1 - averageMargin;
        
        // Only add if the resulting position would be valid
        if (equalTopEdge >= 0 && equalBottomEdge <= 1 && (equalBottomEdge - equalTopEdge) >= (bottomEdge - topEdge)) {
          snapTargets.distanceY.push(equalTopEdge, equalBottomEdge);
        }
      }
    }
    
    // Priority 3: Other placeholder edges and centers
    currentTemplateImages.forEach((placeholder, index) => {
      if (index === excludeIndex) return;
      
      const leftEdge = placeholder.x;
      const rightEdge = placeholder.x + placeholder.width;
      const topEdge = placeholder.y;
      const bottomEdge = placeholder.y + placeholder.height;
      const centerX = placeholder.x + placeholder.width / 2;
      const centerY = placeholder.y + placeholder.height / 2;
      
      // Add edges
      snapTargets.x.push(leftEdge, rightEdge);
      snapTargets.y.push(topEdge, bottomEdge);
      
      // Add centers for center-to-center snapping
      snapTargets.centerX.push(centerX);
      snapTargets.centerY.push(centerY);
      
      // Priority 4: Equal distance from gutter (horizontal symmetry)
      const gutterCenter = 0.5;
      const leftDistanceFromGutter = Math.abs(leftEdge - gutterCenter);
      const rightDistanceFromGutter = Math.abs(rightEdge - gutterCenter);
      
      // If placeholder is clearly on one side, add equal distance targets on the other side
      if (rightEdge < 0.48) { // On left side
        const mirrorLeftEdge = gutterCenter + leftDistanceFromGutter;
        const mirrorRightEdge = gutterCenter + rightDistanceFromGutter;
        if (mirrorRightEdge <= 1) {
          snapTargets.distanceX.push(mirrorLeftEdge, mirrorRightEdge);
        }
      } else if (leftEdge > 0.52) { // On right side
        const mirrorLeftEdge = gutterCenter - rightDistanceFromGutter;
        const mirrorRightEdge = gutterCenter - leftDistanceFromGutter;
        if (mirrorLeftEdge >= 0) {
          snapTargets.distanceX.push(mirrorLeftEdge, mirrorRightEdge);
        }
      }
      
      // Priority 5: Equal distance from vertical midpoint (vertical symmetry)
      const verticalCenter = 0.5;
      const topDistanceFromCenter = Math.abs(topEdge - verticalCenter);
      const bottomDistanceFromCenter = Math.abs(bottomEdge - verticalCenter);
      
      // If placeholder is clearly on one side, add equal distance targets on the other side
      if (bottomEdge < 0.48) { // Above center
        const mirrorTopEdge = verticalCenter + topDistanceFromCenter;
        const mirrorBottomEdge = verticalCenter + bottomDistanceFromCenter;
        if (mirrorBottomEdge <= 1) {
          snapTargets.distanceY.push(mirrorTopEdge, mirrorBottomEdge);
        }
      } else if (topEdge > 0.52) { // Below center
        const mirrorTopEdge = verticalCenter - bottomDistanceFromCenter;
        const mirrorBottomEdge = verticalCenter - topDistanceFromCenter;
        if (mirrorTopEdge >= 0) {
          snapTargets.distanceY.push(mirrorTopEdge, mirrorBottomEdge);
        }
      }
      
      // Priority 6: Gutter-aware symmetrical positions (only for clearly separated elements)
      const isOnLeftPage = rightEdge < 0.45; // Buffer zone around gutter
      const isOnRightPage = leftEdge > 0.55;
      
      if (isOnLeftPage) {
        const distanceFromGutter = 0.5 - rightEdge;
        const symmetricLeftEdge = 0.5 + distanceFromGutter;
        const symmetricRightEdge = symmetricLeftEdge + placeholder.width;
        
        if (symmetricRightEdge <= 1) {
          snapTargets.symmetricX.push(symmetricLeftEdge, symmetricRightEdge);
        }
      } else if (isOnRightPage) {
        const distanceFromGutter = leftEdge - 0.5;
        const symmetricRightEdge = 0.5 - distanceFromGutter;
        const symmetricLeftEdge = symmetricRightEdge - placeholder.width;
        
        if (symmetricLeftEdge >= 0) {
          snapTargets.symmetricX.push(symmetricLeftEdge, symmetricRightEdge);
        }
      }
    });
    
    // Priority 7: Spacing-aware snapping - detect patterns in existing spacing
    const spacingPatterns = { x: new Set<number>(), y: new Set<number>() };
    
    // Analyze horizontal spacing patterns
    for (let i = 0; i < currentTemplateImages.length; i++) {
      if (i === excludeIndex) continue;
      for (let j = i + 1; j < currentTemplateImages.length; j++) {
        if (j === excludeIndex) continue;
        
        const img1 = currentTemplateImages[i];
        const img2 = currentTemplateImages[j];
        
        // Calculate various spacing measurements
        const spacings = [
          // Edge-to-edge spacings
          Math.abs(img2.x - (img1.x + img1.width)), // img1 right to img2 left
          Math.abs(img1.x - (img2.x + img2.width)), // img2 right to img1 left
          Math.abs(img2.y - (img1.y + img1.height)), // img1 bottom to img2 top
          Math.abs(img1.y - (img2.y + img2.height)), // img2 bottom to img1 top
          // Center-to-center spacings
          Math.abs((img1.x + img1.width/2) - (img2.x + img2.width/2)), // horizontal center distance
          Math.abs((img1.y + img1.height/2) - (img2.y + img2.height/2)) // vertical center distance
        ];
        
        // Only consider meaningful spacings (not too small, not too large)
        spacings.forEach(spacing => {
          if (spacing > 0.02 && spacing < 0.5) { // Between 2% and 50% of spread
            spacingPatterns.x.add(spacing);
            spacingPatterns.y.add(spacing);
          }
        });
      }
    }
    
    // Convert spacing patterns to potential snap positions for the dragged element
    const draggedPlaceholder = currentTemplateImages[excludeIndex];
    if (draggedPlaceholder) {
      currentTemplateImages.forEach((otherImg, index) => {
        if (index === excludeIndex) return;
        
        // For each detected spacing pattern, calculate where the dragged element could snap
        spacingPatterns.x.forEach(spacing => {
          // Snap to maintain this spacing from other elements
          snapTargets.spacingX.push(otherImg.x + otherImg.width + spacing); // To the right with spacing
          snapTargets.spacingX.push(otherImg.x - spacing - draggedPlaceholder.width); // To the left with spacing
          
          // Center-to-center spacing
          const otherCenterX = otherImg.x + otherImg.width/2;
          snapTargets.spacingX.push(otherCenterX - spacing - draggedPlaceholder.width/2); // Left of center
          snapTargets.spacingX.push(otherCenterX + spacing - draggedPlaceholder.width/2); // Right of center
        });
        
        spacingPatterns.y.forEach(spacing => {
          // Snap to maintain this spacing from other elements
          snapTargets.spacingY.push(otherImg.y + otherImg.height + spacing); // Below with spacing
          snapTargets.spacingY.push(otherImg.y - spacing - draggedPlaceholder.height); // Above with spacing
          
          // Center-to-center spacing
          const otherCenterY = otherImg.y + otherImg.height/2;
          snapTargets.spacingY.push(otherCenterY - spacing - draggedPlaceholder.height/2); // Above center
          snapTargets.spacingY.push(otherCenterY + spacing - draggedPlaceholder.height/2); // Below center
        });
      });
    }
    
    // Remove duplicates and sort for consistent behavior
    snapTargets.x = [...new Set(snapTargets.x)].sort((a, b) => a - b);
    snapTargets.y = [...new Set(snapTargets.y)].sort((a, b) => a - b);
    snapTargets.centerX = [...new Set(snapTargets.centerX)].sort((a, b) => a - b);
    snapTargets.centerY = [...new Set(snapTargets.centerY)].sort((a, b) => a - b);
    snapTargets.symmetricX = [...new Set(snapTargets.symmetricX)].sort((a, b) => a - b);
    snapTargets.distanceX = [...new Set(snapTargets.distanceX)].sort((a, b) => a - b);
    snapTargets.distanceY = [...new Set(snapTargets.distanceY)].sort((a, b) => a - b);
    snapTargets.spacingX = [...new Set(snapTargets.spacingX.filter(x => x >= 0 && x <= 1))].sort((a, b) => a - b);
    snapTargets.spacingY = [...new Set(snapTargets.spacingY.filter(y => y >= 0 && y <= 1))].sort((a, b) => a - b);
    
    return snapTargets;
  }, [currentTemplateImages]);

  // Apply snapping with clear priority system
  const applySnapping = useCallback((
    placeholder: TemplateImage, 
    snapTargets: {x: number[], y: number[], centerX: number[], centerY: number[], symmetricX: number[], distanceX: number[], distanceY: number[], spacingX: number[], spacingY: number[]}, 
    mode: DragMode
  ): {snapped: TemplateImage, guides: {x: number[], y: number[], symmetricX: number[], marginRefX: number[], marginRefY: number[]}} => {
    const snapped = { ...placeholder };
    const guides = { x: [] as number[], y: [] as number[], symmetricX: [] as number[], marginRefX: [] as number[], marginRefY: [] as number[] };
    
    // Calculate relevant edges based on drag mode
    const centerX = snapped.x + snapped.width / 2;
    const centerY = snapped.y + snapped.height / 2;
    const originalRight = placeholder.x + placeholder.width;
    const originalBottom = placeholder.y + placeholder.height;
    
    // Apply snapping with priority order: edges first, then centers, then symmetrical
    let xSnapped = false;
    let ySnapped = false;
    
    if (mode === 'move') {
      // Priority 1: Center-to-center snapping (tighter tolerance)
      if (!xSnapped) {
        for (const target of snapTargets.centerX) {
          if (Math.abs(centerX - target) <= CENTER_SNAP_TOLERANCE) {
            const offset = target - centerX;
            snapped.x += offset;
            guides.x.push(target);
            xSnapped = true;
            break;
          }
        }
      }
      
      if (!ySnapped) {
        for (const target of snapTargets.centerY) {
          if (Math.abs(centerY - target) <= CENTER_SNAP_TOLERANCE) {
            const offset = target - centerY;
            snapped.y += offset;
            guides.y.push(target);
            ySnapped = true;
            break;
          }
        }
      }
      
      // Priority 2: Edge snapping
      if (!xSnapped) {
        const leftEdge = snapped.x;
        const rightEdge = snapped.x + snapped.width;
        
        for (const target of snapTargets.x) {
          if (Math.abs(leftEdge - target) <= SNAP_TOLERANCE) {
            snapped.x = target;
            guides.x.push(target);
            xSnapped = true;
            break;
          } else if (Math.abs(rightEdge - target) <= SNAP_TOLERANCE) {
            snapped.x = target - snapped.width;
            guides.x.push(target);
            xSnapped = true;
            break;
          }
        }
      }
      
      if (!ySnapped) {
        const topEdge = snapped.y;
        const bottomEdge = snapped.y + snapped.height;
        
        for (const target of snapTargets.y) {
          if (Math.abs(topEdge - target) <= SNAP_TOLERANCE) {
            snapped.y = target;
            guides.y.push(target);
            ySnapped = true;
            break;
          } else if (Math.abs(bottomEdge - target) <= SNAP_TOLERANCE) {
            snapped.y = target - snapped.height;
            guides.y.push(target);
            ySnapped = true;
            break;
          }
        }
      }
      
      // Priority 3: Symmetrical snapping (only if no other snapping occurred)
      if (!xSnapped) {
        for (const target of snapTargets.symmetricX) {
          const leftEdge = snapped.x;
          const rightEdge = snapped.x + snapped.width;
          
          if (Math.abs(leftEdge - target) <= SYMMETRY_TOLERANCE) {
            snapped.x = target;
            guides.symmetricX.push(target);
            xSnapped = true;
            break;
          } else if (Math.abs(rightEdge - target) <= SYMMETRY_TOLERANCE) {
            snapped.x = target - snapped.width;
            guides.symmetricX.push(target);
            xSnapped = true;
            break;
          }
        }
      }
      
      // Priority 4: Equal distance snapping (light touch)
      if (!xSnapped) {
        for (const target of snapTargets.distanceX) {
          const leftEdge = snapped.x;
          const rightEdge = snapped.x + snapped.width;
          
          if (Math.abs(leftEdge - target) <= DISTANCE_SNAP_TOLERANCE) {
            snapped.x = target;
            guides.x.push(target);
            
            // Check if this is canvas edge margin snapping and add reference margin guide
            const leftMargin = leftEdge;
            const rightMargin = 1 - rightEdge;
            if (Math.abs(leftMargin - rightMargin) < 0.01) { // Equal margins detected
              guides.marginRefX.push(1); // Show right canvas edge as reference
            }
            
            xSnapped = true;
            break;
          } else if (Math.abs(rightEdge - target) <= DISTANCE_SNAP_TOLERANCE) {
            snapped.x = target - snapped.width;
            guides.x.push(target);
            
            // Check if this is canvas edge margin snapping and add reference margin guide
            const newLeftEdge = snapped.x;
            const newRightEdge = snapped.x + snapped.width;
            const leftMargin = newLeftEdge;
            const rightMargin = 1 - newRightEdge;
            if (Math.abs(leftMargin - rightMargin) < 0.01) { // Equal margins detected
              guides.marginRefX.push(0); // Show left canvas edge as reference
            }
            
            xSnapped = true;
            break;
          }
        }
      }
      
      if (!ySnapped) {
        for (const target of snapTargets.distanceY) {
          const topEdge = snapped.y;
          const bottomEdge = snapped.y + snapped.height;
          
          if (Math.abs(topEdge - target) <= DISTANCE_SNAP_TOLERANCE) {
            snapped.y = target;
            guides.y.push(target);
            
            // Check if this is canvas edge margin snapping and add reference margin guide
            const newTopEdge = snapped.y;
            const newBottomEdge = snapped.y + snapped.height;
            const topMargin = newTopEdge;
            const bottomMargin = 1 - newBottomEdge;
            if (Math.abs(topMargin - bottomMargin) < 0.01) { // Equal margins detected
              guides.marginRefY.push(1); // Show bottom canvas edge as reference
            }
            
            ySnapped = true;
            break;
          } else if (Math.abs(bottomEdge - target) <= DISTANCE_SNAP_TOLERANCE) {
            snapped.y = target - snapped.height;
            guides.y.push(target);
            
            // Check if this is canvas edge margin snapping and add reference margin guide
            const newTopEdge = snapped.y;
            const newBottomEdge = snapped.y + snapped.height;
            const topMargin = newTopEdge;
            const bottomMargin = 1 - newBottomEdge;
            if (Math.abs(topMargin - bottomMargin) < 0.01) { // Equal margins detected
              guides.marginRefY.push(0); // Show top canvas edge as reference
            }
            
            ySnapped = true;
            break;
          }
        }
      }
      
      // Priority 5: Spacing-aware snapping (lightest touch)
      if (!xSnapped) {
        for (const target of snapTargets.spacingX) {
          const leftEdge = snapped.x;
          const rightEdge = snapped.x + snapped.width;
          
          if (Math.abs(leftEdge - target) <= SPACING_SNAP_TOLERANCE) {
            snapped.x = target;
            guides.x.push(target);
            xSnapped = true;
            break;
          } else if (Math.abs(rightEdge - target) <= SPACING_SNAP_TOLERANCE) {
            snapped.x = target - snapped.width;
            guides.x.push(target);
            xSnapped = true;
            break;
          }
        }
      }
      
      if (!ySnapped) {
        for (const target of snapTargets.spacingY) {
          const topEdge = snapped.y;
          const bottomEdge = snapped.y + snapped.height;
          
          if (Math.abs(topEdge - target) <= SPACING_SNAP_TOLERANCE) {
            snapped.y = target;
            guides.y.push(target);
            ySnapped = true;
            break;
          } else if (Math.abs(bottomEdge - target) <= SPACING_SNAP_TOLERANCE) {
            snapped.y = target - snapped.height;
            guides.y.push(target);
            ySnapped = true;
            break;
          }
        }
      }
    } else {
      // Resize operations - only snap the edges being moved
      if (mode.includes('w') && !xSnapped) {
        for (const target of snapTargets.x) {
          if (Math.abs(snapped.x - target) <= SNAP_TOLERANCE) {
            const proposedWidth = originalRight - target;
            if (proposedWidth >= MIN_SIZE) {
              snapped.x = target;
              snapped.width = proposedWidth;
              guides.x.push(target);
              xSnapped = true;
              break;
            }
          }
        }
      }
      
      if (mode.includes('e') && !xSnapped) {
        const rightEdge = snapped.x + snapped.width;
        for (const target of snapTargets.x) {
          if (Math.abs(rightEdge - target) <= SNAP_TOLERANCE) {
            const proposedWidth = target - snapped.x;
            if (proposedWidth >= MIN_SIZE) {
              snapped.width = proposedWidth;
              guides.x.push(target);
              xSnapped = true;
              break;
            }
          }
        }
      }
      
      if (mode.includes('n') && !ySnapped) {
        for (const target of snapTargets.y) {
          if (Math.abs(snapped.y - target) <= SNAP_TOLERANCE) {
            const proposedHeight = originalBottom - target;
            if (proposedHeight >= MIN_SIZE) {
              snapped.y = target;
              snapped.height = proposedHeight;
              guides.y.push(target);
              ySnapped = true;
              break;
            }
          }
        }
      }
      
      if (mode.includes('s') && !ySnapped) {
        const bottomEdge = snapped.y + snapped.height;
        for (const target of snapTargets.y) {
          if (Math.abs(bottomEdge - target) <= SNAP_TOLERANCE) {
            const proposedHeight = target - snapped.y;
            if (proposedHeight >= MIN_SIZE) {
              snapped.height = proposedHeight;
              guides.y.push(target);
              ySnapped = true;
              break;
            }
          }
        }
      }
      
      // Add distance snapping for resize operations
      if (mode.includes('w') && !xSnapped) {
        for (const target of snapTargets.distanceX) {
          if (Math.abs(snapped.x - target) <= DISTANCE_SNAP_TOLERANCE) {
            const proposedWidth = originalRight - target;
            if (proposedWidth >= MIN_SIZE) {
              snapped.x = target;
              snapped.width = proposedWidth;
              guides.x.push(target);
              
              // Check for canvas edge margin reference
              const rightMargin = 1 - originalRight;
              const leftMargin = snapped.x;
              if (Math.abs(leftMargin - rightMargin) < 0.01) {
                guides.marginRefX.push(1); // Show right canvas edge as reference
              }
              
              xSnapped = true;
              break;
            }
          }
        }
      }
      
      if (mode.includes('e') && !xSnapped) {
        for (const target of snapTargets.distanceX) {
          const newRightEdge = snapped.x + snapped.width;
          if (Math.abs(newRightEdge - target) <= DISTANCE_SNAP_TOLERANCE) {
            const proposedWidth = target - snapped.x;
            if (proposedWidth >= MIN_SIZE) {
              snapped.width = proposedWidth;
              guides.x.push(target);
              
              // Check for canvas edge margin reference
              const leftMargin = snapped.x;
              const rightMargin = 1 - target;
              if (Math.abs(leftMargin - rightMargin) < 0.01) {
                guides.marginRefX.push(0); // Show left canvas edge as reference
              }
              
              xSnapped = true;
              break;
            }
          }
        }
      }
      
      if (mode.includes('n') && !ySnapped) {
        for (const target of snapTargets.distanceY) {
          if (Math.abs(snapped.y - target) <= DISTANCE_SNAP_TOLERANCE) {
            const proposedHeight = originalBottom - target;
            if (proposedHeight >= MIN_SIZE) {
              snapped.y = target;
              snapped.height = proposedHeight;
              guides.y.push(target);
              
              // Check for canvas edge margin reference
              const bottomMargin = 1 - originalBottom;
              const topMargin = snapped.y;
              if (Math.abs(topMargin - bottomMargin) < 0.01) {
                guides.marginRefY.push(1); // Show bottom canvas edge as reference
              }
              
              ySnapped = true;
              break;
            }
          }
        }
      }
      
      if (mode.includes('s') && !ySnapped) {
        for (const target of snapTargets.distanceY) {
          const newBottomEdge = snapped.y + snapped.height;
          if (Math.abs(newBottomEdge - target) <= DISTANCE_SNAP_TOLERANCE) {
            const proposedHeight = target - snapped.y;
            if (proposedHeight >= MIN_SIZE) {
              snapped.height = proposedHeight;
              guides.y.push(target);
              
              // Check for canvas edge margin reference
              const topMargin = snapped.y;
              const bottomMargin = 1 - target;
              if (Math.abs(topMargin - bottomMargin) < 0.01) {
                guides.marginRefY.push(0); // Show top canvas edge as reference
              }
              
              ySnapped = true;
              break;
            }
          }
        }
      }
    }
    
    return { snapped, guides };
  }, []);

  // Update canvas size when dimension changes or window resizes
  useEffect(() => {
    const updateSize = () => setCanvasSize(calculateCanvasSize());
    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [selectedDimension]);

  // Reset modified template when switching templates
  useEffect(() => {
    setModifiedTemplate(null);
    setEditableTemplateJson('');
  }, [currentTemplateIndex, imageCountFilter]);

  // Update editable JSON when modified template changes
  useEffect(() => {
    if (modifiedTemplate) {
      const templateWithModification = {
        ...currentTemplate,
        images: modifiedTemplate
      };
      setEditableTemplateJson(JSON.stringify(templateWithModification, null, 2));
    } else if (currentTemplate) {
      setEditableTemplateJson(JSON.stringify(currentTemplate, null, 2));
    }
  }, [modifiedTemplate, currentTemplate]);

  // Global mouse down handler for deselecting focused placeholders
  const handleGlobalMouseDown = useCallback((e: MouseEvent) => {
    // If a placeholder is focused and we click outside the canvas, deselect it
    if (focusedPlaceholder >= 0) {
      const canvas = canvasRef.current;
      if (canvas) {
        const rect = canvas.getBoundingClientRect();
        const isOutsideCanvas = 
          e.clientX < rect.left || 
          e.clientX > rect.right || 
          e.clientY < rect.top || 
          e.clientY > rect.bottom;
        
        if (isOutsideCanvas) {
          setFocusedPlaceholder(-1);
        }
      }
    }
  }, [focusedPlaceholder]);

  // Mouse event handlers
  const handleMouseDown = useCallback((e: MouseEvent) => {
    const mousePos = getMousePos(e);
    
    // Hide context menu on any mouse down
    setContextMenu(null);
    
    // Check for handle hits first (highest priority)
    for (let i = currentTemplateImages.length - 1; i >= 0; i--) {
      const handleMode = hitTestHandle(mousePos.x, mousePos.y, currentTemplateImages[i]);
      if (handleMode !== 'none') {
        // Set focus to this placeholder
        setFocusedPlaceholder(i);
        
        setDragState({
          mode: handleMode,
          placeholderIndex: i,
          startX: mousePos.x,
          startY: mousePos.y,
          startPlaceholder: { ...currentTemplateImages[i] }
        });
        return;
      }
    }
    
    // Check for placeholder hits
    for (let i = currentTemplateImages.length - 1; i >= 0; i--) {
      if (hitTestPlaceholder(mousePos.x, mousePos.y, currentTemplateImages[i])) {
        // Set focus to this placeholder
        setFocusedPlaceholder(i);
        
        setDragState({
          mode: 'move',
          placeholderIndex: i,
          startX: mousePos.x,
          startY: mousePos.y,
          startPlaceholder: { ...currentTemplateImages[i] }
        });
        return;
      }
    }
    
    // If no placeholder was hit, clear focus
    setFocusedPlaceholder(-1);
  }, [currentTemplateImages, getMousePos, hitTestPlaceholder, hitTestHandle]);

  // Handle right-click context menu
  const handleContextMenu = useCallback((e: MouseEvent) => {
    e.preventDefault();
    const mousePos = getMousePos(e);
    
    // Check which placeholder was right-clicked
    for (let i = currentTemplateImages.length - 1; i >= 0; i--) {
      if (hitTestPlaceholder(mousePos.x, mousePos.y, currentTemplateImages[i])) {
        setContextMenu({
          visible: true,
          x: e.clientX,
          y: e.clientY,
          placeholderIndex: i
        });
        return;
      }
    }
    
    // Hide context menu if right-clicking elsewhere
    setContextMenu(null);
  }, [currentTemplateImages, getMousePos, hitTestPlaceholder]);

  // Copy placeholder dimensions
  const copyPlaceholderDimensions = useCallback((placeholderIndex: number) => {
    if (!currentTemplateImages[placeholderIndex]) return;
    
    const placeholder = currentTemplateImages[placeholderIndex];
    setCopiedDimensions({
      width: placeholder.width,
      height: placeholder.height
    });
    setContextMenu(null);
  }, [currentTemplateImages]);

  // Paste placeholder dimensions
  const pastePlaceholderDimensions = useCallback((placeholderIndex: number) => {
    if (!copiedDimensions || !currentTemplateImages[placeholderIndex]) return;
    
    const placeholder = currentTemplateImages[placeholderIndex];
    
    // Create new template with pasted dimensions (keeping original position)
    const newTemplate = [...currentTemplateImages];
    newTemplate[placeholderIndex] = {
      ...placeholder,
      width: copiedDimensions.width,
      height: copiedDimensions.height
    };
    
    setModifiedTemplate(newTemplate);
    setContextMenu(null);
  }, [currentTemplateImages, copiedDimensions]);


  // Handle editable JSON changes
  const handleJsonChange = useCallback((value: string) => {
    setEditableTemplateJson(value);
  }, []);

  // Apply JSON changes to template
  const applyJsonChanges = useCallback(() => {
    try {
      const parsed = JSON.parse(editableTemplateJson);
      if (parsed.images && Array.isArray(parsed.images)) {
        // Validate that all images have required properties
        const validImages = parsed.images.every((img: any) => 
          img.id && typeof img.x === 'number' && typeof img.y === 'number' && 
          typeof img.width === 'number' && typeof img.height === 'number'
        );
        
        if (validImages) {
          setModifiedTemplate(parsed.images);
        } else {
          alert('Invalid template format: All images must have id, x, y, width, and height properties');
        }
      } else {
        alert('Invalid template format: Must have an "images" array');
      }
    } catch (error) {
      alert('Invalid JSON format');
    }
  }, [editableTemplateJson]);

  // Reset to original template
  const resetToOriginal = useCallback(() => {
    setModifiedTemplate(null);
    setEditableTemplateJson('');
  }, []);

  // Apply aspect ratio to placeholder
  const applyAspectRatio = useCallback((placeholderIndex: number, aspectRatio: '3:2' | '2:3' | '1:1') => {
    if (!currentTemplateImages[placeholderIndex]) return;
    
    const placeholder = currentTemplateImages[placeholderIndex];
    const centerX = placeholder.x + placeholder.width / 2;
    const centerY = placeholder.y + placeholder.height / 2;
    
    // Get the actual spread aspect ratio to correct for visual appearance
    const spreadAspectRatio = dimensionConfig.width / dimensionConfig.height;
    
    // Calculate new dimensions maintaining the same center point
    let newWidth: number;
    let newHeight: number;
    
    if (aspectRatio === '3:2') {
      // 3:2 aspect ratio (horizontal) - adjust for spread dimensions
      // Start with current height and calculate width for visual 3:2 appearance
      newHeight = placeholder.height;
      // Adjust the ratio calculation for the spread's actual aspect ratio
      newWidth = newHeight * (3 / 2) / spreadAspectRatio;
      
      // If new width exceeds canvas, use width as base instead
      if (newWidth > 1) {
        newWidth = Math.min(placeholder.width, 1);
        newHeight = newWidth * (2 / 3) * spreadAspectRatio;
      }
    } else if (aspectRatio === '2:3') {
      // 2:3 aspect ratio (vertical) - adjust for spread dimensions
      // Start with current width and calculate height for visual 2:3 appearance
      newWidth = placeholder.width;
      // Adjust the ratio calculation for the spread's actual aspect ratio
      newHeight = newWidth * (3 / 2) * spreadAspectRatio;
      
      // If new height exceeds canvas, use height as base instead
      if (newHeight > 1) {
        newHeight = Math.min(placeholder.height, 1);
        newWidth = newHeight * (2 / 3) / spreadAspectRatio;
      }
    } else if (aspectRatio === '1:1') {
      // 1:1 aspect ratio (square) - adjust for spread dimensions
      // Keep current height and calculate width to make it appear square
      newHeight = placeholder.height;
      // Adjust for spread aspect ratio to make it appear square
      newWidth = newHeight / spreadAspectRatio;
      
      // If new width exceeds canvas, use width as base instead
      if (newWidth > 1) {
        newWidth = placeholder.width;
        newHeight = newWidth * spreadAspectRatio;
      }
    }
    
    // Ensure minimum size
    newWidth = Math.max(MIN_SIZE, newWidth);
    newHeight = Math.max(MIN_SIZE, newHeight);
    
    // Calculate new position to maintain center
    let newX = centerX - newWidth / 2;
    let newY = centerY - newHeight / 2;
    
    // Ensure placeholder stays within canvas bounds
    newX = Math.max(0, Math.min(1 - newWidth, newX));
    newY = Math.max(0, Math.min(1 - newHeight, newY));
    
    // Update the placeholder
    const newTemplate = [...currentTemplateImages];
    newTemplate[placeholderIndex] = {
      ...placeholder,
      x: newX,
      y: newY,
      width: newWidth,
      height: newHeight
    };
    
    setModifiedTemplate(newTemplate);
    setContextMenu(null);
  }, [currentTemplateImages, dimensionConfig]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    const mousePos = getMousePos(e);
    
    if (dragState) {
      const deltaX = mousePos.x - dragState.startX;
      const deltaY = mousePos.y - dragState.startY;
      
      // Convert deltas to template coordinates
      const { scale, spreadWidthPt, spreadHeightPt } = canvasTransform;
      const deltaTemplateX = deltaX / (scale * spreadWidthPt);
      const deltaTemplateY = deltaY / (scale * spreadHeightPt);
      
      let newPlaceholder = { ...dragState.startPlaceholder };
      
      if (dragState.mode === 'move') {
        newPlaceholder.x = Math.max(0, Math.min(1 - newPlaceholder.width, dragState.startPlaceholder.x + deltaTemplateX));
        newPlaceholder.y = Math.max(0, Math.min(1 - newPlaceholder.height, dragState.startPlaceholder.y + deltaTemplateY));
      } else {
        // Handle resizing - get current placeholder state from template (preserves constraints)
        const currentPlaceholder = currentTemplateImages[dragState.placeholderIndex];
        const original = dragState.startPlaceholder;
        
        // Start with current dimensions (which may have been constrained)
        newPlaceholder.x = currentPlaceholder.x;
        newPlaceholder.y = currentPlaceholder.y;
        newPlaceholder.width = currentPlaceholder.width;
        newPlaceholder.height = currentPlaceholder.height;
        
        // Calculate fixed edges based on current state (not original)
        const rightEdge = currentPlaceholder.x + currentPlaceholder.width;
        const bottomEdge = currentPlaceholder.y + currentPlaceholder.height;
        
        // Handle X-axis resizing (only one should execute)
        if (dragState.mode.includes('w')) {
          // Dragging west (left) edge - right edge stays fixed at current position
          const proposedLeftEdge = original.x + deltaTemplateX;
          // Ensure left edge doesn't go beyond right edge minus minimum size, and doesn't go left of canvas
          const maxLeftEdge = Math.min(rightEdge - MIN_SIZE, 1 - MIN_SIZE);
          const newLeftEdge = Math.max(0, Math.min(maxLeftEdge, proposedLeftEdge));
          
          newPlaceholder.x = newLeftEdge;
          newPlaceholder.width = rightEdge - newLeftEdge;
        } else if (dragState.mode.includes('e')) {
          // Dragging east (right) edge - left edge stays fixed at current position
          const proposedRightEdge = original.x + original.width + deltaTemplateX;
          // Ensure right edge doesn't go beyond left edge plus minimum size, and doesn't go right of canvas
          const minRightEdge = Math.max(currentPlaceholder.x + MIN_SIZE, MIN_SIZE);
          const newRightEdge = Math.max(minRightEdge, Math.min(1, proposedRightEdge));
          
          newPlaceholder.width = newRightEdge - currentPlaceholder.x;
        }
        
        // Handle Y-axis resizing (only one should execute)
        if (dragState.mode.includes('n')) {
          // Dragging north (top) edge - bottom edge stays fixed at current position
          const proposedTopEdge = original.y + deltaTemplateY;
          // Constrain top edge: can't go above 0, can't go below bottom minus MIN_SIZE
          const newTopEdge = Math.max(0, Math.min(bottomEdge - MIN_SIZE, proposedTopEdge));
          
          newPlaceholder.y = newTopEdge;
          newPlaceholder.height = bottomEdge - newTopEdge;
        } else if (dragState.mode.includes('s')) {
          // Dragging south (bottom) edge - top edge stays fixed at current position
          const proposedBottomEdge = original.y + original.height + deltaTemplateY;
          // Ensure bottom edge doesn't go above top edge plus minimum size, and doesn't go below canvas
          const minBottomEdge = Math.max(currentPlaceholder.y + MIN_SIZE, MIN_SIZE);
          const newBottomEdge = Math.max(minBottomEdge, Math.min(1, proposedBottomEdge));
          
          newPlaceholder.height = newBottomEdge - currentPlaceholder.y;
        }
        
        // FOR TEMPLATE EDITOR: ENFORCE ASPECT RATIO LOCK FOR CORNER HANDLES
        // Corner handles MUST maintain aspect ratio - prevent any aspect ratio changes
        const isCornerHandle = dragState.mode === 'resize-nw' || 
                              dragState.mode === 'resize-ne' || 
                              dragState.mode === 'resize-sw' || 
                              dragState.mode === 'resize-se';
        
        if (isCornerHandle) {
          // ALWAYS maintain original aspect ratio for corner handles
          const originalAspectRatio = original.width / original.height;
          
          // Determine which dimension changed more and use that as the primary constraint
          const widthChangeRatio = Math.abs(newPlaceholder.width - currentPlaceholder.width) / currentPlaceholder.width;
          const heightChangeRatio = Math.abs(newPlaceholder.height - currentPlaceholder.height) / currentPlaceholder.height;
          
          if (widthChangeRatio > heightChangeRatio) {
            // Width changed more, adjust height to maintain aspect ratio
            const newHeight = newPlaceholder.width / originalAspectRatio;
            
            if (dragState.mode.includes('n')) {
              // Dragging top edge - adjust top position
              const proposedNewY = bottomEdge - newHeight;
              if (proposedNewY >= 0) {
                newPlaceholder.y = proposedNewY;
                newPlaceholder.height = newHeight;
              } else {
                // If we can't maintain aspect ratio, keep current dimensions
                newPlaceholder.width = currentPlaceholder.width;
                newPlaceholder.height = currentPlaceholder.height;
                newPlaceholder.y = currentPlaceholder.y;
              }
            } else if (dragState.mode.includes('s')) {
              // Dragging bottom edge - height grows from top
              if (newPlaceholder.y + newHeight <= 1) {
                newPlaceholder.height = newHeight;
              } else {
                // If we can't maintain aspect ratio, keep current dimensions
                newPlaceholder.width = currentPlaceholder.width;
                newPlaceholder.height = currentPlaceholder.height;
              }
            }
          } else {
            // Height changed more, adjust width to maintain aspect ratio
            const newWidth = newPlaceholder.height * originalAspectRatio;
            
            if (dragState.mode.includes('w')) {
              // Dragging left edge - adjust left position
              const proposedNewX = rightEdge - newWidth;
              if (proposedNewX >= 0) {
                newPlaceholder.x = proposedNewX;
                newPlaceholder.width = newWidth;
              } else {
                // If we can't maintain aspect ratio, keep current dimensions
                newPlaceholder.width = currentPlaceholder.width;
                newPlaceholder.height = currentPlaceholder.height;
                newPlaceholder.x = currentPlaceholder.x;
              }
            } else if (dragState.mode.includes('e')) {
              // Dragging right edge - width grows from left
              if (newPlaceholder.x + newWidth <= 1) {
                newPlaceholder.width = newWidth;
              } else {
                // If we can't maintain aspect ratio, keep current dimensions
                newPlaceholder.width = currentPlaceholder.width;
                newPlaceholder.height = currentPlaceholder.height;
              }
            }
          }
        }
      }
      
      // Apply snapping if enabled
      let finalPlaceholder = newPlaceholder;
      let guides = { x: [] as number[], y: [] as number[], symmetricX: [] as number[], marginRefX: [] as number[], marginRefY: [] as number[] };
      
      if (snappingEnabled) {
        const snapTargets = calculateSnapTargets(dragState.placeholderIndex);
        const snappingResult = applySnapping(newPlaceholder, snapTargets, dragState.mode);
        finalPlaceholder = snappingResult.snapped;
        guides = snappingResult.guides;
      }
      
      // Ensure placeholder stays within bounds and meets minimum size
      // For move operations, apply standard boundary constraints
      if (dragState.mode === 'move') {
        finalPlaceholder.x = Math.max(0, Math.min(1 - finalPlaceholder.width, finalPlaceholder.x));
        finalPlaceholder.y = Math.max(0, Math.min(1 - finalPlaceholder.height, finalPlaceholder.y));
        finalPlaceholder.width = Math.max(MIN_SIZE, Math.min(1 - finalPlaceholder.x, finalPlaceholder.width));
        finalPlaceholder.height = Math.max(MIN_SIZE, Math.min(1 - finalPlaceholder.y, finalPlaceholder.height));
      } else {
        // For resize operations, only enforce canvas bounds without breaking the resize logic
        // The resize logic above already handles minimum size and fixed edge constraints
        finalPlaceholder.x = Math.max(0, Math.min(1, finalPlaceholder.x));
        finalPlaceholder.y = Math.max(0, Math.min(1, finalPlaceholder.y));
        
        // Ensure the placeholder doesn't extend beyond canvas bounds
        if (finalPlaceholder.x + finalPlaceholder.width > 1) {
          finalPlaceholder.width = 1 - finalPlaceholder.x;
        }
        if (finalPlaceholder.y + finalPlaceholder.height > 1) {
          finalPlaceholder.height = 1 - finalPlaceholder.y;
        }
        
        // Ensure minimum size
        finalPlaceholder.width = Math.max(MIN_SIZE, finalPlaceholder.width);
        finalPlaceholder.height = Math.max(MIN_SIZE, finalPlaceholder.height);
      }
      
      // Update modified template and snap guides
      const newTemplate = [...currentTemplateImages];
      newTemplate[dragState.placeholderIndex] = finalPlaceholder;
      setModifiedTemplate(newTemplate);
      setSnapGuides(guides);
      
    } else {
      // Clear snap guides when not dragging
      setSnapGuides({x: [], y: [], symmetricX: [], marginRefX: [], marginRefY: []});
      
      // Update hover states
      let foundHover = false;
      let foundHandle = 'none' as DragMode;
      
      for (let i = currentTemplateImages.length - 1; i >= 0; i--) {
        const handleMode = hitTestHandle(mousePos.x, mousePos.y, currentTemplateImages[i]);
        if (handleMode !== 'none') {
          setHoveredPlaceholder(i);
          setHoveredHandle(handleMode);
          foundHover = true;
          foundHandle = handleMode;
          break;
        } else if (hitTestPlaceholder(mousePos.x, mousePos.y, currentTemplateImages[i])) {
          setHoveredPlaceholder(i);
          setHoveredHandle('move');
          foundHover = true;
          break;
        }
      }
      
      if (!foundHover) {
        setHoveredPlaceholder(-1);
        setHoveredHandle('none');
      }
    }
  }, [dragState, getMousePos, canvasTransform, currentTemplateImages, hitTestPlaceholder, hitTestHandle, calculateSnapTargets, applySnapping]);

  const handleMouseUp = useCallback(() => {
    setDragState(null);
    setSnapGuides({x: [], y: [], symmetricX: [], marginRefX: [], marginRefY: []}); // Clear snap guides
  }, []);

  // Mouse event listeners
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mousedown', handleGlobalMouseDown);

    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mousedown', handleGlobalMouseDown);
    };
  }, [handleMouseDown, handleContextMenu, handleMouseMove, handleMouseUp, handleGlobalMouseDown]);

  // Close context menu on click outside or escape
  useEffect(() => {
    const handleClickOutside = () => setContextMenu(null);
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setContextMenu(null);
    };

    if (contextMenu?.visible) {
      document.addEventListener('click', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('click', handleClickOutside);
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [contextMenu?.visible]);

  // Render template on canvas
  useEffect(() => {
    if (!canvasRef.current || !currentTemplate) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions
    canvas.width = canvasSize.width;
    canvas.height = canvasSize.height;

    // Clear canvas
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    const { scale, offsetX, offsetY, spreadWidthPt, spreadHeightPt } = canvasTransform;

    // Draw placeholders
    currentTemplateImages.forEach((placeholder, index) => {
      const x = (placeholder.x * spreadWidthPt * scale) + offsetX;
      const y = (placeholder.y * spreadHeightPt * scale) + offsetY;
      const width = placeholder.width * spreadWidthPt * scale;
      const height = placeholder.height * spreadHeightPt * scale;

      // Highlight if hovered, focused, or being dragged
      const isHovered = hoveredPlaceholder === index;
      const isBeingDragged = dragState?.placeholderIndex === index;
      const isFocused = focusedPlaceholder === index;
      const isModified = modifiedTemplate !== null;

      // Draw placeholder rectangle
      ctx.fillStyle = isBeingDragged ? '#dbeafe' : isFocused ? '#fef3c7' : isHovered ? '#f3f4f6' : '#f9fafb';
      ctx.fillRect(x, y, width, height);
      
      // Draw border
      ctx.strokeStyle = isBeingDragged ? '#3b82f6' : isFocused ? '#f59e0b' : isHovered ? '#6b7280' : '#d1d5db';
      ctx.lineWidth = isBeingDragged ? 2 : isFocused ? 2 : 1;
      ctx.strokeRect(x, y, width, height);

      // Draw placeholder number
      ctx.fillStyle = '#374151';
      ctx.font = `${Math.max(12, width / 8)}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      const placeholderNumber = placeholder.id.replace('img', '');
      ctx.fillText(placeholderNumber, x + width / 2, y + height / 2);

      // Draw resize handles if hovered, focused, or being dragged
      if (isHovered || isFocused || isBeingDragged) {
        const handles = [
          { x: x, y: y }, // nw
          { x: x + width/2, y: y }, // n
          { x: x + width, y: y }, // ne
          { x: x + width, y: y + height/2 }, // e
          { x: x + width, y: y + height }, // se
          { x: x + width/2, y: y + height }, // s
          { x: x, y: y + height }, // sw
          { x: x, y: y + height/2 } // w
        ];

        ctx.fillStyle = '#3b82f6';
        handles.forEach(handle => {
          ctx.fillRect(handle.x - HANDLE_SIZE/2, handle.y - HANDLE_SIZE/2, HANDLE_SIZE, HANDLE_SIZE);
        });
      }
    });

    // Draw snap guides
    if (snapGuides.x.length > 0 || snapGuides.y.length > 0 || snapGuides.symmetricX.length > 0 || snapGuides.marginRefX.length > 0 || snapGuides.marginRefY.length > 0) {
      ctx.lineWidth = 1;
      ctx.setLineDash([4, 4]); // Dashed lines
      
      // Draw vertical snap guides (x positions)
      snapGuides.x.forEach(x => {
        // Use different colors for different snap lines
        if (x === 0.5) {
          ctx.strokeStyle = '#8b5cf6'; // Purple for gutter
        } else if (x === 0.25 || x === 0.75) {
          ctx.strokeStyle = '#3b82f6'; // Blue for page centers
        } else {
          ctx.strokeStyle = '#ef4444'; // Red for edges/other
        }
        
        const canvasX = (x * spreadWidthPt * scale) + offsetX;
        ctx.beginPath();
        ctx.moveTo(canvasX, offsetY);
        ctx.lineTo(canvasX, offsetY + (spreadHeightPt * scale));
        ctx.stroke();
      });
      
      // Draw symmetrical gutter-aware snap guides in orange
      snapGuides.symmetricX.forEach(x => {
        ctx.strokeStyle = '#f97316'; // Orange for symmetrical gutter-aware snaps
        ctx.lineWidth = 2; // Slightly thicker to distinguish
        
        const canvasX = (x * spreadWidthPt * scale) + offsetX;
        ctx.beginPath();
        ctx.moveTo(canvasX, offsetY);
        ctx.lineTo(canvasX, offsetY + (spreadHeightPt * scale));
        ctx.stroke();
        
        ctx.lineWidth = 1; // Reset line width
      });
      
      // Draw horizontal snap guides (y positions)
      snapGuides.y.forEach(y => {
        // Use blue for horizontal center
        ctx.strokeStyle = y === 0.5 ? '#3b82f6' : '#ef4444'; // Blue for center, red for others
        const canvasY = (y * spreadHeightPt * scale) + offsetY;
        ctx.beginPath();
        ctx.moveTo(offsetX, canvasY);
        ctx.lineTo(offsetX + (spreadWidthPt * scale), canvasY);
        ctx.stroke();
      });
      
      // Draw margin reference guides in green (showing the opposite margin we're equalizing to)
      snapGuides.marginRefX.forEach(x => {
        ctx.strokeStyle = '#10b981'; // Green for margin reference lines
        ctx.lineWidth = 2; // Thicker to distinguish as reference
        ctx.setLineDash([8, 4]); // Different dash pattern
        
        const canvasX = (x * spreadWidthPt * scale) + offsetX;
        ctx.beginPath();
        ctx.moveTo(canvasX, offsetY);
        ctx.lineTo(canvasX, offsetY + (spreadHeightPt * scale));
        ctx.stroke();
        
        ctx.lineWidth = 1; // Reset line width
        ctx.setLineDash([4, 4]); // Reset dash pattern
      });
      
      snapGuides.marginRefY.forEach(y => {
        ctx.strokeStyle = '#10b981'; // Green for margin reference lines
        ctx.lineWidth = 2; // Thicker to distinguish as reference
        ctx.setLineDash([8, 4]); // Different dash pattern
        
        const canvasY = (y * spreadHeightPt * scale) + offsetY;
        ctx.beginPath();
        ctx.moveTo(offsetX, canvasY);
        ctx.lineTo(offsetX + (spreadWidthPt * scale), canvasY);
        ctx.stroke();
        
        ctx.lineWidth = 1; // Reset line width
        ctx.setLineDash([4, 4]); // Reset dash pattern
      });
      
      ctx.setLineDash([]); // Reset line dash
    }

    // Draw gutter line down the middle of the spread
    ctx.strokeStyle = '#d1d5db';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]); // Small dashed line for gutter
    const gutterX = (0.5 * spreadWidthPt * scale) + offsetX; // Middle of spread
    ctx.beginPath();
    ctx.moveTo(gutterX, offsetY);
    ctx.lineTo(gutterX, offsetY + (spreadHeightPt * scale));
    ctx.stroke();
    ctx.setLineDash([]); // Reset line dash

    // Draw border around entire canvas
    ctx.strokeStyle = '#9ca3af';
    ctx.lineWidth = 2;
    ctx.strokeRect(offsetX, offsetY, spreadWidthPt * scale, spreadHeightPt * scale);

  }, [currentTemplate, currentTemplateImages, selectedDimension, canvasSize, dimensionConfig, canvasTransform, hoveredPlaceholder, dragState, modifiedTemplate, snapGuides, focusedPlaceholder]);

  const handlePreviousTemplate = () => {
    setCurrentTemplateIndex(prev => {
      if (prev === 0) {
        toast.info("Reached beginning of templates, wrapping to end");
        return filteredTemplates.length - 1;
      }
      return prev - 1;
    });
  };

  const handleNextTemplate = () => {
    setCurrentTemplateIndex(prev => {
      if (prev === filteredTemplates.length - 1) {
        toast.info("Reached end of templates, wrapping to beginning");
        return 0;
      }
      return prev + 1;
    });
  };

  // ESC, arrow key, and rotation listeners
  useEffect(() => {
    const handleKeydown = (event: KeyboardEvent) => {
      // Check if the textarea is focused - if so, don't interfere with arrow keys
      const isTextareaFocused = textareaRef.current && document.activeElement === textareaRef.current;
      
      if (event.key === 'Escape') {
        event.preventDefault();
        navigate(-1);
      } else if (event.key === 'ArrowLeft' && !isTextareaFocused) {
        event.preventDefault();
        handlePreviousTemplate();
      } else if (event.key === 'ArrowRight' && !isTextareaFocused) {
        event.preventDefault();
        handleNextTemplate();
      } else if (event.key === 'ArrowUp' && !isTextareaFocused) {
        event.preventDefault();
        // Next project dimension: square -> horizontal -> vertical -> square
        setSelectedDimension(prev => {
          if (prev === 'square') return 'horizontal';
          if (prev === 'horizontal') return 'vertical';
          return 'square';
        });
      } else if (event.key === 'ArrowDown' && !isTextareaFocused) {
        event.preventDefault();
        // Previous project dimension: square -> vertical -> horizontal -> square
        setSelectedDimension(prev => {
          if (prev === 'square') return 'vertical';
          if (prev === 'vertical') return 'horizontal';
          return 'square';
        });
      } else if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
        // Only prevent default and handle copy if a placeholder is focused
        if (focusedPlaceholder >= 0) {
          event.preventDefault();
          copyPlaceholderDimensions(focusedPlaceholder);
        }
        // If no placeholder is focused, allow normal copy behavior
      } else if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
        // Only prevent default and handle paste if a placeholder is focused and we have copied dimensions
        if (focusedPlaceholder >= 0 && copiedDimensions) {
          event.preventDefault();
          pastePlaceholderDimensions(focusedPlaceholder);
        }
        // If no placeholder is focused, allow normal paste behavior
      } else if (event.key === '\\') {
        event.preventDefault();
        // Cycle through classification filters: all -> ALL -> HORIZONTAL -> SQUARE -> VERTICAL -> all
        const allOptions = ['all', ...availableClassifications];
        const currentIndex = allOptions.indexOf(classificationFilter);
        const nextIndex = (currentIndex + 1) % allOptions.length;
        setClassificationFilter(allOptions[nextIndex]);
      }
    };

    document.addEventListener('keydown', handleKeydown);
    return () => document.removeEventListener('keydown', handleKeydown);
  }, [navigate, currentTemplateIndex, filteredTemplates.length, focusedPlaceholder, copyPlaceholderDimensions, pastePlaceholderDimensions, copiedDimensions, classificationFilter, availableClassifications]);

  const handleResetTemplate = () => {
    setModifiedTemplate(null);
  };

  // Set initial template index based on initialTemplateId
  useEffect(() => {
    if (initialTemplateId && filteredTemplates.length > 0) {
      const index = filteredTemplates.findIndex(t => t.id === initialTemplateId);
      if (index >= 0) {
        setCurrentTemplateIndex(index);
      }
    }
  }, [initialTemplateId, filteredTemplates]);

  // Reset template index when filter changes (except on initial load)
  useEffect(() => {
    if (!initialTemplateId) {
      setCurrentTemplateIndex(0);
    }
  }, [imageCountFilter, classificationFilter, initialTemplateId]);

  const handleBack = () => {
    navigate('/');
  };

  // Set cursor based on hover state
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    let cursor = 'default';
    if (hoveredHandle === 'move') cursor = 'move';
    else if (hoveredHandle === 'resize-n' || hoveredHandle === 'resize-s') cursor = 'ns-resize';
    else if (hoveredHandle === 'resize-e' || hoveredHandle === 'resize-w') cursor = 'ew-resize';
    else if (hoveredHandle === 'resize-nw' || hoveredHandle === 'resize-se') cursor = 'nw-resize';
    else if (hoveredHandle === 'resize-ne' || hoveredHandle === 'resize-sw') cursor = 'ne-resize';

    canvas.style.cursor = cursor;
  }, [hoveredHandle]);

  // Don't render if no templates match filter
  if (filteredTemplates.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 p-2">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" onClick={handleBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <h1 className="text-2xl font-bold">Template Editor</h1>
            </div>
          </div>
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-gray-500">
                No templates found matching the current filters
                {imageCountFilter !== 'all' && ` (${imageCountFilter} images)`}
                {classificationFilter !== 'all' && ` (${classificationFilter} classification)`}.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-2">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h1 className="text-2xl font-bold">Template Editor</h1>
            {modifiedTemplate && (
              <Button variant="outline" size="sm" onClick={handleResetTemplate}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            )}
            <Button 
              variant={snappingEnabled ? "default" : "outline"} 
              size="sm" 
              onClick={() => setSnappingEnabled(!snappingEnabled)}
            >
              <Magnet className="h-4 w-4 mr-2" />
              Snap
            </Button>
          </div>
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Project Dimension</CardTitle>
            </CardHeader>
            <CardContent>
              <Select 
                value={selectedDimension} 
                onValueChange={(value: ProjectDimension) => setSelectedDimension(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="square">Square Spread (12×12 pages)</SelectItem>
                  <SelectItem value="horizontal">Horizontal Spread (12×8 pages)</SelectItem>
                  <SelectItem value="vertical">Vertical Spread (8×12 pages)</SelectItem>
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter by Images
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select 
                value={imageCountFilter} 
                onValueChange={setImageCountFilter}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Templates</SelectItem>
                  {availableImageCounts.map(count => (
                    <SelectItem key={count} value={count.toString()}>
                      {count} Image{count !== 1 ? 's' : ''}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter by Classification
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select 
                value={classificationFilter} 
                onValueChange={setClassificationFilter}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Templates</SelectItem>
                  {availableClassifications.map(classification => (
                    <SelectItem key={classification} value={classification}>
                      {classification}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Template Navigation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between gap-1">
                <Button variant="outline" size="sm" onClick={handlePreviousTemplate} className="h-8 w-8 p-0 flex-shrink-0">
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-xs text-gray-600 text-center flex-1 min-w-0">
                  {currentTemplateIndex + 1} of {filteredTemplates.length}
                </span>
                <Button variant="outline" size="sm" onClick={handleNextTemplate} className="h-8 w-8 p-0 flex-shrink-0">
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Template Info</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm">
                <div><strong>ID:</strong> {currentTemplate?.id}</div>
                <div><strong>Name:</strong> {currentTemplate?.name}</div>
                <div><strong>Images:</strong> {currentTemplate?.images.length}</div>
                <div><strong>Classification:</strong> {currentTemplate?.classification?.join(', ')}</div>
                {modifiedTemplate && (
                  <div className="text-blue-600 font-medium">Modified</div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Canvas */}
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-center">
              <canvas 
                ref={canvasRef}
                className="border border-gray-300 shadow-lg"
                style={{ 
                  maxWidth: '100%',
                  height: 'auto'
                }}
              />
            </div>
            <div className="text-center mt-4 text-sm text-gray-500">
              {dimensionConfig.label} - {currentTemplate?.name} ({currentTemplate?.images.length} images)
              {imageCountFilter !== 'all' && (
                <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                  Filtered: {imageCountFilter} images
                </span>
              )}
              {classificationFilter !== 'all' && (
                <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                  Filtered: {classificationFilter}
                </span>
              )}
              {modifiedTemplate && (
                <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
                  Modified
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Template Code Display - Two Columns */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
          {/* Original Template */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Original Template Code</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-50 p-4 rounded text-xs overflow-x-auto max-h-96">
                <code>{JSON.stringify(currentTemplate, null, 2)}</code>
              </pre>
            </CardContent>
          </Card>

          {/* Modified Template */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                Modified Template Code
                {modifiedTemplate && (
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
                    Live
                  </span>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <textarea
                  ref={textareaRef}
                  value={editableTemplateJson}
                  onChange={(e) => handleJsonChange(e.target.value)}
                  className="w-full h-96 bg-gray-50 p-4 rounded text-xs font-mono border border-gray-200 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Edit template JSON here..."
                  spellCheck={false}
                />
                <div className="flex gap-2">
                  <Button 
                    variant="default" 
                    size="sm" 
                    onClick={applyJsonChanges}
                    className="text-xs"
                  >
                    Apply Changes
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={resetToOriginal}
                    className="text-xs"
                  >
                    Reset to Original
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Context Menu */}
        {contextMenu?.visible && (
          <div
            className="fixed bg-white border border-gray-300 shadow-lg rounded-md py-1 z-50"
            style={{
              left: contextMenu.x,
              top: contextMenu.y,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
              onClick={() => copyPlaceholderDimensions(contextMenu.placeholderIndex)}
            >
              📋 Copy Dimensions
            </button>
            {copiedDimensions && (
              <button
                className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
                onClick={() => pastePlaceholderDimensions(contextMenu.placeholderIndex)}
              >
                📋 Paste Dimensions
              </button>
            )}
            <div className="border-t border-gray-200 my-1"></div>
            <button
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
              onClick={() => applyAspectRatio(contextMenu.placeholderIndex, '3:2')}
            >
              <span className="w-4 h-3 bg-gray-300 rounded-sm"></span>
              3:2 (Horizontal)
            </button>
            <button
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
              onClick={() => applyAspectRatio(contextMenu.placeholderIndex, '2:3')}
            >
              <span className="w-3 h-4 bg-gray-300 rounded-sm"></span>
              2:3 (Vertical)
            </button>
            <button
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
              onClick={() => applyAspectRatio(contextMenu.placeholderIndex, '1:1')}
            >
              <span className="w-3 h-3 bg-gray-300 rounded-sm"></span>
              1:1 (Square)
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateEditor;