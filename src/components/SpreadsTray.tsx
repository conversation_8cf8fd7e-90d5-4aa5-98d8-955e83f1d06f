import React, { useState, useEffect, useRef, useCallback, forwardRef, useMemo } from 'react'; // Added forwardRef and useMemo
import { ImagePlus } from 'lucide-react';
import { Book, Plus, Trash2, Move } from 'lucide-react';
import { normalizeBackgroundImageUrl } from '@/utils/imageUtils';
import { adjustTemplateForGap, AdjustedPlaceholderPt, ImageGap } from '@/lib/templates'; // Added import
import { TemplateData, TemplateImage } from '@/lib/templates/interfaces'; // Import TemplateData and TemplateImage types
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from './ui/alert-dialog'; // Removed AlertDialogTrigger as it's not directly used here
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from './ui/context-menu';
// import { useToast } from './ui/use-toast'; // No longer needed if using sonner directly
import { toast } from 'sonner'; // Import sonner toast from the package
import '@/styles/resizing.css'; // Import our custom resizing styles

// @dnd-kit imports for drag and drop
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  MouseSensor,
  useSensor,
  useSensors,
  DragOverlay,
  closestCenter,
  pointerWithin,
  rectIntersection,
} from '@dnd-kit/core';
import {
  SortableContext,
  useSortable,
  horizontalListSortingStrategy,
  arrayMove,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// TanStack Virtual for horizontal virtualization
import { useVirtualizer } from '@tanstack/react-virtual';

import { SpreadBackgroundData } from './SpreadBackgroundManager'; // Import for per-spread background

// Define the structure for image transformations (zoom/pan/rotate)
export interface ImageTransform {
  scale: number;    // Zoom level (1.0 is original size)
  focalX: number;   // Horizontal focal point (0.0 left, 0.5 center, 1.0 right)
  focalY: number;   // Vertical focal point (0.0 top, 0.5 center, 1.0 bottom)
  fit?: 'cover' | 'contain'; // Fit mode ('contain' or 'cover')
  rotation?: number; // Rotation in degrees (0-360)
}

// Define the structure for an image placed in a placeholder, including transform
export interface ImagePlacement {
  placeholderId: string;
  imageId?: string;
  transform: ImageTransform; // Non-optional to ensure it's always present
  individualBorder?: {
    enabled: boolean;
    size: number; // in points
    color: string;
  };
}

export interface Spread {
  id: string;
  templateId: string;
  images: ImagePlacement[]; // Updated to use the new structure
  thumbnailDataUrl?: string; // Optional field for storing canvas snapshot
  spreadBackgroundData?: SpreadBackgroundData | null; // Optional: Data for per-spread background
}

import { ImageFile } from './ImageTray'; // Add ImageFile import

// Using TemplateImage from interfaces instead of local definition

interface SpreadsTrayProps {
  spreads: Spread[];
  currentSpreadId: string;
  onSelectSpread: (spreadId: string) => void;
  onAddSpread?: () => void;
  onDeleteSpread?: (spreadId: string) => void; // Keep for potential single delete actions? Or remove if unused.
  onDeleteMultipleSpreads?: (spreadIds: string[]) => void; // Add the new prop for batch deletion
  onDeleteAllSpreadsAndAddBlank?: () => void; // Prop to handle deleting all and adding a blank one
  onReorderSpreads?: (reorderedSpreads: Spread[]) => void;
  onAddSpreadFromImages?: (
    images: ImageFile[],
    index: number,
    initialFit: 'cover' | 'contain',
    sourceSpreadId?: string, // Optional: ID of the spread the image came from (if from SpreadCanvas)
    sourcePlaceholderId?: string // Optional: ID of the placeholder the image came from
  ) => void;
  templateMap: Record<string, TemplateData>;
  allImages: ImageFile[];
  usedImageIds?: string[]; // <-- Add this prop
  onHoverChange?: (isHovering: boolean) => void; // Prop to notify parent about hover state
  onFocusChange?: (isFocused: boolean) => void; // Add focus change handler prop
  onSelectionChange?: (selectedIds: string[]) => void; // Add selection change handler prop
  onHeightChange?: (newHeight: number) => void; // New prop for height change notification
  trayHeight?: number; // Height prop from parent
  spreadDimensionsPt?: { width: number; height: number }; // Add spreadDimensionsPt prop
  defaultDropModeIsCover?: boolean; // Keep this prop
  spreadBackgroundImage?: string | null; // Add this prop
  projectBackgroundImageOpacity?: number; // Add opacity prop
  canvasBackgroundColor?: string; // Add this prop for the canvas background color
  // dominantFitModeForDrop?: 'cover' | 'contain'; // REMOVED prop
  imageGap?: number | ImageGap; // Add imageGap prop (can be number or object)
  bleedValue?: number; // Added for border logic
  imageBorderSize?: number; // Added for border logic
  imageBorderColor?: string; // Added for border logic
  // Add a prop to receive notification of Photoshop roundtrip updates
  updatedImageIds?: string[];
  trackPreviewRequest?: (originalPath: string, resetCounter?: boolean) => void; // Add trackPreviewRequest prop for preview generation notification
  onAddImageToExistingSpread?: (
    targetSpreadId: string,
    image: ImageFile,
    initialFit: 'cover' | 'contain',
    sourceSpreadId?: string, // Optional: ID of the spread the image came from
    sourcePlaceholderId?: string // Optional: ID of the placeholder the image came from
  ) => Promise<void> | void;
  onRemoveImagesFromSource?: (
    sourceSpreadId: string,
    placeholderIds: string[]
  ) => Promise<void> | void;
  maxTemplateSize?: number; // Max images any template can hold
  onOfferAutobuildRequest?: (data: { droppedImages: ImageFile[]; currentSpreadId: string; existingImageCount: number; maxTemplateSize: number; }) => void; // For autobuild prompt
  projectBackgroundImageZoom?: number;
  projectBackgroundImagePanX?: number;
  projectBackgroundImagePanY?: number;
  projectBackgroundImagePath?: string | null; // Added: Original path of project background
  projectBackgroundImageNaturalWidth?: number;
  projectBackgroundImageNaturalHeight?: number;
  // Props for border rendering (already added to SpreadsTrayProps)
  // bleedValue?: number;
  // imageBorderSize?: number;
  // imageBorderColor?: string;
  defaultSpreadBackground?: SpreadBackgroundData | null; // Added for default spread background updates
  theme?: 'light' | 'dark'; // Add theme prop
  isFocusMode?: boolean; // Add isFocusMode prop
  customTemplates?: Record<string, TemplateImage[]>; // Add custom templates prop
}

// First define the interface before using it
interface SpreadThumbnailPreviewProps {
  spread: Spread;
  templateMap: Record<string, TemplateData>;
  allImages: ImageFile[];
  thumbnailSizes: {
    width: number;
    height: number;
    contentAspectRatio: number;
    containerAspectRatio: number;
  };
  spreadDimensionsPt?: { width: number; height: number }; // Add spread dimensions
  spreadBackgroundImage?: string | null; // Add this prop for the content background
  projectBackgroundImagePath?: string | null; // Added: Original path of project background
  projectBackgroundImageOpacity?: number; // Add opacity prop
  canvasBackgroundColor?: string; // Add this prop for the canvas background color
  defaultSpreadBackground?: SpreadBackgroundData | null; // Added for default spread background updates
  imageGap?: number | ImageGap; // Add imageGap prop (can be number or object)
  projectBackgroundImageZoom?: number;
  projectBackgroundImagePanX?: number;
  projectBackgroundImagePanY?: number;
  projectBackgroundImageNaturalWidth?: number;
  projectBackgroundImageNaturalHeight?: number;
  bleedValue?: number; // Added for border logic
  imageBorderSize?: number; // Added for border logic
  imageBorderColor?: string; // Added for border logic
  imageRefreshTimestamps?: Record<string, number>; // Added: For cache-busting project BG in thumbnail
  theme?: 'light' | 'dark'; // Add theme prop
  customTemplates?: Record<string, TemplateImage[]>; // Add custom templates prop
}

// Wrap component with forwardRef, add ref parameter
const SpreadsTray = forwardRef<HTMLDivElement, SpreadsTrayProps>(({
  spreads,
  currentSpreadId,
  onSelectSpread,
  onAddSpread,
  onDeleteSpread, // Keep destructuring for now
  onDeleteMultipleSpreads, // Destructure the new prop
  onDeleteAllSpreadsAndAddBlank, // Destructure the new prop
  onReorderSpreads,
  onAddSpreadFromImages, // Destructure new prop
  templateMap,
  allImages,
  usedImageIds = [], // <-- Destructure with default
  onHoverChange, // Destructure the new prop
  onFocusChange, // Destructure new prop
  onSelectionChange, // Destructure new prop
  onHeightChange, // Destructure new prop
  trayHeight = 175, // Destructure height prop with default
  spreadDimensionsPt = { width: 0, height: 0 }, // Destructure with default
  defaultDropModeIsCover = false, // Keep destructuring
  spreadBackgroundImage, // Destructure the new prop
  projectBackgroundImagePath, // Destructure the new prop
  projectBackgroundImageOpacity, // Destructure opacity prop
  canvasBackgroundColor, // Destructure the new prop
  defaultSpreadBackground, // Destructure default spread background
  imageGap = 0, // Destructure with default
  updatedImageIds = [], // Destructure the new prop for Photoshop roundtrip
  trackPreviewRequest, // Destructure trackPreviewRequest prop for preview generation notification
  onAddImageToExistingSpread, // Destructure new prop
  onRemoveImagesFromSource, // Destructure new prop for source cleanup
  maxTemplateSize, // Destructure new prop
  onOfferAutobuildRequest, // Destructure new prop
  projectBackgroundImageZoom,
  projectBackgroundImagePanX,
  projectBackgroundImagePanY,
  projectBackgroundImageNaturalWidth,
  projectBackgroundImageNaturalHeight,
  bleedValue, // Destructure new prop
  imageBorderSize, // Destructure new prop
  imageBorderColor, // Destructure new prop
  theme = 'light', // Destructure theme prop with default
  isFocusMode = false, // Destructure isFocusMode with a default
  customTemplates = {}, // Destructure customTemplates with default
  // defaultSpreadBackground, // REMOVE Duplicate destructuring
  // dominantFitModeForDrop = 'contain', // REMOVED destructuring
}, ref) => { // Destructure props, add ref
  // const { toast: uiToast } = useToast(); // Removed unused hook
  // State for multi-selection
  const [selectedSpreadIds, setSelectedSpreadIds] = useState<Set<string>>(new Set());
  const [lastClickedIndex, setLastClickedIndex] = useState<number | null>(null);
  // State for deletion confirmation dialog
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const [spreadsToDelete, setSpreadsToDelete] = useState<string[]>([]); // Store IDs to delete
  const scrollContainerRef = useRef<HTMLDivElement>(null); // Ref for the scrollable container
  // const [isFocused, setIsFocused] = useState(false); // Track focus state - Keep internal state if needed for styling etc.
  const [isFocused, setIsFocused] = useState(false); // Keep internal focus state

  // State for drop indicator position
  const [dropIndicatorIndex, setDropIndicatorIndex] = useState<number | null>(null);
  // State to track if images are being dragged over
  const [isDraggingImagesOver, setIsDraggingImagesOver] = useState<boolean>(false);
  const [dragOverThumbnailTargetId, setDragOverThumbnailTargetId] = useState<string | null>(null); // State for highlighting thumbnail drop target
  
  // Add state for the max tray height
  const [maxTrayHeight, setMaxTrayHeight] = useState<number>(window.innerHeight / 3);
  
  // Add state to track image refresh timestamps for cache busting
  
  // State for duplicate image warning dialog
  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);
  const [duplicateWarningData, setDuplicateWarningData] = useState<{
    images: ImageFile[];
    targetIndex: number;
    initialFit: 'cover' | 'contain';
    sourceSpreadId?: string;
    sourcePlaceholderId?: string;
    dropType: 'newSpread' | 'existingSpread';
    targetSpreadId?: string;
  } | null>(null);
  const [imageRefreshTimestamps, setImageRefreshTimestamps] = useState<Record<string, number>>({});

  // @dnd-kit setup for drag and drop
  const [activeSpreadId, setActiveSpreadId] = useState<string | null>(null);
  const [isDraggingSpread, setIsDraggingSpread] = useState(false);

  // Configure sensors for mouse interaction only (no touch for desktop app)
  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 8, // Require 8px movement before activating drag
    },
  });
  const sensors = useSensors(mouseSensor);

  // Virtual scrolling refs
  const scrollElementRef = useRef<HTMLDivElement>(null);
  

  // Effect to update refresh timestamps when updatedImageIds changes
  useEffect(() => {
    if (updatedImageIds && updatedImageIds.length > 0) {
      console.log(`[SpreadsTray] Refreshing thumbnails for ${updatedImageIds.length} updated images`);
      const newTimestamps = { ...imageRefreshTimestamps };
      
      // Update timestamps for each updated image
      updatedImageIds.forEach(imageId => {
        newTimestamps[imageId] = Date.now();
      });
      
      setImageRefreshTimestamps(newTimestamps);
    }
  }, [updatedImageIds]);

  // Add effect to update maxTrayHeight on window resize
  useEffect(() => {
    const updateMaxHeight = () => {
      setMaxTrayHeight(window.innerHeight / 3);
    };
    
    // Set initial max height
    updateMaxHeight();
    
    // Add resize listener
    window.addEventListener('resize', updateMaxHeight);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', updateMaxHeight);
    };
  }, []);

  // Calculate thumbnail size based on tray height
  const thumbnailSizes = useMemo(() => {
    // Simplified approach inspired by ImageTray (but for horizontal layout)
    // Define vertical overhead, but use a higher value specifically for the initial height
    const initialTrayHeight = 175; // Update initial height constant
    const initialOverhead = 60;   // Adjusted overhead for 200px initial height
    const scalingOverhead = 65;   // Keep scaling overhead the same
    
    const verticalOverhead = (trayHeight === initialTrayHeight) ? initialOverhead : scalingOverhead;
    
    // Calculate available height directly
    const availableHeight = Math.max(20, trayHeight - verticalOverhead); // Ensure minimum height
    
    // Use the available height directly for the thumbnail
    const heightPx = Math.floor(availableHeight);
    
    // Get the actual spread aspect ratio (for content positioning)
    // Ensure dimensions are valid positive numbers before calculating aspect ratio
    const contentAspectRatio = (spreadDimensionsPt && spreadDimensionsPt.width > 0 && spreadDimensionsPt.height > 0)
      ? (spreadDimensionsPt.width * 2) / spreadDimensionsPt.height
      : 2; // Default to 2:1 if dimensions are invalid/zero
    
    // Use a more boxy container aspect ratio (1:1 to 4:3 range)
    // For very wide spreads, limit how wide the container gets
    const maxContainerAspectRatio = 1.5; // Limit how wide the container gets for extreme aspect ratios
    const containerAspectRatio = Math.min(contentAspectRatio, maxContainerAspectRatio);
    
    // Calculate width based on height and the container aspect ratio
    const widthPx = Math.round(heightPx * containerAspectRatio);
    
    // Define consistent spacing between thumbnails
    const gap = 16; // Fixed gap between thumbnails
    
    return {
      width: widthPx,
      height: heightPx,
      gap: gap,
      contentAspectRatio, // Store the original content aspect ratio for letterboxing
      containerAspectRatio // Store the container aspect ratio
    };
  }, [trayHeight, spreadDimensionsPt]);

  // Virtual scrolling setup for horizontal layout
  const virtualizer = useVirtualizer({
    count: spreads.length,
    getScrollElement: () => scrollElementRef.current,
    estimateSize: useCallback(() => thumbnailSizes.width + thumbnailSizes.gap, [thumbnailSizes.width, thumbnailSizes.gap]), // Width + consistent gap
    horizontal: true,
    overscan: 2, // Render 2 items before and after visible area
  });

  // Force virtualizer to recalculate when thumbnail sizes change
  useEffect(() => {
    virtualizer.measure();
  }, [thumbnailSizes.width, thumbnailSizes.height, thumbnailSizes.gap, virtualizer]);

  // @dnd-kit drag handlers
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    setActiveSpreadId(active.id as string);
    setIsDraggingSpread(true);
    setIsDraggingImagesOver(false);
  }, []);

  const handleDragOver = useCallback((event: DragOverEvent) => {
    // Handle drop indicator positioning
    const { active, over } = event;
    if (!over) {
      setDropIndicatorIndex(null);
      return;
    }

    // Calculate drop position for visual indicator
    const activeIndex = spreads.findIndex(s => s.id === active.id);
    const overIndex = spreads.findIndex(s => s.id === over.id);
    
    if (activeIndex !== -1 && overIndex !== -1) {
      // Determine if we should insert before or after the over item
      const dropIndex = activeIndex < overIndex ? overIndex + 1 : overIndex;
      setDropIndicatorIndex(dropIndex);
    }
  }, [spreads]);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    
    setActiveSpreadId(null);
    setIsDraggingSpread(false);
    setDropIndicatorIndex(null);

    if (over && active.id !== over.id) {
      // Actual reordering occurred
      const activeIndex = spreads.findIndex(s => s.id === active.id);
      const overIndex = spreads.findIndex(s => s.id === over.id);
      
      if (activeIndex !== -1 && overIndex !== -1) {
        const reorderedSpreads = arrayMove(spreads, activeIndex, overIndex);
        onReorderSpreads?.(reorderedSpreads);
      }
    } else {
      // No reordering - treat as selection (either click or cancelled drag)
      const activeIndex = spreads.findIndex(s => s.id === active.id);
      if (activeIndex !== -1) {
        setSelectedSpreadIds(new Set([active.id as string]));
        setLastClickedIndex(activeIndex);
        onSelectSpread?.(active.id as string);
        
        if (ref && 'current' in ref && ref.current) {
          ref.current.focus({preventScroll: true});
        }
      }
    }
  }, [spreads, onReorderSpreads, onSelectSpread, ref]);




  // --- Multi-select Click Handler ---
  const handleSpreadClick = (
    event: React.MouseEvent<HTMLDivElement>,
    clickedId: string,
    clickedIndex: number
  ) => {
    const isRightClick = event.button === 2;
    const isCmdOrCtrl = event.metaKey || event.ctrlKey;
    const isShift = event.shiftKey;

    let newSelectedIds = new Set(selectedSpreadIds);
    let newLastClickedIndex = lastClickedIndex;

    // If it's a right-click on an already selected item, and no modifiers are pressed,
    // do not change the selection. The context menu will operate on the existing selection.
    if (isRightClick && selectedSpreadIds.has(clickedId) && !isCmdOrCtrl && !isShift) {
      // Still update the active spread for editing if it's different
      if (clickedId !== currentSpreadId) { // currentSpreadId is a prop from BookEditor
        onSelectSpread(clickedId);
      }
      // Crucially, do not call setSelectedSpreadIds or setLastClickedIndex here.
      return; // Exit early to preserve multi-selection for the context menu
    }

    // Existing logic for Shift, Cmd/Ctrl, and normal left-clicks (or right-click on unselected)
    if (isShift && lastClickedIndex !== null) {
      // Range selection (Shift + Click)
      const start = Math.min(lastClickedIndex, clickedIndex);
      const end = Math.max(lastClickedIndex, clickedIndex);
      // Keep existing selections if Cmd/Ctrl is also held, otherwise start fresh
      if (!isCmdOrCtrl) {
          newSelectedIds.clear();
      }
      for (let i = start; i <= end; i++) {
        newSelectedIds.add(spreads[i].id);
      }
      // Don't update lastClickedIndex on shift-click range selection
    } else if (isCmdOrCtrl) {
      // Toggle selection (Cmd/Ctrl + Click)
      if (newSelectedIds.has(clickedId)) {
        newSelectedIds.delete(clickedId);
      } else {
        newSelectedIds.add(clickedId);
      }
      newLastClickedIndex = clickedIndex; // Update last clicked for potential shift-click later
    } else {
      // Single selection (Normal Left Click, or Right Click on an unselected item)
      newSelectedIds.clear();
      newSelectedIds.add(clickedId);
      newLastClickedIndex = clickedIndex;
    }

    setSelectedSpreadIds(newSelectedIds);
    setLastClickedIndex(newLastClickedIndex);

    // Always update the *active* spread being edited
    // This is safe because if it was a right-click on selected, we returned early.
    onSelectSpread(clickedId);
  };


  // --- Deletion Logic ---

  // Handler to prepare for deletion (called from context menu)
  const handleDeleteRequest = (rightClickedSpreadId: string) => {
    let idsToDelete: string[] = [];

    // If the right-clicked item is part of the current selection, delete the whole selection
    if (selectedSpreadIds.has(rightClickedSpreadId)) {
      idsToDelete = Array.from(selectedSpreadIds);
    } else {
      // Otherwise, delete only the right-clicked item
      idsToDelete = [rightClickedSpreadId];
    }

    // Check if attempting to delete all spreads
    if (idsToDelete.length >= spreads.length) {
      if (onDeleteAllSpreadsAndAddBlank) {
        // Allow deleting all if the specific handler is provided
        setSpreadsToDelete(idsToDelete);
        setIsAlertOpen(true);
      } else {
        // Otherwise, prevent deleting all spreads (old behavior)
        toast.info("Cannot delete all spreads. Action not configured."); // Changed from toast.warn
        console.warn("Cannot delete all spreads if onDeleteAllSpreadsAndAddBlank is not provided.");
        return;
      }
    } else if (idsToDelete.length > 0 && onDeleteMultipleSpreads) { // Handle normal multi-delete
      setSpreadsToDelete(idsToDelete);
      setIsAlertOpen(true);
    }
  };

  // Handler for the actual deletion confirmation from the dialog
  const confirmDelete = () => {
    // Check if we are deleting all spreads and the specific handler is available
    if (spreadsToDelete.length >= spreads.length && onDeleteAllSpreadsAndAddBlank) {
      onDeleteAllSpreadsAndAddBlank();
      setSelectedSpreadIds(new Set());
      setLastClickedIndex(null);
    } else if (spreadsToDelete.length > 0 && onDeleteMultipleSpreads) {
      // Standard multiple spread deletion
      onDeleteMultipleSpreads(spreadsToDelete);
      setSelectedSpreadIds(new Set()); // Clear selection after deletion
      setLastClickedIndex(null);
    } else if (spreadsToDelete.length > 0 && onDeleteSpread) {
      // Fallback to single delete (should ideally not be reached if batch is always used)
      console.warn("Using single delete fallback for multiple spreads.");
      spreadsToDelete.forEach(id => onDeleteSpread && onDeleteSpread(id));
    }
    setIsAlertOpen(false); // Close dialog
    setSpreadsToDelete([]); // Reset IDs
  };

  // Dialog handlers for duplicate warning
  const handleDuplicateConfirm = async () => {
    if (duplicateWarningData) {
      const { images, targetIndex, initialFit, sourceSpreadId, sourcePlaceholderId, dropType, targetSpreadId } = duplicateWarningData;
      
      if (dropType === 'newSpread') {
        onAddSpreadFromImages(images, targetIndex, initialFit, sourceSpreadId, sourcePlaceholderId);
        // Track preview requests for all dropped images
        if (trackPreviewRequest) {
          images.forEach((image, index) => {
            // Reset the counter only for the first image in the batch
            trackPreviewRequest(image.originalPath, index === 0);
          });
        }
        if (images.length === 1) {
          toast.success(`New spread created with image: ${images[0].name}`);
        } else {
          toast.success(`New spread created with ${images.length} images.`);
        }
      } else if (dropType === 'existingSpread' && targetSpreadId) {
        if (images.length === 1) {
          // Single image dropped
          await onAddImageToExistingSpread(targetSpreadId, images[0], initialFit, sourceSpreadId, sourcePlaceholderId);
          // Track preview request for the dropped image
          if (trackPreviewRequest) {
            // Reset the counter for a new image drop
            trackPreviewRequest(images[0].originalPath, true);
          }
          toast.success(`Image "${images[0].name}" added to spread ${targetIndex + 1}`);
        } else {
          // Multiple images dropped - use the same logic as in the original handler
          // This would need more complex handling for autobuild, etc.
          // For now, just add them iteratively
          for (let i = 0; i < images.length; i++) {
            const image = images[i];
            await onAddImageToExistingSpread(targetSpreadId, image, initialFit, sourceSpreadId, sourcePlaceholderId);
            if (trackPreviewRequest) {
              // Reset the counter only for the first image
              trackPreviewRequest(image.originalPath, i === 0);
            }
          }
          if (images.length > 0) {
            toast.success(`${images.length} image(s) processed for spread ${targetIndex + 1}.`);
          }
        }
      }
    }
    
    setShowDuplicateWarning(false);
    setDuplicateWarningData(null);
  };

  const handleDuplicateCancel = () => {
    setShowDuplicateWarning(false);
    setDuplicateWarningData(null);
    toast.info("Image placement cancelled.");
  };

  // --- Effect to handle keyboard navigation selection ---
  useEffect(() => {
    // Ensure current spread is selected when changed via keyboard navigation 
    if (currentSpreadId && !selectedSpreadIds.has(currentSpreadId)) {
      setSelectedSpreadIds(new Set([currentSpreadId]));
    }
  }, [currentSpreadId, selectedSpreadIds]);

  // --- Effect to scroll the current spread into view ---
  useEffect(() => {
    if (scrollContainerRef.current && currentSpreadId) {
      const activeThumbnail = scrollContainerRef.current.querySelector<HTMLDivElement>(`[data-spread-id="${currentSpreadId}"]`);
      if (activeThumbnail) {
        activeThumbnail.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'nearest'
        });
      }
    }
  }, [currentSpreadId, spreads]);
  
  // --- Focus effect for keyboard navigation ---
  useEffect(() => {
    // Focus the tray when current spread changes (for keyboard navigation)
    if (currentSpreadId && ref && 'current' in ref && ref.current) {
      ref.current.focus({preventScroll: true});
    }
  }, [currentSpreadId, ref]);

  // --- Report Selection Changes ---
  useEffect(() => {
    onSelectionChange?.(Array.from(selectedSpreadIds)); // Report as an array
  }, [selectedSpreadIds, onSelectionChange]); // Report whenever selection changes

  // --- Focus Handling ---
  const handleFocus = useCallback(() => {
    setIsFocused(true);
    onFocusChange?.(true);
  }, [onFocusChange]);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    onFocusChange?.(false);
  }, [onFocusChange]);


  // Use refs to access current values in the stable global listener
  const stateRef = useRef({
    isFocused,
    selectedSpreadIds,
    currentSpreadId,
    spreads,
    onSelectSpread,
    setSelectedSpreadIds,
    setSpreadsToDelete,
    setIsAlertOpen
  });

  // Update ref whenever state changes
  useEffect(() => {
    stateRef.current = {
      isFocused,
      selectedSpreadIds,
      currentSpreadId,
      spreads,
      onSelectSpread,
      setSelectedSpreadIds,
      setSpreadsToDelete,
      setIsAlertOpen
    };
  });

  // Set up global keyboard listener with stable reference (no dependencies!)
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      const { 
        isFocused: currentIsFocused, 
        selectedSpreadIds: currentSelectedSpreadIds,
        currentSpreadId: currentCurrentSpreadId,
        spreads: currentSpreads,
        onSelectSpread: currentOnSelectSpread,
        setSelectedSpreadIds: currentSetSelectedSpreadIds,
        setSpreadsToDelete: currentSetSpreadsToDelete,
        setIsAlertOpen: currentSetIsAlertOpen
      } = stateRef.current;

      // Only handle when SpreadsTray is conceptually focused (has selection or current spread)
      const isSpreadsTrayActive = currentIsFocused || currentSelectedSpreadIds.size > 0;
      
      if (!isSpreadsTrayActive) return;
      
      // Select All (Cmd+A or Ctrl+A)
      if ((event.metaKey || event.ctrlKey) && (event.key === 'a' || event.key === 'A')) {
        // Only handle if we're not in an input or textarea
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
          return;
        }
        
        event.preventDefault();
        const allSpreadIds = new Set(currentSpreads.map(s => s.id));
        currentSetSelectedSpreadIds(allSpreadIds);
      }
      // Escape key to deselect all selected spread thumbnails
      else if (event.key === 'Escape' && currentSelectedSpreadIds.size > 0) {
        event.preventDefault();
        currentSetSelectedSpreadIds(new Set());
        if (currentCurrentSpreadId) {
          currentOnSelectSpread(currentCurrentSpreadId);
        }
      }
      // Note: Delete key handling is now managed by BookEditor's global handler
      // This prevents duplicate delete dialogs
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, []); // No dependencies needed - uses refs for current state

  // --- Keyboard Shortcuts ---
  const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLDivElement>) => {
    // Note: All keyboard shortcuts are now handled by the global listener (handleGlobalKeyDown)
    // This local handler is kept for potential future local-only shortcuts
    
    // Future keyboard shortcuts can be added here
  }, [isFocused]);


  // --- Keep Context Menu Deletion Logic ---
  // handleDeleteRequest and confirmDelete remain as they are triggered by the context menu, not the keyboard directly.

  // --- Ref for the container and Drag Over Logic for Indicator ---
  const containerRef = useRef<HTMLDivElement>(null); // Ref for the flex container holding thumbnails

  // Handler for container drag over event
  const handleContainerDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;

    const isImageTrayDrag = e.dataTransfer.types.includes('application/json');
    const isCanvasImageDrag = e.dataTransfer.types.includes('application/x-bookproofs-canvas-image');
    const isCanvasMultiImageDrag = e.dataTransfer.types.includes('application/x-bookproofs-canvas-images');
    const isAnyImageDrag = isImageTrayDrag || isCanvasImageDrag || isCanvasMultiImageDrag;
    const isSpreadDrag = isDraggingSpread; // Check if we initiated a spread drag internally

    if (!isAnyImageDrag && !isSpreadDrag) {
      // If it's not any recognized image drag nor an internal spread drag, ignore it
      setIsDraggingImagesOver(false);
      setDragOverThumbnailTargetId(null); // Clear thumbnail target
      // Do NOT preventDefault, disallowing the drop
      return;
    }

    e.preventDefault(); // Allow dropping

    // Set drop effect based on drag type
    if (isAnyImageDrag) {
      e.dataTransfer.dropEffect = 'copy';
      if (!isDraggingImagesOver) setIsDraggingImagesOver(true);
    } else if (isSpreadDrag) {
      e.dataTransfer.dropEffect = 'move';
      if (isDraggingImagesOver) setIsDraggingImagesOver(false);
    }
    
    // Clear thumbnail target when dragging over the container (between items)
    if (dragOverThumbnailTargetId) setDragOverThumbnailTargetId(null);


    // --- Calculate Indicator Position ---
    const container = containerRef.current;
    // Get only the spread thumbnail elements, ignore others like the add button for index calculation
    const thumbnailElements = Array.from(container.children).filter(
      (child): child is HTMLElement =>
        child instanceof HTMLElement && child.hasAttribute('data-spread-id')
    );

    let newIndex: number | null = null;

    // Get the container's position, accounting for padding and parent containers
    const containerRect = container.getBoundingClientRect();
    
    // Iterate through the actual thumbnail elements to find the drop position
    for (let i = 0; i < thumbnailElements.length; i++) {
      const child = thumbnailElements[i];
      const rect = child.getBoundingClientRect();
      const midpoint = rect.left + rect.width / 2;

      // If the mouse is to the left of the midpoint of this thumbnail,
      // the drop target index is the index of this thumbnail.
      if (e.clientX < midpoint) {
        newIndex = i;
        break;
      }
    }

    // If the loop finishes without finding a position (mouse is to the right of all midpoints),
    // the drop target index is after the last thumbnail.
    if (newIndex === null) {
      newIndex = thumbnailElements.length;
    }

    // Update the state only if the calculated index is different from the current one
    if (newIndex !== dropIndicatorIndex) {
      setDropIndicatorIndex(newIndex);
    }
  };

  const handleContainerDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;
    const rect = containerRef.current.getBoundingClientRect();
    // Clear indicator only if mouse moves horizontally outside the container bounds
    // Check if the mouse is truly leaving the container element horizontally
    if (e.clientX < rect.left || e.clientX > rect.right || e.clientY < rect.top || e.clientY > rect.bottom) {
       // Check if the related target is outside the container; null means leaving the window
       if (!e.relatedTarget || !containerRef.current.contains(e.relatedTarget as Node)) {
          setDropIndicatorIndex(null);
          setIsDraggingImagesOver(false); // Also reset image drag flag
       }
    }
    // Note: We no longer check e.relatedTarget or vertical position (e.clientY)
  };

  // Custom gap adjustment function for custom templates that preserves positioning
  const adjustCustomTemplateForGap = useCallback((
    customLayout: TemplateImage[],
    imageGapInput: number | ImageGap,
    spreadWidthPt: number,
    spreadHeightPt: number
  ): AdjustedPlaceholderPt[] => {
    // Handle both single number and object-based imageGapInput
    const gapHorizontal = typeof imageGapInput === 'number' ? imageGapInput : imageGapInput.horizontal;
    const gapVertical = typeof imageGapInput === 'number' ? imageGapInput : imageGapInput.vertical;

    // Check for zero gap case - apply small overlap to prevent hairlines
    if (gapHorizontal <= 0 && gapVertical <= 0) {
      const overlap = 0.5;
      return customLayout.map(p => ({
        id: p.id,
        adjustedX_pt: p.x * spreadWidthPt,
        adjustedY_pt: p.y * spreadHeightPt,
        adjustedWidth_pt: (p.width * spreadWidthPt) + overlap,
        adjustedHeight_pt: (p.height * spreadHeightPt) + overlap,
      }));
    }

    // For custom templates, preserve the original positioning relationships
    // Apply gaps by detecting neighbor relationships and adjusting accordingly
    const adjustedLayout: AdjustedPlaceholderPt[] = [];
    const FRAC_EPSILON = 1e-6;
    
    for (const placeholder of customLayout) {
      // Initialize adjustment values
      let leftAdjustment = 0;
      let rightAdjustment = 0;
      let topAdjustment = 0;
      let bottomAdjustment = 0;
      
      // Check if there are neighbors to the left
      const hasLeftNeighbor = customLayout.some(p => 
        p !== placeholder && 
        Math.abs(p.x + p.width - placeholder.x) < FRAC_EPSILON &&
        (p.y < placeholder.y + placeholder.height && p.y + p.height > placeholder.y)
      );
      
      // Check if there are neighbors to the right
      const hasRightNeighbor = customLayout.some(p => 
        p !== placeholder && 
        Math.abs(p.x - (placeholder.x + placeholder.width)) < FRAC_EPSILON &&
        (p.y < placeholder.y + placeholder.height && p.y + p.height > placeholder.y)
      );
      
      // Check if there are neighbors above
      const hasTopNeighbor = customLayout.some(p => 
        p !== placeholder && 
        Math.abs(p.y + p.height - placeholder.y) < FRAC_EPSILON &&
        (p.x < placeholder.x + placeholder.width && p.x + p.width > placeholder.x)
      );
      
      // Check if there are neighbors below
      const hasBottomNeighbor = customLayout.some(p => 
        p !== placeholder && 
        Math.abs(p.y - (placeholder.y + placeholder.height)) < FRAC_EPSILON &&
        (p.x < placeholder.x + placeholder.width && p.x + p.width > placeholder.x)
      );
      
      // Apply adjustments based on neighboring placeholders
      if (hasLeftNeighbor) leftAdjustment = gapHorizontal / 2;
      if (hasRightNeighbor) rightAdjustment = gapHorizontal / 2;
      if (hasTopNeighbor) topAdjustment = gapVertical / 2;
      if (hasBottomNeighbor) bottomAdjustment = gapVertical / 2;
      
      // Calculate final dimensions with adjustments
      const adjustedX_pt = placeholder.x * spreadWidthPt + leftAdjustment;
      const adjustedY_pt = placeholder.y * spreadHeightPt + topAdjustment;
      const adjustedWidth_pt = Math.max(0, placeholder.width * spreadWidthPt - leftAdjustment - rightAdjustment);
      const adjustedHeight_pt = Math.max(0, placeholder.height * spreadHeightPt - topAdjustment - bottomAdjustment);
      
      adjustedLayout.push({
        id: placeholder.id,
        adjustedX_pt,
        adjustedY_pt,
        adjustedWidth_pt,
        adjustedHeight_pt,
      });
    }
    
    return adjustedLayout;
  }, []);

  // VirtualizedSpreadItem component with @dnd-kit integration
  const VirtualizedSpreadItem = React.memo(({ 
    spread, 
    index, 
    virtualItem,
    isActive,
    isSelected,
    isDragging 
  }: {
    spread: Spread;
    index: number;
    virtualItem: any;
    isActive: boolean;
    isSelected: boolean;
    isDragging: boolean;
  }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging: isSortableDragging,
    } = useSortable({ id: spread.id });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.5 : 1,
      position: 'absolute' as const,
      left: virtualItem.start, // Use virtual start position directly
      top: 0,
      width: thumbnailSizes.width, // Use actual thumbnail width, not virtual size
      height: thumbnailSizes.height,
    };

    // Create a combined ref for both dnd-kit and selectstart handling
    const thumbnailRef = useRef<HTMLDivElement>(null);
    const combinedRef = (element: HTMLDivElement | null) => {
      setNodeRef(element);
      thumbnailRef.current = element;
    };

    // Prevent text selection on this thumbnail
    useEffect(() => {
      const element = thumbnailRef.current;
      if (element) {
        const preventSelection = (e: Event) => e.preventDefault();
        element.addEventListener('selectstart', preventSelection);
        return () => element.removeEventListener('selectstart', preventSelection);
      }
    }, []);

    // Define the thumbnail element structure
    const thumbnailDiv = (
      <div
        ref={combinedRef}
        style={style}
        {...attributes}
        {...listeners}
        className={`
          rounded-md cursor-pointer overflow-hidden relative flex-shrink-0 ${
            isSelected || isActive 
              ? (theme === 'dark' ? 'ring-2 ring-white' : 'ring-2 ring-primary')
              : (theme === 'dark' ? 'border border-gray-600 hover:border-gray-500' : 'border border-gray-200 hover:border-gray-300')
          } ${isDragging ? 'opacity-30' : 'opacity-100'}
          transition-opacity transition-colors
        `}
        onMouseEnter={() => onHoverChange?.(true)}
        onMouseLeave={() => {
          onHoverChange?.(false);
          if (dragOverThumbnailTargetId === spread.id) {
            setDragOverThumbnailTargetId(null);
          }
        }}
        onDragOver={(e) => {
          // Handle image drops from ImageTray/SpreadCanvas
          e.preventDefault();
          e.stopPropagation();
          const isImageTrayDrag = e.dataTransfer.types.includes('application/json');
          const isCanvasImageDrag = e.dataTransfer.types.includes('application/x-bookproofs-canvas-image');
          const isCanvasMultiImageDrag = e.dataTransfer.types.includes('application/x-bookproofs-canvas-images');
          
          if (isImageTrayDrag || isCanvasImageDrag || isCanvasMultiImageDrag) {
            e.dataTransfer.dropEffect = 'copy';
            setDragOverThumbnailTargetId(spread.id);
          }
        }}
        onDragLeave={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setDragOverThumbnailTargetId(null);
        }}
        onDrop={async (e) => {
          // Handle image drops from ImageTray/SpreadCanvas
          e.preventDefault();
          e.stopPropagation();
          const isImageTrayDrop = e.dataTransfer.types.includes('application/json');
          const isCanvasImageDrop = e.dataTransfer.types.includes('application/x-bookproofs-canvas-image');
          const isCanvasMultiImageDrop = e.dataTransfer.types.includes('application/x-bookproofs-canvas-images');

          if ((isImageTrayDrop || isCanvasImageDrop || isCanvasMultiImageDrop) && onAddImageToExistingSpread) {
            try {
              let dataType: string;
              let jsonData: string;
              let parsedData: any;
              
              if (isImageTrayDrop) {
                dataType = 'application/json';
              } else if (isCanvasMultiImageDrop) {
                dataType = 'application/x-bookproofs-canvas-images';
              } else {
                dataType = 'application/x-bookproofs-canvas-image';
              }
              
              jsonData = e.dataTransfer.getData(dataType);
              parsedData = JSON.parse(jsonData);
              
              let droppedImageFiles: ImageFile[] = [];
              let sourceSpreadId: string | undefined = undefined;
              let sourcePlaceholderId: string | undefined = undefined;
              let sourcePlaceholderIds: string[] = [];

              if (isImageTrayDrop) {
                droppedImageFiles = Array.isArray(parsedData) ?
                  parsedData.filter(item => item && item.id) as ImageFile[] :
                  (parsedData && parsedData.id ? [parsedData as ImageFile] : []);
              } else if (isCanvasMultiImageDrop) {
                if (Array.isArray(parsedData)) {
                  droppedImageFiles = parsedData.map(({ sourceSpreadId: sId, sourcePlaceholderId: pId, ...imgData }) => imgData as ImageFile).filter(img => img && img.id);
                  sourcePlaceholderIds = parsedData.map(item => item.sourcePlaceholderId).filter(Boolean);
                  if (parsedData.length > 0) {
                    sourceSpreadId = parsedData[0].sourceSpreadId;
                  }
                }
              } else if (isCanvasImageDrop) {
                const { sourceSpreadId: sId, sourcePlaceholderId: pId, ...imgData } = parsedData as ImageFile & { sourceSpreadId?: string, sourcePlaceholderId?: string };
                if (imgData && imgData.id) {
                  droppedImageFiles = [imgData as ImageFile];
                  sourceSpreadId = sId;
                  sourcePlaceholderId = pId;
                  if (pId) sourcePlaceholderIds = [pId];
                }
              }

              if (droppedImageFiles.length > 0) {
                const modifierPressed = e.shiftKey;
                const initialFit = defaultDropModeIsCover ? (modifierPressed ? 'contain' : 'cover') : (modifierPressed ? 'cover' : 'contain');

                // Check for reused images on thumbnail drops
                const reusedImages = droppedImageFiles.filter(img => usedImageIds.includes(img.id));
                if (reusedImages.length > 0 && isImageTrayDrop) {
                  setDuplicateWarningData({
                    images: droppedImageFiles,
                    targetIndex: index,
                    initialFit,
                    sourceSpreadId,
                    sourcePlaceholderId,
                    dropType: 'existingSpread',
                    targetSpreadId: spread.id
                  });
                  setShowDuplicateWarning(true);
                  return;
                }

                if (droppedImageFiles.length === 1) {
                  await onAddImageToExistingSpread(spread.id, droppedImageFiles[0], initialFit, sourceSpreadId, sourcePlaceholderId);
                  
                  if (sourceSpreadId && sourcePlaceholderIds.length > 0 && onRemoveImagesFromSource) {
                    await onRemoveImagesFromSource(sourceSpreadId, sourcePlaceholderIds);
                  }
                  
                  if (trackPreviewRequest) {
                    trackPreviewRequest(droppedImageFiles[0].originalPath, true);
                  }
                  toast.success(`Image "${droppedImageFiles[0].name}" added to spread ${index + 1}`);
                } else {
                  // Handle multiple images - check for autobuild
                  const existingImageCount = spread.images.filter(img => img.imageId).length;
                  const totalImages = existingImageCount + droppedImageFiles.length;
                  
                  // Get template capacity
                  const template = templateMap[spread.templateId];
                  const templateCapacity = template?.images?.length || 0;
                  
                  // Check if autobuild should be offered - only if total images exceed the largest available template
                  if (onOfferAutobuildRequest && maxTemplateSize && totalImages > maxTemplateSize) {
                    onOfferAutobuildRequest({
                      droppedImages: droppedImageFiles,
                      currentSpreadId: spread.id,
                      existingImageCount,
                      maxTemplateSize
                    });
                    
                    // Remove source images even when autobuild is triggered
                    if (sourceSpreadId && sourcePlaceholderIds.length > 0 && onRemoveImagesFromSource) {
                      await onRemoveImagesFromSource(sourceSpreadId, sourcePlaceholderIds);
                    }
                  } else {
                    // Add images normally if within capacity
                    for (let i = 0; i < droppedImageFiles.length; i++) {
                      const image = droppedImageFiles[i];
                      await onAddImageToExistingSpread(spread.id, image, initialFit, sourceSpreadId, sourcePlaceholderId);
                      if (trackPreviewRequest) {
                        trackPreviewRequest(image.originalPath, i === 0);
                      }
                    }
                    
                    if (sourceSpreadId && sourcePlaceholderIds.length > 0 && onRemoveImagesFromSource) {
                      await onRemoveImagesFromSource(sourceSpreadId, sourcePlaceholderIds);
                    }
                    
                    if (droppedImageFiles.length > 0) {
                      toast.success(`${droppedImageFiles.length} image(s) processed for spread ${index + 1}.`);
                    }
                  }
                }
              }
            } catch (error) {
              console.error("Error processing image drop on thumbnail:", error);
              toast.error("Failed to add image to spread.");
            }
          }
          setDragOverThumbnailTargetId(null);
        }}
        data-spread-id={spread.id}
      >
        {/* Thumbnail Preview Container */}
        <div className="w-full h-full bg-gray-50 relative overflow-hidden">
          <SpreadThumbnailPreview
            key={`${spread.id}-preview-${JSON.stringify(spread.images)}-${spread.spreadBackgroundData?.imagePath}-${defaultSpreadBackground?.imagePath}`}
            spread={spread}
            templateMap={templateMap}
            allImages={allImages}
            thumbnailSizes={thumbnailSizes}
            spreadDimensionsPt={spreadDimensionsPt}
            spreadBackgroundImage={spreadBackgroundImage}
            projectBackgroundImagePath={projectBackgroundImagePath}
            projectBackgroundImageOpacity={projectBackgroundImageOpacity}
            canvasBackgroundColor={canvasBackgroundColor}
            defaultSpreadBackground={defaultSpreadBackground}
            imageGap={imageGap}
            projectBackgroundImageZoom={projectBackgroundImageZoom}
            projectBackgroundImagePanX={projectBackgroundImagePanX}
            projectBackgroundImagePanY={projectBackgroundImagePanY}
            projectBackgroundImageNaturalWidth={projectBackgroundImageNaturalWidth}
            projectBackgroundImageNaturalHeight={projectBackgroundImageNaturalHeight}
            bleedValue={bleedValue}
            imageBorderSize={imageBorderSize}
            imageBorderColor={imageBorderColor}
            imageRefreshTimestamps={imageRefreshTimestamps}
            theme={theme}
            customTemplates={customTemplates}
          />

          {/* Spread Number Overlay */}
          <div className="absolute bottom-0.5 left-0.5 bg-black bg-opacity-70 text-white text-[9px] px-1.5 py-0.5 rounded-tr-md pointer-events-none z-10">
            {index + 1}
          </div>

          {/* Drag indicator */}
          <Move className="absolute top-1 left-1 w-3 h-3 text-gray-400 opacity-50 filter drop-shadow(0 1px 1px rgba(0,0,0,0.5)) pointer-events-none" />
          
          {/* Highlight for canvas image drop */}
          {dragOverThumbnailTargetId === spread.id && (
            <div className="absolute inset-0 bg-blue-500 bg-opacity-30 ring-2 ring-blue-600 rounded-md pointer-events-none transition-all duration-150 z-20"></div>
          )}
        </div>
      </div>
    );

    // Conditionally wrap with ContextMenu only if deletable
    const canDelete = (spreads.length > 1 && onDeleteMultipleSpreads) || (spreads.length >= 1 && onDeleteAllSpreadsAndAddBlank);
    if (canDelete) {
      return (
        <ContextMenu>
          <ContextMenuTrigger asChild>
            {thumbnailDiv}
          </ContextMenuTrigger>
          <ContextMenuContent>
            <ContextMenuItem
              className="text-red-600 focus:bg-red-50 focus:text-red-700"
              onSelect={() => handleDeleteRequest(spread.id)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              {isSelected && selectedSpreadIds.size > 1
                ? `Delete ${selectedSpreadIds.size} Selected Spreads`
                : `Delete Spread ${index + 1}`
              }
            </ContextMenuItem>
          </ContextMenuContent>
        </ContextMenu>
      );
    } else {
      return thumbnailDiv;
    }
  });

  // Define the component inside the SpreadsTray component to have access to thumbnailSizes
  // This is a local component definition, not exported
  const SpreadThumbnailPreview = React.memo(({ spread, templateMap, allImages, thumbnailSizes, spreadDimensionsPt, spreadBackgroundImage: projectGlobalBackgroundImage, projectBackgroundImagePath, projectBackgroundImageOpacity, canvasBackgroundColor, defaultSpreadBackground, imageGap = 0, projectBackgroundImageZoom, projectBackgroundImagePanX, projectBackgroundImagePanY, projectBackgroundImageNaturalWidth, projectBackgroundImageNaturalHeight, bleedValue, imageBorderSize, imageBorderColor, imageRefreshTimestamps, theme, customTemplates = {} }: SpreadThumbnailPreviewProps) => {
    const template = templateMap[spread.templateId];
    
    // Use custom template if available for this spread
    const customTemplate = customTemplates[spread.id];
    const effectiveTemplate = customTemplate ? { ...template, images: customTemplate } : template;

    const pageWidthPt = spreadDimensionsPt?.width || 1;
    const pageHeightPt = spreadDimensionsPt?.height || 1;
    // Calculate thumbnailPixelsPerPoint: Use the width of a single page in the thumbnail content area
    // thumbContentActualWidthPx represents the full spread width in the thumbnail.
    // So, for a single page, it's thumbContentActualWidthPx / 2.
    // However, thumbnailSizes.width is already the width of ONE page in the thumbnail container.
    // And spreadDimensionsPt.width is ONE page width in points.
    const thumbnailPixelsPerPoint = pageWidthPt > 0 ? thumbnailSizes.width / pageWidthPt : 1;

    const getSpreadBackgroundStyles = (
      individualSpreadBgData: SpreadBackgroundData | null | undefined,
      projectBgImageUrl: string | null | undefined, // This is the project-wide background URL
      defaultSpreadBgData: SpreadBackgroundData | null | undefined, // New: Default spread background data
      canvasBgColor: string | undefined,
      thumbContentActualWidthPx: number,
      thumbContentActualHeightPx: number,
      projBgZoom?: number,
      projBgPanX?: number,
      projBgPanY?: number,
      projBgOriginalPath?: string | null,
      localRefreshTimestamps?: Record<string, number>,
      allImagesForLookup?: ImageFile[],
      projBgNaturalWidth?: number,
      projBgNaturalHeight?: number
    ): {
      colorStyle: React.CSSProperties;
      projectImageStyle?: React.CSSProperties; // For project-wide background
      activeSpreadImageStyle?: React.CSSProperties; // For individual or default spread background
    } => {
      const colorStyle: React.CSSProperties = {
        backgroundColor: canvasBgColor || '#f9fafb',
      };
      let projectImageStyle: React.CSSProperties | undefined = undefined;
      let activeSpreadImageStyle: React.CSSProperties | undefined = undefined;
  
      const applyTransformAndCacheBust = (
        basePath: string,
        transform: { scale: number; focalX: number; focalY: number; fit?: 'cover' | 'contain' },
        naturalWidth: number,
        naturalHeight: number,
        opacityValue: number | undefined,
        originalPathForCacheBust: string | null | undefined, // The non-busted original path of the image
        typeSuffix: string // e.g., 'proj', 'indiv', 'default'
      ) => {
        let finalImageUrl = normalizeBackgroundImageUrl(basePath); // basePath is already 1st level busted
  
        if (originalPathForCacheBust && localRefreshTimestamps && allImagesForLookup) {
          const imageFile = allImagesForLookup.find(img => img.originalPath === originalPathForCacheBust);
          if (imageFile && localRefreshTimestamps[imageFile.id]) {
            const trayCacheBuster = `?t_tray_${typeSuffix}=${localRefreshTimestamps[imageFile.id]}`;
            finalImageUrl = finalImageUrl.includes('?') ? `${finalImageUrl}&${trayCacheBuster.substring(1)}` : `${finalImageUrl}${trayCacheBuster}`;
          }
        }
  
        const { scale = 1, focalX = 0.5, focalY = 0.5 } = transform;
        const imageRatio = naturalWidth / naturalHeight;
        const containerRatio = thumbContentActualWidthPx / thumbContentActualHeightPx;
        let baseRenderedW: number, baseRenderedH: number;
  
        if (imageRatio > containerRatio) {
          baseRenderedH = thumbContentActualHeightPx;
          baseRenderedW = thumbContentActualHeightPx * imageRatio;
        } else {
          baseRenderedW = thumbContentActualWidthPx;
          baseRenderedH = thumbContentActualWidthPx / imageRatio;
        }
  
        const scaledW_px = baseRenderedW * scale;
        const scaledH_px = baseRenderedH * scale;
        const offsetX_px = (thumbContentActualWidthPx / 2) - (scaledW_px * focalX);
        const offsetY_px = (thumbContentActualHeightPx / 2) - (scaledH_px * focalY);
  
        return {
          position: 'absolute',
          inset: 0,
          backgroundImage: `url("${finalImageUrl}")`,
          backgroundSize: `${scaledW_px}px ${scaledH_px}px`,
          backgroundPosition: `${offsetX_px}px ${offsetY_px}px`,
          backgroundRepeat: 'no-repeat',
          opacity: opacityValue ?? 1,
          pointerEvents: 'none',
        } as React.CSSProperties;
      };
  
      // 1. Project-wide background (lowest layer if activeSpreadImageStyle is also present)
      if (projectBgImageUrl && projBgNaturalWidth && projBgNaturalHeight && thumbContentActualWidthPx > 0 && thumbContentActualHeightPx > 0) {
        projectImageStyle = applyTransformAndCacheBust(
          projectBgImageUrl,
          { scale: projBgZoom ?? 1, focalX: (projBgPanX ?? 50) / 100, focalY: (projBgPanY ?? 50) / 100, fit: 'cover' },
          projBgNaturalWidth,
          projBgNaturalHeight,
          projectBackgroundImageOpacity,
          projBgOriginalPath, // Original path for project background
          'proj'
        );
      } else if (projectBgImageUrl) { // Fallback for project BG if natural dimensions are missing
          let finalBgImage = normalizeBackgroundImageUrl(projectBgImageUrl);
          if (projBgOriginalPath && localRefreshTimestamps && allImagesForLookup) {
              const projectBgImageFile = allImagesForLookup.find(img => img.originalPath === projBgOriginalPath);
              if (projectBgImageFile && localRefreshTimestamps[projectBgImageFile.id]) {
                  const trayCacheBuster = `?t_tray_proj=${localRefreshTimestamps[projectBgImageFile.id]}`;
                  finalBgImage = finalBgImage.includes('?') ? `${finalBgImage}&${trayCacheBuster.substring(1)}` : `${finalBgImage}${trayCacheBuster}`;
              }
          }
          projectImageStyle = {
              position: 'absolute', inset: 0, backgroundImage: `url("${finalBgImage}")`,
              backgroundSize: 'cover', backgroundPosition: 'center', backgroundRepeat: 'no-repeat',
              opacity: projectBackgroundImageOpacity !== undefined ? projectBackgroundImageOpacity : 1,
              pointerEvents: 'none',
          };
      }
  
  
      // 2. Determine active spread background (individual or default)
      let activeBgDataToUse: SpreadBackgroundData | null | undefined = individualSpreadBgData;
      let activeBgTypeSuffix = 'indiv';
  
      if (!activeBgDataToUse?.imagePath && defaultSpreadBgData?.imagePath) {
        activeBgDataToUse = defaultSpreadBgData;
        activeBgTypeSuffix = 'default';
      }
  
      if (activeBgDataToUse?.imagePath && activeBgDataToUse.naturalWidth && activeBgDataToUse.naturalHeight && thumbContentActualWidthPx > 0 && thumbContentActualHeightPx > 0) {
        // To get the *original* path for cache-busting, we need to find the corresponding ImageFile
        // The activeBgDataToUse.imagePath is already cache-busted by BookEditor.
        const normalizedActiveBgPath = normalizeBackgroundImageUrl(activeBgDataToUse.imagePath);
        const basePathForLookup = normalizedActiveBgPath ? normalizedActiveBgPath.split('?')[0] : null;
        
        const originalImageFile = allImagesForLookup?.find(img => {
          const normalizedImgPreviewUrl = normalizeBackgroundImageUrl(img.previewUrl);
          return normalizedImgPreviewUrl ? normalizedImgPreviewUrl.split('?')[0] === basePathForLookup : false;
        });
  
        activeSpreadImageStyle = applyTransformAndCacheBust(
          activeBgDataToUse.imagePath, // This is already 1st-level cache-busted
          activeBgDataToUse.transform || { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'cover' },
          activeBgDataToUse.naturalWidth,
          activeBgDataToUse.naturalHeight,
          activeBgDataToUse.opacity,
          originalImageFile?.originalPath, // Pass the true original path for 2nd-level cache bust lookup
          activeBgTypeSuffix
        );
      }
      return { colorStyle, projectImageStyle, activeSpreadImageStyle };
    };
 
    const adjustedPlaceholdersData = useMemo(() => {
      if (!effectiveTemplate || !spreadDimensionsPt || spreadDimensionsPt.width === 0 || spreadDimensionsPt.height === 0) {
        // If template is null (e.g. __blank__ or error) or dimensions are invalid, return empty array.
        // For __blank__, template.images would be empty anyway.
        return [];
      }
      
      // Check if this is a custom template
      if (customTemplate) {
        // Use custom gap adjustment function that preserves positioning
        return adjustCustomTemplateForGap(
          customTemplate, // Use the custom layout directly
          imageGap, // imageGap with multiplied values
          spreadDimensionsPt.width * 2, // spreadWidthPt for full spread
          spreadDimensionsPt.height    // spreadHeightPt
        );
      } else {
        // Use standard gap adjustment for unaltered templates
        return adjustTemplateForGap(
          effectiveTemplate, // Pass full template object for custom gap handling
          imageGap, // imageGap with multiplied values
          spreadDimensionsPt.width * 2, // spreadWidthPt for full spread
          spreadDimensionsPt.height    // spreadHeightPt
        );
      }
    }, [effectiveTemplate, customTemplate, imageGap, spreadDimensionsPt, adjustCustomTemplateForGap]);

    // Handle blank spreads specifically (template will be null or template.images empty)
    if (spread.templateId === '__blank__' || !effectiveTemplate) {
      return (
        <div 
          className="w-full h-full bg-gray-50 flex items-center justify-center" 
          style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
        >
          <ImagePlus className="w-6 h-6 text-gray-400" />
        </div>
      );
    }

    // Handle case where a non-blank template ID is missing (error state)
    if (!effectiveTemplate) {
      return <span className="text-xs text-red-500 absolute inset-0 flex items-center justify-center" title={`Error: Template '${spread.templateId}' not found`}>!</span>;
    }

    // Calculate letterboxing dimensions
    const contentAspectRatio = thumbnailSizes.contentAspectRatio;
    const containerAspectRatio = thumbnailSizes.containerAspectRatio;
    
    // Calculate letterbox dimensions when content is wider than container
    const letterboxStyle: React.CSSProperties = {};
    let thumbContentActualWidthPx = thumbnailSizes.width; // Default to full thumbnail width
    let thumbContentActualHeightPx = thumbnailSizes.height; // Default to full thumbnail height

    if (contentAspectRatio > containerAspectRatio) {
      // Content is wider than container - letterbox top/bottom
      thumbContentActualHeightPx = thumbnailSizes.width / contentAspectRatio;
      const verticalPadding = (thumbnailSizes.height - thumbContentActualHeightPx) / 2;
      letterboxStyle.marginTop = `${verticalPadding}px`;
      letterboxStyle.marginBottom = `${verticalPadding}px`;
      letterboxStyle.height = `${thumbContentActualHeightPx}px`;
      letterboxStyle.width = '100%'; // Takes full width of the thumbnail box
      // thumbContentActualWidthPx remains thumbnailSizes.width
    } else if (contentAspectRatio < containerAspectRatio) {
      // Content is taller than container - pillarbox left/right
      thumbContentActualWidthPx = thumbnailSizes.height * contentAspectRatio;
      const horizontalPadding = (thumbnailSizes.width - thumbContentActualWidthPx) / 2;
      letterboxStyle.marginLeft = `${horizontalPadding}px`;
      letterboxStyle.marginRight = `${horizontalPadding}px`;
      letterboxStyle.width = `${thumbContentActualWidthPx}px`;
      letterboxStyle.height = '100%'; // Takes full height of the thumbnail box
      // thumbContentActualHeightPx remains thumbnailSizes.height
    } else {
      // Aspect ratios are the same, no letter/pillar boxing needed for the content div itself
      letterboxStyle.width = '100%';
      letterboxStyle.height = '100%';
      // thumbContentActualWidthPx and thumbContentActualHeightPx are already thumbnailSizes.width/height
    }

    const { colorStyle: backgroundDivColorStyle, projectImageStyle, activeSpreadImageStyle } = getSpreadBackgroundStyles(
      spread.spreadBackgroundData,
      projectGlobalBackgroundImage,
      defaultSpreadBackground, // Pass the default spread background data
      canvasBackgroundColor,
      thumbContentActualWidthPx,
      thumbContentActualHeightPx,
      projectBackgroundImageZoom,
      projectBackgroundImagePanX,
      projectBackgroundImagePanY,
      projectBackgroundImagePath, // Pass original path for cache busting
      imageRefreshTimestamps,     // Pass timestamps for cache busting
      allImages,                  // Pass allImages for lookup for cache busting
      projectBackgroundImageNaturalWidth,
      projectBackgroundImageNaturalHeight
    );

    // --- Render Logic ---
    // Priority 1: Display the generated snapshot if available
    if (spread.thumbnailDataUrl) {
      return (
        <div className="w-full h-full bg-gray-50 flex items-center justify-center"> {/* Changed bg-white to bg-gray-50 */}
          <img
            src={spread.thumbnailDataUrl}
            alt={`Spread ${spread.id} thumbnail`}
            className="w-full h-full object-contain pointer-events-none" // Use contain to show the whole snapshot
            loading="lazy" // Lazy load snapshot images
            style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
          />
        </div>
      );
    }

    // Priority 2: Fallback to rendering placeholders if no snapshot
    return (
      // Add letterboxing container with gray background to match template placeholders
      <div className="w-full h-full flex items-center justify-center" style={{ backgroundColor: theme === 'dark' ? '#171717' : '#f9fafb' }}>
        {/* Actual content container with letterboxing margins */}
        <div
          style={{
            ...letterboxStyle, // Applies margins for centering
            position: 'relative'
          }}
          className="relative" // Ensures placeholders are positioned relative to this div
        >
          {/* White background layer for opacity calculations - dark mode only */}
          {theme === 'dark' && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: '#ffffff',
                zIndex: 0
              }}
            />
          )}
          
          {/* Render Project Background Image (Bottom Layer) */}
          {projectImageStyle && <div style={{...projectImageStyle, zIndex: 1}} />}
          {/* Render Active Spread Background Image (Individual or Default) */}
          {activeSpreadImageStyle && <div style={{...activeSpreadImageStyle, zIndex: 2}} />}
          
          {/* Project background color layer */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              ...backgroundDivColorStyle, // Apply project background color
              zIndex: 0.5
            }}
          />

          {adjustedPlaceholdersData.map((adjPlaceholder, placeholderIndex) => {
            const spreadImage = spread.images.find(img => img.placeholderId === adjPlaceholder.id);
            const imageFile = spreadImage?.imageId ? allImages.find(img => img.id === spreadImage.imageId) : null;
            
            // Get transform data, default if missing (use focal points)
            const transform = spreadImage?.transform ?? { scale: 1, focalX: 0.5, focalY: 0.5, fit: 'contain' };
            // Extract rotation for wrapper div
            const rotation = transform.rotation || 0;
            // const fitMode = transform.fit ?? 'contain'; // fitMode not directly used for placeholderStyle with new logic

            const placeholderStyle: React.CSSProperties = {
              position: 'absolute',
              overflow: 'hidden',
              left: `${(adjPlaceholder.adjustedX_pt / (spreadDimensionsPt.width * 2)) * 100}%`,
              top: `${(adjPlaceholder.adjustedY_pt / spreadDimensionsPt.height) * 100}%`,
              width: `${(adjPlaceholder.adjustedWidth_pt / (spreadDimensionsPt.width * 2)) * 100}%`,
              height: `${(adjPlaceholder.adjustedHeight_pt / spreadDimensionsPt.height) * 100}%`,
              backgroundColor: imageFile ? 'transparent' : '#f8f8f8',
              zIndex: 3, // Ensure all foreground images appear above backgrounds
            };

            const imageStyleObject = (() => {
              // Determine the pixel dimensions of the placeholder container within the thumbnail
              // thumbContentActualWidthPx and thumbContentActualHeightPx are the pixel dimensions of the letterboxed content area (which represents the full spread)
              
              // Calculate the pixel width and height of the current placeholder div
              const placeholderContainerW_px = (adjPlaceholder.adjustedWidth_pt / (spreadDimensionsPt.width * 2)) * thumbContentActualWidthPx;
              const placeholderContainerH_px = (adjPlaceholder.adjustedHeight_pt / spreadDimensionsPt.height) * thumbContentActualHeightPx;

              const imageNativeDims = imageFile ? { width: imageFile.naturalWidth ?? 0, height: imageFile.naturalHeight ?? 0 } : undefined;

              if (!imageFile || !imageNativeDims || placeholderContainerW_px <= 0 || placeholderContainerH_px <= 0 || imageNativeDims.width <= 0 || imageNativeDims.height <= 0) {
                return { type: 'fallback' as const, style: { position: 'absolute' as 'absolute', width: '100%', height: '100%', top: '0', left: '0', objectFit: 'contain' as 'contain' } };
              }

              const { scale = 1, focalX = 0.5, focalY = 0.5, fit = 'contain', rotation = 0 } = transform;
              const containerW = placeholderContainerW_px;
              const containerH = placeholderContainerH_px;
              const imageW = imageNativeDims.width; // This will be 0 if naturalWidth was undefined
              const imageH = imageNativeDims.height; // This will be 0 if naturalHeight was undefined
              const imageRatio = imageW / imageH;
              const containerRatio = containerW / containerH;

              let baseRenderedW: number;
              let baseRenderedH: number;

              if (fit === 'contain') {
                if (imageRatio > containerRatio) {
                  baseRenderedW = containerW;
                  baseRenderedH = containerW / imageRatio;
                } else {
                  baseRenderedH = containerH;
                  baseRenderedW = containerH * imageRatio;
                }
              } else { // fit === 'cover'
                if (imageRatio > containerRatio) {
                  baseRenderedH = containerH;
                  baseRenderedW = containerH * imageRatio;
                } else {
                  baseRenderedW = containerW;
                  baseRenderedH = containerW / imageRatio;
                }
              }

              const scaledW = baseRenderedW * scale;
              const scaledH = baseRenderedH * scale;

              const focalPointXOnImage = scaledW * focalX;
              const focalPointYOnImage = scaledH * focalY;

              const containerCenterX = containerW / 2;
              const containerCenterY = containerH / 2;

              const offsetX = containerCenterX - focalPointXOnImage;
              const offsetY = containerCenterY - focalPointYOnImage;

              return {
                type: 'direct' as const,
                style: {
                  position: 'absolute' as 'absolute',
                  width: `${scaledW}px`,
                  height: `${scaledH}px`,
                  left: `${offsetX}px`,
                  top: `${offsetY}px`,
                }
              };
            })();

            // Border rendering logic starts here, after imageStyleObject is defined
            const borderElements: JSX.Element[] = [];

            // Determine effective border settings
            const individualBorderSettings = spreadImage?.individualBorder;
            const useIndividualBorder = individualBorderSettings?.enabled === true;

            const effectiveBorderEnabled = useIndividualBorder || (imageBorderSize !== undefined && imageBorderSize > 0);
            const effectiveBorderSize = useIndividualBorder ? individualBorderSettings.size : imageBorderSize;
            const effectiveBorderColor = useIndividualBorder ? individualBorderSettings.color : imageBorderColor;

            if (imageFile && effectiveBorderEnabled && effectiveBorderSize && effectiveBorderColor && thumbnailPixelsPerPoint && spreadDimensionsPt && bleedValue !== undefined) {
              // Scale border thickness for thumbnails to match visual proportion of SpreadCanvas
              const borderThicknessThumbnailPx = effectiveBorderSize * thumbnailPixelsPerPoint * 0.5;
              if (borderThicknessThumbnailPx > 0) {
                const { style: imgStyleCalculated } = imageStyleObject; // imageStyleObject is now in scope

                // Placeholder dimensions in pixels within the thumbnail's content area
                // thumbContentActualWidthPx and thumbContentActualHeightPx are from the outer scope
                const placeholderContainerW_px = (adjPlaceholder.adjustedWidth_pt / (pageWidthPt * 2)) * thumbContentActualWidthPx;
                const placeholderContainerH_px = (adjPlaceholder.adjustedHeight_pt / pageHeightPt) * thumbContentActualHeightPx;

                // Common Edge Checks (Placeholder vs. Page)
                const placeholderFullSpreadX_pt = adjPlaceholder.adjustedX_pt;
                const placeholderFullSpreadY_pt = adjPlaceholder.adjustedY_pt;
                const edgeTolerancePt = 2.0;
                const bleedIsActive = bleedValue > 0;

                const isTopEdgeOutside = placeholderFullSpreadY_pt < edgeTolerancePt;
                const isBottomEdgeOutside = (placeholderFullSpreadY_pt + adjPlaceholder.adjustedHeight_pt) >= (pageHeightPt - edgeTolerancePt);
                
                // Corrected Left/Right Edge Detection for two-page spreads
                let isLeftEdgeOutside: boolean;
                let isRightEdgeOutside: boolean;

                if (placeholderFullSpreadX_pt + adjPlaceholder.adjustedWidth_pt <= pageWidthPt) { // Entirely on left page or exactly at gutter
                  isLeftEdgeOutside = placeholderFullSpreadX_pt < edgeTolerancePt;
                  isRightEdgeOutside = (placeholderFullSpreadX_pt + adjPlaceholder.adjustedWidth_pt) >= (pageWidthPt - edgeTolerancePt);
                } else if (placeholderFullSpreadX_pt >= pageWidthPt) { // Entirely on right page
                  isLeftEdgeOutside = (placeholderFullSpreadX_pt - pageWidthPt) < edgeTolerancePt;
                  isRightEdgeOutside = ((placeholderFullSpreadX_pt - pageWidthPt) + adjPlaceholder.adjustedWidth_pt) >= (pageWidthPt - edgeTolerancePt);
                } else { // Spans the gutter
                  isLeftEdgeOutside = placeholderFullSpreadX_pt < edgeTolerancePt; // Check left edge against start of spread
                  isRightEdgeOutside = (placeholderFullSpreadX_pt + adjPlaceholder.adjustedWidth_pt) >= (pageWidthPt * 2 - edgeTolerancePt); // Check right edge against end of spread
                  // For spanning images, the "inner" edges near the gutter are not considered "outside" for border hiding.
                  // The existing logic will show borders unless the *outermost* edges of the placeholder are at the page edge AND bleed is active.
                }

                let showTopBorder = true, showBottomBorder = true, showLeftBorder = true, showRightBorder = true;

                if (transform.fit === 'contain') {
                  const offsetX_thumb = parseFloat(imgStyleCalculated.left || '0');
                  const offsetY_thumb = parseFloat(imgStyleCalculated.top || '0');
                  const scaledW_thumb = parseFloat(imgStyleCalculated.width || '0');
                  const scaledH_thumb = parseFloat(imgStyleCalculated.height || '0');

                  const imageEdgeTolerancePx_thumb = thumbnailPixelsPerPoint * 2.0;
                  const gapLeftImageToPlaceholderPx_thumb = offsetX_thumb;
                  const gapRightImageToPlaceholderPx_thumb = placeholderContainerW_px - (offsetX_thumb + scaledW_thumb);
                  const gapTopImageToPlaceholderPx_thumb = offsetY_thumb;
                  const gapBottomImageToPlaceholderPx_thumb = placeholderContainerH_px - (offsetY_thumb + scaledH_thumb);

                  const isImageNearTopPlaceholderEdge = gapTopImageToPlaceholderPx_thumb < imageEdgeTolerancePx_thumb;
                  const isImageNearBottomPlaceholderEdge = gapBottomImageToPlaceholderPx_thumb < imageEdgeTolerancePx_thumb;
                  const isImageNearLeftPlaceholderEdge = gapLeftImageToPlaceholderPx_thumb < imageEdgeTolerancePx_thumb;
                  const isImageNearRightPlaceholderEdge = gapRightImageToPlaceholderPx_thumb < imageEdgeTolerancePx_thumb;

                  // Hybrid border calculation: each edge switches immediately from image-edge to placeholder-edge
                  
                  // Determine if each edge should stick to placeholder (immediate switch, no transition)
                  const transitionThreshold_thumb = 5 * thumbnailPixelsPerPoint / 3; // Scale threshold to thumbnail size
                  // Special handling: only stick to placeholder when image edge is actually AT or BEYOND placeholder edge
                  const topStuckToPlaceholder_thumb = gapTopImageToPlaceholderPx_thumb <= 0; // Image extends above or touches placeholder top
                  const bottomStuckToPlaceholder_thumb = gapBottomImageToPlaceholderPx_thumb <= 0; // Image extends below or touches placeholder bottom
                  const leftStuckToPlaceholder_thumb = gapLeftImageToPlaceholderPx_thumb <= 0; // Image extends left or touches placeholder left
                  const rightStuckToPlaceholder_thumb = gapRightImageToPlaceholderPx_thumb <= 0; // Image extends right or touches placeholder right

                  // For transition calculations (0 = around image, 1 = stuck to placeholder)
                  const topTransition_thumb = topStuckToPlaceholder_thumb ? 1 : 0;
                  const bottomTransition_thumb = bottomStuckToPlaceholder_thumb ? 1 : 0;
                  const leftTransition_thumb = leftStuckToPlaceholder_thumb ? 1 : 0;
                  const rightTransition_thumb = rightStuckToPlaceholder_thumb ? 1 : 0;

                  // Border visibility still respects bleed logic
                  showTopBorder = !(isTopEdgeOutside && topStuckToPlaceholder_thumb && bleedIsActive);
                  showBottomBorder = !(isBottomEdgeOutside && bottomStuckToPlaceholder_thumb && bleedIsActive);
                  showLeftBorder = !(isLeftEdgeOutside && leftStuckToPlaceholder_thumb && bleedIsActive);
                  showRightBorder = !(isRightEdgeOutside && rightStuckToPlaceholder_thumb && bleedIsActive);

                  // Calculate effective image bounds within placeholder
                  const imageLeft_thumb = Math.max(0, offsetX_thumb);
                  const imageRight_thumb = Math.min(placeholderContainerW_px, offsetX_thumb + scaledW_thumb);
                  const imageTop_thumb = Math.max(0, offsetY_thumb);
                  const imageBottom_thumb = Math.min(placeholderContainerH_px, offsetY_thumb + scaledH_thumb);
                  const effectiveImageWidth_thumb = imageRight_thumb - imageLeft_thumb;
                  const effectiveImageHeight_thumb = imageBottom_thumb - imageTop_thumb;

                  const finalBorderThicknessPx = Math.max(1, Math.round(borderThicknessThumbnailPx));

                  // Calculate border positions and dimensions for each edge
                  
                  // Top border: position transitions, width respects image bounds
                  const topBorderTop_thumb = offsetY_thumb * (1 - topTransition_thumb) + 0 * topTransition_thumb;
                  const topBorderLeft_thumb = offsetX_thumb * (1 - topTransition_thumb) + imageLeft_thumb * topTransition_thumb;
                  const topBorderWidth_thumb = scaledW_thumb * (1 - topTransition_thumb) + effectiveImageWidth_thumb * topTransition_thumb;
                  
                  // Bottom border: position transitions, width respects image bounds
                  const bottomBorderTop_thumb = (offsetY_thumb + scaledH_thumb - finalBorderThicknessPx) * (1 - bottomTransition_thumb) + (placeholderContainerH_px - finalBorderThicknessPx) * bottomTransition_thumb;
                  const bottomBorderLeft_thumb = offsetX_thumb * (1 - bottomTransition_thumb) + imageLeft_thumb * bottomTransition_thumb;
                  const bottomBorderWidth_thumb = scaledW_thumb * (1 - bottomTransition_thumb) + effectiveImageWidth_thumb * bottomTransition_thumb;
                  
                  // Left border: position transitions, height respects image bounds and other borders
                  const leftBorderTop_thumb = (offsetY_thumb + (showTopBorder ? finalBorderThicknessPx : 0)) * (1 - leftTransition_thumb) + 
                                            (imageTop_thumb + (showTopBorder ? finalBorderThicknessPx : 0)) * leftTransition_thumb;
                  const leftBorderLeft_thumb = offsetX_thumb * (1 - leftTransition_thumb) + 0 * leftTransition_thumb;
                  const leftBorderHeight_thumb = (scaledH_thumb - (showTopBorder ? finalBorderThicknessPx : 0) - (showBottomBorder ? finalBorderThicknessPx : 0)) * (1 - leftTransition_thumb) + 
                                                (effectiveImageHeight_thumb - (showTopBorder ? finalBorderThicknessPx : 0) - (showBottomBorder ? finalBorderThicknessPx : 0)) * leftTransition_thumb;
                  
                  // Right border: position transitions, height respects image bounds and other borders
                  const rightBorderTop_thumb = (offsetY_thumb + (showTopBorder ? finalBorderThicknessPx : 0)) * (1 - rightTransition_thumb) + 
                                               (imageTop_thumb + (showTopBorder ? finalBorderThicknessPx : 0)) * rightTransition_thumb;
                  const rightBorderLeft_thumb = (offsetX_thumb + scaledW_thumb - finalBorderThicknessPx) * (1 - rightTransition_thumb) + 
                                               (placeholderContainerW_px - finalBorderThicknessPx) * rightTransition_thumb;
                  const rightBorderHeight_thumb = (scaledH_thumb - (showTopBorder ? finalBorderThicknessPx : 0) - (showBottomBorder ? finalBorderThicknessPx : 0)) * (1 - rightTransition_thumb) + 
                                                  (effectiveImageHeight_thumb - (showTopBorder ? finalBorderThicknessPx : 0) - (showBottomBorder ? finalBorderThicknessPx : 0)) * rightTransition_thumb;

                  // Render individual divs for 'contain' mode with hybrid positioning
                  if (showTopBorder) borderElements.push(<div key="top" style={{ position: 'absolute', top: `${topBorderTop_thumb}px`, left: `${topBorderLeft_thumb}px`, width: `${topBorderWidth_thumb}px`, height: `${finalBorderThicknessPx}px`, backgroundColor: effectiveBorderColor, zIndex: 5, pointerEvents: 'none' }} />);
                  if (showBottomBorder) borderElements.push(<div key="bottom" style={{ position: 'absolute', top: `${bottomBorderTop_thumb}px`, left: `${bottomBorderLeft_thumb}px`, width: `${bottomBorderWidth_thumb}px`, height: `${finalBorderThicknessPx}px`, backgroundColor: effectiveBorderColor, zIndex: 5, pointerEvents: 'none' }} />);
                  if (showLeftBorder) borderElements.push(<div key="left" style={{ position: 'absolute', top: `${leftBorderTop_thumb}px`, left: `${leftBorderLeft_thumb}px`, width: `${finalBorderThicknessPx}px`, height: `${leftBorderHeight_thumb}px`, backgroundColor: effectiveBorderColor, zIndex: 5, pointerEvents: 'none' }} />);
                  if (showRightBorder) borderElements.push(<div key="right" style={{ position: 'absolute', top: `${rightBorderTop_thumb}px`, left: `${rightBorderLeft_thumb}px`, width: `${finalBorderThicknessPx}px`, height: `${rightBorderHeight_thumb}px`, backgroundColor: effectiveBorderColor, zIndex: 5, pointerEvents: 'none' }} />);

                } else { // fit === 'cover'
                  showTopBorder = !(isTopEdgeOutside && bleedIsActive);
                  showBottomBorder = !(isBottomEdgeOutside && bleedIsActive);
                  showLeftBorder = !(isLeftEdgeOutside && bleedIsActive);
                  showRightBorder = !(isRightEdgeOutside && bleedIsActive);

                  const topBorderEffectiveSizePx = showTopBorder ? Math.max(1, Math.round(borderThicknessThumbnailPx)) : 0;
                  const bottomBorderEffectiveSizePx = showBottomBorder ? Math.max(1, Math.round(borderThicknessThumbnailPx)) : 0;
                  const finalBorderThicknessPx = Math.max(1, Math.round(borderThicknessThumbnailPx));

                  if (showTopBorder) borderElements.push(<div key="top" style={{ position: 'absolute', top: '0px', left: '0px', width: '100%', height: `${finalBorderThicknessPx}px`, backgroundColor: effectiveBorderColor, zIndex: 5, pointerEvents: 'none' }} />);
                  if (showBottomBorder) borderElements.push(<div key="bottom" style={{ position: 'absolute', bottom: '0px', left: '0px', width: '100%', height: `${finalBorderThicknessPx}px`, backgroundColor: effectiveBorderColor, zIndex: 5, pointerEvents: 'none' }} />);
                  if (showLeftBorder) borderElements.push(<div key="left" style={{ position: 'absolute', top: `${topBorderEffectiveSizePx}px`, left: '0px', width: `${finalBorderThicknessPx}px`, height: `calc(100% - ${topBorderEffectiveSizePx}px - ${bottomBorderEffectiveSizePx}px)`, backgroundColor: effectiveBorderColor, zIndex: 5, pointerEvents: 'none' }} />);
                  if (showRightBorder) borderElements.push(<div key="right" style={{ position: 'absolute', top: `${topBorderEffectiveSizePx}px`, right: '0px', width: `${finalBorderThicknessPx}px`, height: `calc(100% - ${topBorderEffectiveSizePx}px - ${bottomBorderEffectiveSizePx}px)`, backgroundColor: effectiveBorderColor, zIndex: 5, pointerEvents: 'none' }} />);
                }
              }
            }

            return (
              <div key={`${spread.id}-${adjPlaceholder.id}-container`} style={placeholderStyle}>
                <div style={{ width: '100%', height: '100%', overflow: 'hidden', position: 'relative' }}>
                  {imageFile ? (() => {
                    // For contain mode with borders and rotation, create border-aware clipping
                    const hasRotation = (rotation || 0) !== 0;
                    const hasBorders = effectiveBorderEnabled && transform.fit === 'contain';
                    
                    if (hasRotation && hasBorders && effectiveBorderSize && effectiveBorderColor && thumbnailPixelsPerPoint) {
                      // Calculate the same border values as in the border rendering logic
                      const borderThicknessThumbnailPx = effectiveBorderSize * thumbnailPixelsPerPoint * 0.5;
                      
                      if (borderThicknessThumbnailPx > 0) {
                        const { style: imgStyleCalculated } = imageStyleObject;
                        
                        // Calculate placeholder container dimensions (same as border calculation logic)
                        // Check that required variables are available
                        if (!adjPlaceholder || !pageWidthPt || !pageHeightPt || !thumbContentActualWidthPx || !thumbContentActualHeightPx) {
                          console.error('Missing required variables for border clipping:', {
                            adjPlaceholder: !!adjPlaceholder,
                            pageWidthPt,
                            pageHeightPt,
                            thumbContentActualWidthPx,
                            thumbContentActualHeightPx
                          });
                          return null;
                        }
                        
                        const placeholderContainerW_px = (adjPlaceholder.adjustedWidth_pt / (pageWidthPt * 2)) * thumbContentActualWidthPx;
                        const placeholderContainerH_px = (adjPlaceholder.adjustedHeight_pt / pageHeightPt) * thumbContentActualHeightPx;
                        
                        const offsetX_thumb = parseFloat(imgStyleCalculated.left || '0');
                        const offsetY_thumb = parseFloat(imgStyleCalculated.top || '0');
                        const scaledW_thumb = parseFloat(imgStyleCalculated.width || '0');
                        const scaledH_thumb = parseFloat(imgStyleCalculated.height || '0');

                        const gapLeftImageToPlaceholderPx_thumb = offsetX_thumb;
                        const gapRightImageToPlaceholderPx_thumb = placeholderContainerW_px - (offsetX_thumb + scaledW_thumb);
                        const gapTopImageToPlaceholderPx_thumb = offsetY_thumb;
                        const gapBottomImageToPlaceholderPx_thumb = placeholderContainerH_px - (offsetY_thumb + scaledH_thumb);

                        const topStuckToPlaceholder_thumb = gapTopImageToPlaceholderPx_thumb <= 0;
                        const bottomStuckToPlaceholder_thumb = gapBottomImageToPlaceholderPx_thumb <= 0;
                        const leftStuckToPlaceholder_thumb = gapLeftImageToPlaceholderPx_thumb <= 0;
                        const rightStuckToPlaceholder_thumb = gapRightImageToPlaceholderPx_thumb <= 0;

                        const topTransition_thumb = topStuckToPlaceholder_thumb ? 1 : 0;
                        const bottomTransition_thumb = bottomStuckToPlaceholder_thumb ? 1 : 0;
                        const leftTransition_thumb = leftStuckToPlaceholder_thumb ? 1 : 0;
                        const rightTransition_thumb = rightStuckToPlaceholder_thumb ? 1 : 0;

                        // Calculate clip boundaries at OUTSIDE border edges (border position + border thickness)
                        const clipTop = (offsetY_thumb * (1 - topTransition_thumb) + 0 * topTransition_thumb);
                        const clipLeft = (offsetX_thumb * (1 - leftTransition_thumb) + 0 * leftTransition_thumb);
                        const clipRight = ((offsetX_thumb + scaledW_thumb) * (1 - rightTransition_thumb) + placeholderContainerW_px * rightTransition_thumb);
                        const clipBottom = ((offsetY_thumb + scaledH_thumb) * (1 - bottomTransition_thumb) + placeholderContainerH_px * bottomTransition_thumb);
                        
                        const clipWidth = clipRight - clipLeft;
                        const clipHeight = clipBottom - clipTop;

                        return (
                          <div
                            style={{
                              position: 'absolute',
                              top: `${clipTop}px`,
                              left: `${clipLeft}px`,
                              width: `${clipWidth}px`,
                              height: `${clipHeight}px`,
                              overflow: 'hidden',
                              pointerEvents: 'none'
                            }}
                          >
                            <div
                              style={{ 
                                position: 'absolute', 
                                width: `${placeholderContainerW_px}px`, 
                                height: `${placeholderContainerH_px}px`,
                                left: `${-clipLeft}px`,
                                top: `${-clipTop}px`,
                                pointerEvents: 'none',
                                transform: `rotate(${rotation}deg)`,
                                transformOrigin: `${placeholderContainerW_px/2}px ${placeholderContainerH_px/2}px`
                              }}
                            >
                              <img
                                key={`${spread.id}-${adjPlaceholder.id}-img`}
                                src={`${imageFile.thumbnailUrl}${imageFile.id && imageRefreshTimestamps[imageFile.id] ? `?t=${imageRefreshTimestamps[imageFile.id]}` : ''}`}
                                alt={`Placeholder ${adjPlaceholder.id}`}
                                draggable="false"
                                style={{
                                  ...imageStyleObject.style,
                                  filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none'
                                }}
                                className={`block pointer-events-none ${
                                  imageStyleObject.type === 'direct'
                                    ? 'max-w-none max-h-none'
                                    : 'w-full h-full object-contain'
                                }`}
                              />
                            </div>
                          </div>
                        );
                      }
                    }
                    
                    // Fallback for no rotation or no borders - use original structure
                    return (
                      <div
                        style={{ 
                          position: 'absolute', 
                          width: '100%', 
                          height: '100%', 
                          pointerEvents: 'none',
                          transform: `rotate(${rotation}deg)`,
                          transformOrigin: 'center center',
                        }}
                      >
                        <img
                          key={`${spread.id}-${adjPlaceholder.id}-img`}
                          src={`${imageFile.thumbnailUrl}${imageFile.id && imageRefreshTimestamps[imageFile.id] ? `?t=${imageRefreshTimestamps[imageFile.id]}` : ''}`}
                          alt={`Placeholder ${adjPlaceholder.id}`}
                          draggable="false"
                          style={imageStyleObject.style}
                          className={`block pointer-events-none ${
                            imageStyleObject.type === 'direct'
                              ? 'max-w-none max-h-none'
                              : 'w-full h-full object-contain'
                          }`}
                        />
                      </div>
                    );
                  })() : null}
                  {borderElements /* Render border elements here */}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  });
  
  // Use the component now defined within the scope that has access to thumbnailSizes
  return (
    <DndContext
      sensors={sensors}
      collisionDetection={pointerWithin}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div className="relative">
      {/* Background layer - inverted in dark mode */}
      <div
        className="bg-white border-t border-gray-200 outline-none absolute inset-0"
        style={{
          height: isFocusMode ? '0px' : `${trayHeight}px`,
          transition: "height 0.3s ease",
          overflow: isFocusMode ? 'hidden' : 'hidden',
          filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none',
          zIndex: 0
        }}
      />
      
      {/* Content layer - thumbnails NOT inverted */}
      <div
        ref={ref}
        className="outline-none relative flex flex-col overflow-hidden"
        style={{
          height: isFocusMode ? '0px' : `${trayHeight}px`,
          transition: "height 0.3s ease",
          overflow: isFocusMode ? 'hidden' : 'hidden',
          zIndex: 1
        }}
        tabIndex={0}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        onMouseDown={(e) => {
          // Use mousedown instead of click to bypass click consumption
          
          // Only handle left clicks
          if (e.button !== 0) return;
          
          // Find which thumbnail was clicked
          const target = e.target as HTMLElement;
          const thumbnailElement = target.closest('[data-spread-id]');
          
          if (thumbnailElement) {
            const spreadId = thumbnailElement.getAttribute('data-spread-id');
            const index = spreads.findIndex(s => s.id === spreadId);
            
            if (spreadId && index !== -1) {
              // Create a synthetic event that matches the expected interface
              const syntheticEvent = {
                ...e,
                button: e.button,
                metaKey: e.metaKey,
                ctrlKey: e.ctrlKey,
                shiftKey: e.shiftKey,
              } as React.MouseEvent<HTMLDivElement>;
              
              handleSpreadClick(syntheticEvent, spreadId, index);
              
              // Prevent event from bubbling and causing blur
              e.stopPropagation();
              
              if (ref && 'current' in ref && ref.current) {
                ref.current.focus({preventScroll: true});
                
                // Force focus with a small delay to ensure it sticks
                setTimeout(() => {
                  if (ref && 'current' in ref && ref.current) {
                    ref.current.focus({preventScroll: true});
                  }
                }, 10);
              }
            }
          }
        }}
      >
      <div className="px-4 py-1.5 flex items-center justify-between border-b border-transparent flex-shrink-0">
        <div className="flex items-center gap-1.5">
          <Book className="w-3.5 h-3.5 text-gray-500" />
          <span 
            className="font-medium text-sm"
            style={{ color: theme === 'dark' ? '#808080' : undefined }}
          >
            Spreads
          </span>
        </div>
      </div>
      
      {/* Add a spacer div between header and content for extra spacing */}
      <div className="h-2 flex-shrink-0"></div>
      
      
      {/* Virtualized scrollable container with @dnd-kit */}
      <div
        ref={scrollElementRef}
        className="flex-1 overflow-x-auto overflow-y-hidden"
        style={{
          minHeight: 0,
          paddingTop: '4px',
          paddingBottom: '4px',
          paddingLeft: '15px',
          ...(theme === 'dark' ? {
            filter: 'invert(1)',
          } : {}),
        }}
      >
        {/* Counter-invert content to preserve original colors */}
        <div 
          className=""
          style={theme === 'dark' ? { filter: 'invert(1)' } : {}}
        >
          <SortableContext items={spreads.map(s => s.id)} strategy={horizontalListSortingStrategy}>
            <div
              style={{
                height: thumbnailSizes.height,
                width: virtualizer.getTotalSize() + thumbnailSizes.width + thumbnailSizes.gap, // Add space for add button
                position: 'relative',
                paddingLeft: `${thumbnailSizes.gap / 2}px`, // Left edge padding using gap
                paddingRight: `${thumbnailSizes.gap / 2}px`, // Right edge padding using gap
              }}
              onDragOver={(e) => {
                // Handle image drops for creating new spreads
                e.preventDefault();
                const isImageTrayDrag = e.dataTransfer.types.includes('application/json');
                const isCanvasImageDrag = e.dataTransfer.types.includes('application/x-bookproofs-canvas-image');
                const isCanvasMultiImageDrag = e.dataTransfer.types.includes('application/x-bookproofs-canvas-images');
                
                if (isImageTrayDrag || isCanvasImageDrag || isCanvasMultiImageDrag) {
                  e.dataTransfer.dropEffect = 'copy';
                  setIsDraggingImagesOver(true);
                  
                  // Calculate drop position for new spread creation using virtual items
                  const scrollElement = scrollElementRef.current;
                  if (scrollElement) {
                    const rect = e.currentTarget.getBoundingClientRect();
                    const clientX = e.clientX;
                    const containerLeft = rect.left + (thumbnailSizes.gap / 2); // Account for padding
                    const relativeX = clientX - containerLeft;
                    
                    // Use virtual items to find the correct drop position
                    const virtualItems = virtualizer.getVirtualItems();
                    let dropIndex = 0;
                    
                    for (let i = 0; i < virtualItems.length; i++) {
                      const item = virtualItems[i];
                      const itemCenter = item.start + thumbnailSizes.width / 2;
                      
                      if (relativeX < itemCenter) {
                        dropIndex = item.index;
                        break;
                      }
                      dropIndex = item.index + 1;
                    }
                    
                    // Clamp to valid range
                    dropIndex = Math.max(0, Math.min(spreads.length, dropIndex));
                    setDropIndicatorIndex(dropIndex);
                  }
                }
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                setIsDraggingImagesOver(false);
                setDropIndicatorIndex(null);
              }}
              onDrop={async (e) => {
                // Handle image drops for creating new spreads
                e.preventDefault();
                const isImageTrayDrop = e.dataTransfer.types.includes('application/json');
                const isCanvasImageDrop = e.dataTransfer.types.includes('application/x-bookproofs-canvas-image');
                const isCanvasMultiImageDrop = e.dataTransfer.types.includes('application/x-bookproofs-canvas-images');

                if ((isImageTrayDrop || isCanvasImageDrop || isCanvasMultiImageDrop) && onAddSpreadFromImages) {
                  try {
                    let dataType: string;
                    let jsonData: string;
                    let parsedData: any;
                    
                    if (isImageTrayDrop) {
                      dataType = 'application/json';
                    } else if (isCanvasMultiImageDrop) {
                      dataType = 'application/x-bookproofs-canvas-images';
                    } else {
                      dataType = 'application/x-bookproofs-canvas-image';
                    }
                    
                    jsonData = e.dataTransfer.getData(dataType);
                    parsedData = JSON.parse(jsonData);
                    
                    let droppedImageFiles: ImageFile[] = [];
                    let sourceSpreadId: string | undefined = undefined;
                    let sourcePlaceholderId: string | undefined = undefined;

                    if (isImageTrayDrop) {
                      droppedImageFiles = Array.isArray(parsedData) ?
                        parsedData.filter(item => item && item.id) as ImageFile[] :
                        (parsedData && parsedData.id ? [parsedData as ImageFile] : []);
                    } else if (isCanvasMultiImageDrop) {
                      if (Array.isArray(parsedData)) {
                        droppedImageFiles = parsedData.map(({ sourceSpreadId: sId, sourcePlaceholderId: pId, ...imgData }) => imgData as ImageFile).filter(img => img && img.id);
                        if (parsedData.length > 0) {
                          sourceSpreadId = parsedData[0].sourceSpreadId;
                          sourcePlaceholderId = parsedData[0].sourcePlaceholderId;
                        }
                      }
                    } else if (isCanvasImageDrop) {
                      const { sourceSpreadId: sId, sourcePlaceholderId: pId, ...imgData } = parsedData as ImageFile & { sourceSpreadId?: string, sourcePlaceholderId?: string };
                      if (imgData && imgData.id) {
                        droppedImageFiles = [imgData as ImageFile];
                        sourceSpreadId = sId;
                        sourcePlaceholderId = pId;
                      }
                    }

                    if (droppedImageFiles.length > 0) {
                      const modifierPressed = e.shiftKey;
                      const initialFit = defaultDropModeIsCover ? (modifierPressed ? 'contain' : 'cover') : (modifierPressed ? 'cover' : 'contain');
                      const targetIndex = dropIndicatorIndex || spreads.length;

                      await onAddSpreadFromImages(droppedImageFiles, targetIndex, initialFit, sourceSpreadId, sourcePlaceholderId);
                      
                      // Remove images from source spread if they came from canvas
                      if (sourceSpreadId && (isCanvasImageDrop || isCanvasMultiImageDrop) && onRemoveImagesFromSource) {
                        // For multi-image drops, collect all source placeholder IDs
                        const sourcePlaceholderIds: string[] = [];
                        if (isCanvasMultiImageDrop && Array.isArray(parsedData)) {
                          sourcePlaceholderIds.push(...parsedData.map(item => item.sourcePlaceholderId).filter(Boolean));
                        } else if (isCanvasImageDrop && sourcePlaceholderId) {
                          sourcePlaceholderIds.push(sourcePlaceholderId);
                        }
                        
                        if (sourcePlaceholderIds.length > 0) {
                          await onRemoveImagesFromSource(sourceSpreadId, sourcePlaceholderIds);
                        }
                      }
                      
                      toast.success(`New spread created with ${droppedImageFiles.length} image(s)`);
                    }
                  } catch (error) {
                    console.error("Error creating spread from images:", error);
                    toast.error("Failed to create spread from images.");
                  }
                }
                setIsDraggingImagesOver(false);
                setDropIndicatorIndex(null);
              }}
            >
              {/* Drop indicator for @dnd-kit spread reorder and image drops */}
              {dropIndicatorIndex !== null && (isDraggingSpread || isDraggingImagesOver) && (() => {
                // Calculate position based on virtual items for accurate positioning
                const virtualItems = virtualizer.getVirtualItems();
                let indicatorLeft = thumbnailSizes.gap / 2; // Default left padding using gap
                
                if (dropIndicatorIndex === 0) {
                  // Insert at the beginning - position at the left edge
                  indicatorLeft = thumbnailSizes.gap / 2;
                } else if (dropIndicatorIndex >= spreads.length) {
                  // Insert at the end - position after the last item
                  const lastVirtualItem = virtualItems.find(item => item.index === spreads.length - 1);
                  if (lastVirtualItem) {
                    indicatorLeft = lastVirtualItem.start + thumbnailSizes.width + (thumbnailSizes.gap / 2);
                  } else {
                    // Fallback if virtual item not found
                    indicatorLeft = (thumbnailSizes.gap / 2) + spreads.length * (thumbnailSizes.width + thumbnailSizes.gap);
                  }
                } else {
                  // Insert between items - position before the target index
                  const targetVirtualItem = virtualItems.find(item => item.index === dropIndicatorIndex);
                  if (targetVirtualItem) {
                    indicatorLeft = targetVirtualItem.start - (thumbnailSizes.gap / 2);
                  } else {
                    // Fallback calculation if virtual item not found
                    indicatorLeft = (thumbnailSizes.gap / 2) + dropIndicatorIndex * (thumbnailSizes.width + thumbnailSizes.gap) - (thumbnailSizes.gap / 2);
                  }
                }
                
                return (
                  <div 
                    style={{
                      position: 'absolute',
                      left: `${indicatorLeft}px`,
                      top: '4px',
                      bottom: '4px',
                      width: '2px',
                      backgroundColor: 'rgba(96, 165, 250, 0.7)',
                      borderRadius: '1px',
                      zIndex: 40
                    }}
                  />
                );
              })()}

              {/* Virtualized spread items */}
              {virtualizer.getVirtualItems().map((virtualItem) => {
                const spread = spreads[virtualItem.index];
                const isActive = currentSpreadId === spread.id;
                const isSelected = selectedSpreadIds.has(spread.id);
                const isDragging = activeSpreadId === spread.id;

                return (
                  <VirtualizedSpreadItem
                    key={spread.id}
                    spread={spread}
                    index={virtualItem.index}
                    virtualItem={virtualItem}
                    isActive={isActive}
                    isSelected={isSelected}
                    isDragging={isDragging}
                  />
                );
              })}

              {/* Add new spread button */}
              <button
                className="rounded-md border border-gray-200 border-dashed flex items-center justify-center hover:bg-gray-50"
                style={{ 
                  position: 'absolute',
                  left: virtualizer.getTotalSize(),
                  top: 0,
                  width: `${thumbnailSizes.width}px`,
                  height: `${thumbnailSizes.height}px`
                }}
                onClick={onAddSpread}
                title="Add new spread"
              >
                <Plus className="w-5 h-5 text-gray-400" />
              </button>
            </div>
          </SortableContext>
        </div>
      </div>

      {/* Single AlertDialog instance controlled by state for deletion */}
      <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
        <AlertDialogContent onKeyDown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            confirmDelete(); // Call confirm delete on Enter
          }
        }}>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {spreadsToDelete.length >= spreads.length && onDeleteAllSpreadsAndAddBlank
                ? 'Delete All Spreads?'
                : spreadsToDelete.length > 1
                  ? `Delete ${spreadsToDelete.length} Spreads?`
                  : 'Delete Spread?'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {spreadsToDelete.length >= spreads.length && onDeleteAllSpreadsAndAddBlank
                ? 'This will delete all spreads and add a new blank spread.'
                : `This will delete the selected ${spreadsToDelete.length > 1 ? `${spreadsToDelete.length} spreads` : '1 spread'}.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSpreadsToDelete([])}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Duplicate Image Warning Dialog */}
      <AlertDialog open={showDuplicateWarning} onOpenChange={setShowDuplicateWarning}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Image Already Used</AlertDialogTitle>
            <AlertDialogDescription>
              {duplicateWarningData && (
                <>
                  {duplicateWarningData.images.length === 1 ? (
                    <>
                      The image "{duplicateWarningData.images[0].name}" has already been used in this project.
                    </>
                  ) : (
                    <>
                      {duplicateWarningData.images.filter(img => usedImageIds.includes(img.id)).length} of the {duplicateWarningData.images.length} images you're trying to add have already been used in this project.
                    </>
                  )}
                  <br /><br />
                  Would you like to use {duplicateWarningData.images.length === 1 ? 'it' : 'them'} again? This will create duplicate placements of the same {duplicateWarningData.images.length === 1 ? 'image' : 'images'}.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDuplicateCancel}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDuplicateConfirm}>
              Use Again
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      </div>
    </div>

      {/* DragOverlay for visual feedback during drag */}
      <DragOverlay>
        {activeSpreadId ? (() => {
          const activeSpread = spreads.find(s => s.id === activeSpreadId);
          if (!activeSpread) return null;
          
          return (
            <div 
              className="rounded-md border border-gray-300 bg-white opacity-90 shadow-lg overflow-hidden"
              style={{
                width: `${thumbnailSizes.width}px`,
                height: `${thumbnailSizes.height}px`,
              }}
            >
              <SpreadThumbnailPreview
                spread={activeSpread}
                templateMap={templateMap}
                allImages={allImages}
                thumbnailSizes={thumbnailSizes}
                spreadDimensionsPt={spreadDimensionsPt}
                spreadBackgroundImage={spreadBackgroundImage}
                projectBackgroundImagePath={projectBackgroundImagePath}
                projectBackgroundImageOpacity={projectBackgroundImageOpacity}
                canvasBackgroundColor={canvasBackgroundColor}
                defaultSpreadBackground={defaultSpreadBackground}
                imageGap={imageGap}
                projectBackgroundImageZoom={projectBackgroundImageZoom}
                projectBackgroundImagePanX={projectBackgroundImagePanX}
                projectBackgroundImagePanY={projectBackgroundImagePanY}
                projectBackgroundImageNaturalWidth={projectBackgroundImageNaturalWidth}
                projectBackgroundImageNaturalHeight={projectBackgroundImageNaturalHeight}
                bleedValue={bleedValue}
                imageBorderSize={imageBorderSize}
                imageBorderColor={imageBorderColor}
                imageRefreshTimestamps={imageRefreshTimestamps}
                theme={theme}
                customTemplates={customTemplates}
              />
            </div>
          );
        })() : null}
      </DragOverlay>
    </DndContext>
  );
});
SpreadsTray.displayName = "SpreadsTray"; // Add display name for DevTools

export default React.memo(SpreadsTray);
