import React, { KeyboardEvent } from 'react';
import { Slider } from '@/components/ui/slider';
import { SlidersHorizontal } from 'lucide-react';
import { Label } from '@/components/ui/label';

interface ImageGapControlProps {
  value: number;
  onChange: (value: number) => void;
}

const ImageGapControl = ({ value, onChange }: ImageGapControlProps) => {
  const handleSliderChange = (newValue: number[]) => {
    onChange(newValue[0]);
  };

  // Only prevent default behavior for arrow keys without stopping propagation
  // This allows SpreadsTray keyboard listeners to still work
  const handleKeyDown = (e: KeyboardEvent) => {
    if (
      e.key === 'ArrowUp' || 
      e.key === 'ArrowDown' || 
      e.key === 'ArrowLeft' || 
      e.key === 'ArrowRight'
    ) {
      e.preventDefault(); // Prevent slider value from changing
      // Do NOT call e.stopPropagation() - this allows events to bubble up to SpreadsTray
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <SlidersHorizontal className="h-5 w-5 text-gray-500" />
          <Label className="text-base font-medium">Image Gap</Label>
        </div>
        <span className="text-sm font-medium text-gray-500">{value}pt</span>
      </div>
      
      <Slider
        defaultValue={[value]}
        max={20}
        min={0}
        step={1}
        onValueChange={handleSliderChange}
        onKeyDown={handleKeyDown}
      />
      
      <p className="text-sm text-gray-500">
        Adjust the image gap between images (0pt = no gap, 20pt = maximum gap)
      </p>
    </div>
  );
};

export default React.memo(ImageGapControl);
