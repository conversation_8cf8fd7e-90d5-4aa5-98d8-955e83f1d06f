import React from 'react';
import { AlertTriangle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ImageQualityInfo, ImageQualityStatus } from '@/lib/imageQuality';

export interface WarningSpreadDetail {
  spreadId: string;
  displayIndex: number; // The actual 1-based display index
  issues: Record<string, ImageQualityInfo>; // placeholderId -> issue
}

interface ImageQualityExportWarningProps {
  isOpen: boolean;
  onClose: () => void;
  onProceed: () => void;
  warningDetails: WarningSpreadDetail[];
}

/**
 * Dialog component that shows image quality warnings during PDF export
 */
const ImageQualityExportWarning: React.FC<ImageQualityExportWarningProps> = ({
  isOpen,
  onClose,
  onProceed,
  warningDetails
}) => {
  // Count total issues
  const totalIssues = warningDetails.reduce(
    (count, detail) => count + Object.keys(detail.issues).length,
    0
  );

  // Count severe issues (poor quality)
  const severeIssues = warningDetails.reduce(
    (count, detail) => {
      return count + Object.values(detail.issues).filter(
        issue => issue.status === ImageQualityStatus.POOR
      ).length;
    },
    0
  );

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          onClose();
        }
      }}
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Image Quality Warning
          </DialogTitle>
          <DialogDescription>
            {totalIssues} {totalIssues === 1 ? 'image' : 'images'} in your book may not print well at 300 DPI.
          </DialogDescription>
        </DialogHeader>
        
        <div className="max-h-60 overflow-y-auto my-4 pr-2">
          <div className="space-y-4">
            {warningDetails.map((detail) => (
              <div key={detail.spreadId} className="border rounded-md p-3">
                <h3 className="font-medium text-sm mb-2">Spread {detail.displayIndex}</h3>
                <ul className="space-y-2 text-sm">
                  {Object.values(detail.issues).map((issue) => (
                    <li key={issue.placeholderId} className="flex items-start gap-2">
                      <AlertTriangle
                        className={`h-4 w-4 mt-0.5 ${issue.status === ImageQualityStatus.POOR ? 'text-red-500' : 'text-amber-500'}`}
                      />
                      <span>{issue.message}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={onClose} className="sm:w-auto w-full">
            Review Images
          </Button>
          <Button
            onClick={onProceed}
            className={`sm:w-auto w-full ${severeIssues > 0 ? 'bg-red-600 hover:bg-red-700' : ''}`}
          >
            Proceed with Export
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ImageQualityExportWarning;
