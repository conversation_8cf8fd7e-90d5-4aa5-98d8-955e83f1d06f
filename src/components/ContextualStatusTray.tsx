import React from 'react';
import ContextualStatusBar from './ContextualStatusBar';

interface ContextualStatusTrayProps {
  isMultiSelectActive?: boolean;
  imageSelectionCount?: number;
  isSpreadThumbnailHovered?: boolean;
  theme?: 'light' | 'dark';
}

const ContextualStatusTray: React.FC<ContextualStatusTrayProps> = ({
  isMultiSelectActive = false,
  imageSelectionCount = 0,
  isSpreadThumbnailHovered = false,
  theme = 'light'
}) => {
  return (
    <div 
      className="bg-white border-t border-r border-gray-200 flex flex-col"
      style={{ 
        height: '65px', // Fixed height as specified
      }}
    >
      <div className="flex-1 flex items-center overflow-hidden">
        <div className="w-full h-full flex items-center">
          <div className="w-full h-full flex items-center [&>div]:!min-h-0 [&>div]:!h-full [&>div]:border-t-0">
            <ContextualStatusBar
              isMultiSelectActive={isMultiSelectActive}
              imageSelectionCount={imageSelectionCount}
              isSpreadThumbnailHovered={isSpreadThumbnailHovered}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContextualStatusTray;