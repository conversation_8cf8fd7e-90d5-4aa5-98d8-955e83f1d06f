import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import AspectRatioCard from './AspectRatioCard';
import CustomSizeInput from './CustomSizeInput';
import bookproofsLogo from '/BookProofs-WI.png';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { vendorOptions } from '@/lib/vendorOptions';

interface BookSetupProps {
  theme: 'light' | 'dark';
  onComplete: (aspectRatio: { id: string; title: string; ratio: string; dimensions: string; bleed?: string; safetyMargin?: string }) => void;
  onBack: () => void;
}

const BookSetup = ({ theme, onComplete, onBack }: BookSetupProps) => {
  const [selectedVendor, setSelectedVendor] = useState<string>("Graphistudio");

  const aspectRatios = vendorOptions[selectedVendor];

  const [selectedRatio, setSelectedRatio] = useState<string | null>(null);
  const [customSize, setCustomSize] = useState<{ id: string; title: string; ratio: string; dimensions: string; bleed?: string; safetyMargin?: string } | null>(null);
  const [showClearMessage, setShowClearMessage] = useState<boolean>(false);

  const handleContinue = useCallback(() => {
    if (customSize) {
      onComplete(customSize);
    } else if (selectedRatio) {
      const selected = Object.values(aspectRatios)
        .flat()
        .find(ratio => ratio.id === selectedRatio);
      if (selected) {
        // For predefined sizes, pass the bleed and safetyMargin values from vendorOptions
        onComplete({ ...selected, bleed: selected.bleed || "0.125", safetyMargin: selected.safetyMargin || "0.6" }); // Default values if not specified
      }
    }
  }, [onComplete, customSize, selectedRatio, aspectRatios]);

  const handleCustomSizeCreate = (size: { id: string; title: string; ratio: string; dimensions: string; bleed: string; safetyMargin: string }) => {
    setCustomSize(size);
    setSelectedRatio(null);
  };

  const handlePredefinedSelect = (ratioId: string) => {
    setSelectedRatio(ratioId);
    
    // If there was a custom size, clear it and show the message
    if (customSize !== null) {
      setCustomSize(null);
      setShowClearMessage(true);
      
      // Hide the message after 3 seconds
      setTimeout(() => {
        setShowClearMessage(false);
      }, 3000);
    }
  };
  
  const handleClearCustomSize = useCallback(() => {
    setCustomSize(null);
    setSelectedRatio(null); // Ensure no preset is considered active either
  }, []);

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'Enter' && (selectedRatio || customSize)) {
        handleContinue();
      }
    };
    window.addEventListener('keydown', handleKeyPress);
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [selectedRatio, customSize, handleContinue]);

  const getSelectedRatioDetails = (id: string | null) => {
    if (!id) return null;
    return Object.values(aspectRatios).flat().find(r => r.id === id) || null;
  };

  const handleVendorChange = (vendor: string) => {
    setSelectedVendor(vendor);
    setSelectedRatio(null); // Reset selected ratio when vendor changes
    setCustomSize(null); // Reset custom size when vendor changes
  };


  return (
    <div 
      className="min-h-screen flex flex-col items-center justify-center p-6 animate-fade-in"
      style={theme === 'dark' ? { 
        filter: 'invert(1) hue-rotate(180deg)',
        backgroundColor: '#f3f4f6' // Light gray that will invert to dark
      } : { 
        backgroundColor: '#f3f4f6' // Standard light background
      }}
    >
      <div className="w-full max-w-4xl mx-auto relative">
      <Button variant="ghost" size="icon" onClick={onBack} className="absolute left-6 top-10">
        <ArrowLeft className="h-7 w-7" />
        <span className="sr-only">Back</span>
      </Button>
      
      <img src={bookproofsLogo} alt="Bookproofs Logo" className="mx-auto mb-2 h-16" />
      <h1 className="text-2xl font-bold mb-6 text-center">Design a New Book</h1>
      
      <div className="bg-white rounded-lg shadow-sm border border-app-border p-6 flex flex-col max-h-[calc(100vh-10rem)] overflow-hidden">
        
        <div className="mb-6">
          <label htmlFor="vendor-select" className="block text-md font-semibold mb-1">Select Vendor</label>
          <Select value={selectedVendor} onValueChange={handleVendorChange}>
            <SelectTrigger id="vendor-select" className="w-full">
              <SelectValue placeholder="Select a vendor" />
            </SelectTrigger>
            <SelectContent>
              {Object.keys(vendorOptions).map(vendorKey => (
                <SelectItem key={vendorKey} value={vendorKey}>
                  {vendorKey === 'Graphistudio' ? (
                    <div className="flex items-center gap-2">
                      <img 
                        src="/build/vendorlogo/gs.jpg" 
                        alt="Graphistudio logo" 
                        className="w-6 h-6 object-contain rounded"
                        style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}
                      />
                      <span>{vendorKey}</span>
                    </div>
                  ) : vendorKey === 'WHCC' ? (
                    <div className="flex items-center gap-2">
                      <img 
                        src="/build/vendorlogo/whcc.jpg" 
                        alt="WHCC logo" 
                        className="w-6 h-6 object-contain rounded"
                        style={theme === 'dark' ? { filter: 'invert(1) hue-rotate(180deg)' } : {}}
                      />
                      <span>{vendorKey}</span>
                    </div>
                  ) : (
                    vendorKey
                  )}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
              
        <div className="flex-1 overflow-y-auto min-h-0 my-4 pr-4 space-y-8">
          {/* Grid for Square, Horizontal, Vertical Sections */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Square Section */}
            <div>
              <div className="flex flex-col items-center pt-3">
                <Select
                  value={(selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-square-`) ? selectedRatio : "")}
                  onValueChange={(value) => {
                    if (value) handlePredefinedSelect(value);
                  }}
                  disabled={aspectRatios.square.length === 0}
                >
                  <SelectTrigger
                    className="w-full max-w-[150px] sm:max-w-[120px] md:max-w-[100px] lg:max-w-[150px] mx-auto h-auto p-0 border-transparent bg-transparent hover:bg-transparent focus:ring-0 focus:ring-offset-0 data-[state=open]:ring-0"
                  >
                    <AspectRatioCard
                      title={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-square-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.title || 'Square') : 'Square'
                      }
                      ratio={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-square-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.ratio || (aspectRatios.square.length > 0 ? aspectRatios.square[0].ratio : '1:1')) : (aspectRatios.square.length > 0 ? aspectRatios.square[0].ratio : '1:1')
                      }
                      dimensions={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-square-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.dimensions || '') : ''
                      }
                      displayText={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-square-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.title || 'Square') : 'Square'
                      }
                      selected={selectedRatio?.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-square-`) ?? false}
                      onClick={() => {}} // Handled by SelectTrigger
                      className={`w-full h-full pointer-events-none ${aspectRatios.square.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {aspectRatios.square.map((item) => (
                      <SelectItem key={item.id} value={item.id}>
                        <span className="text-sm font-medium">{item.title}</span>
                      </SelectItem>
                    ))}
                     {aspectRatios.square.length === 0 && <SelectItem value="disabled" disabled>No square options</SelectItem>}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Horizontal Section */}
            <div>
              <div className="flex flex-col items-center pt-3">
                <Select
                  value={(selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-horizontal-`) ? selectedRatio : "")}
                  onValueChange={(value) => {
                    if (value) handlePredefinedSelect(value);
                  }}
                  disabled={aspectRatios.horizontal.length === 0}
                >
                  <SelectTrigger
                    className="w-full max-w-[200px] sm:max-w-[160px] md:max-w-[130px] lg:max-w-[200px] mx-auto h-auto p-0 border-transparent bg-transparent hover:bg-transparent focus:ring-0 focus:ring-offset-0 data-[state=open]:ring-0"
                    style={{ aspectRatio: (aspectRatios.horizontal.length > 0 ? aspectRatios.horizontal[0].ratio : '4:3').replace(':', '/') }}
                  >
                    <AspectRatioCard
                      title={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-horizontal-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.title || 'Horizontal') : 'Horizontal'
                      }
                      ratio={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-horizontal-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.ratio || (aspectRatios.horizontal.length > 0 ? aspectRatios.horizontal[0].ratio : '4:3')) : (aspectRatios.horizontal.length > 0 ? aspectRatios.horizontal[0].ratio : '4:3')
                      }
                      dimensions={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-horizontal-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.dimensions || '') : ''
                      }
                      displayText={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-horizontal-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.title || 'Horizontal') : 'Horizontal'
                      }
                      selected={selectedRatio?.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-horizontal-`) ?? false}
                      onClick={() => {}} // Handled by SelectTrigger
                      className={`w-full h-full pointer-events-none ${aspectRatios.horizontal.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {aspectRatios.horizontal.map((item) => (
                      <SelectItem key={item.id} value={item.id}>
                        <span className="text-sm font-medium">{item.title}</span>
                      </SelectItem>
                    ))}
                    {aspectRatios.horizontal.length === 0 && <SelectItem value="disabled" disabled>No horizontal options</SelectItem>}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Vertical Section */}
            <div>
              <div className="flex flex-col items-center pt-3">
                <Select
                  value={(selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-vertical-`) ? selectedRatio : "")}
                  onValueChange={(value) => {
                    if (value) handlePredefinedSelect(value);
                  }}
                  disabled={aspectRatios.vertical.length === 0}
                >
                  <SelectTrigger
                    className="w-full max-w-[150px] sm:max-w-[120px] md:max-w-[100px] lg:max-w-[150px] mx-auto h-auto p-0 border-transparent bg-transparent hover:bg-transparent focus:ring-0 focus:ring-offset-0 data-[state=open]:ring-0"
                    style={{ aspectRatio: (aspectRatios.vertical.length > 0 ? aspectRatios.vertical[0].ratio : '3:4').replace(':', '/') }}
                  >
                    <AspectRatioCard
                      title={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-vertical-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.title || 'Vertical') : 'Vertical'
                      }
                      ratio={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-vertical-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.ratio || (aspectRatios.vertical.length > 0 ? aspectRatios.vertical[0].ratio : '3:4')) : (aspectRatios.vertical.length > 0 ? aspectRatios.vertical[0].ratio : '3:4')
                      }
                      dimensions={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-vertical-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.dimensions || '') : ''
                      }
                      displayText={
                        selectedRatio && selectedRatio.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-vertical-`) ?
                        (getSelectedRatioDetails(selectedRatio)?.title || 'Vertical') : 'Vertical'
                      }
                      selected={selectedRatio?.startsWith(`${selectedVendor.toLowerCase().charAt(0)}-vertical-`) ?? false}
                      onClick={() => {}} // Handled by SelectTrigger
                      className={`w-full h-full pointer-events-none ${aspectRatios.vertical.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {aspectRatios.vertical.map((item) => (
                      <SelectItem key={item.id} value={item.id}>
                        <span className="text-sm font-medium">{item.title}</span>
                      </SelectItem>
                    ))}
                    {aspectRatios.vertical.length === 0 && <SelectItem value="disabled" disabled>No vertical options</SelectItem>}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Custom Section - Placed below the grid */}
          <div className="pt-6 border-t border-app-border mt-8"> {/* Added padding-top, border-top and margin-top for separation */}
            <div className="flex flex-col mb-3">
              <div className="flex justify-between items-center">
                <h3 className="text-md font-semibold">Custom Format</h3>
                {showClearMessage && (
                  <div className="text-sm text-amber-600 animate-pulse">
                    Custom size cleared
                  </div>
                )}
              </div>
              {/* Removed text about preset format clearing custom dimensions */}
            </div>
            <CustomSizeInput
              onSizeCreate={handleCustomSizeCreate}
              isActive={customSize !== null}
              onClear={handleClearCustomSize}
            />
          </div>
        </div>
        
        <div className="flex justify-between flex-shrink-0 pt-4 mt-4 border-t border-app-border">
          <div className="text-sm text-muted-foreground">
            {(selectedRatio || customSize) ?
              "Format selected. Click Continue to proceed." :
              "Please select a format to continue."}
          </div>
          <Button
            onClick={handleContinue}
            disabled={!selectedRatio && !customSize}
            className=""
            size={(selectedRatio || customSize) ? "lg" : "default"}
          >
            {(selectedRatio || customSize) ? "Continue →" : "Continue"}
          </Button>
        </div>
      </div>
      </div>
    </div>
  );
};

export default BookSetup;
