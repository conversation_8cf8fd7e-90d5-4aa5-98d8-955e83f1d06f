import React, { useState } from 'react';
import { Checkbox } from './ui/checkbox';
import { Label } from './ui/label';
import { X as LucideX } from 'lucide-react'; // Import X icon for dismiss

interface OnboardingTooltipProps {
  show: boolean; // Controls visibility based on hover state from parent
  targetId: string; // To associate the tooltip with a specific placeholder (still useful for unique IDs)
  onDismiss: () => void; // Callback when tooltip is dismissed (either by 'X' or after checking "don't show")
  onSetDontShowAgainGlobally: () => void; // Callback when "Don't show again" is checked
}

const OnboardingTooltip: React.FC<OnboardingTooltipProps> = ({
  show,
  targetId,
  onDismiss,
  onSetDontShowAgainGlobally,
}) => {
  const [dontShowAgainChecked, setDontShowAgainChecked] = useState(false);

  const handleCheckboxChange = (checked: boolean | 'indeterminate') => {
    if (typeof checked === 'boolean') {
      setDontShowAgainChecked(checked); // Keep local state for checkbox visual
      if (checked) {
        onSetDontShowAgainGlobally(); // Signal to save this preference globally
        onDismiss(); // Then dismiss the tooltip
      }
      // If unchecked, we don't need to do anything with global settings here,
      // as the global setting is a one-way street (once set, it stays).
      // The parent will control if it should even attempt to show based on global settings.
    }
  };

  if (!show) {
    return null;
  }

  return (
    <div
      className="absolute top-2 left-1/2 -translate-x-1/2 p-3 bg-gray-800 text-white rounded-md shadow-lg z-50 w-64 text-sm"
      // Prevent mouse events on the tooltip itself from triggering mouseleave on the placeholder
      onMouseEnter={(e) => e.stopPropagation()}
      onMouseLeave={(e) => e.stopPropagation()}
    >
      <button
        onClick={onDismiss}
        className="absolute top-1 right-1 text-gray-400 hover:text-white p-1"
        aria-label="Dismiss tip"
      >
        <LucideX size={16} />
      </button>
      <p className="mb-2 pr-4"> {/* Added pr-4 to avoid text overlapping with X button */}
        Tip: Drag from the top of an image to move images between placeholders. Drag in the main area to pan when zoomed.
      </p>
      <div className="flex items-center space-x-2">
        <Checkbox
          id={`dont-show-onboarding-${targetId}`}
          checked={dontShowAgainChecked}
          onCheckedChange={handleCheckboxChange}
          className="border-white data-[state=checked]:bg-blue-500 data-[state=checked]:text-white"
        />
        <Label htmlFor={`dont-show-onboarding-${targetId}`} className="text-xs text-gray-300">
          Don’t show again
        </Label>
      </div>
    </div>
  );
};

export default OnboardingTooltip;