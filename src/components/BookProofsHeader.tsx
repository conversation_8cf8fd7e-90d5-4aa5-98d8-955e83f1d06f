import React, { useState, useEffect, useCallback } from 'react'; // Import hooks
import { Button } from '@/components/ui/button';
import {
  Save,
  FileText,
  Undo,
  Redo,
  Maximize, // Add Maximize icon
  Minimize, // Add Minimize icon
  FolderOpen, // Add FolderOpen icon for Load
  FilePlus, // Add FilePlus icon for New Project
  CloudUpload, // Add CloudUpload icon for Proof button
  FolderSymlink, // Add FolderSymlink icon for Export button
  RotateCcw // Add RotateCcw icon for Reset Trays button
} from 'lucide-react';
import bookproofsLogo from '/BookProofs-WI.png'; // Import the logo

interface BookProofsHeaderProps {
  currentSpreadNumber?: number;
  totalSpreads?: number;
  dimensions?: string;
  commonSize?: string; // Add common size prop
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  onExportPdf?: () => void; // Add prop for PDF export handler
  onSaveProject?: () => void; // Add prop for Save handler
  isDirty?: boolean; // Add prop to track unsaved changes
  onLoadProject?: () => void; // Add prop for Load handler
  onNewProject?: () => void; // Add prop for New Project handler
  onSaveProjectAs?: () => void; // Add prop for Save As handler (optional, might not be needed for a button)
  isCoverVisible?: boolean; // Add prop to indicate when cover is visible
  onProofUpload?: () => void; // Add prop for Proof Upload handler
  theme?: 'light' | 'dark'; // Add theme prop
  onResetTrays?: () => void; // Add prop for Reset Trays handler
}

const BookProofsHeader = ({
  currentSpreadNumber = 1,
  totalSpreads = 1,
  dimensions = "12×8″",
  commonSize,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
  onExportPdf, // Destructure the new prop
  onSaveProject, // Destructure save handler
  isDirty = false, // Destructure dirty state
  onLoadProject, // Destructure load handler
  onNewProject, // Destructure new project handler
  onSaveProjectAs, // Destructure Save As handler (even if not used directly by a button yet)
  isCoverVisible = false, // Destructure cover visibility with default false
  onProofUpload, // Destructure proof upload handler
  theme = 'light', // Destructure theme prop with default
  onResetTrays // Destructure reset trays handler
}: BookProofsHeaderProps) => {
  const [isNativeFullscreen, setIsNativeFullscreen] = useState(false); // State for native fullscreen
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup listener on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Fetch initial native fullscreen state
  useEffect(() => {
    const checkFullscreen = async () => {
      if (window.electronAPI) {
        const currentStatus = await window.electronAPI.isNativeFullScreen();
        setIsNativeFullscreen(currentStatus);
      }
    };
    checkFullscreen();
    // Optional: Add listener for fullscreen change events from main process if needed
  }, []);

  const handleToggleNativeFullscreen = useCallback(async () => {
    if (window.electronAPI) {
      const newState = !isNativeFullscreen;
      await window.electronAPI.setNativeFullScreen(newState);
      setIsNativeFullscreen(newState); // Update state after setting
    }
  }, [isNativeFullscreen]); // Dependency on current state
  const handleUndo = () => {
    if (onUndo) {
      onUndo();
    }
  };

  const handleRedo = () => {
    if (onRedo) {
      onRedo();
    }
  };

  return (
    <header className="bg-white border-b border-gray-200 py-2 px-4 flex items-center justify-between shadow-sm relative" style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}> {/* Added relative and theme style */}
      {/* Left section: Logo, New, Load, Save, Undo/Redo */}
      <div className="flex items-center gap-3"> {/* Adjusted gap for consistency */}
        <img src={bookproofsLogo} alt="Bookproofs Logo" className="h-8 mr-2" /> {/* Added logo */}
        {/* New Project Button */}
        <Button
          variant="outline"
          size="sm"
          className="text-sm focus:ring-0 focus-visible:ring-0 focus:ring-offset-0 focus-visible:outline-none"
          onClick={onNewProject}
          title="New Project"
        >
          <FilePlus className="h-4 w-4 mr-2" />
          New
        </Button>
        {/* Load Project Button */}
        <Button
          variant="outline"
          size="sm"
          className="text-sm focus:ring-0 focus-visible:ring-0 focus:ring-offset-0 focus-visible:outline-none"
          onClick={onLoadProject}
          title="Load Project"
        >
          <FolderOpen className="h-4 w-4 mr-2" />
          Load
        </Button>
        {/* Save Project Button */}
        <Button
          variant="outline"
          size="sm"
          className="text-sm focus:ring-0 focus-visible:ring-0 focus:ring-offset-0 focus-visible:outline-none"
          onClick={onSaveProject}
          disabled={!isDirty}
        >
          <Save className={`h-4 w-4 mr-2 ${!isDirty ? 'text-gray-400' : ''}`} />
          Save{isDirty ? '*' : ''}
        </Button>
        {/* Undo Button */}
        {windowWidth >= 1200 && (
          <Button
            variant="ghost"
            size="icon"
            className="w-8 h-8 hover:bg-transparent"
            onClick={handleUndo}
            disabled={!canUndo}
            title="Undo"
          >
            <Undo className={`h-5 w-5 ${!canUndo ? 'text-gray-400' : ''}`} />
          </Button>
        )}
        {/* Redo Button */}
        {windowWidth >= 1200 && (
          <Button
            variant="ghost"
            size="icon"
            className="w-8 h-8 hover:bg-transparent"
            onClick={handleRedo}
            disabled={!canRedo}
            title="Redo"
          >
            <Redo className={`h-5 w-5 ${!canRedo ? 'text-gray-400' : ''}`} />
          </Button>
        )}
      </div>

      {/* Spacer to push right content */}
      <div className="flex-1"></div>

      {/* Center section: Absolutely positioned */}
      {windowWidth >= 1200 && (
        <div 
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-sm text-gray-600 whitespace-nowrap"
          title={commonSize && dimensions ? `Actual Page Dimensions: ${dimensions}` : undefined}
        >
          {commonSize || dimensions} • {isCoverVisible ? "Cover" : `Spread ${currentSpreadNumber} of ${totalSpreads}`}
        </div>
      )}
      
      {/* Right section: Reset Trays, Fullscreen, Proof, Export */}
      <div className="flex items-center gap-3" style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}>
        {/* Reset Trays Button */}
        {onResetTrays && (
          <Button
            variant="ghost"
            size="icon"
            className="w-8 h-8 hover:bg-transparent focus:ring-0 focus-visible:ring-0 focus:ring-offset-0"
            onClick={onResetTrays}
            title="Reset all trays to default positions"
          >
            <RotateCcw className="h-5 w-5" />
          </Button>
        )}
        
        {/* Fullscreen Toggle Button */}
        <Button
          variant="ghost"
          size="icon"
          className="w-8 h-8 hover:bg-transparent focus:ring-0 focus-visible:ring-0 focus:ring-offset-0"
          onClick={handleToggleNativeFullscreen}
          title={isNativeFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
        >
          {isNativeFullscreen ? (
            <Minimize className="h-5 w-5" />
          ) : (
            <Maximize className="h-5 w-5" />
          )}
        </Button>

        {/* Proof Upload Button */}
        <Button
          variant="default"
          size="sm"
          className="text-sm text-white bg-gray-900 hover:bg-gray-800 focus:ring-0 focus-visible:ring-0 focus:ring-offset-0 focus-visible:outline-none"
          onClick={onProofUpload}
          title="Upload to Book Proofs Cloud Proofing"
        >
          <CloudUpload className="h-4 w-4 mr-2" />
          Proof
        </Button>
        
        <Button 
          variant="default" 
          size="sm"
          className="text-sm bg-gray-900 hover:bg-gray-800 focus:ring-0 focus-visible:ring-0 focus:ring-offset-0 focus-visible:outline-none"
          onClick={onExportPdf} // Add onClick handler
          title="Export Project"
        >
          <FolderSymlink className="h-4 w-4 mr-2" />
          Export
        </Button>
      </div>
    </header>
  );
};

export default React.memo(BookProofsHeader);
