import React, { useState, useEffect, useCallback, memo, useMemo, forwardRef, useImperativeHandle, useRef } from 'react';
import { Image, Upload, Filter, ArrowUpNarrowWide, ArrowDownWideNarrow, Star, ArrowDownAZ, ArrowUpAZ, FolderSearch, Pencil, Trash2, GripVertical, Clock, Eye, Loader2, CalendarArrowUp, CalendarArrowDown, ClockArrowUp, ClockArrowDown, SquareArrowOutDownRight, SquareArrowOutUpLeft, ChevronRight } from 'lucide-react'; // Updated icons
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import {
 Dialog,
 DialogContent,
 DialogHeader,
 DialogTitle,
 DialogDescription,
 DialogTrigger,
 DialogClose,
} from "@/components/ui/dialog"; // Import Dialog components
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"; // Import AlertDialog components
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'; // Import for Dialog accessibility
// import { ScrollArea } from '@/components/ui/scroll-area'; // Remove ScrollArea
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator, // Added Separator
  ContextMenuTrigger,
} from "@/components/ui/context-menu"; // Added Context Menu imports
import { Separator } from '@/components/ui/separator';
import { FixedSizeGrid } from 'react-window';
import useResizeObserver from 'use-resize-observer';
import '@/styles/resizing.css';
import { Progress } from '@/components/ui/progress'; // Added for drop progress
import { useUserSettings } from '@/hooks/useUserSettings'; // Import useUserSettings hook
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Base size for thumbnails when slider is at 100%
const BASE_THUMBNAIL_TARGET_SIZE = 100; // px

// Add TypeScript declaration for the window object extension
declare global {
  interface Window {
    bookProofsApp?: {
      getUpdatedFilePath?: (originalPath: string) => string | null;
      getAllUpdatedPaths?: () => Record<string, string>;
      updatedPaths?: Record<string, string>;
      updateImagePaths?: (images: ImageFile[]) => ImageFile[];
      isRestoringState?: boolean; // Flag to indicate undo/redo operation in progress
      imageCache?: Record<string, any>; // Cache for image data
    };
  }
}

export interface ImageFile {
  id: string; // Unique ID for React keys etc.
  name: string; // Original filename for display
  originalPath: string; // Full path to the original image file
  thumbnailUrl: string; // URL (using safe-file://) to the generated thumbnail
  previewUrl?: string | null; // Optional: URL (using safe-file://) to the medium preview image for canvas
  rating?: number; // Optional star rating (0-5)
  dateAdded?: number; // Optional timestamp when image was added
  dateTaken?: number; // Optional timestamp when the photo was taken (from EXIF)
  dateModified?: number; // Optional timestamp when the file was last modified
  naturalWidth?: number; // Optional: Natural width of the image
  naturalHeight?: number; // Optional: Natural height of the image
}

export interface ImageTrayProps {
  images: ImageFile[];
  onAddImages: () => void;
  usedImageIds?: string[]; // New prop to track which images are used in spreads
  showFilenames?: boolean; // Prop to control filename visibility
  showEndOfFilename?: boolean; // Prop to control showing end of filename
  showRatingFilter?: boolean; // Prop to control rating filter UI visibility
  isTemplatesTrayFocused?: boolean; // Add prop for template tray focus state
  onRemoveImages?: (imageIds: string[]) => void; // Handler to remove images from the project
  hoveredCanvasPlaceholderId?: string | null; // ID of the placeholder hovered in SpreadCanvas
  isSpreadThumbnailHovered?: boolean; // Prop indicating if a spread thumbnail is hovered
  onFocusChange?: (isFocused: boolean) => void; // Add focus change handler prop
  onSelectionChange?: (selectedIds: string[]) => void; // Add selection change handler prop
  onWidthChange?: (newWidth: number) => void;
  photoLibraryDisplayMode?: string;
  onFilesProcessedFromDrop?: (newImages: ImageFile[]) => void;  // Handler for autobuild from ImageTray when multiple images are selected in the context menu
  onAutoBuild?: (images: ImageFile[], initialFit?: 'cover' | 'contain') => void; // Handler for autobuild feature
  theme?: 'light' | 'dark'; // Theme prop
  position?: 'left' | 'bottom'; // Position prop to control layout
  height?: number; // Height prop for responsive layout
  // Multi-select state props (managed by BookEditor for ContextualStatusTray)
  isMultiSelectActive?: boolean;
  imageSelectionCount?: number; 
  onMultiSelectStateChange?: (isActive: boolean) => void;
  onPositionChange?: (position: 'left' | 'bottom') => void; // Handler for position toggle
}

// Define the handle type
export interface ImageTrayHandle {
  clearSelection: () => void;
  triggerFileSelect: () => void;
  resetThumbnailSize: () => void;
}

// Wrap component with forwardRef, update ref type
const ImageTray = forwardRef<ImageTrayHandle, ImageTrayProps>(({
  images, onAddImages, usedImageIds = [], showFilenames = false, showEndOfFilename = false, showRatingFilter = true, isTemplatesTrayFocused = false, onRemoveImages, hoveredCanvasPlaceholderId, isSpreadThumbnailHovered, onFocusChange, onSelectionChange, onWidthChange, photoLibraryDisplayMode = "all", onFilesProcessedFromDrop, onAutoBuild, theme = 'light', position = 'left', height, isMultiSelectActive: isMultiSelectActiveProp, imageSelectionCount: imageSelectionCountProp, onMultiSelectStateChange, onPositionChange
}, ref): JSX.Element => {
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  // Use prop values with fallback to internal state for backward compatibility
  const isMultiSelectActive = isMultiSelectActiveProp ?? false;
  const imageSelectionCount = imageSelectionCountProp ?? selectedImages.length;
  const [isOptionKeyPressed, setIsOptionKeyPressed] = useState(false);
  const [filterValue, setFilterValue] = useState<string>(photoLibraryDisplayMode); // Initialize with photoLibraryDisplayMode
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(null); // Anchor for shift-select
  
  // Get all relevant user settings - destructure only what we need
  // This ensures the component re-renders when any of these settings change
  const {
    photoLibrarySortBy, setPhotoLibrarySortBy,
    photoLibrarySortDirection, setPhotoLibrarySortDirection
  } = useUserSettings();
  const [minRating, setMinRating] = useState<number>(0); // 0 = show all, 1-5 = show >= rating
  const [isFocused, setIsFocused] = useState(false); // Track focus state
  const [hoveredImageId, setHoveredImageId] = useState<string | null>(null); // Track hovered image ID
  const [showFullImageModal, setShowFullImageModal] = useState(false); // State for modal visibility
  const [modalImageSrc, setModalImageSrc] = useState<string | null>(null); // State for current image src
  const [modalImageFilename, setModalImageFilename] = useState<string | null>(null); // State for current image filename
  const [imageExifData, setImageExifData] = useState<any | null>(null);
  const [loadingExif, setLoadingExif] = useState(false);
  const [isDraggingOverPlaceholder, setIsDraggingOverPlaceholder] = useState(false);
  
  // State for autobuild confirmation dialog
  const [isAutobuildDialogOpen, setIsAutobuildDialogOpen] = useState(false);
  const [selectedImagesForAutobuild, setSelectedImagesForAutobuild] = useState<ImageFile[]>([]);
  const [isProcessingDroppedOnPlaceholder, setIsProcessingDroppedOnPlaceholder] = useState(false);
  const [droppedOnPlaceholderProgress, setDroppedOnPlaceholderProgress] = useState<{ stage?: string; current: number; total: number; filename?: string; error?: string } | null>(null);
  // Add state for thumbnail size (default based on position: 1 for left, 1.25 for bottom)
  const [thumbnailSize, setThumbnailSize] = useState<number>(position === 'bottom' ? 1.25 : 1);
  
  // Responsive layout logic - use compact mode when height is below default
  const heightThreshold = 260; // Below default height of 280px
  const isCompactMode = position === 'bottom' && height !== undefined && height < heightThreshold;
  
  // State for cycling through compact mode controls
  const [compactModeSection, setCompactModeSection] = useState<'filter' | 'rating' | 'sort'>('filter');
  
  // Remove drag selection state variables
  const dragCounter = useRef(0); // Ref for drag enter/leave counting

  // State and Ref for container dimensions needed by FixedSizeGrid
  // Use the hook to get dimensions and the ref
  const { ref: gridContainerRef, width: gridWidth = 0, height: gridHeight = 0 } = useResizeObserver<HTMLDivElement>();
  
  // Add ref for the FixedSizeGrid component to control scrolling
  const gridRef = useRef<any>(null);
  
  // Calculate number of columns based on available width and thumbnail size
  const columnCount = useMemo(() => {
    const calcStart = performance.now();
    if (gridWidth <= 0) return 1; // Default to 1 column if width is not available

    if (position === 'bottom') {
      // For bottom position, map slider range (0.5-2.0) to column range (5-9) for better performance
      // This ensures smooth scaling across the entire slider range
      const minCols = 5;
      const maxCols = 9;
      const minSlider = 0.5;
      const maxSlider = 2.0;
      
      // Invert the mapping: smaller thumbnailSize (0.5) = more columns (9), larger thumbnailSize (2.0) = fewer columns (5)
      const normalizedSlider = (thumbnailSize - minSlider) / (maxSlider - minSlider); // 0-1 range
      const invertedSlider = 1 - normalizedSlider; // Invert so smaller thumbnails = higher value
      const mappedCols = Math.round(minCols + (invertedSlider * (maxCols - minCols)));
      
      return mappedCols;
    } else {
      // For left position, cap at 5 columns maximum for better performance
      const desiredCellWidth = BASE_THUMBNAIL_TARGET_SIZE * thumbnailSize;
      const calculatedCols = Math.max(1, Math.floor(gridWidth / desiredCellWidth));
      const cappedCols = Math.min(calculatedCols, 5);
      
      return cappedCols;
    }
  }, [gridWidth, thumbnailSize, position]);



  // Monitor for keyboard events globally
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Meta' || e.key === 'Control') {
        onMultiSelectStateChange?.(true);
      } else if (e.key === 'Alt' || e.key === 'Option') {
        // Track Option/Alt key press
        setIsOptionKeyPressed(true);
        console.log('[ImageTray] Option key pressed');
      } else if (e.key === 'Escape') {
        // Clear selection on Escape key press
        setSelectedImages([]);
      }
    };
    
    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === 'Meta' || e.key === 'Control') {
        onMultiSelectStateChange?.(false);
      } else if (e.key === 'Alt' || e.key === 'Option') {
        // Track Option/Alt key release
        setIsOptionKeyPressed(false);
        console.log('[ImageTray] Option key released');
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [onMultiSelectStateChange]);

  // Always update filterValue when photoLibraryDisplayMode changes
  // This ensures the setting persists even when the user manually changes the filter
  useEffect(() => {
    setFilterValue(photoLibraryDisplayMode);
  }, [photoLibraryDisplayMode]);

  useEffect(() => {
    if (window.electronAPI?.onDroppedFilesProgress) {
      const removeProgressListener = window.electronAPI.onDroppedFilesProgress((eventData) => {
        // Only update progress if this component initiated the drop processing
        if (!isProcessingDroppedOnPlaceholder) return;

        if (eventData === null) {
          // Main process signals completion of all files for this batch
          // Individual results are handled when processDroppedFiles resolves
        } else if (eventData.stage === 'error') {
          toast.error(`Error processing ${eventData.filename || 'a file'}: ${eventData.error}`);
          setDroppedOnPlaceholderProgress(prev => prev ? { ...prev, current: eventData.current, error: eventData.error, stage: 'error' } : null);
        } else {
          setDroppedOnPlaceholderProgress({
            stage: eventData.stage,
            current: eventData.current,
            total: eventData.total,
            filename: eventData.filename,
          });
        }
      });
      return () => {
        removeProgressListener?.();
      };
    }
  }, [isProcessingDroppedOnPlaceholder]); // Re-run if this specific processing state changes

  // --- REMOVED Delete Key useEffect ---

  // --- Report Selection Changes ---
  useEffect(() => {
    onSelectionChange?.(selectedImages);
  }, [selectedImages, onSelectionChange]); // Report whenever selection changes

  // Filter images based on used/unused status
  // Apply filtering and sorting
  const filteredAndSortedImages = useMemo(() => {
    return images
      .filter(image => { // Filter by used/unused status
        if (filterValue === "all") return true;
        const isUsed = usedImageIds.includes(image.id);
        return filterValue === "used" ? isUsed : !isUsed;
      })
      .filter(image => { // Filter by minimum rating
        // Treat rating 0 or undefined as passing if minRating is 0
        return (image.rating ?? 0) >= minRating;
      })
      .sort((a, b) => { // Updated sorting logic
        if (photoLibrarySortBy === 'name') {
          // Natural sort for filenames with numbers
          const nameA = a.name.toLowerCase();
          const nameB = b.name.toLowerCase();
          
          // Natural sort implementation that handles numbers in filenames
          const naturalCompare = (a: string, b: string) => {
            // Regular expression to split a string into parts that are either all digits or all non-digits
            const chunks = /(\d+|\D+)/g;
            const partsA = a.match(chunks) || [];
            const partsB = b.match(chunks) || [];
            
            // Compare each chunk
            for (let i = 0; i < Math.min(partsA.length, partsB.length); i++) {
              // If both parts are numeric, convert to number and compare
              const numA = parseInt(partsA[i]);
              const numB = parseInt(partsB[i]);
              
              if (!isNaN(numA) && !isNaN(numB)) {
                if (numA !== numB) {
                  return numA - numB;
                }
              } else {
                // Standard string comparison for non-numeric parts
                const comp = partsA[i].localeCompare(partsB[i]);
                if (comp !== 0) {
                  return comp;
                }
              }
            }
            
            // If all parts are equal up to the length of the shorter string,
            // the shorter string comes first
            return partsA.length - partsB.length;
          };
          
          const compareResult = naturalCompare(nameA, nameB);
          return photoLibrarySortDirection === 'asc' ? compareResult : -compareResult;
        } else if (photoLibrarySortBy === 'timeTaken') {
          // Sort by time the photo was taken (from EXIF data)
          const timeA = a.dateTaken ?? a.dateAdded ?? 0;
          const timeB = b.dateTaken ?? b.dateAdded ?? 0;
          return photoLibrarySortDirection === 'asc' ? timeA - timeB : timeB - timeA;
        } else { // Default to 'modified' (date modified)
          const modifiedA = a.dateModified ?? a.dateAdded ?? 0;
          const modifiedB = b.dateModified ?? b.dateAdded ?? 0;
          return photoLibrarySortDirection === 'asc' ? modifiedA - modifiedB : modifiedB - modifiedA;
        }
      });
  }, [images, filterValue, usedImageIds, minRating, photoLibrarySortBy, photoLibrarySortDirection]); // Dependencies for memoization
 
  // --- Focus Handling (Moved after filteredAndSortedImages) ---
  const handleFocus = useCallback(() => {
    setIsFocused(true);
    onFocusChange?.(true);
  }, [onFocusChange]);
 
  const handleBlur = useCallback(() => {
    setIsFocused(false);
    onFocusChange?.(false);
  }, [onFocusChange]);
 
  // Effect to synchronize selection with filtered/sorted images
  useEffect(() => {
    const visibleImageIds = new Set(filteredAndSortedImages.map(img => img.id));
    setSelectedImages(prevSelected => prevSelected.filter(id => visibleImageIds.has(id)));
    // Reset shift-click anchor when filters change to avoid unexpected range selections
    setLastSelectedIndex(null);
  }, [filteredAndSortedImages]); // Rerun when the visible images change

  // State for auto-scrolling
  const [autoScrollDirection, setAutoScrollDirection] = useState<{ vertical: number, horizontal: number }>({ vertical: 0, horizontal: 0 });
  const autoScrollActive = useRef<boolean>(false);
  const autoScrollTimerRef = useRef<number | null>(null);

  // Track current scroll position for auto-scrolling
  const [scrollInfo, setScrollInfo] = useState({ scrollTop: 0, scrollLeft: 0 });
  
  // Add scroll handler for the grid
  const handleGridScroll = useCallback((newScrollInfo: { scrollTop: number, scrollLeft: number }) => {
    setScrollInfo(newScrollInfo);
  }, []);

  // Auto-scroll animation function using requestAnimationFrame
  const performAutoScroll = useCallback(() => {
    if (!autoScrollActive.current) {
      if (autoScrollTimerRef.current) {
        cancelAnimationFrame(autoScrollTimerRef.current);
        autoScrollTimerRef.current = null;
      }
      return;
    }

    const scrollSpeed = 12; // Increased speed for faster scrolling

    // Use react-window's scrollTo method for smooth scrolling
    if (gridRef.current) {
      let newScrollTop = scrollInfo.scrollTop;
      let newScrollLeft = scrollInfo.scrollLeft;

      // Apply vertical scrolling if needed
      if (autoScrollDirection.vertical !== 0) {
        newScrollTop = newScrollTop + (autoScrollDirection.vertical * scrollSpeed);
        newScrollTop = Math.max(0, newScrollTop); // Prevent negative scroll values
      }

      // Apply horizontal scrolling if needed
      if (autoScrollDirection.horizontal !== 0) {
        newScrollLeft = newScrollLeft + (autoScrollDirection.horizontal * scrollSpeed);
        newScrollLeft = Math.max(0, newScrollLeft); // Prevent negative scroll values
      }

      // Update scroll position state (this will be used by onScroll callback)
      setScrollInfo({
        scrollTop: newScrollTop,
        scrollLeft: newScrollLeft
      });

      // Apply scrolling through react-window's API
      gridRef.current.scrollTo({
        scrollLeft: newScrollLeft,
        scrollTop: newScrollTop
      });
      
      // Continue the animation loop
      autoScrollTimerRef.current = requestAnimationFrame(performAutoScroll);
    }
  }, [autoScrollDirection]);

  // Start or stop auto-scrolling based on direction changes
  useEffect(() => {
    if ((autoScrollDirection.vertical !== 0 || autoScrollDirection.horizontal !== 0) && isFocused) {
      autoScrollActive.current = true;
      if (!autoScrollTimerRef.current) {
        autoScrollTimerRef.current = requestAnimationFrame(performAutoScroll);
      }
    } else {
      autoScrollActive.current = false;
    }

    return () => {
      if (autoScrollTimerRef.current) {
        cancelAnimationFrame(autoScrollTimerRef.current);
        autoScrollTimerRef.current = null;
      }
    };
  }, [autoScrollDirection, isFocused, performAutoScroll]);

  // Update click handler to use the single click logic only
  const handleImageClick = (imageId: string, index: number, e: React.MouseEvent) => {
    if (e.button === 2) { // 2 is the right mouse button
      return; // Do not process right-clicks in the normal click handler
    }

    const ctrlOrMeta = isMultiSelectActive; // Use the state tracked by useEffect
    const shift = e.shiftKey;

    if (shift && lastSelectedIndex !== null) {
      // Shift-click range selection
      const start = Math.min(lastSelectedIndex, index);
      const end = Math.max(lastSelectedIndex, index);
      const rangeIds = filteredAndSortedImages.slice(start, end + 1).map(img => img.id);
      
      if (ctrlOrMeta) {
        // Ctrl/Cmd+Shift: Add the range to existing selection
        setSelectedImages(prev => {
          const newSelection = [...prev];
          
          // Add all images in range that aren't already selected
          rangeIds.forEach(id => {
            if (!newSelection.includes(id)) {
              newSelection.push(id);
            }
          });
          
          return newSelection;
        });
      } else {
        // Shift only: Replace selection with the range
        setSelectedImages(rangeIds);
      }
      // Don't update lastSelectedIndex on shift-click to maintain the anchor point
    } else if (ctrlOrMeta) {
      // Cmd/Ctrl multi-select
      setSelectedImages(prev =>
        prev.includes(imageId)
          ? prev.filter(id => id !== imageId) // Deselect if already selected
          : [...prev, imageId] // Add to selection
      );
      setLastSelectedIndex(index); // Update anchor on multi-select click
    } else {
      // Single click (or first click in a sequence)
      setSelectedImages([imageId]);
      setLastSelectedIndex(index); // Update anchor
    }
  };

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, image: ImageFile) => {
    // Check if Option/Alt key is pressed at the start of drag
    const isOptionPressed = e.altKey;
    if (isOptionPressed !== isOptionKeyPressed) {
      console.log('[ImageTray] Option key state detected in dragStart:', isOptionPressed);
      setIsOptionKeyPressed(isOptionPressed);
    }
  
    // Set data for external drop targets (like SettingsPanel) expecting a URL/path
    const imageSrcForDrop = image.previewUrl || image.thumbnailUrl || image.originalPath; // Prioritize preview/thumbnail
    e.dataTransfer.setData('text/plain', imageSrcForDrop);

    // Check if dragging multiple images or single image
    if (selectedImages.includes(image.id) && selectedImages.length > 1) {
      // Multiple images selected
      const selectedImageObjects = images.filter(img => selectedImages.includes(img.id));
      e.dataTransfer.setData('application/json', JSON.stringify(selectedImageObjects)); // Keep JSON for internal multi-image drops
      e.dataTransfer.setData('application/x-bookproofs-multiple-images', 'true'); // Add marker for multiple images
      // For multi-image drag, text/plain might not be suitable for SettingsPanel,
      // but we set the first image's src just in case. SettingsPanel should ideally handle single image drops.
    } else {
      // Single image selected
      e.dataTransfer.setData('application/json', JSON.stringify(image));
      e.dataTransfer.setData('application/x-bookproofs-single-image', 'true'); // Add marker for single image
    }

    // Create a custom drag preview that shows the number of images being dragged
    if (selectedImages.length > 1) {
        try {
          // Create a custom drag image showing the first image plus a counter
          const dragPreview = document.createElement('div');
          dragPreview.style.position = 'absolute';
          // Position off-screen initially to avoid background interference
          dragPreview.style.top = '-9999px';
          dragPreview.style.left = '-9999px';
          dragPreview.style.display = 'flex';
          dragPreview.style.alignItems = 'center';
          dragPreview.style.padding = '6px'; // Increased padding
          dragPreview.style.background = 'white';
          dragPreview.style.border = '1px solid #e2e8f0'; // Use Tailwind color name for consistency if possible, or keep hex
          dragPreview.style.borderRadius = '6px'; // More prominent rounded corners
          dragPreview.style.boxShadow = '0 3px 15px rgba(0, 0, 0, 0.1)'; // Slightly larger shadow
          // Removed zIndex
          
          // Create thumbnail (1.5x size)
          const thumbnail = document.createElement('img');
          thumbnail.style.width = '60px'; // 40px * 1.5
          thumbnail.style.height = '60px'; // 40px * 1.5
          thumbnail.style.objectFit = 'contain';
          thumbnail.style.marginRight = '12px'; // 8px * 1.5
          
          // Try to find an existing image for this ID that's already loaded in the DOM
          const existingImg = document.querySelector(`[data-image-id="${image.id}"] img`);
          
          // If a loaded image was found in the DOM, use that one
          if (existingImg && (existingImg as HTMLImageElement).complete && (existingImg as HTMLImageElement).naturalWidth > 0) {
            thumbnail.src = (existingImg as HTMLImageElement).src;
          } else {
            // Otherwise, use the original thumbnailUrl
            thumbnail.src = image.thumbnailUrl;
          }
          
          // Create counter badge (1.5x size)
          const counter = document.createElement('div');
          counter.textContent = `${selectedImages.length} images`; // Original text
          counter.style.background = '#0ea5e9'; // Sky-500
          counter.style.color = 'white';
          counter.style.borderRadius = '999px';
          counter.style.padding = '3px 12px'; // Increased padding (2px 8px * 1.5)
          counter.style.fontSize = '18px'; // Increased font size (12px * 1.5)
          counter.style.fontWeight = 'bold';
          // Removed lineHeight, minWidth, textAlign
          
          // Assemble drag preview
          dragPreview.appendChild(thumbnail);
          dragPreview.appendChild(counter);
          
          // Add to document temporarily to allow setDragImage to capture it
          document.body.appendChild(dragPreview);
          
          // Set as drag image (original offset)
          e.dataTransfer.setDragImage(dragPreview, 20, 20);
          
          // Clean up the temporary element *after* a short delay (original delay)
          setTimeout(() => {
            if (document.body.contains(dragPreview)) {
              document.body.removeChild(dragPreview);
            }
          }, 100); // Original timeout
        } catch (err) {
          console.warn('Error creating custom drag image:', err);
          // Fallback: Don't set a custom drag image if an error occurs
        }
    } else {
      // Single image drag preview - data already set above

      // Create custom drag preview for single image
      try {
        // Create a custom drag image - KEEPING THE SAME METHOD but making container invisible
        const dragPreview = document.createElement('div');
        dragPreview.style.position = 'absolute';
        dragPreview.style.top = '-9999px'; // Position off-screen initially
        dragPreview.style.left = '-9999px';
        dragPreview.style.display = 'flex';
        dragPreview.style.alignItems = 'center';
        dragPreview.style.padding = '0';
        dragPreview.style.background = 'transparent';
        dragPreview.style.border = 'none';
        dragPreview.style.borderRadius = '0';
        dragPreview.style.boxShadow = 'none';
        dragPreview.style.overflow = 'hidden'; // Ensure nothing outside the image shows
        dragPreview.style.opacity = '1'; // Make fully visible
        dragPreview.style.pointerEvents = 'none'; // Prevent any mouse interaction
        
        // Create thumbnail (double the size compared to multi-image drag)
        const thumbnail = document.createElement('img');
        thumbnail.style.width = '120px';
        thumbnail.style.height = '120px';
        thumbnail.style.objectFit = 'contain';
        thumbnail.style.background = 'transparent';
        thumbnail.style.padding = '0';
        thumbnail.style.margin = '0';
        thumbnail.style.border = 'none';
        thumbnail.style.boxShadow = 'none';
        
        // Try to find an existing image for this ID that's already loaded in the DOM
        const existingImg = document.querySelector(`[data-image-id="${image.id}"] img`);
        
        // If a loaded image was found in the DOM, use that one
        if (existingImg && (existingImg as HTMLImageElement).complete && (existingImg as HTMLImageElement).naturalWidth > 0) {
          thumbnail.src = (existingImg as HTMLImageElement).src;
        } else {
          // Otherwise, use the original thumbnailUrl
          thumbnail.src = image.thumbnailUrl;
        }
        
        // Assemble drag preview
        dragPreview.appendChild(thumbnail);
        
        // Add to document temporarily to allow setDragImage to capture it
        document.body.appendChild(dragPreview);
        
        // Set as drag image
        e.dataTransfer.setDragImage(dragPreview, 20, 20);
        
        // Clean up the temporary element after a short delay
        setTimeout(() => {
          if (document.body.contains(dragPreview)) {
            document.body.removeChild(dragPreview);
          }
        }, 100);
      } catch (err) {
        console.warn('Error creating custom drag image for single image:', err);
        // Fallback: Don't set a custom drag image if an error occurs
      }

      // If dragging a non-selected image, update selection to just this image
      if (!selectedImages.includes(image.id)) {
        setSelectedImages([image.id]);
      }
    }
    
    e.dataTransfer.effectAllowed = 'copy';
  };
  

  // --- Reusable function to show the image preview modal ---
  const showImagePreviewModal = useCallback(async (imageToView: ImageFile) => {
    // Reset modal state before fetching
    setModalImageSrc(null);
    setModalImageFilename(null); // Reset filename
    setImageExifData(null);
    setLoadingExif(true);
    setShowFullImageModal(false); // Ensure modal is closed before trying to reopen

    try {
      // Check if API is available - we're definitely in Electron since this is an Electron-only app
      if (!window.electronAPI) {
        console.error('[ImageTray] window.electronAPI is not available');
        toast.error("Preview error: API not available. Please restart the app.");
        setLoadingExif(false);
        return;
      }
      
      // Check if the required functions are available
      if (typeof window.electronAPI.getImageDataUrl !== 'function') {
        console.error('[ImageTray] window.electronAPI.getImageDataUrl is not a function');
        toast.error("Preview error: Image data URL function not available. Please restart the app.");
        setLoadingExif(false);
        return;
      }
      
      if (typeof window.electronAPI.getExifData !== 'function') {
        console.error('[ImageTray] window.electronAPI.getExifData is not a function');
        toast.error("Preview error: EXIF data function not available. Please restart the app.");
        setLoadingExif(false);
        return;
      }
      
      // Get the most up-to-date path for this image
      let imagePath = imageToView.originalPath;
      
      // Check if we have an updated path in our cache
      if (window.bookProofsApp?.getUpdatedFilePath) {
        const updatedPath = window.bookProofsApp.getUpdatedFilePath(imageToView.originalPath);
        if (updatedPath) {
          console.log(`[ImageTray] Using updated path for preview: ${updatedPath}`);
          imagePath = updatedPath;
        }
      }
      
      // First check if the file exists at the specified path
      if (window.electronAPI?.checkFilesExist) {
        const fileExists = await window.electronAPI.checkFilesExist([imagePath], true);
        if (!fileExists[0]) {
          toast.error(`File not found at ${imagePath}. The file may have been moved or deleted.`);
          setLoadingExif(false);
          return;
        }
      }
      
      // Call the main process to get the image data URL and EXIF data
      console.log(`[ImageTray] Getting image data for ${imagePath}`);
      
      try {
        const [imageResult, exifResult] = await Promise.all([
          window.electronAPI.getImageDataUrl(imagePath),
          window.electronAPI.getExifData(imagePath)
        ]);

        if (imageResult.success && imageResult.dataUrl) {
          setModalImageSrc(imageResult.dataUrl); // Set the source to the received Data URL
          setModalImageFilename(imageResult.filename || null); // Set the filename

          // Store EXIF data if successful
          if (exifResult.success && exifResult.exifData) {
            setImageExifData(exifResult.exifData); // Store the structured data

            // Access detailed EXIF data potentially nested within a 'raw' property or similar
            const rawExif = exifResult.exifData.raw; // Assuming detailed tags are here

            // Check for color space/profile info within the raw data
            const colorInfo = rawExif?.ColorSpace;
            const profileName = rawExif?.ICC?.ProfileDescription;

            // Determine profile based on found info
            const determinedProfile = profileName || (colorInfo === 1 ? 'sRGB' : 'Unknown/Other');
            // You might want to update state or use determinedProfile here if needed
          } else {
            console.error("Failed to load EXIF data:", exifResult.error);
            setImageExifData(null);
          }
          
          setShowFullImageModal(true); // Show the modal *after* getting the data
        } else {
          toast.error(`Could not load image preview: ${imageResult.error || 'Unknown error'}`);
          setModalImageSrc(null); // Ensure src is null on error
          setModalImageFilename(null); // Ensure filename is null on error
          setImageExifData(null);
          setShowFullImageModal(false);
        }
      } catch (error) {
        // Handle any errors that occur during the Promise.all
        console.error('Error loading image preview:', error);
        toast.error(`Error loading image preview: ${error.message || 'Unknown error'}`);
        setModalImageSrc(null);
        setModalImageFilename(null); // Ensure filename is null on error
        setImageExifData(null);
        setShowFullImageModal(false);
      } finally {
        setLoadingExif(false);
      }
    } catch (error: any) {
      toast.error(`Error loading image preview: ${error.message}`);
      setModalImageSrc(null);
      setModalImageFilename(null); // Ensure filename is null on error
      setImageExifData(null);
      setShowFullImageModal(false);
    } finally {
      setLoadingExif(false);
    }
  }, [setModalImageSrc, setModalImageFilename, setImageExifData, setLoadingExif, setShowFullImageModal]); // Dependencies

  // --- Keyboard Handler for Select All & Fullscreen ---
  const handleKeyDown = useCallback(async (e: React.KeyboardEvent<HTMLDivElement>) => {
    // Check for Cmd+A or Ctrl+A
    if ((e.metaKey || e.ctrlKey) && e.key === 'a') {
      // More robust focus detection: check if the event target is within the image tray
      // or if the tray has focus. This handles cases where child elements temporarily have focus.
      const isWithinTray = e.currentTarget.contains(e.target as Node);
      if (isFocused || isWithinTray) {
        e.preventDefault(); // Prevent browser default select all
        setSelectedImages(filteredAndSortedImages.map(img => img.id));
      }
    } else if (e.key === ' ') { // Spacebar press - remove strict focus check for spacebar too
      const isWithinTray = e.currentTarget.contains(e.target as Node);
      if (isFocused || isWithinTray) {
        e.preventDefault(); // Prevent default spacebar action (scrolling)

        // If modal is already open, just close it and don't reopen
        if (showFullImageModal) {
          setShowFullImageModal(false);
          setImageExifData(null); // Clear EXIF data when closing
          return;
        }

        // Check if there's a valid last selected index
        if (lastSelectedIndex !== null && filteredAndSortedImages[lastSelectedIndex]) {
          const imageToView = filteredAndSortedImages[lastSelectedIndex];
          // Use the reusable function
          showImagePreviewModal(imageToView);
        }
      }
    }
    // Note: Escape key is handled by the global listener added in useEffect
  }, [isFocused, filteredAndSortedImages, setSelectedImages, showFullImageModal, showImagePreviewModal, lastSelectedIndex]); // Added lastSelectedIndex dependency

  // Provide the imperative handle for clearing selection
  useImperativeHandle(ref, () => ({
    clearSelection: () => {
      setSelectedImages([]);
    },
    triggerFileSelect: () => {
     // This will call the onAddImages prop, which should trigger the file dialog
     // in the parent component (BookEditor) via useImageImport hook
     onAddImages();
   },
    resetThumbnailSize: () => {
      // Reset to default based on current position
      setThumbnailSize(position === 'bottom' ? 1.25 : 1);
    }
  }), [onAddImages, position]);

  // --- Handle clicks on the grid container (dead space / between items) ---
  const handleGridContainerMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (e.button === 2) { // 2 is the right mouse button
      return; // Do not process right-clicks in this handler, they are for context menu
    }

    // Check if the click originated from an image cell or its descendants
    let targetElement = e.target as HTMLElement;
    let isClickOnImageCell = false;

    // Traverse up the DOM tree from the click target to the container
    while (targetElement && targetElement !== e.currentTarget) {
      // Check if the current element in the traversal is an image cell
      if (targetElement.getAttribute('data-image-cell') === 'true') {
        isClickOnImageCell = true;
        break; // Found an image cell, no need to check further up
      }
      // Move up to the parent element
      targetElement = targetElement.parentElement as HTMLElement;
    }

    // If the click did *not* originate from within an image cell, clear the selection.
    // This handles clicks on the container background and the space between grid items.
    if (!isClickOnImageCell) {
      setSelectedImages([]);
    }
  }, [setSelectedImages]); // Dependency on setSelectedImages

  // Get the current filtered image count for status display
  const filteredImageCount = filteredAndSortedImages.length;
  
  // Create and update sortOptions array locally
  const sortOptions = useMemo(() => [
    { value: 'name-asc', label: 'A to Z', icon: <ArrowUpAZ size={16} /> },
    { value: 'name-desc', label: 'Z to A', icon: <ArrowDownAZ size={16} /> },
    { value: 'modified-desc', label: 'Last Modified', icon: <CalendarArrowDown size={16} /> },
    { value: 'modified-asc', label: 'First Modified', icon: <CalendarArrowUp size={16} /> },
    { value: 'timeTaken-desc', label: 'Newest Taken First', icon: <ClockArrowDown size={16} /> },
    { value: 'timeTaken-asc', label: 'Oldest Taken First', icon: <ClockArrowUp size={16} /> },
  ], []); // No dependencies for this static array

  const handleDropOnPlaceholder = useCallback(async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOverPlaceholder(false);

    if (isProcessingDroppedOnPlaceholder) return; // Prevent multiple concurrent drops

    if (!window.electronAPI?.processDroppedFiles) {
      toast.error("File processing API is not available.");
      return;
    }
    if (!onFilesProcessedFromDrop) {
      console.warn("ImageTray: onFilesProcessedFromDrop prop is not provided. Dropped files will not be added.");
      // toast.error("Cannot process dropped files: Handler not configured."); // Optional user feedback
      return;
    }

    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;

    const imageFileObjects = Array.from(files).filter(
      file => file.type.startsWith('image/') && (file as any).path
    );

    if (imageFileObjects.length === 0) {
      toast.error('No valid image files with paths found in dropped items.');
      return;
    }
    const filePaths = imageFileObjects.map(file => (file as any).path as string);

    setIsProcessingDroppedOnPlaceholder(true);
    setDroppedOnPlaceholderProgress({ stage: 'starting_import', current: 0, total: filePaths.length });

    try {
      const processedImages: ImageFile[] | null = await window.electronAPI.processDroppedFiles(filePaths, { generatePreviews: false });
      if (processedImages && processedImages.length > 0) {
        onFilesProcessedFromDrop(processedImages);
        toast.success(`Added ${processedImages.length} images via drop.`);
      } else if (filePaths.length > 0 && (!processedImages || processedImages.length === 0)) {
        // Files were attempted but none were successfully processed
        toast.error('Could not process the dropped images. Please ensure they are valid image files and the project location is set.');
      }
    } catch (error: any) {
      console.error('Error invoking processDroppedFiles from ImageTray:', error);
      toast.error(`Failed to add dropped images: ${error.message || 'Unknown error'}`);
    } finally {
      setIsProcessingDroppedOnPlaceholder(false);
      setDroppedOnPlaceholderProgress(null);
      dragCounter.current = 0; // Reset drag counter
    }
  }, [onFilesProcessedFromDrop, isProcessingDroppedOnPlaceholder]); // Keep existing dependencies

  const handleDragEnterPlaceholder = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current++;
    if (dragCounter.current === 1 && !isProcessingDroppedOnPlaceholder) {
      setIsDraggingOverPlaceholder(true);
    }
  }, [isProcessingDroppedOnPlaceholder, setIsDraggingOverPlaceholder]);

  const handleDragOverPlaceholder = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault(); // Crucial for allowing drop
    e.stopPropagation();
    // Visual state is handled by onDragEnter and onDragLeave
  }, []); // No dependencies needed if it only calls preventDefault/stopPropagation

  const handleDragLeavePlaceholder = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current--;
    if (dragCounter.current === 0 && !isProcessingDroppedOnPlaceholder) {
      setIsDraggingOverPlaceholder(false);
    }
  }, [isProcessingDroppedOnPlaceholder, setIsDraggingOverPlaceholder]);

  // Helper function to render filter controls
  const renderFilterControls = () => (
    <>
      {/* Filter by Used/Unused - Now responsive */}
      <div className="flex flex-wrap items-center gap-y-2">
        <div className="flex items-center gap-2 mr-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <span className="text-sm whitespace-nowrap">Filter:</span>
        </div>
        
        <ToggleGroup
          type="single"
          value={filterValue}
          onValueChange={(value) => value && setFilterValue(value)}
          onKeyDown={(e) => {
            // Prevent ToggleGroup's default arrow key navigation and stop bubbling
            if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
              e.preventDefault(); // Prevent default ToggleGroup behavior, allow bubbling
            }
          }}
          className="flex-wrap"
        >
          <ToggleGroupItem value="all" size="sm" className="text-xs focus-visible:ring-0 focus-visible:ring-offset-0 px-2">
            All
          </ToggleGroupItem>
          <ToggleGroupItem value="used" size="sm" className="text-xs focus-visible:ring-0 focus-visible:ring-offset-0 px-2">
            Used
          </ToggleGroupItem>
          <ToggleGroupItem value="unused" size="sm" className="text-xs focus-visible:ring-0 focus-visible:ring-offset-0 px-2">
            Unused
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
      {/* Rating Filter (Conditionally Rendered) - Now responsive */}
      {showRatingFilter && (
        <div className="flex flex-wrap items-center gap-y-2">
           <div className="flex items-center gap-2 mr-2">
             <Star className="w-4 h-4 text-gray-500" />
             <span className="text-sm whitespace-nowrap">Min Rating:</span>
           </div>
           <div className="flex items-center flex-wrap">
             {[1, 2, 3, 4, 5].map((rating) => (
               <button
                 key={rating}
                 onClick={() => setMinRating(prev => prev === rating ? 0 : rating)} // Click again to reset to 0
                 className={`p-0.5 text-gray-300 hover:text-yellow-400 focus:outline-none ${minRating >= rating ? 'text-yellow-400' : ''}`}
                 title={`${rating} star${rating > 1 ? 's' : ''} or more`}
               >
                 <Star className={`w-4 h-4 ${minRating >= rating ? 'fill-current' : ''}`} />
               </button>
             ))}
           </div>
         </div>
      )}
      {/* Sort Controls - Now responsive with toggle direction */}
      <div className="flex flex-wrap items-center gap-1 gap-y-2">
        <div className="flex items-center gap-2 mr-1">
          <Clock className="w-4 h-4 text-gray-500" />
          <span className="text-sm whitespace-nowrap">Sort:</span>
        </div>
        
        {/* Filename Sort (Toggle) - Icon only */}
        <Button
          variant={photoLibrarySortBy === 'name' ? 'secondary' : 'ghost'}
          size="sm"
          className="px-2 focus-visible:ring-0 focus-visible:ring-offset-0"
          onClick={() => {
            if (photoLibrarySortBy === 'name') {
              // Toggle direction if already selected
              setPhotoLibrarySortDirection(photoLibrarySortDirection === 'asc' ? 'desc' : 'asc');
            } else {
              // Set to name with default asc direction
              setPhotoLibrarySortBy('name');
              setPhotoLibrarySortDirection('asc');
            }
          }}
          title={`Sort by Filename (${photoLibrarySortBy === 'name' && photoLibrarySortDirection === 'desc' ? 'Z to A' : 'A to Z'})`}
        >
          {photoLibrarySortBy === 'name' && photoLibrarySortDirection === 'desc'
            ? <ArrowDownAZ className="w-4 h-4" />
            : <ArrowUpAZ className="w-4 h-4" />
          }
        </Button>
        
        {/* Modified Date Sort (Toggle) - Icon changes with direction */}
        <Button
          variant={photoLibrarySortBy === 'modified' ? 'secondary' : 'ghost'}
          size="sm"
          className="px-2 focus-visible:ring-0 focus-visible:ring-offset-0"
          onClick={() => {
            if (photoLibrarySortBy === 'modified') {
              // Toggle direction if already selected
              setPhotoLibrarySortDirection(photoLibrarySortDirection === 'desc' ? 'asc' : 'desc');
            } else {
              // Set to modified with default desc direction
              setPhotoLibrarySortBy('modified');
              setPhotoLibrarySortDirection('desc');
            }
          }}
          title={`Sort by Date Modified (${photoLibrarySortBy === 'modified' && photoLibrarySortDirection === 'asc' ? 'Oldest First' : 'Newest First'})`}
        >
          {photoLibrarySortBy === 'modified' && photoLibrarySortDirection === 'asc'
            ? <CalendarArrowUp className="w-4 h-4" /> // Oldest first (ascending)
            : <CalendarArrowDown className="w-4 h-4" /> // Newest first (descending)
          }
        </Button>
        
        {/* Time Taken Sort (Toggle) - Icon only */}
        <Button
          variant={photoLibrarySortBy === 'timeTaken' ? 'secondary' : 'ghost'}
          size="sm"
          className="px-2 focus-visible:ring-0 focus-visible:ring-offset-0"
          onClick={() => {
            if (photoLibrarySortBy === 'timeTaken') {
              // Toggle direction if already selected
              setPhotoLibrarySortDirection(photoLibrarySortDirection === 'desc' ? 'asc' : 'desc');
            } else {
              // Set to timeTaken with default desc direction
              setPhotoLibrarySortBy('timeTaken');
              setPhotoLibrarySortDirection('desc');
            }
          }}
          title={`Sort by Time Taken (${photoLibrarySortBy === 'timeTaken' && photoLibrarySortDirection === 'asc' ? 'Oldest First' : 'Newest First'})`}
        >
          {photoLibrarySortBy === 'timeTaken' && photoLibrarySortDirection === 'asc'
            ? <ClockArrowUp className="w-4 h-4" /> // Oldest first (ascending)
            : <ClockArrowDown className="w-4 h-4" /> // Newest first (descending)
          }
        </Button>
      </div>
    </>
  );

  // Function to cycle through compact mode sections
  const cycleCompactModeSection = () => {
    setCompactModeSection(current => {
      if (current === 'filter') {
        return showRatingFilter ? 'rating' : 'sort';
      } else if (current === 'rating') {
        return 'sort';
      } else {
        return 'filter';
      }
    });
  };

  // Helper function to render compact filter controls with cycling
  const renderCompactFilterControls = () => (
    <div className="flex items-center gap-1 overflow-hidden">
      <div className="flex items-center gap-1 flex-shrink-0">
        <Filter className="w-4 h-4 text-gray-500" />
        <span className="text-sm whitespace-nowrap">Filter:</span>
      </div>
      
      <ToggleGroup
        type="single"
        value={filterValue}
        onValueChange={(value) => value && setFilterValue(value)}
        onKeyDown={(e) => {
          // Prevent ToggleGroup's default arrow key navigation and stop bubbling
          if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
            e.preventDefault(); // Prevent default ToggleGroup behavior, allow bubbling
          }
        }}
        className="flex items-center gap-0.5 flex-1 min-w-0"
      >
        <ToggleGroupItem value="all" size="sm" className="text-xs focus-visible:ring-0 focus-visible:ring-offset-0 px-1.5 flex-shrink-0">
          All
        </ToggleGroupItem>
        <ToggleGroupItem value="used" size="sm" className="text-xs focus-visible:ring-0 focus-visible:ring-offset-0 px-1.5 flex-shrink-0">
          Used
        </ToggleGroupItem>
        <ToggleGroupItem value="unused" size="sm" className="text-xs focus-visible:ring-0 focus-visible:ring-offset-0 px-1.5 flex-shrink-0">
          Unused
        </ToggleGroupItem>
      </ToggleGroup>
      
      {/* Cycle button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={cycleCompactModeSection}
        className="px-1.5 focus-visible:ring-0 focus-visible:ring-offset-0 flex-shrink-0"
        title="Show more options"
      >
        <ChevronRight className="w-4 h-4" />
      </Button>
    </div>
  );

  // Helper function to render compact rating controls
  const renderCompactRatingControls = () => (
    <div className="flex items-center gap-1 overflow-hidden">
      <div className="flex items-center gap-1 flex-shrink-0">
        <Star className="w-4 h-4 text-gray-500" />
        <span className="text-sm whitespace-nowrap">Min Rating:</span>
      </div>
      <div className="flex items-center gap-0.5 flex-1 min-w-0">
        {[1, 2, 3, 4, 5].map((rating) => (
          <button
            key={rating}
            onClick={() => setMinRating(prev => prev === rating ? 0 : rating)}
            className={`p-0.5 text-gray-300 hover:text-yellow-400 focus:outline-none flex-shrink-0 ${minRating >= rating ? 'text-yellow-400' : ''}`}
            title={`${rating} star${rating > 1 ? 's' : ''} or more`}
          >
            <Star className={`w-3.5 h-3.5 ${minRating >= rating ? 'fill-current' : ''}`} />
          </button>
        ))}
      </div>
      
      {/* Cycle button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={cycleCompactModeSection}
        className="px-1.5 focus-visible:ring-0 focus-visible:ring-offset-0 flex-shrink-0"
        title="Show more options"
      >
        <ChevronRight className="w-4 h-4" />
      </Button>
    </div>
  );

  // Helper function to render compact sort controls
  const renderCompactSortControls = () => (
    <div className="flex items-center gap-1 overflow-hidden">
      <div className="flex items-center gap-1 flex-shrink-0">
        <Clock className="w-4 h-4 text-gray-500" />
        <span className="text-sm whitespace-nowrap">Sort:</span>
      </div>
      
      <div className="flex items-center gap-0.5 flex-1 min-w-0">
        {/* Filename Sort */}
        <Button
          variant={photoLibrarySortBy === 'name' ? 'secondary' : 'ghost'}
          size="sm"
          className="px-1.5 focus-visible:ring-0 focus-visible:ring-offset-0 flex-shrink-0"
          onClick={() => {
            if (photoLibrarySortBy === 'name') {
              setPhotoLibrarySortDirection(photoLibrarySortDirection === 'asc' ? 'desc' : 'asc');
            } else {
              setPhotoLibrarySortBy('name');
              setPhotoLibrarySortDirection('asc');
            }
          }}
          title={`Sort by Filename (${photoLibrarySortBy === 'name' && photoLibrarySortDirection === 'desc' ? 'Z to A' : 'A to Z'})`}
        >
          {photoLibrarySortBy === 'name' && photoLibrarySortDirection === 'desc'
            ? <ArrowDownAZ className="w-4 h-4" />
            : <ArrowUpAZ className="w-4 h-4" />
          }
        </Button>
        
        {/* Modified Date Sort */}
        <Button
          variant={photoLibrarySortBy === 'modified' ? 'secondary' : 'ghost'}
          size="sm"
          className="px-1.5 focus-visible:ring-0 focus-visible:ring-offset-0 flex-shrink-0"
          onClick={() => {
            if (photoLibrarySortBy === 'modified') {
              setPhotoLibrarySortDirection(photoLibrarySortDirection === 'desc' ? 'asc' : 'desc');
            } else {
              setPhotoLibrarySortBy('modified');
              setPhotoLibrarySortDirection('desc');
            }
          }}
          title={`Sort by Date Modified (${photoLibrarySortBy === 'modified' && photoLibrarySortDirection === 'asc' ? 'Oldest First' : 'Newest First'})`}
        >
          {photoLibrarySortBy === 'modified' && photoLibrarySortDirection === 'asc'
            ? <CalendarArrowUp className="w-4 h-4" />
            : <CalendarArrowDown className="w-4 h-4" />
          }
        </Button>
        
        {/* Time Taken Sort */}
        <Button
          variant={photoLibrarySortBy === 'timeTaken' ? 'secondary' : 'ghost'}
          size="sm"
          className="px-1.5 focus-visible:ring-0 focus-visible:ring-offset-0 flex-shrink-0"
          onClick={() => {
            if (photoLibrarySortBy === 'timeTaken') {
              setPhotoLibrarySortDirection(photoLibrarySortDirection === 'desc' ? 'asc' : 'desc');
            } else {
              setPhotoLibrarySortBy('timeTaken');
              setPhotoLibrarySortDirection('desc');
            }
          }}
          title={`Sort by Time Taken (${photoLibrarySortBy === 'timeTaken' && photoLibrarySortDirection === 'asc' ? 'Oldest First' : 'Newest First'})`}
        >
          {photoLibrarySortBy === 'timeTaken' && photoLibrarySortDirection === 'asc'
            ? <ClockArrowUp className="w-4 h-4" />
            : <ClockArrowDown className="w-4 h-4" />
          }
        </Button>
      </div>
      
      {/* Cycle button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={cycleCompactModeSection}
        className="px-1.5 focus-visible:ring-0 focus-visible:ring-offset-0 flex-shrink-0"
        title="Show more options"
      >
        <ChevronRight className="w-4 h-4" />
      </Button>
    </div>
  );

  // Responsive layout for buttons
  return (
    // Use a local ref for the main div if needed for internal logic,
    // but the forwarded ref is now for the handle.
    <div
      // ref={ref} // Don't assign the handle ref directly to the div
      className={`bg-white border-t border-gray-200 ${position === 'left' ? 'border-r' : ''} border-gray-200 flex flex-col h-full outline-none relative`}
      // Removed inline style for width - parent controls width
      tabIndex={0} // Make it focusable
      onFocus={handleFocus} // Use updated handler
      onBlur={handleBlur}   // Use updated handler
      onKeyDown={handleKeyDown} // Add keydown handler for select all
      style={{ filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none' }}
    >
      <div className="px-5 py-3 flex items-center justify-between border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Image className="w-4 h-4 text-gray-500" />
          <span className="font-medium">Photo Library</span>
        </div>
        <div className="flex items-center gap-3">
          {selectedImages.length > 0 && (
            <div className="text-xs text-gray-500">
              {selectedImages.length} selected
            </div>
          )}
          {onPositionChange && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-gray-100"
              onClick={() => onPositionChange(position === 'left' ? 'bottom' : 'left')}
              title={position === 'left' ? 'Move to bottom' : 'Move to left'}
            >
              {position === 'left' ? (
                <SquareArrowOutDownRight className="w-4 h-4 text-gray-500" />
              ) : (
                <SquareArrowOutUpLeft className="w-4 h-4 text-gray-500" />
              )}
            </Button>
          )}
        </div>
      </div>
      
      {/* Conditional layout based on position */}
      {position === 'bottom' ? (
        // Bottom position: Horizontal layout
        <div className="flex-1 flex flex-row overflow-hidden">
          {/* Left side controls */}
          <div className="w-64 flex-shrink-0 px-4 py-2 border-r border-gray-200 space-y-2">
            {/* Show appropriate controls based on mode */}
            {isCompactMode ? (
              compactModeSection === 'filter' ? renderCompactFilterControls() :
              compactModeSection === 'rating' ? renderCompactRatingControls() :
              renderCompactSortControls()
            ) : renderFilterControls()}
            
            {/* Thumbnail Size Slider and Add More Photos Button (always shown in bottom position) */}
            {images.length > 0 && (
              <>
                {!isCompactMode && <Separator className="my-2" />}
                <div className="space-y-3">
                  {/* Thumbnail Size Slider */}
                  <div className="space-y-1">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <input
                            type="range"
                            min=".5"
                            max="2"
                            step="0.01"
                            value={thumbnailSize}
                            onChange={(e) => setThumbnailSize(parseFloat(e.target.value))}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                          />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Thumbnail Size</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  
                  {/* Add More Photos Button */}
                  <Button
                    variant="outline"
                    onClick={onAddImages}
                    className="w-full flex items-center justify-center gap-1"
                  >
                    <Upload className="w-4 h-4" />
                    <span>Add More Photos</span>
                  </Button>
                </div>
              </>
            )}
          </div>
          
          {/* Main content area - Right side */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {filteredAndSortedImages.length === 0 ? (
        <div className="flex-1 flex items-center justify-center p-4 overflow-hidden">
          {images.length === 0 ? (
            <div
              className={`
                p-4 flex flex-col items-center justify-center border-2 rounded-md w-full min-h-0
                ${isProcessingDroppedOnPlaceholder ? 'border-primary bg-primary/5 opacity-70' :
                  isDraggingOverPlaceholder ? 'border-primary bg-primary/5' : 'border-gray-200 hover:border-gray-300'}
                transition-colors duration-150 ease-in-out
              `}
              style={{ maxHeight: 'calc(100% - 16px)' }}
              onDragEnter={handleDragEnterPlaceholder}
              onDragOver={handleDragOverPlaceholder}
              onDragLeave={handleDragLeavePlaceholder}
              onDrop={handleDropOnPlaceholder}
            >
              {isProcessingDroppedOnPlaceholder && droppedOnPlaceholderProgress ? (
                <>
                  <Loader2 className="w-10 h-10 text-primary animate-spin mb-3" />
                  <p className="text-sm text-gray-700 mb-1 text-center">
                    {droppedOnPlaceholderProgress.stage === 'error' && droppedOnPlaceholderProgress.error ? (
                      <span className="text-red-600">{droppedOnPlaceholderProgress.error}</span>
                    ) : droppedOnPlaceholderProgress.stage === 'starting_import' ? (
                      `Preparing to import ${droppedOnPlaceholderProgress.total} images...`
                    ) : (
                      <>
                        Importing Images {droppedOnPlaceholderProgress.filename ? `: ${droppedOnPlaceholderProgress.filename}` : ''}<br />
                        ({droppedOnPlaceholderProgress.current} of {droppedOnPlaceholderProgress.total})
                      </>
                    )}
                  </p>
                  <Progress value={(droppedOnPlaceholderProgress.current / droppedOnPlaceholderProgress.total) * 100} className="w-3/4 mt-1" />
                </>
              ) : (
                <>
                  <Upload className={`${position === 'bottom' ? 'w-8 h-8' : 'w-10 h-10'} text-gray-300 ${position === 'bottom' ? 'mb-2' : 'mb-3'}`} />
                  <p className={`${position === 'bottom' ? 'text-xs' : 'text-sm'} text-gray-600 mb-1 text-center`}>
                    {isDraggingOverPlaceholder ? "Drop images here" : "Drag & drop images or click 'Import Images'"}
                  </p>
                  <p className={`text-xs text-gray-400 ${position === 'bottom' ? 'mb-2' : 'mb-4'} text-center`}>Supports JPG & PNG with embedded ICC color profiles</p>
                  <Button
                    variant="outline"
                    onClick={onAddImages} // This opens the UploadArea modal
                    className={position === 'bottom' ? 'text-xs py-1' : 'text-sm'}
                    disabled={isProcessingDroppedOnPlaceholder}
                  >
                    Import Images
                  </Button>
                </>
              )}
            </div>
          ) : (
            <div className="text-center p-8">
              <p className="text-sm text-gray-500">No {filterValue} images found matching your filters.</p>
              {filterValue !== "all" && (
                <Button
                  variant="outline"
                  onClick={() => setFilterValue("all")}
                  className="text-xs mt-2"
                >
                  Show all images
                </Button>
              )}
            </div>
          )}
        </div>
      ) : (
        // Container for the virtualized grid and the button below it
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Grid container with ref and overlay for handling selection */}
          <div 
            ref={gridContainerRef}
            className="flex-1 p-[5px] w-full overflow-auto relative" // Reduced right padding, will add margin in grid
            data-grid-container="true"
            style={{
              outline: 'dashed 1px rgba(100, 100, 100, 0.1)',
              cursor: 'default', // Ensure default cursor
              willChange: position === 'bottom' ? 'transform, contents' : 'auto' // Optimize for bottom position
            }}
            onMouseDown={handleGridContainerMouseDown} // Add mouse down handler here
          >
            {/* Explicitly check for positive dimensions before rendering ImageGrid */}
            {gridWidth > 0 && gridHeight > 0 && (
              // @ts-ignore
              <ImageGrid
                images={filteredAndSortedImages}
                selectedImages={selectedImages}
                usedImageIds={usedImageIds}
                handleImageClick={handleImageClick}
                handleDragStart={handleDragStart}
                width={gridWidth}
                height={gridHeight}
                showFilenames={showFilenames}
                showEndOfFilename={showEndOfFilename}
                onRemoveImages={onRemoveImages}
                columnCount={columnCount}
                gridRef={gridRef}
                onGridScroll={handleGridScroll}
                overscanColumnCount={Math.min(4, columnCount > 1 ? 2 * columnCount : 4)}
                overscanRowCount={Math.min(4, columnCount > 1 ? 2 * columnCount : 4)}
                setHoveredImageId={setHoveredImageId} // Pass down hover state setter
                showImagePreviewModal={showImagePreviewModal} // Pass down the preview function
                onAutoBuild={onAutoBuild} // Pass down the autobuild function
                setSelectedImagesForAutobuild={setSelectedImagesForAutobuild} // Pass down setter for selected images
                setIsAutobuildDialogOpen={setIsAutobuildDialogOpen} // Pass down setter for dialog open state
                theme={theme} // Pass theme to ImageGrid
              />
            )}
          </div>
        </div>
            )}
          </div>
        </div>
      ) : (
        // Left position: Vertical layout (original)
        <>
          {/* Filter controls */}
          <div className="px-4 py-2 border-b border-gray-200 space-y-2">
            {renderFilterControls()}
          </div>
          
          {filteredAndSortedImages.length === 0 ? (
            <div className="flex-1 flex items-start justify-center p-4 pt-8">
              {images.length === 0 ? (
                <div
                  className={`
                    p-8 flex flex-col items-center justify-center border-2 rounded-md w-full
                    ${isProcessingDroppedOnPlaceholder ? 'border-primary bg-primary/5 opacity-70' :
                      isDraggingOverPlaceholder ? 'border-primary bg-primary/5' : 'border-gray-200 hover:border-gray-300'}
                    transition-colors duration-150 ease-in-out
                  `}
                  onDragEnter={handleDragEnterPlaceholder}
                  onDragOver={handleDragOverPlaceholder}
                  onDragLeave={handleDragLeavePlaceholder}
                  onDrop={handleDropOnPlaceholder}
                >
                  {isProcessingDroppedOnPlaceholder && droppedOnPlaceholderProgress ? (
                    <>
                      <Loader2 className="w-10 h-10 text-primary animate-spin mb-3" />
                      <p className="text-sm text-gray-700 mb-1 text-center">
                        {droppedOnPlaceholderProgress.stage === 'error' && droppedOnPlaceholderProgress.error ? (
                          <span className="text-red-600">{droppedOnPlaceholderProgress.error}</span>
                        ) : droppedOnPlaceholderProgress.stage === 'starting_import' ? (
                          `Preparing to import ${droppedOnPlaceholderProgress.total} images...`
                        ) : (
                          <>
                            Importing Images {droppedOnPlaceholderProgress.filename ? `: ${droppedOnPlaceholderProgress.filename}` : ''}<br />
                            ({droppedOnPlaceholderProgress.current} of {droppedOnPlaceholderProgress.total})
                          </>
                        )}
                      </p>
                      <Progress value={(droppedOnPlaceholderProgress.current / droppedOnPlaceholderProgress.total) * 100} className="w-3/4 mt-1" />
                    </>
                  ) : (
                    <>
                      <Upload className="w-10 h-10 text-gray-300 mb-3" />
                      <p className="text-sm text-gray-600 mb-1 text-center">
                        {isDraggingOverPlaceholder ? "Drop images here" : "Drag & drop images or click 'Import Images'"}
                      </p>
                      <p className="text-xs text-gray-400 mb-4 text-center">Supports JPG & PNG with embedded ICC color profiles</p>
                      <Button
                        variant="outline"
                        onClick={onAddImages}
                        className="flex items-center justify-center gap-1"
                      >
                        <Upload className="w-4 h-4" />
                        <span>Import Images</span>
                      </Button>
                    </>
                  )}
                </div>
              ) : (
                <div className="text-center p-8">
                  <p className="text-sm text-gray-500">No {filterValue} images found matching your filters.</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setFilterValue('all');
                      setMinRating(0);
                    }}
                    className="mt-2 text-xs"
                  >
                    Show all images
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex-1 flex flex-col overflow-hidden">
              <div 
                ref={gridContainerRef}
                className="flex-1 p-[5px] w-full overflow-auto relative"
                data-grid-container="true"
                style={{
                  outline: 'dashed 1px rgba(100, 100, 100, 0.1)',
                  cursor: 'default'
                }}
                onMouseDown={handleGridContainerMouseDown}
              >
                {gridWidth > 0 && gridHeight > 0 && (
                  // @ts-ignore
                  <ImageGrid
                    images={filteredAndSortedImages}
                    selectedImages={selectedImages}
                    usedImageIds={usedImageIds}
                    handleImageClick={handleImageClick}
                    handleDragStart={handleDragStart}
                    width={gridWidth}
                    height={gridHeight}
                    showFilenames={showFilenames}
                    showEndOfFilename={showEndOfFilename}
                    onRemoveImages={onRemoveImages}
                    columnCount={columnCount}
                    gridRef={gridRef}
                    onGridScroll={handleGridScroll}
                    overscanColumnCount={Math.min(4, columnCount > 1 ? 2 * columnCount : 4)}
                    overscanRowCount={Math.min(4, columnCount > 1 ? 2 * columnCount : 4)}
                    setHoveredImageId={setHoveredImageId}
                    showImagePreviewModal={showImagePreviewModal}
                    onAutoBuild={onAutoBuild}
                    setSelectedImagesForAutobuild={setSelectedImagesForAutobuild}
                    setIsAutobuildDialogOpen={setIsAutobuildDialogOpen}
                    theme={theme}
                  />
                )}
              </div>
            </div>
          )}
          
          {images.length > 0 && (
            <>
              <Separator className="my-2" />
              <div className="p-4 pt-0 space-y-3">
                <div className="space-y-1">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <input
                          type="range"
                          min=".5"
                          max="2"
                          step="0.01"
                          value={thumbnailSize}
                          onChange={(e) => setThumbnailSize(parseFloat(e.target.value))}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Thumbnail Size</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                
                <Button
                  variant="outline"
                  onClick={onAddImages}
                  className="w-full flex items-center justify-center gap-1"
                >
                  <Upload className="w-4 h-4" />
                  <span>Add More Photos</span>
                </Button>
              </div>
            </>
          )}
        </>
      )}
      

      {/* Full Image Preview Modal */}
      <Dialog open={showFullImageModal} onOpenChange={(open) => {
        if (!open) {
          setImageExifData(null); // Clear EXIF data when closing
        }
        setShowFullImageModal(open);
      }}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] flex flex-col p-2 sm:p-4">
          {/* Add VisuallyHidden title for accessibility */}
          <VisuallyHidden>
            <DialogTitle>Image Preview</DialogTitle>
            {/* Add VisuallyHidden description for accessibility */}
            <DialogDescription>Full resolution preview of the selected image.</DialogDescription>
          </VisuallyHidden>
          
          {/* Image container */}
          <div className="relative w-full flex-1 flex items-center justify-center overflow-hidden"> {/* Added overflow-hidden */}
            {modalImageSrc ? (
              <img
                src={modalImageSrc}
                alt="Full resolution preview"
                className="max-w-full max-h-[80vh] object-contain" // Adjusted max-h to leave room for EXIF info
              />
            ) : (
              <p>Loading image...</p> // Placeholder while fetching or if src is null
            )}
          </div>
          
          {/* EXIF Information Section */}
          {(imageExifData || modalImageFilename) && ( // Show grey area if either EXIF or filename exists
            <div className="w-full mt-3 px-2 py-1.5 bg-gray-100 rounded text-xs text-gray-700 flex flex-wrap gap-x-3 gap-y-1 justify-center"> {/* Adjusted gap */}
              {/* Filename display */}
              {modalImageFilename && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">File:</span>
                  <span className="truncate" title={modalImageFilename}>{modalImageFilename}</span>
                </div>
              )}
              {/* Dimensions */}
              {imageExifData && imageExifData.dimensions && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Dimensions:</span>
                  <span>{imageExifData.dimensions.width} × {imageExifData.dimensions.height}px</span>
                </div>
              )}
              
              {/* Color Profile - Enhanced to show more details */}
              {imageExifData && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Color Profile:</span>
                  <span>
                    {/* For Unknown ICC Profiles, check if we have a ProfileClass or other identifiers */}
                    {imageExifData.colorProfile === "ICC Profile (Unknown)" && imageExifData.raw?.ICC
                      ? `ICC Profile (${imageExifData.raw.ICC.ProfileClass || "Unknown Type"})`
                      : imageExifData.colorProfile?.replace(" (default)", "") || "sRGB"}
                      
                    {/* Only show ColorSpaceData if it's not redundant with the profile name */}
                    {imageExifData.raw?.ICC?.ColorSpaceData &&
                     imageExifData.colorProfile && !imageExifData.colorProfile.includes(imageExifData.raw.ICC.ColorSpaceData) &&
                     ` • ${imageExifData.raw.ICC.ColorSpaceData}`}
                      
                    {/* Only show sRGB if it's not already mentioned in the profile name */}
                    {imageExifData.raw?.ColorSpace === 1 &&
                     imageExifData.colorProfile && !imageExifData.colorProfile.toLowerCase().includes('srgb') &&
                     ` • sRGB`}
                      
                    {/* Only show Adobe RGB if it's not already mentioned in the profile name */}
                    {imageExifData.raw?.ColorSpace === 2 &&
                     imageExifData.colorProfile && !imageExifData.colorProfile.toLowerCase().includes('adobe') &&
                     ` • Adobe RGB`}
                  </span>
                </div>
              )}
              
              {/* Star Rating */}
              {imageExifData && imageExifData.rating > 0 && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Rating:</span>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-3 h-3 ${star <= imageExifData.rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
                      />
                    ))}
                  </div>
                </div>
              )}
              
              {/* Time Captured */}
              {imageExifData && imageExifData.captureTime && (
                <div className="flex items-center gap-1">
                  <span className="font-medium">Captured:</span>
                  <span>{imageExifData.captureTime}</span>
                </div>
              )}
            </div>
          )}
          
          {/* Loading indicator for EXIF data */}
          {loadingExif && !imageExifData && (
            <div className="w-full mt-3 px-2 py-1.5 bg-gray-100 rounded text-xs text-gray-500 text-center">
              Loading image information...
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Autobuild Confirmation Dialog */}
      <AlertDialog open={isAutobuildDialogOpen} onOpenChange={setIsAutobuildDialogOpen}>
        <AlertDialogContent onKeyDown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            const actionButton = (e.currentTarget as HTMLElement).querySelector<HTMLButtonElement>('[data-alert-dialog-action="autobuild-confirm"]');
            actionButton?.click();
          }
        }}>
          <AlertDialogHeader>
            <AlertDialogTitle>Autobuild with Selected Images</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedImagesForAutobuild.length > 0 ? (
                `You have selected ${selectedImagesForAutobuild.length} image(s). Would you like to automatically build new spreads for the selected images?`
              ) : (
                "No images selected for autobuild."
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setIsAutobuildDialogOpen(false);
              setSelectedImagesForAutobuild([]);
            }}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              data-alert-dialog-action="autobuild-confirm"
              onClick={() => {
                if (selectedImagesForAutobuild.length > 0 && onAutoBuild) {
                  // Call the autobuild handler with the selected images
                  onAutoBuild(selectedImagesForAutobuild);
                  setIsAutobuildDialogOpen(false);
                  setSelectedImagesForAutobuild([]);
                }
              }}
              disabled={selectedImagesForAutobuild.length === 0 || !onAutoBuild}
            >
              Auto-build Spreads
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </div>
  );
});
ImageTray.displayName = "ImageTray"; // Add display name for DevTools
// --- Grid Cell Component ---
// Memoized to prevent unnecessary re-renders of individual cells
const Cell = memo(({ columnIndex, rowIndex, style, data }: {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    images: ImageFile[];
    selectedImages: string[];
    usedImageIds: string[];
    columnCount: number;
    handleImageClick: (id: string, index: number, e: React.MouseEvent) => void;
    handleDragStart: (e: React.DragEvent<HTMLDivElement>, image: ImageFile) => void;
    showFilenames: boolean;
    showEndOfFilename: boolean;
    onRemoveImages?: (imageIds: string[]) => void;
    setHoveredImageId: (id: string | null) => void; // Add hover state setter prop
    showImagePreviewModal: (image: ImageFile) => void; // Pass down the preview function
    onAutoBuild?: (images: ImageFile[], initialFit?: 'cover' | 'contain') => void; // Pass down the autobuild function
    showAutobuildConfirmation?: (imagesToBuild: ImageFile[]) => void; // Function to show autobuild confirmation dialog
    theme: 'light' | 'dark'; // Add theme to Cell data
  };
}) => {
  const { 
    images, 
    selectedImages, 
    usedImageIds, 
    columnCount, 
    handleImageClick, 
    handleDragStart, 
    showFilenames, 
    showEndOfFilename,
    onRemoveImages,
    setHoveredImageId, // Destructure the new prop
    showImagePreviewModal, // Destructure the preview function
    onAutoBuild, // Destructure the autobuild function
    showAutobuildConfirmation, // Destructure the function to show autobuild confirmation dialog
    theme // Destructure theme
  } = data;
  
  const index = rowIndex * columnCount + columnIndex;

  // Optimization: Don't render anything if the index is out of bounds
  if (index >= images.length) {
    return null;
  }

  const image = images[index];
  const isSelected = selectedImages.includes(image.id);
  const isUsed = usedImageIds.includes(image.id);

  // Determine badge based on usage
  const badge = isUsed ? (
    <div className="absolute top-1 right-1 bg-primary text-primary-foreground rounded-full w-3 h-3 flex items-center justify-center text-[7px] pointer-events-none">
      ✓
    </div>
  ) : null;

  return (
    <div style={style} className="p-1 pr-2"> {/* Added right padding to cells */}
      <ContextMenu>
        <ContextMenuTrigger asChild>
          {/* Container for image and optional filename */}
          <div className="flex flex-col h-full w-full">
            {/* Image Container */}
            <div
              key={image.id}
              data-image-id={image.id}
              data-image-cell="true"
              className={`
                flex-1 w-full rounded-md border overflow-hidden !cursor-pointer relative mb-1 {/* Added ! to force cursor style */}
                ${isSelected ? 'ring-2 ring-primary ring-offset-0' : 'border-gray-200 hover:border-gray-300'}
                ${isUsed ? 'opacity-60' : ''}
              `}
              draggable="true"
              onDragStart={(e) => handleDragStart(e, image)}
              onClick={(e) => {
                // Make sure image cell clicks propagate to handle selection 
                handleImageClick(image.id, index, e);
                // Stop selection from preventing drag behavior
                e.stopPropagation();
              }}
              onMouseEnter={() => setHoveredImageId(image.id)} // Set hovered ID on enter
              onMouseLeave={() => setHoveredImageId(null)}   // Clear hovered ID on leave
            >
              <img
                src={`${image.thumbnailUrl}?t=${Date.now()}`} // Use thumbnail URL + cache buster
                alt={image.name}
                // Use object-contain to maintain aspect ratio and fit within bounds
                className={`w-full h-full object-contain ${theme === 'dark' ? '' : 'bg-gray-50'}`} // Dark mode background handled by style
                style={{ 
                  filter: theme === 'dark' ? 'invert(1) hue-rotate(180deg)' : 'none',
                  backgroundColor: theme === 'dark' ? '#171717' : undefined
                }}
                loading="lazy"
                draggable="false"
              />
              {badge}
            </div>
            {/* Conditional Filename */}
            {showFilenames && (
              <div
                className="text-[11px] text-gray-500 text-center overflow-hidden whitespace-nowrap px-1" // Removed text-ellipsis, handled by logic below
                title={image.name} // Show full name on hover
              >
                {showEndOfFilename
                  ? (image.name.length > 15 ? `...${image.name.slice(-12)}` : image.name) // Show ...end
                  : image.name // Show start (CSS will handle ellipsis if needed via overflow-hidden)
                }
              </div>
            )}
          </div>
        </ContextMenuTrigger>
        <ContextMenuContent className="w-48">
          {/* NEW: Image Preview Item */}
          <ContextMenuItem
            onSelect={() => {
              showImagePreviewModal(image); // Call the preview function with the current cell's image
            }}
          >
            <Eye className="mr-2 h-4 w-4" />
            Image Preview
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem
            onSelect={() => {
              // Assuming an IPC function exists via preload script
              // Check if window.electronAPI and the function exist before calling
              if (typeof window.electronAPI?.showItemInFolder === 'function') {
                // Use the most up-to-date path for this image
                // First check if the file exists at the current path
                if (typeof window.electronAPI?.checkFilesExist === 'function') {
                  window.electronAPI.checkFilesExist([image.originalPath], true)
                    .then(results => {
                      if (results[0]) {
                        // File exists at current path, show it in finder
                        console.log(`[ImageTray] Revealing file at current path: ${image.originalPath}`);
                        window.electronAPI.showItemInFolder(image.originalPath);
                      } else {
                        // Try to get the latest path from the project data
                        console.log(`[ImageTray] File not found at ${image.originalPath}, checking for updated path...`);
                        
                        // Try to find the updated path in the spread elements
                        let foundUpdatedPath = false;
                        
                        // If we have access to the file availability hook, we can check there too
                        if (window.bookProofsApp?.getUpdatedFilePath) {
                          const updatedPath = window.bookProofsApp.getUpdatedFilePath(image.originalPath);
                          if (updatedPath) {
                            console.log(`[ImageTray] Found updated path: ${updatedPath}`);
                            window.electronAPI.showItemInFolder(updatedPath);
                            foundUpdatedPath = true;
                          }
                        }
                        
                        if (!foundUpdatedPath) {
                          // File doesn't exist and no updated path found, show an error
                          toast.error(`File not found at ${image.originalPath}. The file may have been moved or deleted.`);
                        }
                      }
                    })
                    .catch(error => {
                      console.error('Error checking file existence:', error);
                      // Try to show the file anyway
                      window.electronAPI.showItemInFolder(image.originalPath);
                    });
                } else {
                  // Fall back to just showing the file
                  window.electronAPI.showItemInFolder(image.originalPath);
                }
              } else {
                console.warn('electronAPI.showItemInFolder is not available.');
                // Optionally provide feedback to the user here, e.g., using a toast notification
                // alert('Could not reveal file: Feature not available.');
              }
            }}
          >
            <FolderSearch className="mr-2 h-4 w-4" />
            Reveal in Finder {/* TODO: Make OS-specific? Maybe check process.platform in main process */}
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem
            onMouseDown={(e) => e.stopPropagation()} // Prevent mousedown from propagating to grid container
            onSelect={async () => { // Make the handler async
              if (typeof window.electronAPI?.editInPhotoshop === 'function') {
                // Determine which images to edit based on selection
                const imagesToEditObjects = selectedImages.length > 0 ?
                  images.filter(img => selectedImages.includes(img.id)) :
                  [image]; // The image associated with the right-clicked cell

                // Check if selection exceeds the maximum limit
                if (imagesToEditObjects.length > 20) {
                  toast.warning('You can edit a maximum of 20 images in Photoshop at once.', {
                    description: `You have selected ${imagesToEditObjects.length} images.`
                  });
                  return;
                }

                const filePathsToEdit = imagesToEditObjects.map(img => {
                  let imagePath = img.originalPath;
                  if (window.bookProofsApp?.getUpdatedFilePath) {
                    const updatedPath = window.bookProofsApp.getUpdatedFilePath(img.originalPath);
                    if (updatedPath) {
                      console.log(`[ImageTray] Using updated path for Edit in Photoshop: ${updatedPath}`);
                      imagePath = updatedPath;
                    }
                  }
                  return imagePath;
                });

                if (filePathsToEdit.length === 0) {
                  toast.error('No valid image paths found to edit.');
                  return;
                }

                try {
                  // Send all file paths at once
                  await window.electronAPI.editInPhotoshop(filePathsToEdit);
                  
                  if (filePathsToEdit.length > 1) {
                    toast.success(`Sent ${filePathsToEdit.length} images to Photoshop.`);
                  } else {
                    toast.success(`Sent ${imagesToEditObjects[0].name} to Photoshop.`);
                  }
                } catch (error: any) {
                  console.error('Error sending images to Photoshop:', error);
                  const errorMessage = error.message || (error.toString ? error.toString() : "Unknown error");
                  toast.error(
                    `Failed to send images to Photoshop.`,
                    {
                      description: `${errorMessage}. Check console for details.`
                    }
                  );
                }
              } else {
                console.warn('electronAPI.editInPhotoshop is not available.');
                toast.error('Edit in Photoshop feature not available.');
              }
            }}
          >
            <Pencil className="mr-2 h-4 w-4" />
            Edit in Photoshop
          </ContextMenuItem>
          <ContextMenuSeparator />
          {/* Autobuild option - only show when multiple images are selected */}
          {selectedImages.length > 1 && (
            <>
              <ContextMenuItem
                onMouseDown={(e) => e.stopPropagation()} // Prevent mousedown from propagating to grid container
                onSelect={() => {
                  if (onAutoBuild && showAutobuildConfirmation) {
                    // Get the selected images
                    const selectedImageObjects = images.filter(img => selectedImages.includes(img.id));
                    // Show confirmation dialog instead of directly calling the handler
                    showAutobuildConfirmation(selectedImageObjects);
                  } else {
                    console.warn('onAutoBuild or showAutobuildConfirmation handler is not provided.');
                    toast.error('Autobuild feature is not available.');
                  }
                }}
              >
                <Image className="mr-2 h-4 w-4" />
                Autobuild
              </ContextMenuItem>
              <ContextMenuSeparator />
            </>
          )}
          <ContextMenuItem
            className="text-destructive focus:text-destructive focus:bg-destructive/10" // Destructive styling
            onMouseDown={(e) => e.stopPropagation()} // Prevent mousedown from propagating to grid container
            onSelect={() => {
              if (onRemoveImages) {
                // If there's any selection (one or more images), remove all selected images.
                // Otherwise (no images selected), remove just the right-clicked image.
                const idsToRemove = selectedImages.length > 0 ? selectedImages : [image.id];
                onRemoveImages(idsToRemove);
              } else {
                console.warn('onRemoveImages handler is not provided to ImageTray.');
              }
            }}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Remove
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    </div>
  );
});
Cell.displayName = 'GridCell'; // Add display name for React DevTools

// --- ImageGrid Component ---
// This component wraps the FixedSizeGrid
interface ImageGridProps {
  images: ImageFile[];
  selectedImages: string[];
  usedImageIds: string[];
  handleImageClick: (id: string, index: number, e: React.MouseEvent) => void;
  handleDragStart: (e: React.DragEvent<HTMLDivElement>, image: ImageFile) => void;
  width: number;
  height: number;
  showFilenames?: boolean;
  showEndOfFilename?: boolean;
  onRemoveImages?: (imageIds: string[]) => void;
  columnCount?: number;
  gridRef?: React.RefObject<any>; // Add gridRef prop
  onGridScroll?: (scrollInfo: { scrollLeft: number; scrollTop: number }) => void; // Add scroll handler prop
  overscanColumnCount?: number; // Add overscanColumnCount prop
  overscanRowCount?: number; // Add overscanRowCount prop
  setHoveredImageId: (id: string | null) => void; // Add hover state setter prop
  showImagePreviewModal: (image: ImageFile) => void; // Pass down the preview function
  onAutoBuild?: (images: ImageFile[], initialFit?: 'cover' | 'contain') => void; // Pass down the autobuild function
  setSelectedImagesForAutobuild: (images: ImageFile[]) => void; // Function to set selected images for autobuild
  setIsAutobuildDialogOpen: (isOpen: boolean) => void; // Function to open/close autobuild dialog
  theme: 'light' | 'dark'; // Add theme prop to ImageGridProps
}

const ImageGrid = ({
  images,
  selectedImages,
  usedImageIds,
  handleImageClick,
  handleDragStart,
  width,
  height,
  showFilenames,
  showEndOfFilename,
  onRemoveImages,
  columnCount = 2, // Default to 2 columns, but accept dynamic count
  gridRef, // Accept gridRef from parent
  onGridScroll, // Accept scroll handler from parent
  overscanColumnCount = 5, // Default overscanColumnCount
  overscanRowCount = 5, // Default overscanRowCount
  setHoveredImageId, // Accept hover state setter
  showImagePreviewModal, // Pass down the preview function
  onAutoBuild, // Pass down the autobuild function
  setSelectedImagesForAutobuild, // Pass down setter for selected images
  setIsAutobuildDialogOpen, // Pass down setter for dialog open state
  theme // Destructure theme prop
}: ImageGridProps) => {
  // Calculate cell dimensions based on available width and column count
  // Subtract scrollbar width plus a small margin
  const adjustedWidth = width - 10; // Reduced allowance but keep small margin from scrollbar
  
  // Calculate how many columns can fit, ensuring at least 1
  // Use gridWidth directly; ImageGrid will account for its internal padding
  const columnWidth = adjustedWidth / columnCount;
  
// Diagnostic log
  const rowCount = Math.ceil(images.length / columnCount);

  // Data object to pass to the Cell component via itemData prop
  const itemData = {
    images,
    selectedImages,
    usedImageIds,
    columnCount,
    handleImageClick,
    handleDragStart,
    showFilenames,
    showEndOfFilename,
    onRemoveImages,
    setHoveredImageId, // Pass down hover state setter
    showImagePreviewModal, // Pass down the preview function
    onAutoBuild, // Pass down the autobuild function
    showAutobuildConfirmation: (imagesToBuild: ImageFile[]) => {
      setSelectedImagesForAutobuild(imagesToBuild);
      setIsAutobuildDialogOpen(true);
    }, // Pass down function to show autobuild confirmation
    theme: theme // Pass theme to Cell data
  };

  return (
    <FixedSizeGrid
      ref={gridRef} // Use the ref passed from parent
      columnCount={columnCount}
      columnWidth={columnWidth}
      height={height}
      rowCount={rowCount}
      rowHeight={columnWidth}
      width={width}
      itemData={itemData}
      className="react-window-grid"
      onScroll={onGridScroll ? ({ scrollLeft, scrollTop }) => onGridScroll({ scrollLeft, scrollTop }) : undefined}
      overscanColumnCount={overscanColumnCount} // Use overscanColumnCount
      overscanRowCount={overscanRowCount}    // Use overscanRowCount
    >
      {Cell}
    </FixedSizeGrid>
  );
};


export default ImageTray;
