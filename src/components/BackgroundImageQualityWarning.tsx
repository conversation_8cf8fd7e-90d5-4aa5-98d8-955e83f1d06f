import React from 'react';
import { AlertTriangle } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ImageQualityInfo, ImageQualityStatus } from '@/lib/imageQuality';

interface BackgroundImageQualityWarningProps {
  qualityInfo: ImageQualityInfo;
}

/**
 * Component to display a warning icon when the background image has quality issues
 * This is a modified version of ImageQualityWarning specifically for the background image
 */
const BackgroundImageQualityWarning: React.FC<BackgroundImageQualityWarningProps> = ({
  qualityInfo
}) => {
  // Determine icon color based on quality status
  const getIconColor = () => {
    switch (qualityInfo.status) {
      case ImageQualityStatus.WARNING:
        return 'text-amber-500';
      case ImageQualityStatus.POOR:
        return 'text-red-500';
      default:
        return 'text-green-500';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div
            className="absolute top-2 left-2 z-[100] cursor-pointer"
            aria-label="Background image quality warning"
          >
            <div className="bg-black/20 rounded-full p-1">
              <AlertTriangle
                className={`h-4 w-4 ${getIconColor()} drop-shadow-md`}
                strokeWidth={2.5}
              />
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom" align="start" className="max-w-xs z-[110]">
          <div className="text-sm">
            <p className="font-medium">{qualityInfo.message}</p>
            <p className="text-xs mt-1 text-gray-500">
              This background image may not print well at 300 DPI. Consider using a higher resolution image or reducing the zoom level.
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default BackgroundImageQualityWarning;
