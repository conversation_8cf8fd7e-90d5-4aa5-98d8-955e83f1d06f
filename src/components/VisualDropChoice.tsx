import React, { useMemo, useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { Plus, Replace } from 'lucide-react';

interface VisualDropChoiceProps {
  isVisible: boolean;
  onChoiceSelect: (choice: 'replace' | 'add') => void;
  placeholderId: string;
  onProcessDrop?: (e: React.DragEvent, placeholderId: string) => void;
  onDragLeave?: () => void;
  bleedAreaMode?: 'hide' | 'indicate' | 'ignore';
  bleedInPixels?: number;
  outsideEdges?: {
    top: boolean;
    bottom: boolean;
    left: boolean;
    right: boolean;
  };
}

export const VisualDropChoice: React.FC<VisualDropChoiceProps> = ({
  isVisible,
  onChoiceSelect,
  placeholderId,
  onProcessDrop,
  onDragLeave,
  bleedAreaMode = 'indicate',
  bleedInPixels = 0,
  outsideEdges = { top: false, bottom: false, left: false, right: false }
}) => {
  const [hoveredSide, setHoveredSide] = useState<'replace' | 'add' | null>(null);
  const [isDragActive, setIsDragActive] = useState(false);
  const cleanupTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const dragLeaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get placeholder element and calculate bounds (memoized to prevent excessive updates)
  const bounds = useMemo(() => {
    if (!isVisible || !placeholderId) {
      return null;
    }
    const placeholderElement = document.getElementById(`placeholder-${placeholderId}`);
    if (!placeholderElement) {
      return null;
    }
    
    const rect = placeholderElement.getBoundingClientRect();
    
    // If bleed area is hidden, adjust bounds only for outside edges
    if (bleedAreaMode === 'hide' && bleedInPixels > 0) {
      const leftAdjustment = outsideEdges.left ? bleedInPixels : 0;
      const topAdjustment = outsideEdges.top ? bleedInPixels : 0;
      const rightAdjustment = outsideEdges.right ? bleedInPixels : 0;
      const bottomAdjustment = outsideEdges.bottom ? bleedInPixels : 0;
      
      return {
        left: rect.left + leftAdjustment,
        top: rect.top + topAdjustment,
        width: rect.width - leftAdjustment - rightAdjustment,
        height: rect.height - topAdjustment - bottomAdjustment,
        right: rect.right - rightAdjustment,
        bottom: rect.bottom - bottomAdjustment,
      };
    }
    
    return rect;
  }, [isVisible, placeholderId, bleedAreaMode, bleedInPixels, outsideEdges]);

  // Effect to handle global drag events and cleanup
  useEffect(() => {
    if (!isVisible) {
      setIsDragActive(false);
      setHoveredSide(null);
      return;
    }

    // Set drag as active when overlay becomes visible
    setIsDragActive(true);

    // Global dragend listener to ensure cleanup
    const handleGlobalDragEnd = () => {
      setIsDragActive(false);
      setHoveredSide(null);
      // Trigger cleanup with a small delay to ensure any pending drops are processed
      cleanupTimeoutRef.current = setTimeout(() => {
        onDragLeave?.();
      }, 100);
    };

    // Global dragover listener to detect when drag is still active
    const handleGlobalDragOver = (e: DragEvent) => {
      // If we detect a dragover outside our overlay, start a timeout to clean up
      const overlayElement = document.querySelector('[data-visual-drop-choice="true"]');
      if (overlayElement && !overlayElement.contains(e.target as Node)) {
        if (dragLeaveTimeoutRef.current) {
          clearTimeout(dragLeaveTimeoutRef.current);
        }
        dragLeaveTimeoutRef.current = setTimeout(() => {
          if (isDragActive) {
            setIsDragActive(false);
            setHoveredSide(null);
            onDragLeave?.();
          }
        }, 150);
      } else {
        // We're back over the overlay, cancel the cleanup timeout
        if (dragLeaveTimeoutRef.current) {
          clearTimeout(dragLeaveTimeoutRef.current);
          dragLeaveTimeoutRef.current = null;
        }
      }
    };

    // Add global listeners
    document.addEventListener('dragend', handleGlobalDragEnd);
    document.addEventListener('dragover', handleGlobalDragOver);

    // Cleanup function
    return () => {
      document.removeEventListener('dragend', handleGlobalDragEnd);
      document.removeEventListener('dragover', handleGlobalDragOver);
      if (cleanupTimeoutRef.current) {
        clearTimeout(cleanupTimeoutRef.current);
      }
      if (dragLeaveTimeoutRef.current) {
        clearTimeout(dragLeaveTimeoutRef.current);
      }
    };
  }, [isVisible, isDragActive, onDragLeave]);

  if (!isVisible || !placeholderId || !bounds) {
    return null;
  }
  
  const handleDrop = (e: React.DragEvent, choice: 'replace' | 'add') => {
    e.preventDefault();
    e.stopPropagation();
    
    // First set the choice
    onChoiceSelect(choice);
    
    // Then trigger the actual drop processing
    if (onProcessDrop) {
      onProcessDrop(e, placeholderId);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Cancel any pending cleanup when actively dragging over
    if (dragLeaveTimeoutRef.current) {
      clearTimeout(dragLeaveTimeoutRef.current);
      dragLeaveTimeoutRef.current = null;
    }
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Cancel any pending cleanup when entering
    if (dragLeaveTimeoutRef.current) {
      clearTimeout(dragLeaveTimeoutRef.current);
      dragLeaveTimeoutRef.current = null;
    }
    
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Clear any existing timeout
    if (dragLeaveTimeoutRef.current) {
      clearTimeout(dragLeaveTimeoutRef.current);
    }

    // Use timeout to debounce and ensure we're truly leaving
    dragLeaveTimeoutRef.current = setTimeout(() => {
      const currentTarget = e.currentTarget as HTMLElement;
      const relatedTarget = e.relatedTarget as Node;
      
      // Check if we're truly leaving the overlay container
      if (!currentTarget || !relatedTarget || !currentTarget.contains(relatedTarget)) {
        setHoveredSide(null);
        setIsDragActive(false);
        onDragLeave?.();
      }
    }, 100);
  };

  const handleSideDragEnter = (side: 'replace' | 'add') => {
    setHoveredSide(side);
  };

  const handleSideDragLeave = (e: React.DragEvent, side: 'replace' | 'add') => {
    // Use a small timeout to debounce and allow for more reliable hover state management
    setTimeout(() => {
      const currentTarget = e.currentTarget as HTMLElement;
      const relatedTarget = e.relatedTarget as Node;
      
      // Only clear hover state if we're truly leaving this side and not in the other side
      if (currentTarget && relatedTarget && !currentTarget.contains(relatedTarget)) {
        // Check if we're moving to the other side - if so, don't clear yet
        const parentContainer = currentTarget.parentElement;
        if (parentContainer && !parentContainer.contains(relatedTarget)) {
          setHoveredSide(null);
        }
      }
    }, 10);
  };

  // Create the overlay content
  const overlayContent = (
    <div
      data-visual-drop-choice="true"
      className="fixed z-[9999] pointer-events-auto"
      style={{
        left: bounds.left,
        top: bounds.top,
        width: bounds.width,
        height: bounds.height,
      }}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
    >
      {/* Semi-transparent overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-30 pointer-events-none" />
      
      {/* Vertical divider line */}
      <div className="absolute top-0 bottom-0 left-1/2 w-px bg-white opacity-80 transform -translate-x-1/2 pointer-events-none" />
      
      {/* Left side - Replace */}
      <div
        className={`replace-side absolute left-0 top-0 w-1/2 h-full flex flex-col items-center justify-center cursor-pointer transition-colors duration-150 pointer-events-auto ${
          hoveredSide === 'replace' ? 'bg-white bg-opacity-5' : ''
        }`}
        onDragOver={handleDragOver}
        onDragEnter={() => handleSideDragEnter('replace')}
        onDragLeave={(e) => handleSideDragLeave(e, 'replace')}
        onDrop={(e) => handleDrop(e, 'replace')}
      >
        <Replace className="h-6 w-6 text-white mb-2 pointer-events-none" />
        <span className="text-white text-xs font-medium text-center px-2 pointer-events-none">
          Replace Current Image
        </span>
      </div>
      
      {/* Right side - Add */}
      <div
        className={`add-side absolute right-0 top-0 w-1/2 h-full flex flex-col items-center justify-center cursor-pointer transition-colors duration-150 pointer-events-auto ${
          hoveredSide === 'add' ? 'bg-white bg-opacity-5' : ''
        }`}
        onDragOver={handleDragOver}
        onDragEnter={() => handleSideDragEnter('add')}
        onDragLeave={(e) => handleSideDragLeave(e, 'add')}
        onDrop={(e) => handleDrop(e, 'add')}
      >
        <Plus className="h-6 w-6 text-white mb-2 pointer-events-none" />
        <span className="text-white text-xs font-medium text-center px-2 pointer-events-none">
          Add to Spread
        </span>
      </div>
    </div>
  );

  // Use portal to render outside document flow - this prevents DOM hierarchy issues
  return createPortal(overlayContent, document.body);
};

export default VisualDropChoice;