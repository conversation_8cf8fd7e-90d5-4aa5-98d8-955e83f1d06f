import React, { createContext, useContext, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Define the context type
type ProofingContextType = {
  showImageNumbers: boolean;
  toggleImageNumbers: () => void;
};

// Create context with default values
const ProofingContext = createContext<ProofingContextType>({
  showImageNumbers: false,
  toggleImageNumbers: () => {},
});

// Custom hook to use the proofing context
export const useProofing = () => useContext(ProofingContext);

// Provider component
export const ProofingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [showImageNumbers, setShowImageNumbers] = useState(false);

  const toggleImageNumbers = () => {
    setShowImageNumbers(prev => !prev);
  };

  return (
    <ProofingContext.Provider value={{ showImageNumbers, toggleImageNumbers }}>
      {children}
    </ProofingContext.Provider>
  );
};

// Image Number Overlay Component
export interface ImageNumberOverlayProps { // Added export
  imageIndex: number;
  placeholderId: string;
  imageRenderStyle?: { // CSS style values (e.g., "10px")
    left: string;
    top: string;
    width: string;
    height: string;
  };
  placeholderPixelDimensions?: { // Raw numbers
    width: number;
    height: number;
  };
  bleedAreaMode?: 'hide' | 'indicate' | 'ignore'; // Added bleed area mode
  bleedInPixels?: number; // Added bleed value in pixels
}

export const ImageNumberOverlay: React.FC<ImageNumberOverlayProps> = ({
  imageIndex,
  placeholderId,
  imageRenderStyle,
  placeholderPixelDimensions,
  bleedAreaMode = 'indicate',
  bleedInPixels = 0,
}) => {
  const { showImageNumbers } = useProofing();

  if (!showImageNumbers || !imageRenderStyle || !placeholderPixelDimensions) {
    return null;
  }

  const imgLeftPx = parseFloat(imageRenderStyle.left);
  const imgTopPx = parseFloat(imageRenderStyle.top);
  const imgWidthPx = parseFloat(imageRenderStyle.width);
  const imgHeightPx = parseFloat(imageRenderStyle.height);

  const phWidthPx = placeholderPixelDimensions.width;
  const phHeightPx = placeholderPixelDimensions.height;

  // Calculate the visible rectangle of the image within the placeholder
  const visImgX = Math.max(0, imgLeftPx);
  const visImgY = Math.max(0, imgTopPx);
  const visImgRight = Math.min(phWidthPx, imgLeftPx + imgWidthPx);
  const visImgBottom = Math.min(phHeightPx, imgTopPx + imgHeightPx);

  const visImgW = visImgRight - visImgX;
  const visImgH = visImgBottom - visImgY;

  // If the image is not visibly within the placeholder, don't show the overlay
  if (visImgW <= 0 || visImgH <= 0) {
    return null;
  }

  const overlayMargin = 4; // px

  // Position the overlay at the bottom-left of the visible image area
  let finalOverlayLeft = visImgX + overlayMargin;
  let finalOverlayBottom = (phHeightPx - visImgBottom) + overlayMargin;

  // Adjust positioning when hide bleed mode is active - push all numbers slightly right and up
  if (bleedAreaMode === 'hide' && bleedInPixels > 0) {
    finalOverlayLeft += 4; // Push 4px right
    finalOverlayBottom += 4; // Push 4px up
  }

  const style: React.CSSProperties = {
    position: 'absolute',
    left: `${finalOverlayLeft}px`,
    bottom: `${finalOverlayBottom}px`,
    zIndex: 10, // Lowered zIndex
    backgroundColor: 'rgba(0,0,0,0.75)',
    color: 'white',
    paddingTop: '0.125rem', // 2px
    paddingBottom: '0.125rem', // 2px
    paddingLeft: '0.5rem', // 8px
    paddingRight: '0.5rem', // 8px
    borderRadius: '0.375rem', // rounded-md (6px)
    fontSize: '0.75rem', // text-xs (12px)
    lineHeight: '1rem', // Tailwind text-xs typically has 1rem line height
    fontWeight: 'bold',
    pointerEvents: 'none',
    minWidth: '1.5rem', // 24px
    textAlign: 'center',
    whiteSpace: 'nowrap', // Prevent wrapping
  };

  return (
    <div
      style={style}
      data-no-print="true"
      data-proofing-overlay="true"
      id={`image-number-${placeholderId}`}
    >
      {imageIndex}
    </div>
  );
};

// Proofing Controls Component
// This component can be placed in the app toolbar or settings panel
export const ProofingControls: React.FC = () => {
  const { showImageNumbers, toggleImageNumbers } = useProofing();
  
  return (
    <div className="flex items-center space-x-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-2">
              <Switch 
                id="show-image-numbers" 
                checked={showImageNumbers} 
                onCheckedChange={toggleImageNumbers}
              />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Show/hide image number overlays for proofing</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}; 