
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface AspectRatioCardProps {
  title: string; // Will be used for dropdowns, not displayed on card if displayText is present
  ratio: string;
  dimensions: string; // Will be used for dropdowns, not displayed on card if displayText is present
  selected?: boolean;
  onClick: () => void;
  className?: string;
  displayText?: string; // New prop for text inside the card
}

const AspectRatioCard = ({
  title,
  ratio,
  dimensions, // Keep for data integrity, but don't display if displayText is used
  selected = false,
  onClick,
  className = '',
  displayText
}: AspectRatioCardProps) => {
  const [widthNum, heightNum] = ratio.split(':').map(num => parseFloat(num));
  const cardAspectRatio = widthNum / heightNum;
  
  // The h3 title below the card will only show if displayText is NOT provided.
  // If displayText is provided, it means this card is a preview, and the title/dimensions are in the dropdown.
  const cardLabel = displayText ? "" : title;

  return (
    <Card
      className={`w-full cursor-pointer transition-all hover:shadow-md ${selected ? 'ring-2 ring-primary shadow-md' : 'border-app-border'} ${className}`}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div
          className="w-full bg-app-tray rounded-md border border-app-border mb-3"
          style={{
            paddingTop: `${(1 / cardAspectRatio) * 100}%`, // Use cardAspectRatio
            position: 'relative'
          }}
        >
          <div className="absolute inset-0 flex flex-col items-center justify-center p-1">
            {displayText ? (
              <span className="text-sm font-medium">{displayText}</span>
            ) : (
              // Fallback to dimensions if no displayText (original behavior for non-preview cards)
              <div className="text-xs leading-tight text-center">
                 <div className="mb-0.5">{dimensions}</div>
              </div>
            )}
          </div>
          {selected && (
            <div className="absolute inset-0 bg-primary/10 pointer-events-none"></div>
          )}
        </div>
        {cardLabel && ( // Only show h3 if cardLabel is not empty
          <h3 className="font-medium text-sm text-center">{cardLabel}</h3>
        )}
      </CardContent>
    </Card>
  );
};

export default AspectRatioCard;
