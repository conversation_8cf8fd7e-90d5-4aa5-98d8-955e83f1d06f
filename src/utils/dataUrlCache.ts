// Shared cache for Data URLs to prevent duplicate fetches across components
export const dataUrlCache = new Map<string, string>();

// Cache for tracking which paths are currently being fetched to prevent duplicate requests
const pendingFetches = new Map<string, Promise<string | null>>();

// Cache for tracking processed timestamps to avoid re-processing the same photoshop round trip across component instances
const processedTimestamps = new Set<string>();

// Helper function to get normalized path key for cache
export const getNormalizedPathKey = (path: string): string => {
  let normalizedPath = path;
  
  // Strip safe-file:// prefix if present
  if (normalizedPath.startsWith('safe-file://')) {
    normalizedPath = normalizedPath.substring('safe-file://'.length);
  }
  
  // Remove query parameters like ?t=timestamp
  normalizedPath = normalizedPath.split('?')[0];
  
  return normalizedPath;
};

// Helper function to get cached Data URL
export const getCachedDataUrl = (path: string): string | undefined => {
  const key = getNormalizedPathKey(path);
  return dataUrlCache.get(key);
};

// Helper function to cache Data URL
export const setCachedDataUrl = (path: string, dataUrl: string): void => {
  const key = getNormalizedPathKey(path);
  dataUrlCache.set(key, dataUrl);
};

// Helper function to clear cached Data URL (for file updates)
export const clearCachedDataUrl = (path: string): void => {
  const key = getNormalizedPathKey(path);
  dataUrlCache.delete(key);
  // Also clear any pending fetches for this path
  pendingFetches.delete(key);
};

// Helper function to check if timestamp has been processed
export const isTimestampProcessed = (timestamp: string): boolean => {
  return processedTimestamps.has(timestamp);
};

// Helper function to mark timestamp as processed
export const markTimestampProcessed = (timestamp: string): void => {
  processedTimestamps.add(timestamp);
};

// Helper function to fetch Data URL with deduplication
export const fetchDataUrl = async (path: string): Promise<string | null> => {
  const key = getNormalizedPathKey(path);
  
  // Check cache first
  const cached = dataUrlCache.get(key);
  if (cached) {
    return cached;
  }
  
  // Check if already fetching this path
  const pending = pendingFetches.get(key);
  if (pending) {
    return pending;
  }
  
  // Start new fetch
  const fetchPromise = window.electronAPI?.getImageDataUrl(key)
    .then(result => {
      if (result.success && result.dataUrl) {
        setCachedDataUrl(path, result.dataUrl);
        return result.dataUrl;
      }
      return null;
    })
    .catch(error => {
      console.error(`Error fetching Data URL for ${key}:`, error);
      return null;
    })
    .finally(() => {
      // Remove from pending fetches when done
      pendingFetches.delete(key);
    });
  
  if (fetchPromise) {
    pendingFetches.set(key, fetchPromise);
    return fetchPromise;
  }
  
  return null;
};