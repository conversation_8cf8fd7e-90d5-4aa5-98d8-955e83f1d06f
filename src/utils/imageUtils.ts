/**
 * Utility functions for handling images in the application
 */

/**
 * Normalizes the URL format for project background images to ensure consistency
 * across the application. This prevents issues with broken image links on reload.
 * 
 * @param url - The URL to normalize
 * @returns Normalized URL with proper protocol prefix
 */
export function normalizeBackgroundImageUrl(url: string | null): string | null {
  if (!url) return null;
  
  // Remove any cache busting parameters
  const baseUrl = url.split('?')[0];
  
  // If it's already a persistent URL format, return as is
  if (baseUrl.startsWith('safe-file://')) {
    return baseUrl;
  }
  
  // If it's a file:// URL, convert to safe-file:// format
  if (baseUrl.startsWith('file://')) {
    return `safe-file://${baseUrl.substring(7)}`;
  }
  
  // If it's a blob: URL, leave as is (it will be replaced later with a persistent URL)
  if (baseUrl.startsWith('blob:')) {
    return baseUrl;
  }
  
  // For all other URLs, prefix with safe-file:// to ensure it loads correctly
  return `safe-file://${baseUrl}`;
}

/**
 * Extracts the original path from a normalized background image URL
 * 
 * @param url - The normalized URL
 * @returns The original path without safe-file:// prefix
 */
export function getOriginalPathFromUrl(url: string | null): string | null {
  if (!url) return null;
  
  // Remove cache busting parameters
  const baseUrl = url.split('?')[0];
  
  // Remove safe-file:// prefix if present
  if (baseUrl.startsWith('safe-file://')) {
    return baseUrl.substring(12);
  }
  
  // For file:// URLs, just return the path portion
  if (baseUrl.startsWith('file://')) {
    return baseUrl.substring(7);
  }
  
  // For all other URLs, return as is
  return baseUrl;
}
