import React, { createContext, useState, useContext, useCallback, useEffect, ReactNode } from 'react';

// Define the shape of the hover info
interface CanvasHoverInfo {
  placeholderId: string | null;
  isZoomed: boolean;
}

// Define the shape of the context data
interface StatusBarContextType {
  canvasHoverInfo: CanvasHoverInfo;
  setCanvasHoverInfo: (info: CanvasHoverInfo) => void;
  isOptionKeyPressed: boolean;
}

// Create the context with a default value
const StatusBarContext = createContext<StatusBarContextType | undefined>(undefined);

// Create the provider component
interface StatusBarProviderProps {
  children: ReactNode;
}

export const StatusBarProvider: React.FC<StatusBarProviderProps> = ({ children }) => {
  const [canvasHoverInfo, setCanvasHoverInfoState] = useState<CanvasHoverInfo>({ placeholderId: null, isZoomed: false });
  const [isOptionKeyPressed, setIsOptionKeyPressed] = useState<boolean>(false);

  // Use useCallback for the setter function if needed elsewhere, though direct state setter is often fine
  const setCanvasHoverInfo = useCallback((info: CanvasHoverInfo) => {
    setCanvasHoverInfoState(info);
  }, []);

  // Add global event listeners for Option key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Alt' || e.key === 'Option') {
        setIsOptionKeyPressed(true);
        console.log('[StatusBarContext] Option key pressed');
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === 'Alt' || e.key === 'Option') {
        setIsOptionKeyPressed(false);
        console.log('[StatusBarContext] Option key released');
      }
    };
    
    // Also track Option key state using altKey property
    const handleGlobalMouseMove = (e: MouseEvent) => {
      // During drag operations, keyup events might not fire reliably
      // So we also check the altKey state on mouse move
      const newOptionKeyState = e.altKey;
      if (newOptionKeyState !== isOptionKeyPressed) {
        console.log('[StatusBarContext] Option key state changed during mouse move:', newOptionKeyState);
        setIsOptionKeyPressed(newOptionKeyState);
      }
    };
    
    // Track Option key state during drag operations
    const handleDragOver = (e: DragEvent) => {
      // Always check the current altKey state during dragover
      // This ensures we always have the latest state
      const newOptionKeyState = e.altKey;
      
      // Update state regardless of previous state to ensure we're always in sync
      // This is more aggressive but ensures the state is always correct
      if (newOptionKeyState !== isOptionKeyPressed) {
        console.log('[StatusBarContext] Option key state changed during drag:', newOptionKeyState);
        setIsOptionKeyPressed(newOptionKeyState);
      }
    };
    
    // Also track during drag events
    const handleDrag = (e: DragEvent) => {
      const newOptionKeyState = e.altKey;
      if (newOptionKeyState !== isOptionKeyPressed) {
        console.log('[StatusBarContext] Option key state changed during drag event:', newOptionKeyState);
        setIsOptionKeyPressed(newOptionKeyState);
      }
    };
    
    // Add global event listeners
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    window.addEventListener('mousemove', handleGlobalMouseMove);
    window.addEventListener('dragover', handleDragOver);
    window.addEventListener('drag', handleDrag);
    
    // Clean up event listeners
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('mousemove', handleGlobalMouseMove);
      window.removeEventListener('dragover', handleDragOver);
      window.removeEventListener('drag', handleDrag);
    };
  }, [isOptionKeyPressed]);

  const value = { canvasHoverInfo, setCanvasHoverInfo, isOptionKeyPressed };

  return (
    <StatusBarContext.Provider value={value}>
      {children}
    </StatusBarContext.Provider>
  );
};

// Create a custom hook for easy context consumption
export const useStatusBar = (): StatusBarContextType => {
  const context = useContext(StatusBarContext);
  if (context === undefined) {
    throw new Error('useStatusBar must be used within a StatusBarProvider');
  }
  return context;
};