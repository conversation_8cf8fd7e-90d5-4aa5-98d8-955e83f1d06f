/* Resizing styles */
.resizing {
  cursor: ew-resize;
  user-select: none;
}

/* Modify resizing cursor for horizontal resize */
body.resizing .spread-tray-resize-handle {
  cursor: ns-resize !important;
}

/* Status bar container styles */
.status-bar-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 4.5rem; /* Added minimum height for approximately 3 lines of text */
}

/* Always visible resize handle styles */
.image-tray-resize-handle,
.spread-tray-resize-handle {
  /* Removed opacity: 0 and transition */
  /* Add a subtle highlight */
  background: linear-gradient(
    to right,
    rgba(248, 250, 252, 0) 0%,
    rgba(248, 250, 252, 0.02) 100%
  );
}

/* Horizontal gradient for the spread tray resize handle */
.spread-tray-resize-handle {
  background: linear-gradient(
    to bottom,
    rgba(248, 250, 252, 0) 0%,
    rgba(248, 250, 252, 0.02) 100%
  );
}

/* Removed hover/focus styles that changed opacity */

/* The actual visual indicator for the resize handle */
.resize-handle-line {
  position: relative;
  height: 140px;
  width: 2px; /* Increased from 2px to 4px for better visibility */
  /* Create a subtle gradient with higher opacity in the middle */
  background: linear-gradient(
    to bottom,
    rgba(203, 213, 225, 0.1) 0%,
    rgba(203, 213, 225, 0.8) 50%,
    rgba(203, 213, 225, 0.1) 100%
  );
  /* Add a gentle shadow for better visibility */
  box-shadow: 0 0 2px rgba(203, 213, 225, 0.3);
  opacity: 0.8; /* Increased base opacity */
  transition: all 0.2s ease; /* Keep transition for resizing state */
}

/* Horizontal resize handle line */
.resize-handle-line.horizontal {
  height: 1px;
  width: 40px;
  /* Create a subtle gradient with higher opacity in the middle */
  background: linear-gradient(
    to right,
    rgba(203, 213, 225, 0.1) 0%,
    rgba(203, 213, 225, 0.8) 50%,
    rgba(203, 213, 225, 0.1) 100%
  );
}

/* Centered dot for better visibility */
.resize-handle-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background-color: rgba(148, 163, 184, 0.6);
  opacity: 0.9; /* Increased base opacity */
  transition: all 0.2s ease; /* Keep transition for resizing state */
}

/* Removed hover styles for line and dot */

/* Responsive text adjustments for narrow widths */
@media (max-width: 240px) {
  .status-bar-container {
    font-size: 0.65rem;
  }
}

/* Ensure content doesn't overflow when very narrow */
@media (max-width: 200px) {
  .status-bar-container {
    font-size: 0.6rem;
    line-height: 1.1;
  }
}

/* Improve appearance when resizing - disable all pointer events except for the handle itself */
body.resizing * {
  pointer-events: none;
}

body.resizing [data-resize-handle="true"] {
  pointer-events: auto;
}

/* Ensure the handle is visible when resizing is active */
body.resizing .image-tray-resize-handle,
body.resizing .spread-tray-resize-handle {
  opacity: 0.8;
  background: linear-gradient(
    to right,
    rgba(248, 250, 252, 0) 0%,
    rgba(226, 232, 240, 0.15) 100%
  );
}

/* Horizontal gradient for spread tray handle when resizing */
body.resizing .spread-tray-resize-handle {
  background: linear-gradient(
    to bottom,
    rgba(248, 250, 252, 0) 0%,
    rgba(226, 232, 240, 0.15) 100%
  );
}

/* Show the actual handle line more prominently during active resizing */
body.resizing .resize-handle-line {
  opacity: 1;
  height: 80px;
  /* Enhanced visibility during resizing with a more visible gradient */
  background: linear-gradient(
    to bottom,
    rgba(100, 116, 139, 0.3) 0%,
    rgba(100, 116, 139, 1) 50%,
    rgba(100, 116, 139, 0.3) 100%
  );
  box-shadow: 0 0 4px rgba(148, 163, 184, 0.6);
}

/* Horizontal resize handle line during active resizing */
body.resizing .resize-handle-line.horizontal {
  width: 80px;
  height: 1px;
  background: linear-gradient(
    to right,
    rgba(100, 116, 139, 0.3) 0%,
    rgba(100, 116, 139, 1) 50%,
    rgba(100, 116, 139, 0.3) 100%
  );
}

/* Make the dot more visible during active resizing */
body.resizing .resize-handle-dot {
  width: 5px;
  height: 5px;
  background-color: white;
  box-shadow: 0 0 4px rgba(100, 116, 139, 0.9);
}

/* Specific fix for the Templates panel resize handle (right side only) */
.image-tray-resize-handle[aria-label="Resize templates tray"] {
  width: 12px !important; /* Override any inline width to ensure consistent grabbable area */
  left: 0px !important; /* Position it further left to increase the grabbable area */
  z-index: 10; /* Ensure it's above other elements */
}

/* Drag selection box styles */
.drag-selection-box {
  position: absolute;
  background-color: rgba(14, 165, 233, 0.25); /* Increased opacity */
  border: 2px solid rgba(14, 165, 233, 0.8); /* Stronger border */
  pointer-events: none;
  z-index: 100; /* Increased z-index */
  border-radius: 2px;
  transition: none; /* Remove transition for more direct feedback */
  box-shadow: 0 0 3px rgba(14, 165, 233, 0.4); /* More visible shadow */
}

/* Class for adding animated dashed border */
.drag-selection-box::before {
  content: '';
  position: absolute;
  top: -1px;
  right: -1px;
  bottom: -1px;
  left: -1px;
  border: 2px dashed rgba(14, 165, 233, 1.0); /* Solid white border */
  border-radius: 2px;
  animation: dashedBorder 20s linear infinite;
  pointer-events: none;
}

@keyframes dashedBorder {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 100px 0;
  }
}

/* Default cursor styles for the entire application */
* {
  cursor: inherit;
}

html, body {
  cursor: default;
}

/* Make sure the image tray and its contents have the default cursor */
[data-grid-container="true"] {
  cursor: default !important;
}

[data-grid-container="true"] * {
  cursor: inherit;
}

/* Use standard pointer for images */
[data-image-cell="true"] {
  cursor: default !important;
}

/* Standard cursor for filenames and other text */
[data-image-cell="true"] + div {
  cursor: default !important;
}

/* Override any crosshair cursors that might be set elsewhere */
.react-window-grid *:not(.drag-selection-box) {
  cursor: inherit;
}

/* Cursor style when drag selecting */
body.drag-selecting {
  cursor: pointer;
  user-select: none;
}

/* Once dragging has started and moved a bit, switch to crosshair */
body.drag-selecting-active {
  cursor: crosshair;
  user-select: none;
}

body.drag-selecting * {
  pointer-events: none;
}

body.drag-selecting [data-image-cell="true"] {
  pointer-events: auto;
}

/* Add a global rule for react-window-grid to ensure we're targeting it specifically */
.react-window-grid {
  cursor: default !important;
}

/* Target the inner elements even more specifically */
.react-window-grid > div,
.react-window-grid > div > div {
  cursor: default !important;
}

/* Ensure specific inner divs that might be causing issues inherit their parent's cursor */
.react-window-grid div[style] {
  cursor: inherit !important;
}

/* Only allow crosshair cursor during active selection */
body:not(.drag-selecting-active) [data-grid-container="true"],
body:not(.drag-selecting-active) [data-grid-container="true"] * {
  cursor: default !important;
}

body:not(.drag-selecting-active) [data-image-cell="true"] {
  cursor: default !important;
}

/* Additional hover state rules */
[data-grid-container="true"]:hover {
  cursor: default !important;
}

[data-grid-container="true"] *:hover {
  cursor: inherit !important;
}

[data-image-cell="true"]:hover {
  cursor: default !important;
}

[data-image-cell="true"] + div:hover {
  cursor: default !important;
} 