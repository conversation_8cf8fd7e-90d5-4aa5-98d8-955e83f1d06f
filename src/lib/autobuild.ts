import { TemplateData, TemplateImage } from './templates/interfaces';
import { templates1Image } from './templates/layouts/1-image';
import { templates2Image } from './templates/layouts/2-image';
import { templates3Image } from './templates/layouts/3-image';
import { templates4Image } from './templates/layouts/4-image';
import { templates5Image } from './templates/layouts/5-image';
import { templates6Image } from './templates/layouts/6-image';
import { templates7Image } from './templates/layouts/7-image';
import { templates8Image } from './templates/layouts/8-image';
import { templates9Image } from './templates/layouts/9-image';
import { templates10Image } from './templates/layouts/10-image';

export interface AutoBuildInstruction {
  template: TemplateData;
  imageCount: number; // Number of images this template should receive
  // The calling code will handle distributing the actual images.
}

// Interface for image metadata used in template selection
export interface ImageMetadata {
  id: string;
  width: number;
  height: number;
  lastModified?: number; // Timestamp for grouping similar images
  isPortrait?: boolean; // Whether the image is portrait orientation
}

// List of allowed template IDs for autobuild
const allowedTemplateIds = [
  // 1-image templates
  'full-bleed', 'small-center-inset', 'center-inset',
  
  // 2-image templates
  'two-up', 'vert-center-split', 'inset-overlap', 'two-squares-center',
  
  // 3-image templates
  'three-up', 'large-left', 'large-right',
  
  // 4-image templates
  'left-pane-3-grid', 'four-vert-equal',
  
  // 5-image templates
  'large-left-4-grid', 'large-right-4-grid', 'split-center', 'five-vert-strips',
  
  // 6-image templates
  'grid-3x2', 'grid-2x3', 'two-large-four-small', 'four-small-two-large', 'mosaic-6',
  
  // 7-image templates
  'seven-alternating-grid', 'seven-vertical-focus',
  
  // 8-image templates
  'grid-4x2', 'grid-2x4', 'center-split-strip-plus-6',
  
  // 9-image templates
  'center-focus-plus-8', 'cross-shape', 'large-center-plus-8',
  
  // 10-image templates
  'grid-5x2', 'grid-2x5', 'complex-grid-10'
];

// Filter templates by allowed IDs
const filterTemplatesByAllowedIds = (templates: TemplateData[]): TemplateData[] => {
  return templates.filter(template => allowedTemplateIds.includes(template.id));
};

// Organize all templates by capacity (number of placeholders)
const templatesByCapacity: Record<number, TemplateData[]> = {
  1: filterTemplatesByAllowedIds(templates1Image),
  2: filterTemplatesByAllowedIds(templates2Image),
  3: filterTemplatesByAllowedIds(templates3Image),
  4: filterTemplatesByAllowedIds(templates4Image),
  5: filterTemplatesByAllowedIds(templates5Image),
  6: filterTemplatesByAllowedIds(templates6Image),
  7: filterTemplatesByAllowedIds(templates7Image),
  8: filterTemplatesByAllowedIds(templates8Image),
  9: filterTemplatesByAllowedIds(templates9Image),
  10: filterTemplatesByAllowedIds(templates10Image),
};

// Get all available template capacities
const availableCapacities = Object.keys(templatesByCapacity)
  .map(Number)
  .filter(capacity => templatesByCapacity[capacity].length > 0) // Only include capacities with available templates
  .sort((a, b) => a - b);

/**
 * Determines if a template is more suitable for portrait or landscape images
 */
function getTemplateOrientation(template: TemplateData): 'portrait' | 'landscape' | 'neutral' {
  const portraitCount = template.images.filter(img => img.height > img.width).length;
  const landscapeCount = template.images.filter(img => img.width >= img.height).length;
  
  if (portraitCount > landscapeCount) return 'portrait';
  if (landscapeCount > portraitCount) return 'landscape';
  return 'neutral';
}

/**
 * Select the best template for a set of images based on their orientation.
 */
function selectTemplateForImages(
  imageCount: number,
  portraitCount: number,
  landscapeCount: number,
  usedTemplateIds: Set<string> = new Set()
): TemplateData | undefined {
  const templates = templatesByCapacity[imageCount];
  if (!templates || templates.length === 0) {
    console.warn(`No templates available for exact capacity ${imageCount}.`);
    return undefined;
  }

  let desiredOrientation: 'portrait' | 'landscape' | 'neutral' = 'neutral';
  if (portraitCount > landscapeCount) {
    desiredOrientation = 'portrait';
  } else if (landscapeCount > portraitCount) {
    desiredOrientation = 'landscape';
  }
  
  const matchingTemplates = templates.filter(t => 
    !usedTemplateIds.has(t.id) && getTemplateOrientation(t) === desiredOrientation
  );
  if (matchingTemplates.length > 0) {
    return matchingTemplates[Math.floor(Math.random() * matchingTemplates.length)];
  }
  
  const unusedTemplates = templates.filter(t => !usedTemplateIds.has(t.id));
  if (unusedTemplates.length > 0) {
    return unusedTemplates[Math.floor(Math.random() * unusedTemplates.length)];
  }
  
  return templates[Math.floor(Math.random() * templates.length)];
}

/**
 * Recursively finds combinations of capacities that sum up to the target.
 */
function findCapacityCombinationsRecursive(
  target: number,
  capacities: number[],
  startIndex: number,
  currentCombination: number[],
  allCombinations: number[][]
): void {
  if (target === 0) {
    allCombinations.push([...currentCombination]);
    return;
  }
  if (target < 0) {
    return;
  }

  for (let i = startIndex; i < capacities.length; i++) {
    const capacity = capacities[i];
    currentCombination.push(capacity);
    findCapacityCombinationsRecursive(target - capacity, capacities, i, currentCombination, allCombinations);
    currentCombination.pop(); // Backtrack
  }
}

// FUTURE CONSIDERATIONS:
// - Threshold Percentages (LARGE_CAPACITY_THRESHOLD_RATIO, SMALL_CAPACITY_THRESHOLD_RATIO): Fine-tune.
// - Definition of "Contains Large" vs. "Average Size": Explore average capacity for more nuance.
// - The "TOTAL_IMAGES_CUTOFF": Might need to be relative to maxAvailableCapacity.
const LARGE_CAPACITY_THRESHOLD_RATIO = 0.6; 
const SMALL_CAPACITY_THRESHOLD_RATIO = 0.35; 
const TOTAL_IMAGES_CUTOFF = 20; 
const PREFERRED_COMBINATION_CHANCE = 0.65;
// const ONE_IMAGE_TEMPLATE_PASS_CHANCE = 0.10; // No longer used for the primary 1-image spread filtering logic.
const TWO_IMAGE_TEMPLATE_PASS_CHANCE = 0.60; // ~60% chance to keep combos with 2-image templates (for totalImages >= 20)

/**
 * Distributes images across spreads with preferences for template sizes based on totalImages.
 */
function distributeImagesAcrossSpreads(totalImages: number): number[] {
  if (totalImages <= 0) return [];
  if (availableCapacities.length === 0) {
    console.error("Autobuild: No available template capacities to distribute images.");
    return [];
  }

  let candidateCombinations: number[][] = [];
  findCapacityCombinationsRecursive(totalImages, availableCapacities, 0, [], candidateCombinations);

  if (candidateCombinations.length === 0) {
    console.warn(`No capacity combinations found for ${totalImages} images initially.`);
    if (availableCapacities.includes(1)) {
      console.log(`Using fallback distribution for ${totalImages} images: Array of 1s.`);
      return Array(totalImages).fill(1).sort(() => Math.random() - 0.5);
    }
    console.error(`Critical: No capacity combinations found for ${totalImages} images, and 1-image template capacity is not available for fallback.`);
    return [];
  }

  // Stage 1: Strictly avoid 1-image templates if possible.
  // Attempt to use combinations without 1-image spreads first.
  if (candidateCombinations.length > 0) { // Only process if there are combinations to filter
    const combinationsWithoutOneImage = candidateCombinations.filter(combo => !combo.some(cap => cap === 1));

    if (combinationsWithoutOneImage.length > 0) {
      candidateCombinations = combinationsWithoutOneImage;
      // console.log(`Autobuild: Prioritizing combinations without 1-image spreads for ${totalImages} images.`); // Optional logging
    } else {
      // All available combinations must include a 1-image spread.
      // Keep candidateCombinations as is, allowing 1-image spreads because they are necessary.
      console.warn(`Autobuild: No combinations found without 1-image spreads for ${totalImages} images. 1-image spreads may be used if necessary.`);
    }
  }

  // Stage 2: De-prioritize 2-image templates if totalImages >= TOTAL_IMAGES_CUTOFF
  // This operates on candidateCombinations which might have been filtered by Stage 1.
  if (totalImages >= TOTAL_IMAGES_CUTOFF) {
    const combinationsBeforeTwoImageFilter = [...candidateCombinations]; // Operate on a copy of the current state
    if (combinationsBeforeTwoImageFilter.length > 0) { // Only apply if there are still combinations
      let twoImageFiltered: number[][] = [];
      for (const combo of combinationsBeforeTwoImageFilter) { // Iterate over the list *before* this specific filter
        if (combo.some(cap => cap === 2)) {
          if (Math.random() < TWO_IMAGE_TEMPLATE_PASS_CHANCE) {
            twoImageFiltered.push(combo);
          }
        } else {
          twoImageFiltered.push(combo); // Auto-pass if no 2-image templates
        }
      }

      if (twoImageFiltered.length > 0) {
        candidateCombinations = twoImageFiltered;
      } else {
        // Filtering 2-image templates resulted in an empty list, but we had combos before this step.
        // candidateCombinations remains as combinationsBeforeTwoImageFilter (i.e., the list prior to this 2-image filter).
        console.warn(`After 2-image template de-prioritization for ${totalImages} images, no combinations remained. Using list from before this 2-image filter stage.`);
      }
    }
  }
  
  // If after all filtering, candidateCombinations is empty, handle fallback.
  if (candidateCombinations.length === 0) {
    console.warn(`After all de-prioritization filters, no combinations remained for ${totalImages} images.`);
    // Attempt to use the original full list of combinations before any de-prioritization
    const originalCombinationsForFallback: number[][] = [];
    findCapacityCombinationsRecursive(totalImages, availableCapacities, 0, [], originalCombinationsForFallback);
    candidateCombinations = originalCombinationsForFallback; // Assign the newly populated list

    if (candidateCombinations.length === 0) { // Check if re-population ALSO yielded nothing
        if (availableCapacities.includes(1)) {
            return Array(totalImages).fill(1).sort(() => Math.random() - 0.5);
        }
        return [];
    } else {
        console.log(`Autobuild: Fallback to original combinations for ${totalImages} images was successful as filtering yielded no results.`);
    }
  }


  const maxAvailableCapacity = availableCapacities[availableCapacities.length - 1]; 
  const largeCapacityThreshold = maxAvailableCapacity * LARGE_CAPACITY_THRESHOLD_RATIO;

  const preferredCombinations: number[][] = [];
  const otherCombinations: number[][] = [];

  for (const combo of candidateCombinations) { // Use the (potentially) filtered list
    if (totalImages > TOTAL_IMAGES_CUTOFF) {
      if (combo.some(cap => cap > largeCapacityThreshold)) {
        preferredCombinations.push(combo);
      } else {
        otherCombinations.push(combo);
      }
    } else {
      if (combo.every(cap => cap <= largeCapacityThreshold)) {
        preferredCombinations.push(combo);
      } else {
        otherCombinations.push(combo);
      }
    }
  }

  let chosenCombination: number[];
  const pickFromPreferred = Math.random() < PREFERRED_COMBINATION_CHANCE;

  if (pickFromPreferred) {
    if (preferredCombinations.length > 0) {
      chosenCombination = preferredCombinations[Math.floor(Math.random() * preferredCombinations.length)];
    } else if (otherCombinations.length > 0) { 
      chosenCombination = otherCombinations[Math.floor(Math.random() * otherCombinations.length)];
    } else { 
      chosenCombination = candidateCombinations[Math.floor(Math.random() * candidateCombinations.length)]; // Fallback to any from candidate list
    }
  } else {
    if (otherCombinations.length > 0) {
      chosenCombination = otherCombinations[Math.floor(Math.random() * otherCombinations.length)];
    } else if (preferredCombinations.length > 0) { 
      chosenCombination = preferredCombinations[Math.floor(Math.random() * preferredCombinations.length)];
    } else { 
      chosenCombination = candidateCombinations[Math.floor(Math.random() * candidateCombinations.length)]; // Fallback to any from candidate list
    }
  }
  
  return chosenCombination.sort(() => Math.random() - 0.5); 
}

/**
 * Generates enhanced instructions for auto-building spreads.
 */
export function autoBuildSpreadInstructions(
  totalImages: number, 
  imageMetadata?: ImageMetadata[]
): AutoBuildInstruction[] {
  const instructions: AutoBuildInstruction[] = [];
  const usedTemplateIds = new Set<string>();
  
  const distribution = distributeImagesAcrossSpreads(totalImages);

  if (distribution.length === 0 && totalImages > 0) {
    console.error(`Autobuild failed: No image distribution could be determined for ${totalImages} images.`);
    return [];
  }
  
  let imagesAssigned = 0;
  distribution.forEach(count => imagesAssigned += count);
  if (imagesAssigned !== totalImages && totalImages > 0) {
      console.error(`Autobuild distribution error: Distributed ${imagesAssigned} images instead of ${totalImages}. Aborting.`);
      return [];
  }

  if (imageMetadata && imageMetadata.length > 0) {
    const sortedImages = [...imageMetadata].sort((a, b) => {
      if (a.lastModified && b.lastModified) return b.lastModified - a.lastModified; // Reversed order: newer first
      return 0;
    });
    
    let imageIndex = 0;
    for (const imageCountForSpread of distribution) {
      if (imageIndex >= totalImages) break;

      const spreadImages = sortedImages.slice(imageIndex, imageIndex + imageCountForSpread);
      imageIndex += imageCountForSpread;
      
      const portraitCount = spreadImages.filter(img => 
        img.isPortrait !== undefined ? img.isPortrait : (img.height > img.width)
      ).length;
      const landscapeCount = spreadImages.length - portraitCount;
      
      const template = selectTemplateForImages(imageCountForSpread, portraitCount, landscapeCount, usedTemplateIds);
      
      if (template) {
        if (template.images.length !== imageCountForSpread) {
            console.error(`Template capacity mismatch: Selected template ${template.id} has ${template.images.length} placeholders, but expected ${imageCountForSpread}. Skipping.`);
            continue; 
        }
        usedTemplateIds.add(template.id);
        instructions.push({ template, imageCount: imageCountForSpread });
      } else {
        console.error(`Failed to find a template for imageCount ${imageCountForSpread} with metadata. This spread will be skipped.`);
      }
    }
  } else {
    for (const imageCountForSpread of distribution) {
      const template = selectTemplateForImages(imageCountForSpread, 0, 0, usedTemplateIds);
      if (template) {
         if (template.images.length !== imageCountForSpread) {
            console.error(`Template capacity mismatch (no metadata): Selected template ${template.id} has ${template.images.length} placeholders, but expected ${imageCountForSpread}. Skipping.`);
            continue;
        }
        usedTemplateIds.add(template.id);
        instructions.push({ template, imageCount: imageCountForSpread });
      } else {
        console.error(`Failed to find a template for imageCount ${imageCountForSpread} (no metadata). This spread will be skipped.`);
      }
    }
  }
  
  return instructions;
}