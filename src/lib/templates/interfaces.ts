// src/lib/templates/interfaces.ts

// Define structure for template images (placeholders)
export interface TemplateImage {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

// Template classification types
export type TemplateClassification = 'ALL' | 'SQUARE' | 'HORIZONTAL' | 'VERTICAL';

// Project dimension types
export type ProjectDimension = 'square' | 'horizontal' | 'vertical';

// Interface to allow separate horizontal/vertical gap values
export interface ImageGap {
  horizontal: number;
  vertical: number;
}

// Interface for adjusted placeholder output (dimensions in points)
export interface AdjustedPlaceholderPt {
  id: string; // Corresponds to the original TemplateImage id
  adjustedX_pt: number;
  adjustedY_pt: number;
  adjustedWidth_pt: number;
  adjustedHeight_pt: number;
}

// Type for template-specific gap adjustment function
export type TemplateGapAdjustmentFunction = (
  baseLayout: TemplateImage[],
  imageGapInput: number | ImageGap,
  spreadWidthPt: number,
  spreadHeightPt: number
) => AdjustedPlaceholderPt[];

// Define structure for a template definition
export interface TemplateData {
  id: string;
  name: string;
  images: TemplateImage[];
  classification: TemplateClassification[];
  adjustForGap?: TemplateGapAdjustmentFunction; // Optional template-specific gap adjustment
}

// Utility function to determine project dimension from aspect ratio
export function getProjectDimension(aspectRatio: { ratio: string }): ProjectDimension {
  const ratio = aspectRatio.ratio;
  
  // Parse ratio string (e.g., "1:1", "4:3", "6:4")
  const [width, height] = ratio.split(':').map(Number);
  
  if (width === height) {
    return 'square';
  } else if (width > height) {
    return 'horizontal';
  } else {
    return 'vertical';
  }
}

// Utility function to get allowed template classifications for a project dimension
export function getAllowedTemplateClassifications(projectDimension: ProjectDimension): TemplateClassification[] {
  switch (projectDimension) {
    case 'square':
      return ['ALL', 'SQUARE'];
    case 'horizontal':
      return ['ALL', 'HORIZONTAL'];
    case 'vertical':
      return ['ALL', 'VERTICAL'];
    default:
      return ['ALL'];
  }
}

// Utility function to check if a template is allowed for a project dimension
export function isTemplateAllowedForProject(
  templateClassifications: TemplateClassification[], 
  projectDimension: ProjectDimension
): boolean {
  const allowedClassifications = getAllowedTemplateClassifications(projectDimension);
  return templateClassifications.some(classification => 
    allowedClassifications.includes(classification)
  );
}