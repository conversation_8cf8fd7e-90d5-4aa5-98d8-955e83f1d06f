// src/lib/templates/layouts/3-image.ts
import { TemplateData } from '../interfaces';
import { adjustEmphasisForGap } from '../gapAdjustments';

export const templates3Image: TemplateData[] = [
   {
  "id": "3-0-s",
  "name": "3-0-s",
  "images": [
    {
      "id": "img1",
      "x": 0.5625,
      "y": 0,
      "width": 0.375,
      "height": 0.5
    },
    {
      "id": "img2",
      "x": 0.5625,
      "y": 0.5,
      "width": 0.375,
      "height": 0.5
    },
    {
      "id": "img3",
      "x": 0,
      "y": 0,
      "width": 0.5,
      "height": 1
    }
  ],
  "classification": [
    "VERTICAL",
    "SQUARE"
  ]
},
  {
  "id": "3-1-s",
  "name": "3-1-s",
  "images": [
    {
      "id": "img1",
      "x": 0.0625,
      "y": 0,
      "width": 0.375,
      "height": 0.5
    },
    {
      "id": "img2",
      "x": 0.0625,
      "y": 0.5,
      "width": 0.375,
      "height": 0.5
    },
    {
      "id": "img3",
      "x": 0.5,
      "y": 0,
      "width": 0.5,
      "height": 1
    }
  ],
  "classification": [
    "VERTICAL",
    "SQUARE"
  ]
},
  {
    "id": "3-0",
    "name": "3-0",
    "images": [
      {
        "id": "img1",
        "x": 0.625,
        "y": 0,
        "width": 0.25,
        "height": 0.5
      },
      {
        "id": "img2",
        "x": 0.625,
        "y": 0.5,
        "width": 0.25,
        "height": 0.5
      },
      {
        "id": "img3",
        "x": 0,
        "y": 0,
        "width": 0.5,
        "height": 1
      }
    ],
    "classification": [
      "HORIZONTAL",
      "VERTICAL",
      "SQUARE"
    ]
  },
  {
  "id": "3-1",
  "name": "3-1",
  "images": [
    {
      "id": "img1",
      "x": 0.125,
      "y": 0,
      "width": 0.25,
      "height": 0.5
    },
    {
      "id": "img2",
      "x": 0.125,
      "y": 0.5,
      "width": 0.25,
      "height": 0.5
    },
    {
      "id": "img3",
      "x": 0.5,
      "y": 0,
      "width": 0.5,
      "height": 1
    }
  ],
  "classification": [
    "HORIZONTAL",
    "VERTICAL",
    "SQUARE"
  ]
},
  {
    id: '3-2',
    name: '3-2',
    images: [
      // Using 4 decimal places for better precision in 1/3 division
      { id: 'img1', x: 0,      y: 0, width: 0.3333, height: 1 },
      { id: 'img2', x: 0.3333, y: 0, width: 0.3333, height: 1 },
      { id: 'img3', x: 0.6666, y: 0, width: 0.3333, height: 1 }  // Use consistent width
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-4',
    name: '3-4',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.7, height: 1 },
      { id: 'img2', x: 0.7, y: 0, width: 0.3, height: 0.5 },
      { id: 'img3', x: 0.7, y: 0.5, width: 0.3, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-5',
    name: '3-5',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.3, height: 0.5 },
      { id: 'img2', x: 0, y: 0.5, width: 0.3, height: 0.5 },
      { id: 'img3', x: 0.3, y: 0, width: 0.7, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-7',
    name: '3-7',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.5, height: 0.5 },
      { id: 'img2', x: 0, y: 0.5, width: 0.5, height: 0.5 },
      { id: 'img3', x: 0.5, y: 0, width: 0.5, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-8',
    name: '3-8',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.5, height: 1 },
      { id: 'img2', x: 0.5, y: 0, width: 0.5, height: 0.5 },
      { id: 'img3', x: 0.5, y: 0.5, width: 0.5, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-9',
    name: '3-9',
    images: [
      { id: 'img1', x: 0, y: 0,      width: 1, height: 0.3333 },
      { id: 'img2', x: 0, y: 0.3333, width: 1, height: 0.3333 }, // Use consistent height
      { id: 'img3', x: 0, y: 0.6666, width: 1, height: 0.3333 }  // Adjust y, use consistent height
    ],
    classification: ['VERTICAL']
  },
  {
    id: '3-10',
    name: '3-10',
    images: [
      { id: 'img1', x: 0, y: 0.19617178188319423, width: 0.33379992361414224, height: 0.6076564362336114 },
      { id: 'img2', x: 0.3773031245453222, y: 0, width: 0.24414375090935544, height: 1 },
      { id: 'img3', x: 0.6662000763858578, y: 0.19617178188319429, width: 0.33379992361414224, height: 0.6076564362336114 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-11',
    name: '3-11',
    images: [
      { id: 'img1', x: 0, y: 0.19617178188319423, width: 0.33379992361414224, height: 0.6076564362336114 },
      { id: 'img2', x: 0.3773031245453222, y: 0.13378437363596685, width: 0.24414375090935544, height: 0.7324312527280663 },
      { id: 'img3', x: 0.6662000763858578, y: 0.19617178188319429, width: 0.33379992361414224, height: 0.6076564362336114 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-12',
    name: '3-12', 
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.4, height: 0.7 },
      { id: 'img2', x: 0.3, y: 0.15, width: 0.4, height: 0.7 },
      { id: 'img3', x: 0.6, y: 0.3, width: 0.4, height: 0.7 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-13',
    name: '3-13', 
    images: [
      { id: 'img1', x: 0.6, y: 0, width: 0.4, height: 0.7 },
      { id: 'img2', x: 0.3, y: 0.15, width: 0.4, height: 0.7 },
      { id: 'img3', x: 0, y: 0.3, width: 0.4, height: 0.7 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-14',
    name: '3-14',
    images: [
      { id: 'img1', x: 0, y: 0.19626415375446957, width: 0.3333333333333333, height: 0.6074716924910608 },
      { id: 'img2', x: 0.3333333333333333, y: 0.19626415375446957, width: 0.3333333333333333, height: 0.6074716924910608 },
      { id: 'img3', x: 0.6666666666666666, y: 0.19626415375446957, width: 0.3333333333333334, height: 0.6074716924910608 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-15',
    name: '3-15',
    images: [
      { id: 'img1', x: 0.04457812454532227, y: 0, width: 0.24414375090935544, height: 1 },
      { id: 'img2', x: 0.37787812454532227, y: 0, width: 0.24414375090935544, height: 1 },
      { id: 'img3', x: 0.7111781245453223, y: 0, width: 0.24414375090935544, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
  "id": "3-15-s",
  "name": "3-15-s",
  "images": [
    {
      "id": "img1",
      "x": 0.04457812454532227,
      "y": 0.13378437363596685,
      "width": 0.24414375090935544,
      "height": 0.7324312527280663
    },
    {
      "id": "img2",
      "x": 0.37787812454532227,
      "y": 0.13378437363596685,
      "width": 0.24414375090935544,
      "height": 0.7324312527280663
    },
    {
      "id": "img3",
      "x": 0.7111781245453223,
      "y": 0.13378437363596685,
      "width": 0.24414375090935544,
      "height": 0.7324312527280663
    }
  ],
  "classification": [
    "VERTICAL",
    "SQUARE"
  ]
},
  {
    id: '3-16',
    name: '3-16',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.5, height: 1 },
      { id: 'img2', x: 0.6595441401134876, y: 0.11263855780691306, width: 0.1809117197730248, height: 0.32933551847437414 },
      { id: 'img3', x: 0.6595441401134876, y: 0.5614392133492253, width: 0.1809117197730248, height: 0.32933551847437414 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-17',
    name: '3-17',
    images: [
      { id: 'img1', x: 0.5, y: 0, width: 0.5, height: 1 },
      { id: 'img2', x: 0.15954414011348764, y: 0.11263855780691306, width: 0.1809117197730248, height: 0.32933551847437414 },
      { id: 'img3', x: 0.15954414011348764, y: 0.5614392133492253, width: 0.1809117197730248, height: 0.32933551847437414 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-18',
    name: '3-18',
    images: [
      { id: 'img1', x: 0.5, y: 0, width: 0.5, height: 1 },
      { id: 'img2', x: 0.0576691401134876, y: 0.08533224076281293, width: 0.3846617197730248, height: 0.37712157330154933 },
      { id: 'img3', x: 0.0576691401134876, y: 0.5341328963051252, width: 0.3846617197730248, height: 0.37712157330154933 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-19',
    name: '3-19',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.5, height: 1 },
      { id: 'img2', x: 0.5576691401134876, y: 0.08533224076281293, width: 0.3846617197730248, height: 0.37712157330154933 },
      { id: 'img3', x: 0.5576691401134876, y: 0.5341328963051252, width: 0.3846617197730248, height: 0.37712157330154933 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-20',
    name: '3-20',
    images: [
      { id: 'img1', x: 0, y: 0.10751862336114408, width: 0.285, height: 0.7849627532777119 },
      { id: 'img2', x: 0.285, y: 0.10751862336114408, width: 0.43, height: 0.7849627532777119 },
      { id: 'img3', x: 0.715, y: 0.10751862336114408, width: 0.285, height: 0.7849627532777119 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE'],
    adjustForGap: adjustEmphasisForGap
  },
  {
    id: '3-21',
    name: '3-21',
    images: [
      { id: 'img1', x: 0, y: 0.10751862336114408, width: 0.285, height: 0.7849627532777119 },
      { id: 'img2', x: 0.285, y: 0.10751862336114403, width: 0.285, height: 0.7849627532777119 },
      { id: 'img3', x: 0.5700000000000001, y: 0.107518623361144, width: 0.42999999999999994, height: 0.7849627532777119 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE'],
    adjustForGap: adjustEmphasisForGap
  },
  {
    id: '3-22',
    name: '3-22',
    images: [
      { id: 'img1', x: 0, y: 0.10751862336114408, width: 0.43, height: 0.7849627532777119 },
      { id: 'img2', x: 0.43, y: 0.10751862336114408, width: 0.285, height: 0.7849627532777119 },
      { id: 'img3', x: 0.715, y: 0.10751862336114408, width: 0.285, height: 0.7849627532777119 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE'],
    adjustForGap: adjustEmphasisForGap
  },
  {
    id: '3-23',
    name: '3-23',
    images: [
      { id: 'img1', x: 0, y: 0.10751862336114415, width: 0.2874656263640332, height: 0.7849627532777117 },
      { id: 'img2', x: 0.357706446420777, y: 0.10751862336114404, width: 0.28458710715844604, height: 0.7849627532777119 },
      { id: 'img3', x: 0.7125343736359668, y: 0.10751862336114404, width: 0.28458710715844604, height: 0.7849627532777119 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-24',
    name: '3-24',
    images: [
      { id: 'img1', x: 0, y: 0.07509237187127511, width: 0.3112156263640333, height: 0.8498152562574497 },
      { id: 'img2', x: 0.3443921868179833, y: 0.07509237187127517, width: 0.3112156263640333, height: 0.8498152562574497 },
      { id: 'img3', x: 0.6887843736359667, y: 0.07509237187127518, width: 0.3112156263640333, height: 0.8498152562574497 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-25',
    name: '3-25',
    images: [
      { id: 'img1', x: 0.75, y: 0.06485250297973782, width: 0.1730666875818422, height: 0.47258138408820083 },
      { id: 'img2', x: 0, y: 0, width: 0.665, height: 1 },
      { id: 'img3', x: 0.75, y: 0.6045930341189704, width: 0.17306668758184218, height: 0.31007472511821677 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-26',
    name: '3-26',
    images: [
      { id: 'img1', x: 0.07693331241815782, y: 0.06485250297973782, width: 0.1730666875818422, height: 0.47258138408820083 },
      { id: 'img2', x: 0.335, y: 0, width: 0.665, height: 1 },
      { id: 'img3', x: 0.07693331241815782, y: 0.6045930341189704, width: 0.17306668758184218, height: 0.31007472511821677 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-27',
    name: '3-27',
    images: [
      { id: 'img1', x: 0.75125, y: 0.450554231227652, width: 0.1730666875818422, height: 0.47258138408820083 },
      { id: 'img2', x: 0, y: 0, width: 0.665, height: 1 },
      { id: 'img3', x: 0.75, y: 0.08533224076281291, width: 0.17306668758184218, height: 0.31007472511821677 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-28',
    name: '3-28',
    images: [
      { id: 'img1', x: 0.07568331241815781, y: 0.450554231227652, width: 0.1730666875818422, height: 0.47258138408820083 },
      { id: 'img2', x: 0.335, y: 0, width: 0.665, height: 1 },
      { id: 'img3', x: 0.07693331241815782, y: 0.08533224076281291, width: 0.17306668758184218, height: 0.31007472511821677 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '3-3',
    name: '3-3',
    images: [
      { id: 'img1', x: 0, y: 0, width: 1, height: 0.7 },
      { id: 'img2', x: 0, y: 0.7, width: 0.5, height: 0.3 },
      { id: 'img3', x: 0.5, y: 0.7, width: 0.5, height: 0.3 }
    ],
    classification: ['VERTICAL']
  },
  {
  "id": "3-3-S",
  "name": "3-3-S",
  "images": [
    {
      "id": "img1",
      "x": 0,
      "y": 0,
      "width": 1,
      "height": 0.5924999999999999
    },
    {
      "id": "img2",
      "x": 0,
      "y": 0.5924999999999999,
      "width": 0.5,
      "height": 0.4075000000000001
    },
    {
      "id": "img3",
      "x": 0.5,
      "y": 0.5924999999999999,
      "width": 0.5,
      "height": 0.4075000000000001
    }
  ],
  "classification": [
    "VERTICAL",
    "SQUARE"
  ]
},
{
    id: '3-6',
    name: '3-6',
    images: [
      { id: 'img1', x: 0, y: 0.3, width: 1, height: 0.7 },
      { id: 'img2', x: 0, y: 0, width: 0.5, height: 0.3 },
      { id: 'img3', x: 0.5, y: 0, width: 0.5, height: 0.3 }
    ],
    classification: ['VERTICAL']
  },
  {
  "id": "3-6-s",
  "name": "3-6-s",
  "images": [
    {
      "id": "img1",
      "x": 0,
      "y": 0.44499999999999995,
      "width": 1,
      "height": 0.555
    },
    {
      "id": "img2",
      "x": 0,
      "y": 0,
      "width": 0.5,
      "height": 0.44499999999999995
    },
    {
      "id": "img3",
      "x": 0.5,
      "y": 0,
      "width": 0.5,
      "height": 0.44499999999999995
    }
  ],
  "classification": [
    "VERTICAL",
    "SQUARE"
  ]
},
];