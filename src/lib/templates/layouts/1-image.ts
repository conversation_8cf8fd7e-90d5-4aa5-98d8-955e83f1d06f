// src/lib/templates/layouts/1-image.ts
import { TemplateData } from '../interfaces';

export const templates1Image: TemplateData[] = [
  {
    id: '1-0',
    name: '1-0',
    images: [
      { id: 'img1', x: 0, y: 0, width: 1, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-1',
    name: '1-1',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.5, height: 1 } // Full left page
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-2',
    name: '1-2',
    images: [
      { id: 'img1', x: 0.5, y: 0, width: 0.5, height: 1 } // Full right page
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-3',
    name: '1-3',
    images: [
      { id: 'img1', x: 0.2, y: 0.15, width: 0.6, height: 0.7 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-4',
    name: '1-4',
    images: [
      { id: 'img1', x: 0.1, y: 0, width: 0.8, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-5',
    name: '1-5',
    images: [
      { id: 'img1', x: 0.05, y: 0, width: 0.9, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-6',
    name: '1-6',
    images: [
      { id: 'img1', x: 0.05, y: 0, width: 0.7, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-7',
    name: '1-7',
    images: [
      { id: 'img1', x: 0.25, y: 0, width: 0.7, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-8',
    name: '1-8',
    images: [
      { id: 'img1', x: 0.2, y: 0, width: 0.6, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-9',
    name: '1-9',
    images: [
      { id: 'img1', x: 0.1, y: 0, width: 0.6, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-10',
    name: '1-10',
    images: [
      { id: 'img1', x: 0.3, y: 0, width: 0.6, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-11',
    name: '1-11',
    images: [
      { id: 'img1', x: 0.025, y: 0, width: 0.95, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-12',
    name: '1-12',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.8, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-13',
    name: '1-13',
    images: [
      { id: 'img1', x: 0.2, y: 0, width: 0.8, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-14',
    name: '1-14',
    images: [
      { id: 'img1', x: 0, y: 0, width: 1, height: 0.5 } // Full width, top half
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-15',
    name: '1-15',
    images: [
      { id: 'img1', x: 0, y: 0.5, width: 1, height: 0.5 } // Full width, bottom half
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-16',
    name: '1-16',
    images: [
      { id: 'img1', x: 0.25, y: 0.25, width: 0.5, height: 0.5 } // 50% size, centered
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-17',
    name: '1-17',
    images: [
      { id: 'img1', x: 0.3, y: 0, width: 0.4, height: 1 } // 40% width, centered vertically
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-18',
    name: '1-18',
    images: [
      { id: 'img1', x: 0.1, y: 0.1, width: 0.8, height: 0.8 } // 80% size, centered
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-19',
    name: '1-19',
    images: [
      { id: 'img1', x: 0.05, y: 0.05, width: 0.4, height: 0.9 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-20',
    name: '1-20',
    images: [
      { id: 'img1', x: 0.55, y: 0.05, width: 0.4, height: 0.9 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-21',
    name: '1-21',
    images: [
      { id: 'img1', x: 0.2, y: 0.2, width: 0.6, height: 0.6 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-22',
    name: '1-22',
    images: [
      { id: 'img1', x: 0.3, y: 0.15, width: 0.4, height: 0.7 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-23',
    name: '1-23',
    images: [
      { id: 'img1', x: 0.05, y: 0.05, width: 0.4, height: 0.4 } // Assuming square based on visual
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-24',
    name: '1-24',
    images: [
      { id: 'img1', x: 0.55, y: 0.05, width: 0.4, height: 0.4 } // Assuming square
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-25',
    name: '1-25',
    images: [
      { id: 'img1', x: 0.1, y: 0.25, width: 0.3, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-26',
    name: '1-26',
    images: [
      { id: 'img1', x: 0.6, y: 0.25, width: 0.3, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-27',
    name: '1-27',
    images: [
      { id: 'img1', x: 0.1, y: 0.1, width: 0.3, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-28',
    name: '1-28',
    images: [
      { id: 'img1', x: 0.1, y: 0.4, width: 0.3, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-29',
    name: '1-29',
    images: [
      { id: 'img1', x: 0.6, y: 0.1, width: 0.3, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-30',
    name: '1-30',
    images: [
      { id: 'img1', x: 0.6, y: 0.4, width: 0.3, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-31',
    name: '1-31',
    images: [
      { id: 'img1', x: 0.5, y: 0.1, width: 0.4, height: 0.8 } // Height is 2x width for a square on half a page
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-32',
    name: '1-32',
    images: [
      { id: 'img1', x: 0.1, y: 0.05, width: 0.4, height: 0.9 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-33',
    name: '1-33',
    images: [
      { id: 'img1', x: 0.25, y: 0.1, width: 0.5, height: 0.8 } // Height is 0.8 to be inset from top/bottom
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-34',
    name: '1-34',
    images: [
      { id: 'img1', x: 0.5, y: 0.05, width: 0.4, height: 0.9 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-35',
    name: '1-35',
    images: [
      { id: 'img1', x: 0, y: 0.05, width: 0.4, height: 0.9 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-36',
    name: '1-36',
    images: [
      { id: 'img1', x: 0.15, y: 0.1, width: 0.7, height: 0.8 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-37',
    name: '1-37',
    images: [
      { id: 'img1', x: 0.5, y: 0.1, width: 0.35, height: 0.8 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-38',
    name: '1-38',
    images: [
      { id: 'img1', x: 0.05, y: 0.1, width: 0.35, height: 0.8 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-39',
    name: '1-39',
    images: [
      { id: 'img1', x: 0.3, y: 0, width: 0.4, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-40',
    name: '1-40',
    images: [
      { id: 'img1', x: 0.1, y: 0, width: 0.3, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-41',
    name: '1-41',
    images: [
      { id: 'img1', x: 0.6, y: 0, width: 0.3, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-42',
    name: '1-42',
    images: [
      { id: 'img1', x: 0.05, y: 0.05, width: 0.6, height: 0.6 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '1-43',
    name: '1-43',
    images: [
      { id: 'img1', x: 0.25, y: 0.45, width: 0.7, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  }
];