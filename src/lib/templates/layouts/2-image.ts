// src/lib/templates/layouts/2-image.ts
import { TemplateData } from '../interfaces';
import { adjustEmphasisForGap } from '../gapAdjustments';

export const templates2Image: TemplateData[] = [
  {
    id: '2-0',
    name: '2-0',
    images: [
      // Edges meet exactly at the midpoint
      { id: 'img1', x: 0, y: 0, width: 0.5, height: 1 },
      { id: 'img2', x: 0.5, y: 0, width: 0.5, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-1',
    name: '2-1',
    images: [
      {
    "id": "img1",
    "x": 0,
    "y": 0,
    "width": 0.6667,
    "height": 1
  },
  {
    "id": "img2",
    "x": 0.6667,
    "y": 0,
    "width": 0.3333,
    "height": 1
  }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE'],
    adjustForGap: adjustEmphasisForGap
  },
  {
    id: '2-2',
    name: '2-2',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.3333, height: 1 },
      { id: 'img2', x: 0.3333, y: 0, width: 0.6667, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE'],
    adjustForGap: adjustEmphasisForGap
  },
  {
    id: '2-3',
    name: '2-3',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.65, height: 1 },
      { id: 'img2', x: 0.7, y: 0.25, width: 0.25, height: 0.5 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-4',
    name: '2-4',
    images: [
      { id: 'img1', x: 0.04999999999999999, y: 0.25, width: 0.25, height: 0.5 },
      { id: 'img2', x: 0.35, y: 0, width: 0.65, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-5',
    name: '2-5',
    images: [
      { id: 'img1', x: 0.05, y: 0.05, width: 0.4, height: 0.9 },
      { id: 'img2', x: 0.55, y: 0.05, width: 0.349375, height: 0.69875 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-6',
    name: '2-6',
    images: [
      { id: 'img1', x: 0.55, y: 0.05, width: 0.4, height: 0.9 },
      { id: 'img2', x: 0.10062499999999996, y: 0.2512500000000001, width: 0.349375, height: 0.69875 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-7',
    name: '2-7',
    images: [
      // Approximation of a diagonal split
      { id: 'img1', x: 0, y: 0, width: 0.6, height: 0.6 },
      { id: 'img2', x: 0.4, y: 0.4, width: 0.6, height: 0.6 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-8',
    name: '2-8',
    images: [
      { id: 'img1', x: 0.4, y: 0, width: 0.6, height: 0.6 },
      { id: 'img2', x: 0, y: 0.4, width: 0.6, height: 0.6 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-9',
    name: '2-9',
    images: [
      { id: 'img1', x: 0.05, y: 0.05, width: 0.6, height: 0.9 },
      { id: 'img2', x: 0.47375, y: 0.14625000000000007, width: 0.47624999999999995, height: 0.7074999999999999 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-10',
    name: '2-10',
    images: [
      { id: 'img1', x: 0.35, y: 0.05, width: 0.6, height: 0.9 },
      { id: 'img2', x: 0.05000000000000002, y: 0.14625000000000007, width: 0.47624999999999995, height: 0.7074999999999999 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-11',
    name: '2-11',
    images: [
      { id: 'img1', x: 0.1, y: 0, width: 0.35, height: 1 },
      { id: 'img2', x: 0.55, y: 0, width: 0.35, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-12',
    name: '2-12',
    images: [
      // Assuming spread is wider than tall, make squares based on height
      { id: 'img1', x: 0.1, y: 0.2, width: 0.3, height: 0.6 }, // Adjust width based on aspect ratio if needed
      { id: 'img2', x: 0.6, y: 0.2, width: 0.3, height: 0.6 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-13',
    name: '2-13',
    images: [
      { id: 'img1', x: 0.1, y: 0.2, width: 0.3, height: 0.6 },
      { id: 'img2', x: 0.6285531245453223, y: 5.551115123125783e-17, width: 0.24414375090935544, height: 0.9999999999999999 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL']
  },
  {
    id: '2-14',
    name: '2-14',
    images: [
      { id: 'img1', x: 0.05, y: 0.09999999999999996, width: 0.4, height: 0.8 },
      { id: 'img2', x: 0.6279281245453223, y: 1.1102230246251565e-16, width: 0.24414375090935544, height: 0.9999999999999999 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL']
  },
   {
    id: '2-15',
    name: '2-15',
    images: [
      { id: 'img1', x: 0.05, y: 0.09999999999999996, width: 0.4, height: 0.8 },
      { id: 'img2', x: 0.6279281245453223, y: 1.1102230246251565e-16, width: 0.24414375090935544, height: 0.9999999999999999 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL']
  },
  {
  "id": "2-16",
  "name": "2-16",
  "images": [
    {
      "id": "img1",
      "x": 0,
      "y": 0,
      "width": 0.5,
      "height": 0.9962414065910082
    },
    {
      "id": "img2",
      "x": 0.5858390622726611,
      "y": 0.0037585934089917616,
      "width": 0.3283218754546776,
      "height": 0.9924828131820165
    }
  ],
  "classification": [
    "SQUARE"
  ]
},
  {
    id: '2-17',
    name: '2-17',
    images: [
      { id: 'img1', x: 0.1279281245453223, y: 1.1102230246251565e-16, width: 0.24414375090935544, height: 0.9999999999999999 },
      { id: 'img2', x: 0.55, y: 0.09999999999999996, width: 0.4, height: 0.8 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL']
  },
  {
    id: '2-18',
    name: '2-18',
    images: [
      { id: 'img1', x: 0.1279281245453223, y: 1.1102230246251565e-16, width: 0.24414375090935544, height: 0.9999999999999999 },
      { id: 'img2', x: 0.4506765604539502, y: 0, width: 0.5493234395460498, height: 1 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL']
  },
  {
    id: '2-19',
    name: '2-19',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.5493234395460498, height: 1 },
      { id: 'img2', x: 0.6279281245453223, y: 1.1102230246251565e-16, width: 0.24414375090935544, height: 0.9999999999999999 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL']
  },
  {
    id: '2-20',
    name: '2-20',
    images: [
      { id: 'img1', x: 0, y: 0, width: 0.5, height: 1 },
      { id: 'img2', x: 0.6165625, y: 0.23312499999999997, width: 0.26687500000000003, height: 0.5337500000000001 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-21',
    name: '2-21',
    images: [
      { id: 'img1', x: 0.5, y: 0, width: 0.5, height: 1 },
      { id: 'img2', x: 0.1165625, y: 0.23312499999999994, width: 0.26687500000000003, height: 0.5337500000000001 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-22',
    name: '2-22',
    images: [
      { id: 'img1', x: 0.1, y: 0.15, width: 0.3, height: 0.7 },
      { id: 'img2', x: 0.6, y: 0.15, width: 0.3, height: 0.7 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-23',
    name: '2-23',
    images: [
      {
        id: 'img1',
        x: 0.06689218681798342,
        y: 0,
        width: 0.36621562636403315,
        height: 1
      },
      {
        id: 'img2',
        x: 0.5668921868179834,
        y: 0,
        width: 0.36621562636403315,
        height: 1
      }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-24',
    name: '2-24',
    images: [
      { id: 'img1', x: 0, y: 0.09999999999999998, width: 0.5, height: 0.8 },
      { id: 'img2', x: 0.5, y: 0.1, width: 0.5, height: 0.8 }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-25',
    name: '2-25',
    images: [
      {
        id: 'img1',
        x: 0.04875000000000001,
        y: 0,
        width: 0.35,
        height: 1
      },
      {
        id: 'img2',
        x: 0.4503382802269752,
        y: 0,
        width: 0.5493234395460498,
        height: 1
      }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-26',
    name: '2-26',
    images: [
      {
        id: 'img1',
        x: 0.0006617197730248,
        y: 0,
        width: 0.5493234395460498,
        height: 1
      },
      {
        id: 'img2',
        x: 0.60125,
        y: 0,
        width: 0.35,
        height: 1
      }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
   {
    id: '2-27',
    name: '2-27',
    images: [
      { id: 'img1', x: 0, y: 0, width: 1, height: 0.5 },
      { id: 'img2', x: 0, y: 0.5, width: 1, height: 0.5 }
    ],
    classification: ['VERTICAL', 'SQUARE']
  },
  {
    id: '2-28',
    name: '2-28',
    images: [
      {
        id: 'img1',
        x: 0,
        y: 0,
        width: 0.5,
        height: 1
      },
      {
        id: 'img2',
        x: 0.5,
        y: 0.1,
        width: 0.5,
        height: 0.8
      }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  },
  {
    id: '2-29',
    name: '2-29',
    images: [
      {
        id: 'img1',
        x: 0,
        y: 0.1,
        width: 0.5,
        height: 0.8
      },
      {
        id: 'img2',
        x: 0.5,
        y: 0,
        width: 0.5,
        height: 1
      }
    ],
    classification: ['HORIZONTAL', 'VERTICAL', 'SQUARE']
  }
];