// src/lib/templates/gapAdjustments.ts
import { TemplateImage, ImageGap, AdjustedPlaceholderPt } from './interfaces';

// Constants for gap adjustment
const VERTICAL_GAP_MULTIPLIER = 1;

/**
 * Gap adjustment function for emphasis templates that preserve proportions.
 * Maintains the original width proportions when gaps are applied to horizontal layouts.
 */
export const adjustEmphasisForGap = (
  baseLayout: TemplateImage[],
  imageGapInput: number | ImageGap,
  spreadWidthPt: number,
  spreadHeightPt: number
): AdjustedPlaceholderPt[] => {
  // Handle both single number and object-based imageGapInput
  const gapHorizontal = typeof imageGapInput === 'number' ? imageGapInput : imageGapInput.horizontal;
  const gapVertical = typeof imageGapInput === 'number' ? imageGapInput * VERTICAL_GAP_MULTIPLIER : imageGapInput.vertical;

  // Check for zero gap case
  if (gapHorizontal <= 0) {
    const overlap = 0.5;
    return baseLayout.map(p => ({
      id: p.id,
      adjustedX_pt: p.x * spreadWidthPt,
      adjustedY_pt: p.y * spreadHeightPt,
      adjustedWidth_pt: (p.width * spreadWidthPt) + overlap,
      adjustedHeight_pt: (p.height * spreadHeightPt) + overlap,
    }));
  }

  // Calculate total available width after gaps
  const numImages = baseLayout.length;
  const totalGapWidth = (numImages - 1) * gapHorizontal;
  const availableWidth = spreadWidthPt - totalGapWidth;

  // Sort images by x position to maintain left-to-right order
  const sortedImages = [...baseLayout].sort((a, b) => a.x - b.x);
  
  // Calculate proportional widths based on original template proportions
  // Use the sorted order to maintain the emphasis pattern
  const proportionalWidths = sortedImages.map(img => img.width);
  const totalOriginalWidth = proportionalWidths.reduce((sum, w) => sum + w, 0);
  
  // Scale widths proportionally to fit available space
  const adjustedWidths = proportionalWidths.map(w => (w / totalOriginalWidth) * availableWidth);

  const adjustedLayout: AdjustedPlaceholderPt[] = [];
  let currentX = 0;

  sortedImages.forEach((image, index) => {
    const adjustedWidth = Math.max(0, adjustedWidths[index]);
    
    adjustedLayout.push({
      id: image.id,
      adjustedX_pt: currentX,
      adjustedY_pt: image.y * spreadHeightPt,
      adjustedWidth_pt: adjustedWidth,
      adjustedHeight_pt: image.height * spreadHeightPt,
    });

    currentX += adjustedWidth + gapHorizontal;
  });

  return adjustedLayout;
};

/**
 * Gap adjustment function for templates that already have built-in gaps.
 * These templates (like "clean" variants) are designed with spacing included,
 * so additional image gaps should not be applied to avoid double-gapping.
 */
export const adjustPreGappedTemplateForGap = (
  baseLayout: TemplateImage[],
  imageGapInput: number | ImageGap,
  spreadWidthPt: number,
  spreadHeightPt: number
): AdjustedPlaceholderPt[] => {
  // Handle both single number and object-based imageGapInput
  const gapHorizontal = typeof imageGapInput === 'number' ? imageGapInput : imageGapInput.horizontal;

  // Check for zero gap case - apply small overlap to prevent hairlines
  if (gapHorizontal <= 0) {
    const overlap = 0.5;
    return baseLayout.map(p => ({
      id: p.id,
      adjustedX_pt: p.x * spreadWidthPt,
      adjustedY_pt: p.y * spreadHeightPt,
      adjustedWidth_pt: (p.width * spreadWidthPt) + overlap,
      adjustedHeight_pt: (p.height * spreadHeightPt) + overlap,
    }));
  }

  // For templates with built-in gaps, ignore the gap input and use original positioning
  // This prevents double-gapping since the template already has spacing designed in
  return baseLayout.map(p => ({
    id: p.id,
    adjustedX_pt: p.x * spreadWidthPt,
    adjustedY_pt: p.y * spreadHeightPt,
    adjustedWidth_pt: p.width * spreadWidthPt,
    adjustedHeight_pt: p.height * spreadHeightPt,
  }));
};