// src/lib/templates.ts

// src/lib/templates.ts

// Import interfaces and template arrays from subdirectories
import { TemplateData, TemplateImage, ImageGap, AdjustedPlaceholderPt } from './templates/interfaces';

// Re-export types for external use
export type { AdjustedPlaceholderPt, ImageGap };
import { templates1Image } from './templates/layouts/1-image';
import { templates2Image } from './templates/layouts/2-image';
import { templates3Image } from './templates/layouts/3-image';
import { templates4Image } from './templates/layouts/4-image';
import { templates5Image } from './templates/layouts/5-image';
import { templates6Image } from './templates/layouts/6-image';
import { templates7Image } from './templates/layouts/7-image'; // Import 7
import { templates8Image } from './templates/layouts/8-image'; // Import 8
// Import other counts as they are created (e.g., 10...)
import { templates9Image } from './templates/layouts/9-image';
import { templates10Image } from './templates/layouts/10-image'; // Import 10
import { templates11Image } from './templates/layouts/11-image';
import { templates12Image } from './templates/layouts/12-image';
import { templates13Image } from './templates/layouts/13-image';
import { templates14Image } from './templates/layouts/14-image';
import { templates15Image } from './templates/layouts/15-image';
import { templates16Image } from './templates/layouts/16-image';
import { templates17Image } from './templates/layouts/17-image';
import { eighteenImageTemplates } from './templates/layouts/18-image';
import { nineteenImageTemplates } from './templates/layouts/19-image';
import { twentyImageTemplates } from './templates/layouts/20-image';
import { twentyOneImageTemplates } from './templates/layouts/21-image';
import { twentyTwoImageTemplates } from './templates/layouts/22-image';
import { twentyThreeImageTemplates } from './templates/layouts/23-image';
import { twentyFourImageTemplates } from './templates/layouts/24-image';
import { twentyFiveImageTemplates } from './templates/layouts/25-image';
import { twentySixImageTemplates } from './templates/layouts/26-image';

// Combine all imported templates into a single array
// Add a blank template placeholder if needed by the UI logic
const blankTemplate: TemplateData = {
  id: '__blank__',
  name: 'Blank',
  images: [],
  classification: ['ALL']
};

export const allBookTemplates: TemplateData[] = [
  blankTemplate, // Ensure blank is available if needed
  ...templates1Image,
  ...templates2Image,
  ...templates3Image,
  ...templates4Image,
  ...templates5Image,
  ...templates6Image,
  ...templates7Image, // Add 7
  ...templates8Image, // Add 8
  ...templates9Image,
  ...templates10Image, // Add 10
  ...templates11Image,
  ...templates12Image,
  ...templates13Image,
  ...templates14Image,
  ...templates15Image,
  ...templates16Image,
  ...templates17Image,
  ...eighteenImageTemplates,
  ...nineteenImageTemplates,
  ...twentyImageTemplates,
  ...twentyOneImageTemplates,
  ...twentyTwoImageTemplates,
  ...twentyThreeImageTemplates,
  ...twentyFourImageTemplates,
  ...twentyFiveImageTemplates,
  ...twentySixImageTemplates,
  // Add more spreads here as needed
];

// Development-only duplicate ID detection
if (process.env.NODE_ENV === 'development') {
  const templateIds = allBookTemplates.map(t => t.id);
  const uniqueIds = new Set(templateIds);
  if (templateIds.length !== uniqueIds.size) {
    const duplicates = templateIds.filter((id, index) => templateIds.indexOf(id) !== index);
    console.error('🚨 DUPLICATE TEMPLATE IDs IN SOURCE DATA:', {
      duplicateIds: duplicates,
      totalTemplates: templateIds.length,
      uniqueCount: uniqueIds.size,
      message: 'Fix these duplicates in the template source files to prevent UI issues'
    });
  }
}

// Helper function to create a map from the array for easy lookup
export const createTemplateMap = (templates: TemplateData[]): Record<string, TemplateData> => {
  return templates.reduce((map, template) => {
    map[template.id] = template;
    return map;
  }, {} as Record<string, TemplateData>);
};

// --- Gap Adjustment Logic ---

// Define vertical gap multiplier constant
const VERTICAL_GAP_MULTIPLIER = 1; // Vertical gaps calculated same as horizontal gaps

const POINT_EPSILON = 0.01; // Tolerance for comparing calculated point values (used?)
const FRAC_EPSILON = 1e-6; // Tolerance for comparing base fractional values

// Helper function to check if a layout is a simple horizontal strip
const isHorizStripLayout = (layout: TemplateImage[]): boolean => {
  if (layout.length < 2) return false;
  const firstY = layout[0].y;
  const firstHeight = layout[0].height;
  // Check if all elements are on the same horizontal band
  const onSameBand = layout.every(p => Math.abs(p.y - firstY) < FRAC_EPSILON && Math.abs(p.height - firstHeight) < FRAC_EPSILON);
  if (!onSameBand) return false;

  // Check for contiguity
  const sortedLayout = [...layout].sort((a, b) => a.x - b.x);
  for (let i = 0; i < sortedLayout.length - 1; i++) {
    if (Math.abs((sortedLayout[i].x + sortedLayout[i].width) - sortedLayout[i+1].x) > FRAC_EPSILON * 10) { // Allow slightly larger epsilon for sum
      return false; // Not contiguous
    }
  }
  return true;
};

// Helper function to check if a layout is a simple vertical strip
const isVertStripLayout = (layout: TemplateImage[]): boolean => {
  if (layout.length < 2) return false;
  const firstX = layout[0].x;
  const firstWidth = layout[0].width;
  // Check if all elements are on the same vertical band
  const onSameBand = layout.every(p => Math.abs(p.x - firstX) < FRAC_EPSILON && Math.abs(p.width - firstWidth) < FRAC_EPSILON);
  if (!onSameBand) return false;

  // Check for contiguity
  const sortedLayout = [...layout].sort((a, b) => a.y - b.y);
  for (let i = 0; i < sortedLayout.length - 1; i++) {
    if (Math.abs((sortedLayout[i].y + sortedLayout[i].height) - sortedLayout[i+1].y) > FRAC_EPSILON * 10) { // Allow slightly larger epsilon for sum
      return false; // Not contiguous
    }
  }
  return true;
};


/**
 * Calculates the final position and dimensions of template placeholders in points,
 * adjusting for a specified gap between adjacent placeholders. Uses template-specific
 * gap adjustment logic when available, otherwise falls back to generic logic.
 *
 * @param template The template object with layout and optional gap adjustment function.
 * @param imageGapInput The desired gap between images in points or object with horizontal/vertical gaps.
 * @param spreadWidthPt The total width of the spread in points.
 * @param spreadHeightPt The total height of the spread in points.
 * @returns Array of adjusted placeholder data with dimensions in points.
 */
export const adjustTemplateForGap = (
  template: TemplateData | TemplateImage[], // Accept template object or legacy baseLayout for backwards compatibility
  imageGapInput: number | ImageGap,
  spreadWidthPt: number,
  spreadHeightPt: number
): AdjustedPlaceholderPt[] => {

  // Handle both new template object and legacy baseLayout array for backwards compatibility
  const isLegacyCall = Array.isArray(template);
  const baseLayout = isLegacyCall ? template : template.images;
  const templateObj = isLegacyCall ? null : template;

  // If template has custom gap adjustment logic, use it
  if (templateObj?.adjustForGap) {
    return templateObj.adjustForGap(baseLayout, imageGapInput, spreadWidthPt, spreadHeightPt);
  }

  // Fallback to generic gap adjustment logic for templates without custom handling
  return adjustTemplateForGapGeneric(baseLayout, imageGapInput, spreadWidthPt, spreadHeightPt);
};

/**
 * Generic gap adjustment logic for templates without custom handling.
 * This contains the fallback logic for common layout patterns.
 */
export const adjustTemplateForGapGeneric = (
  baseLayout: TemplateImage[],
  imageGapInput: number | ImageGap,
  spreadWidthPt: number,
  spreadHeightPt: number
): AdjustedPlaceholderPt[] => {

  // Handle both single number and object-based imageGapInput
  // Apply VERTICAL_GAP_MULTIPLIER only for backward compatibility if a single number is passed
  const gapHorizontal = typeof imageGapInput === 'number' ? imageGapInput : imageGapInput.horizontal;
  const gapVertical = typeof imageGapInput === 'number' ? imageGapInput * VERTICAL_GAP_MULTIPLIER : imageGapInput.vertical;

  // Check the effective horizontal gap for the zero-gap overlap logic
  if (gapHorizontal <= 0) {
    // If no horizontal gap, convert percentages to points and add a small overlap
    // to prevent hairlines due to browser rendering of percentages.
    // Note: Vertical gap might still exist if input was an object, but overlap logic is based on horizontal.
    const overlap = 0.5; // Adjusted in PT
    return baseLayout.map(p => ({
      id: p.id,
      // No adjustment needed for x=0 or y=0
      adjustedX_pt: p.x * spreadWidthPt,
      adjustedY_pt: p.y * spreadHeightPt,
      // Slightly increase width and height to create overlap
      adjustedWidth_pt: (p.width * spreadWidthPt) + overlap,
      adjustedHeight_pt: (p.height * spreadHeightPt) + overlap,
    }));
  }
// --- Special Handling for Large Left/Right 4 Grid ---
// Check for large-left-4-grid structure (img1 width=0.5, others x>=0.5)
const isLargeLeft4Grid = baseLayout.length === 5 &&
                         baseLayout.some(p => p.id === 'img1' && Math.abs(p.width - 0.5) < FRAC_EPSILON && Math.abs(p.x - 0) < FRAC_EPSILON) &&
                         baseLayout.filter(p => p.id !== 'img1').every(p => p.x >= 0.5 - FRAC_EPSILON);

if (isLargeLeft4Grid) {
    const leftPane = baseLayout.find(p => p.id === 'img1')!;
    const gridPlaceholders = baseLayout.filter(p => p.id !== 'img1').sort((a, b) => a.y === b.y ? a.x - b.x : a.y - b.y); // Sort top-left to bottom-right

    const adjustedLayout: AdjustedPlaceholderPt[] = [];

    // --- Calculate Grid Dimensions ---
    // Grid column occupies right half, less half gap from left pane
    const gridColAvailableWidth = (spreadWidthPt * 0.5) - (gapHorizontal / 2);
    // Grid has 1 horizontal gap between its 2 columns
    const gridHorizGapTotal = gapHorizontal;
    const gridFinalWidthPt = Math.max(0, (gridColAvailableWidth - gridHorizGapTotal) / 2);
    // Grid has 1 vertical gap between its 2 rows
    const gridVertGapTotal = gapVertical; // Use calculated vertical gap
    const gridFinalHeightPt = Math.max(0, (spreadHeightPt - gridVertGapTotal) / 2);
    // --- End Grid Dimensions ---

    // Left Pane (img1) - adjust width based on right neighbors
    const leftPaneInitialX = leftPane.x * spreadWidthPt;
    const leftPaneInitialY = leftPane.y * spreadHeightPt;
    const leftPaneInitialWidth = leftPane.width * spreadWidthPt;
    const leftPaneInitialHeight = leftPane.height * spreadHeightPt;
    const leftPaneWidthAdjustment = gapHorizontal / 2; // Only has right neighbors
    const leftPaneFinalWidth = Math.max(0, leftPaneInitialWidth - leftPaneWidthAdjustment);
    adjustedLayout.push({
        id: leftPane.id,
        adjustedX_pt: leftPaneInitialX,
        adjustedY_pt: leftPaneInitialY,
        adjustedWidth_pt: leftPaneFinalWidth,
        adjustedHeight_pt: leftPaneInitialHeight,
    });

    // Grid Placeholders (img2, img3, img4, img5)
    const gridStartX = spreadWidthPt * 0.5 + gapHorizontal / 2; // Start after left pane + half gap

    gridPlaceholders.forEach((p) => {
        // Determine row (0 or 1) and column (0 or 1) based on original fractions
        const colIndex = (Math.abs(p.x - 0.5) < FRAC_EPSILON) ? 0 : 1;
        const rowIndex = (Math.abs(p.y - 0.0) < FRAC_EPSILON) ? 0 : 1;

        const adjustedX_pt = gridStartX + colIndex * (gridFinalWidthPt + gapHorizontal);
        const adjustedY_pt = rowIndex * (gridFinalHeightPt + gapVertical); // Use calculated vertical gap

        adjustedLayout.push({
            id: p.id,
            adjustedX_pt: adjustedX_pt,
            adjustedY_pt: adjustedY_pt,
            adjustedWidth_pt: gridFinalWidthPt,
            adjustedHeight_pt: gridFinalHeightPt,
        });
    });
    return adjustedLayout;
}

// Check for large-right-4-grid structure (img1 width=0.5, others x<0.5)
const isLargeRight4Grid = baseLayout.length === 5 &&
                          baseLayout.some(p => p.id === 'img1' && Math.abs(p.width - 0.5) < FRAC_EPSILON && Math.abs(p.x - 0.5) < FRAC_EPSILON) &&
                          baseLayout.filter(p => p.id !== 'img1').every(p => p.x < 0.5 - FRAC_EPSILON);

 if (isLargeRight4Grid) {
    const rightPane = baseLayout.find(p => p.id === 'img1')!;
    const gridPlaceholders = baseLayout.filter(p => p.id !== 'img1').sort((a, b) => a.y === b.y ? a.x - b.x : a.y - b.y); // Sort top-left to bottom-right

    const adjustedLayout: AdjustedPlaceholderPt[] = [];

    // --- Calculate Grid Dimensions ---
    // Grid column occupies left half, less half gap from right pane
    const gridColAvailableWidth = (spreadWidthPt * 0.5) - (gapHorizontal / 2);
    // Grid has 1 horizontal gap between its 2 columns
    const gridHorizGapTotal = gapHorizontal;
    const gridFinalWidthPt = Math.max(0, (gridColAvailableWidth - gridHorizGapTotal) / 2);
    // Grid has 1 vertical gap between its 2 rows
    const gridVertGapTotal = gapVertical; // Use calculated vertical gap
    const gridFinalHeightPt = Math.max(0, (spreadHeightPt - gridVertGapTotal) / 2);
    // --- End Grid Dimensions ---

     // Right Pane (img1) - adjust width based on left neighbors
    const rightPaneInitialX = rightPane.x * spreadWidthPt;
    const rightPaneInitialY = rightPane.y * spreadHeightPt;
    const rightPaneInitialWidth = rightPane.width * spreadWidthPt;
    const rightPaneInitialHeight = rightPane.height * spreadHeightPt;
    const rightPaneWidthAdjustment = gapHorizontal / 2; // Only has left neighbors
    const rightPaneFinalWidth = Math.max(0, rightPaneInitialWidth - rightPaneWidthAdjustment);
    const rightPaneAdjustedX = rightPaneInitialX + rightPaneWidthAdjustment; // Shift right by half gap
    adjustedLayout.push({
        id: rightPane.id,
        adjustedX_pt: rightPaneAdjustedX,
        adjustedY_pt: rightPaneInitialY,
        adjustedWidth_pt: rightPaneFinalWidth,
        adjustedHeight_pt: rightPaneInitialHeight,
    });


    // Grid Placeholders (img2, img3, img4, img5)
    const gridStartX = 0; // Grid starts at the left edge

    gridPlaceholders.forEach((p) => {
        // Determine row (0 or 1) and column (0 or 1) based on original fractions
        const colIndex = (Math.abs(p.x - 0.0) < FRAC_EPSILON) ? 0 : 1;
        const rowIndex = (Math.abs(p.y - 0.0) < FRAC_EPSILON) ? 0 : 1;

        const adjustedX_pt = gridStartX + colIndex * (gridFinalWidthPt + gapHorizontal);
        const adjustedY_pt = rowIndex * (gridFinalHeightPt + gapVertical); // Use calculated vertical gap

        adjustedLayout.push({
            id: p.id,
            adjustedX_pt: adjustedX_pt,
            adjustedY_pt: adjustedY_pt,
            adjustedWidth_pt: gridFinalWidthPt,
            adjustedHeight_pt: gridFinalHeightPt,
        });
    });
    return adjustedLayout;
}

  // --- Special Handling for Grid Layouts ---
  
  // Special Handling for 2x2 Grid (4 images)
  const isGrid2x2 = baseLayout.length === 4 && 
                     baseLayout.every(p => Math.abs(p.width - 0.5) < FRAC_EPSILON && Math.abs(p.height - 0.5) < FRAC_EPSILON);

  if (isGrid2x2) {
      const numCols = 2;
      const numRows = 2;

      const totalHorizGap = (numCols - 1) * gapHorizontal;
      const availableWidth = spreadWidthPt - totalHorizGap;
      const finalWidthPt = Math.max(0, availableWidth / numCols);

      const totalVertGap = (numRows - 1) * gapVertical;
      const availableHeight = spreadHeightPt - totalVertGap;
      const finalHeightPt = Math.max(0, availableHeight / numRows);

      const adjustedLayout: AdjustedPlaceholderPt[] = baseLayout.map((p) => {
          let colIndex = -1;
          let rowIndex = -1;

          // Determine position based on original fractional coordinates
          if (Math.abs(p.x - 0.0) < FRAC_EPSILON) colIndex = 0;
          else if (Math.abs(p.x - 0.5) < FRAC_EPSILON) colIndex = 1;

          if (Math.abs(p.y - 0.0) < FRAC_EPSILON) rowIndex = 0;
          else if (Math.abs(p.y - 0.5) < FRAC_EPSILON) rowIndex = 1;

          if (colIndex === -1 || rowIndex === -1) {
              console.warn(`[adjustTemplateForGap] Could not determine row/col for placeholder ${p.id} in 2x2 grid layout.`);
              colIndex = 0; rowIndex = 0; // Fallback
          }

          const adjustedX_pt = colIndex * (finalWidthPt + gapHorizontal);
          const adjustedY_pt = rowIndex * (finalHeightPt + gapVertical);

          return {
              id: p.id,
              adjustedX_pt: adjustedX_pt,
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: finalWidthPt,
              adjustedHeight_pt: finalHeightPt,
          };
      });
      return adjustedLayout;
  }

  // Special Handling for Left/Right Pane | 3 Grid (4 images)
  const isLeftPane3Grid = baseLayout.length === 4 &&
                          baseLayout.some(p => p.id === 'img1' && Math.abs(p.width - 0.5) < FRAC_EPSILON && Math.abs(p.x - 0) < FRAC_EPSILON && Math.abs(p.height - 1.0) < FRAC_EPSILON) &&
                          baseLayout.filter(p => p.id !== 'img1').every(p => Math.abs(p.x - 0.5) < FRAC_EPSILON && Math.abs(p.width - 0.5) < FRAC_EPSILON && Math.abs(p.height - 1/3) < FRAC_EPSILON);

  const isRightPane3Grid = baseLayout.length === 4 &&
                           baseLayout.some(p => p.id === 'img1' && Math.abs(p.width - 0.5) < FRAC_EPSILON && Math.abs(p.x - 0.5) < FRAC_EPSILON && Math.abs(p.height - 1.0) < FRAC_EPSILON) &&
                           baseLayout.filter(p => p.id !== 'img1').every(p => Math.abs(p.x - 0) < FRAC_EPSILON && Math.abs(p.width - 0.5) < FRAC_EPSILON && Math.abs(p.height - 1/3) < FRAC_EPSILON);

  if (isLeftPane3Grid || isRightPane3Grid) {
      const leftPane = isLeftPane3Grid ? baseLayout.find(p => p.id === 'img1')! : null;
      const rightPane = isRightPane3Grid ? baseLayout.find(p => p.id === 'img1')! : null;
      const gridPlaceholders = baseLayout.filter(p => p.id !== 'img1').sort((a, b) => a.y - b.y); // Sort by y position (top to bottom)

      const adjustedLayout: AdjustedPlaceholderPt[] = [];

      // --- Calculate Pane Dimensions ---
      const paneWidth = (spreadWidthPt * 0.5) - (gapHorizontal / 2); // Half width minus half gap

      // --- Calculate Grid Dimensions ---
      // Grid occupies the other half, less half gap from the pane
      const gridColAvailableWidth = (spreadWidthPt * 0.5) - (gapHorizontal / 2);
      const gridColAvailableHeight = spreadHeightPt - (2 * gapVertical); // 3 images with 2 gaps between them
      const gridImageHeight = Math.max(0, gridColAvailableHeight / 3);

      // --- Adjust Left/Right Pane ---
      if (leftPane) {
          adjustedLayout.push({
              id: leftPane.id,
              adjustedX_pt: 0,
              adjustedY_pt: 0,
              adjustedWidth_pt: paneWidth,
              adjustedHeight_pt: spreadHeightPt,
          });
      }

      if (rightPane) {
          adjustedLayout.push({
              id: rightPane.id,
              adjustedX_pt: (spreadWidthPt * 0.5) + (gapHorizontal / 2),
              adjustedY_pt: 0,
              adjustedWidth_pt: paneWidth,
              adjustedHeight_pt: spreadHeightPt,
          });
      }

      // --- Adjust Grid Placeholders ---
      gridPlaceholders.forEach((placeholder, index) => {
          const adjustedX_pt = isLeftPane3Grid ? 
              (spreadWidthPt * 0.5) + (gapHorizontal / 2) : // Right side for left pane layout
              0; // Left side for right pane layout
          const adjustedY_pt = index * (gridImageHeight + gapVertical);

          adjustedLayout.push({
              id: placeholder.id,
              adjustedX_pt,
              adjustedY_pt,
              adjustedWidth_pt: gridColAvailableWidth,
              adjustedHeight_pt: gridImageHeight,
          });
      });

      return adjustedLayout;
  }

  const isGrid3x2 = baseLayout.length === 6 && baseLayout.every(p => Math.abs(p.height - 0.5) < FRAC_EPSILON); // Check if all heights are 0.5
  const isGrid2x3 = baseLayout.length === 6 && baseLayout.every(p => Math.abs(p.width - 0.5) < FRAC_EPSILON); // Check if all widths are 0.5

  if (isGrid3x2 || isGrid2x3) {
      const numCols = isGrid3x2 ? 3 : 2;
      const numRows = isGrid3x2 ? 2 : 3;

      const totalHorizGap = (numCols - 1) * gapHorizontal;
      const availableWidth = spreadWidthPt - totalHorizGap;
      const finalWidthPt = Math.max(0, availableWidth / numCols);

      const totalVertGap = (numRows - 1) * gapVertical; // Use calculated vertical gap
      const availableHeight = spreadHeightPt - totalVertGap;
      const finalHeightPt = Math.max(0, availableHeight / numRows);

      const adjustedLayout: AdjustedPlaceholderPt[] = baseLayout.map((p) => {
          // Determine row and column index based on original fractions
          // Assuming consistent grid structure based on detection
          let colIndex = -1;
          let rowIndex = -1;

          if (isGrid3x2) {
              if (Math.abs(p.x - 0/3) < FRAC_EPSILON) colIndex = 0;
              else if (Math.abs(p.x - 1/3) < FRAC_EPSILON) colIndex = 1;
              else if (Math.abs(p.x - 2/3) < FRAC_EPSILON) colIndex = 2;

              if (Math.abs(p.y - 0.0) < FRAC_EPSILON) rowIndex = 0;
              else if (Math.abs(p.y - 0.5) < FRAC_EPSILON) rowIndex = 1;
          } else { // isGrid2x3
              if (Math.abs(p.x - 0.0) < FRAC_EPSILON) colIndex = 0;
              else if (Math.abs(p.x - 0.5) < FRAC_EPSILON) colIndex = 1;

              if (Math.abs(p.y - 0/3) < FRAC_EPSILON) rowIndex = 0;
              else if (Math.abs(p.y - 1/3) < FRAC_EPSILON) rowIndex = 1;
              else if (Math.abs(p.y - 2/3) < FRAC_EPSILON) rowIndex = 2;
          }

          if (colIndex === -1 || rowIndex === -1) {
              console.warn(`[adjustTemplateForGap] Could not determine row/col for placeholder ${p.id} in grid layout.`);
              // Fallback or default position? For now, just calculate based on index 0,0
              colIndex = 0;
              rowIndex = 0;
          }

          const adjustedX_pt = colIndex * (finalWidthPt + gapHorizontal);
          const adjustedY_pt = rowIndex * (finalHeightPt + gapVertical); // Use calculated vertical gap

                                                        
          return {
              id: p.id,
              adjustedX_pt: adjustedX_pt,
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: finalWidthPt,
              adjustedHeight_pt: finalHeightPt,
          };
      });
      return adjustedLayout;
  }

  // --- Special Handling for 7 Alternating Grid ---
  const is7AltGrid = baseLayout.length === 7 &&
                     baseLayout.filter(p => Math.abs(p.y - 0.0) < FRAC_EPSILON).length === 3 && // 3 top row
                     baseLayout.filter(p => Math.abs(p.y - 0.5) < FRAC_EPSILON).length === 4; // 4 bottom row

  if (is7AltGrid) {
      const topRowPlaceholders = baseLayout.filter(p => Math.abs(p.y - 0.0) < FRAC_EPSILON).sort((a, b) => a.x - b.x);
      const bottomRowPlaceholders = baseLayout.filter(p => Math.abs(p.y - 0.5) < FRAC_EPSILON).sort((a, b) => a.x - b.x);

      const adjustedLayout: AdjustedPlaceholderPt[] = [];

      // Calculate dimensions for each row separately
      const numTopCols = topRowPlaceholders.length; // 3
      const totalTopHorizGap = (numTopCols - 1) * gapHorizontal;
      const availableTopWidth = spreadWidthPt - totalTopHorizGap;
      const finalTopWidthPt = Math.max(0, availableTopWidth / numTopCols);

      const numBottomCols = bottomRowPlaceholders.length; // 4
      const totalBottomHorizGap = (numBottomCols - 1) * gapHorizontal;
      const availableBottomWidth = spreadWidthPt - totalBottomHorizGap;
      const finalBottomWidthPt = Math.max(0, availableBottomWidth / numBottomCols);

      // Height calculation (2 rows, 1 vertical gap)
      const totalVertGap = gapVertical; // Use calculated vertical gap
      const availableHeight = spreadHeightPt - totalVertGap;
      const finalHeightPt = Math.max(0, availableHeight / 2);

      // Process Top Row
      topRowPlaceholders.forEach((p, index) => {
          const adjustedX_pt = index * (finalTopWidthPt + gapHorizontal);
          const adjustedY_pt = 0; // Top row starts at y=0
          adjustedLayout.push({
              id: p.id,
              adjustedX_pt: adjustedX_pt,
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: finalTopWidthPt,
              adjustedHeight_pt: finalHeightPt,
          });
      });

      // Process Bottom Row
      const bottomRowStartY = finalHeightPt + gapVertical; // Start after top row + gap
      bottomRowPlaceholders.forEach((p, index) => {
          const adjustedX_pt = index * (finalBottomWidthPt + gapHorizontal);
          const adjustedY_pt = bottomRowStartY;
          adjustedLayout.push({
              id: p.id,
              adjustedX_pt: adjustedX_pt,
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: finalBottomWidthPt,
              adjustedHeight_pt: finalHeightPt,
          });
      });

      return adjustedLayout;
  }

  // --- Special Handling for 7 Vertical Focus ---
  const is7VertFocus = baseLayout.length === 7 &&
                       baseLayout.some(p => p.id === 'img1' && Math.abs(p.height - 1.0) < FRAC_EPSILON && Math.abs(p.width - 0.4) < FRAC_EPSILON); // Check for tall center image

  if (is7VertFocus) {
      const centerPane = baseLayout.find(p => p.id === 'img1')!;
      const leftStackPlaceholders = baseLayout.filter(p => p.x < 0.5 && p.id !== 'img1').sort((a, b) => a.y - b.y); // img2, img4, img6
      const rightStackPlaceholders = baseLayout.filter(p => p.x > 0.5 && p.id !== 'img1').sort((a, b) => a.y - b.y); // img3, img5, img7

      const adjustedLayout: AdjustedPlaceholderPt[] = [];

      // --- Calculate Stack Dimensions ---
      const numStackRows = 3;
      const totalStackVertGap = (numStackRows - 1) * gapVertical; // Use calculated vertical gap
      const availableStackHeight = spreadHeightPt - totalStackVertGap;
      const finalStackHeightPt = Math.max(0, availableStackHeight / numStackRows);
      // --- End Stack Dimensions ---

      // Center Pane (img1) - adjust width based on left and right neighbors
      const centerInitialX = centerPane.x * spreadWidthPt;
      const centerInitialY = centerPane.y * spreadHeightPt;
      const centerInitialWidth = centerPane.width * spreadWidthPt;
      const centerInitialHeight = centerPane.height * spreadHeightPt;
      const centerWidthAdjustment = (gapHorizontal / 2) + (gapHorizontal / 2); // Neighbors on both sides
      const centerFinalWidth = Math.max(0, centerInitialWidth - centerWidthAdjustment);
      const centerAdjustedX = centerInitialX + (gapHorizontal / 2); // Shift right by half gap
      adjustedLayout.push({
          id: centerPane.id,
          adjustedX_pt: centerAdjustedX,
          adjustedY_pt: centerInitialY, // Starts at y=0
          adjustedWidth_pt: centerFinalWidth,
          adjustedHeight_pt: centerInitialHeight, // Full height
      });

      // Left Stack (img2, img4, img6)
      const leftStackInitialX = leftStackPlaceholders[0].x * spreadWidthPt; // All have same x=0
      const leftStackInitialWidth = leftStackPlaceholders[0].width * spreadWidthPt; // All have same width=0.3
      const leftStackWidthAdjustment = gapHorizontal / 2; // Only right neighbor (img1)
      const leftStackFinalWidth = Math.max(0, leftStackInitialWidth - leftStackWidthAdjustment);
      // No X adjustment needed as it starts at 0

      leftStackPlaceholders.forEach((p, index) => {
          const adjustedY_pt = index * (finalStackHeightPt + gapVertical); // Use calculated vertical gap
          adjustedLayout.push({
              id: p.id,
              adjustedX_pt: leftStackInitialX, // Should be 0
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: leftStackFinalWidth,
              adjustedHeight_pt: finalStackHeightPt,
          });
      });

       // Right Stack (img3, img5, img7)
      const rightStackInitialX = rightStackPlaceholders[0].x * spreadWidthPt; // All have same x=0.7
      const rightStackInitialWidth = rightStackPlaceholders[0].width * spreadWidthPt; // All have same width=0.3
      const rightStackWidthAdjustment = gapHorizontal / 2; // Only left neighbor (img1)
      const rightStackFinalWidth = Math.max(0, rightStackInitialWidth - rightStackWidthAdjustment);
      const rightStackAdjustedX = rightStackInitialX + (gapHorizontal / 2); // Shift right

      rightStackPlaceholders.forEach((p, index) => {
          const adjustedY_pt = index * (finalStackHeightPt + gapVertical); // Use calculated vertical gap
          adjustedLayout.push({
              id: p.id,
              adjustedX_pt: rightStackAdjustedX,
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: rightStackFinalWidth,
              adjustedHeight_pt: finalStackHeightPt,
          });
      });

      return adjustedLayout;
  }

  // --- Special Handling for 8-Image Grid Layouts ---
  const isGrid4x2 = baseLayout.length === 8 && baseLayout.every(p => Math.abs(p.height - 0.5) < FRAC_EPSILON); // Check if all heights are 0.5
  const isGrid2x4 = baseLayout.length === 8 && baseLayout.every(p => Math.abs(p.width - 0.5) < FRAC_EPSILON); // Check if all widths are 0.5

  if (isGrid4x2 || isGrid2x4) {
      const numCols = isGrid4x2 ? 4 : 2;
      const numRows = isGrid4x2 ? 2 : 4;

      const totalHorizGap = (numCols - 1) * gapHorizontal;
      const availableWidth = spreadWidthPt - totalHorizGap;
      const finalWidthPt = Math.max(0, availableWidth / numCols);

      const totalVertGap = (numRows - 1) * gapVertical; // Use calculated vertical gap
      const availableHeight = spreadHeightPt - totalVertGap;
      const finalHeightPt = Math.max(0, availableHeight / numRows);

      const adjustedLayout: AdjustedPlaceholderPt[] = baseLayout.map((p) => {
          // Determine row and column index based on original fractions
          let colIndex = -1;
          let rowIndex = -1;

          if (isGrid4x2) {
              if (Math.abs(p.x - 0/4) < FRAC_EPSILON) colIndex = 0;
              else if (Math.abs(p.x - 1/4) < FRAC_EPSILON) colIndex = 1;
              else if (Math.abs(p.x - 2/4) < FRAC_EPSILON) colIndex = 2;
              else if (Math.abs(p.x - 3/4) < FRAC_EPSILON) colIndex = 3;

              if (Math.abs(p.y - 0.0) < FRAC_EPSILON) rowIndex = 0;
              else if (Math.abs(p.y - 0.5) < FRAC_EPSILON) rowIndex = 1;
          } else { // isGrid2x4
              if (Math.abs(p.x - 0.0) < FRAC_EPSILON) colIndex = 0;
              else if (Math.abs(p.x - 0.5) < FRAC_EPSILON) colIndex = 1;

              if (Math.abs(p.y - 0/4) < FRAC_EPSILON) rowIndex = 0;
              else if (Math.abs(p.y - 1/4) < FRAC_EPSILON) rowIndex = 1;
              else if (Math.abs(p.y - 2/4) < FRAC_EPSILON) rowIndex = 2;
              else if (Math.abs(p.y - 3/4) < FRAC_EPSILON) rowIndex = 3;
          }

          if (colIndex === -1 || rowIndex === -1) {
              console.warn(`[adjustTemplateForGap] Could not determine row/col for placeholder ${p.id} in 8-image grid layout.`);
              colIndex = 0; rowIndex = 0; // Fallback
          }

          const adjustedX_pt = colIndex * (finalWidthPt + gapHorizontal);
          const adjustedY_pt = rowIndex * (finalHeightPt + gapVertical); // Use calculated vertical gap

                                                        
          return {
              id: p.id,
              adjustedX_pt: adjustedX_pt,
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: finalWidthPt,
              adjustedHeight_pt: finalHeightPt,
          };
      });
      return adjustedLayout;
  }

  // --- Special Handling for 10-Image Grid Layouts ---
  const isGrid5x2 = baseLayout.length === 10 && baseLayout.every(p => Math.abs(p.height - 0.5) < FRAC_EPSILON); // Check if all heights are 0.5
  const isGrid2x5 = baseLayout.length === 10 && baseLayout.every(p => Math.abs(p.width - 0.5) < FRAC_EPSILON); // Check if all widths are 0.5

  if (isGrid5x2 || isGrid2x5) {
      const numCols = isGrid5x2 ? 5 : 2;
      const numRows = isGrid5x2 ? 2 : 5;

      const totalHorizGap = (numCols - 1) * gapHorizontal;
      const availableWidth = spreadWidthPt - totalHorizGap;
      const finalWidthPt = Math.max(0, availableWidth / numCols);

      const totalVertGap = (numRows - 1) * gapVertical; // Use calculated vertical gap
      const availableHeight = spreadHeightPt - totalVertGap;
      const finalHeightPt = Math.max(0, availableHeight / numRows);

      const adjustedLayout: AdjustedPlaceholderPt[] = baseLayout.map((p) => {
          // Determine row and column index based on original fractions
          let colIndex = -1;
          let rowIndex = -1;

          if (isGrid5x2) {
              if (Math.abs(p.x - 0/5) < FRAC_EPSILON) colIndex = 0;
              else if (Math.abs(p.x - 1/5) < FRAC_EPSILON) colIndex = 1;
              else if (Math.abs(p.x - 2/5) < FRAC_EPSILON) colIndex = 2;
              else if (Math.abs(p.x - 3/5) < FRAC_EPSILON) colIndex = 3;
              else if (Math.abs(p.x - 4/5) < FRAC_EPSILON) colIndex = 4;

              if (Math.abs(p.y - 0.0) < FRAC_EPSILON) rowIndex = 0;
              else if (Math.abs(p.y - 0.5) < FRAC_EPSILON) rowIndex = 1;
          } else { // isGrid2x5
              if (Math.abs(p.x - 0.0) < FRAC_EPSILON) colIndex = 0;
              else if (Math.abs(p.x - 0.5) < FRAC_EPSILON) colIndex = 1;

              if (Math.abs(p.y - 0/5) < FRAC_EPSILON) rowIndex = 0;
              else if (Math.abs(p.y - 1/5) < FRAC_EPSILON) rowIndex = 1;
              else if (Math.abs(p.y - 2/5) < FRAC_EPSILON) rowIndex = 2;
              else if (Math.abs(p.y - 3/5) < FRAC_EPSILON) rowIndex = 3;
              else if (Math.abs(p.y - 4/5) < FRAC_EPSILON) rowIndex = 4;
          }

          if (colIndex === -1 || rowIndex === -1) {
              console.warn(`[adjustTemplateForGap] Could not determine row/col for placeholder ${p.id} in 10-image grid layout.`);
              colIndex = 0; rowIndex = 0; // Fallback
          }

          const adjustedX_pt = colIndex * (finalWidthPt + gapHorizontal);
          const adjustedY_pt = rowIndex * (finalHeightPt + gapVertical); // Use calculated vertical gap


          return {
              id: p.id,
              adjustedX_pt: adjustedX_pt,
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: finalWidthPt,
              adjustedHeight_pt: finalHeightPt,
          };
      });
      return adjustedLayout;
  }

  // --- Special Handling for Center Split Strip + 6 ---
  const isCenterSplit6 = baseLayout.length === 8 &&
                         baseLayout.some(p => p.id === 'img1' && Math.abs(p.height - 0.5) < FRAC_EPSILON && Math.abs(p.width - 0.4) < FRAC_EPSILON && Math.abs(p.x - 0.3) < FRAC_EPSILON) && // Check img1 props
                         baseLayout.some(p => p.id === 'img2' && Math.abs(p.height - 0.5) < FRAC_EPSILON && Math.abs(p.width - 0.4) < FRAC_EPSILON && Math.abs(p.x - 0.3) < FRAC_EPSILON); // Check img2 props

  if (isCenterSplit6) {
      const centerTop = baseLayout.find(p => p.id === 'img1')!;
      const centerBottom = baseLayout.find(p => p.id === 'img2')!;
      const leftStackPlaceholders = baseLayout.filter(p => p.x < 0.5 && p.id !== 'img1' && p.id !== 'img2').sort((a, b) => a.y - b.y); // img3, img5, img7
      const rightStackPlaceholders = baseLayout.filter(p => p.x > 0.5 && p.id !== 'img1' && p.id !== 'img2').sort((a, b) => a.y - b.y); // img4, img6, img8

      const adjustedLayout: AdjustedPlaceholderPt[] = [];

      // --- Calculate Stack Dimensions ---
      const numStackRows = 3;
      const totalStackVertGap = (numStackRows - 1) * gapVertical; // Use calculated vertical gap
      const availableStackHeight = spreadHeightPt - totalStackVertGap;
      const finalStackHeightPt = Math.max(0, availableStackHeight / numStackRows);
      // --- End Stack Dimensions ---

      // --- Calculate Center Dimensions ---
      const centerInitialWidth = centerTop.width * spreadWidthPt; // Same for top/bottom
      const centerInitialHeight = centerTop.height * spreadHeightPt; // Same for top/bottom
      const centerWidthAdjustment = (gapHorizontal / 2) + (gapHorizontal / 2); // Neighbors left and right
      const centerHeightAdjustment = gapVertical / 2; // Top has bottom neighbor, Bottom has top neighbor
      const centerFinalWidth = Math.max(0, centerInitialWidth - centerWidthAdjustment);
      const centerFinalHeight = Math.max(0, centerInitialHeight - centerHeightAdjustment);
      const centerAdjustedX = (centerTop.x * spreadWidthPt) + (gapHorizontal / 2); // Shift right
      // --- End Center Dimensions ---

      // Center Top (img1)
      adjustedLayout.push({
          id: centerTop.id,
          adjustedX_pt: centerAdjustedX,
          adjustedY_pt: 0, // Starts at y=0
          adjustedWidth_pt: centerFinalWidth,
          adjustedHeight_pt: centerFinalHeight,
      });

      // Center Bottom (img2)
      const centerBottomAdjustedY = centerFinalHeight + gapVertical; // Below top + gap
      adjustedLayout.push({
          id: centerBottom.id,
          adjustedX_pt: centerAdjustedX,
          adjustedY_pt: centerBottomAdjustedY,
          adjustedWidth_pt: centerFinalWidth,
          adjustedHeight_pt: centerFinalHeight,
      });


      // Left Stack (img3, img5, img7)
      const leftStackInitialX = leftStackPlaceholders[0].x * spreadWidthPt; // All have same x=0
      const leftStackInitialWidth = leftStackPlaceholders[0].width * spreadWidthPt; // All have same width=0.3
      const leftStackWidthAdjustment = gapHorizontal / 2; // Only right neighbor (img1/2)
      const leftStackFinalWidth = Math.max(0, leftStackInitialWidth - leftStackWidthAdjustment);
      // No X adjustment needed as it starts at 0

      leftStackPlaceholders.forEach((p, index) => {
          const adjustedY_pt = index * (finalStackHeightPt + gapVertical); // Use calculated vertical gap
          adjustedLayout.push({
              id: p.id,
              adjustedX_pt: leftStackInitialX, // Should be 0
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: leftStackFinalWidth,
              adjustedHeight_pt: finalStackHeightPt,
          });
      });

       // Right Stack (img4, img6, img8)
      const rightStackInitialX = rightStackPlaceholders[0].x * spreadWidthPt; // All have same x=0.7
      const rightStackInitialWidth = rightStackPlaceholders[0].width * spreadWidthPt; // All have same width=0.3
      const rightStackWidthAdjustment = gapHorizontal / 2; // Only left neighbor (img1/2)
      const rightStackFinalWidth = Math.max(0, rightStackInitialWidth - rightStackWidthAdjustment);
      const rightStackAdjustedX = rightStackInitialX + (gapHorizontal / 2); // Shift right

      rightStackPlaceholders.forEach((p, index) => {
          const adjustedY_pt = index * (finalStackHeightPt + gapVertical); // Use calculated vertical gap
          adjustedLayout.push({
              id: p.id,
              adjustedX_pt: rightStackAdjustedX,
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: rightStackFinalWidth,
              adjustedHeight_pt: finalStackHeightPt,
          });
      });

      return adjustedLayout;
  }

  // --- Special Handling for 9-Image Cross Shape ---
  const isCrossShape9 = baseLayout.length === 9 &&
                        baseLayout.some(p => p.id === 'img3' && Math.abs(p.x - 1/3) < FRAC_EPSILON && Math.abs(p.y - 1/3) < FRAC_EPSILON); // Check center image position

  if (isCrossShape9) {

      // Treat as a 3x3 grid for equal distribution
      const numCols = 3;
      const numRows = 3;

      const totalHorizGap = (numCols - 1) * gapHorizontal;
      const availableWidth = spreadWidthPt - totalHorizGap;
      const finalWidthPt = Math.max(0, availableWidth / numCols);

      const totalVertGap = (numRows - 1) * gapVertical; // Use calculated vertical gap
      const availableHeight = spreadHeightPt - totalVertGap;
      const finalHeightPt = Math.max(0, availableHeight / numRows);

      const adjustedLayout: AdjustedPlaceholderPt[] = baseLayout.map((p) => {
          // Determine row and column index based on original fractions
          let colIndex = -1;
          let rowIndex = -1;

          // Column Index
          if (Math.abs(p.x - 0/3) < FRAC_EPSILON) colIndex = 0;
          else if (Math.abs(p.x - 1/3) < FRAC_EPSILON) colIndex = 1;
          else if (Math.abs(p.x - 2/3) < FRAC_EPSILON) colIndex = 2;

          // Row Index
          if (Math.abs(p.y - 0/3) < FRAC_EPSILON) rowIndex = 0;
          else if (Math.abs(p.y - 1/3) < FRAC_EPSILON) rowIndex = 1;
          else if (Math.abs(p.y - 2/3) < FRAC_EPSILON) rowIndex = 2;


          if (colIndex === -1 || rowIndex === -1) {
              console.warn(`[adjustTemplateForGap] Could not determine row/col for placeholder ${p.id} in cross-shape-9 layout.`);
              // Fallback or default position? For now, just calculate based on index 0,0
              colIndex = 0;
              rowIndex = 0;
          }

          const adjustedX_pt = colIndex * (finalWidthPt + gapHorizontal);
          const adjustedY_pt = rowIndex * (finalHeightPt + gapVertical); // Use calculated vertical gap


          return {
              id: p.id,
              adjustedX_pt: adjustedX_pt,
              adjustedY_pt: adjustedY_pt,
              adjustedWidth_pt: finalWidthPt,
              adjustedHeight_pt: finalHeightPt,
          };
      });
      return adjustedLayout;
  }

// --- Handle Simple Strip Layouts --- // This line was previously line 118




  // --- Handle Simple Strip Layouts ---
  if (isHorizStripLayout(baseLayout)) {
    const numPlaceholders = baseLayout.length;
    const totalGapWidth = (numPlaceholders - 1) * gapHorizontal;
    const availableWidth = spreadWidthPt - totalGapWidth;
    const finalWidthPt = Math.max(0, availableWidth / numPlaceholders); // Ensure non-negative

    // Sort by x position to calculate adjustedX correctly
    const sortedLayout = [...baseLayout].sort((a, b) => a.x - b.x);

    return sortedLayout.map((p, index) => {
      const adjustedX_pt = index * (finalWidthPt + gapHorizontal);
      const adjustedY_pt = p.y * spreadHeightPt; // Use original y/height
      const adjustedHeight_pt = p.height * spreadHeightPt;
      return {
        id: p.id,
        adjustedX_pt,
        adjustedY_pt,
        adjustedWidth_pt: finalWidthPt,
        adjustedHeight_pt,
      };
    });
  }

  if (isVertStripLayout(baseLayout)) {
    const numPlaceholders = baseLayout.length;
    const totalGapHeight = (numPlaceholders - 1) * gapVertical; // Use calculated vertical gap
    const availableHeight = spreadHeightPt - totalGapHeight;
    const finalHeightPt = Math.max(0, availableHeight / numPlaceholders); // Ensure non-negative

    // Sort by y position to calculate adjustedY correctly
    const sortedLayout = [...baseLayout].sort((a, b) => a.y - b.y);

    return sortedLayout.map((p, index) => {
      const adjustedX_pt = p.x * spreadWidthPt; // Use original x/width
      const adjustedWidth_pt = p.width * spreadWidthPt;
      const adjustedY_pt = index * (finalHeightPt + gapVertical); // Use calculated vertical gap
      return {
        id: p.id,
        adjustedX_pt,
        adjustedY_pt,
        adjustedWidth_pt,
        adjustedHeight_pt: finalHeightPt,
      };
    });
  }

  // --- Default case: apply gap proportionally to each placeholder ---
  // This handles general layouts that don't match any of the special cases above.
  const adjustedLayout: AdjustedPlaceholderPt[] = [];
  
  // Group placeholders by their spatial relationship
  for (const placeholder of baseLayout) {
    // Initialize adjustment values
    let leftAdjustment = 0;
    let rightAdjustment = 0;
    let topAdjustment = 0;
    let bottomAdjustment = 0;
    
    // Check if there are neighbors to the left
    const hasLeftNeighbor = baseLayout.some(p => 
      p !== placeholder && 
      Math.abs(p.x + p.width - placeholder.x) < FRAC_EPSILON &&
      (p.y < placeholder.y + placeholder.height && p.y + p.height > placeholder.y)
    );
    
    // Check if there are neighbors to the right
    const hasRightNeighbor = baseLayout.some(p => 
      p !== placeholder && 
      Math.abs(p.x - (placeholder.x + placeholder.width)) < FRAC_EPSILON &&
      (p.y < placeholder.y + placeholder.height && p.y + p.height > placeholder.y)
    );
    
    // Check if there are neighbors above
    const hasTopNeighbor = baseLayout.some(p => 
      p !== placeholder && 
      Math.abs(p.y + p.height - placeholder.y) < FRAC_EPSILON &&
      (p.x < placeholder.x + placeholder.width && p.x + p.width > placeholder.x)
    );
    
    // Check if there are neighbors below
    const hasBottomNeighbor = baseLayout.some(p => 
      p !== placeholder && 
      Math.abs(p.y - (placeholder.y + placeholder.height)) < FRAC_EPSILON &&
      (p.x < placeholder.x + placeholder.width && p.x + p.width > placeholder.x)
    );
    
    // Apply adjustments based on neighboring placeholders
    if (hasLeftNeighbor) leftAdjustment = gapHorizontal / 2;
    if (hasRightNeighbor) rightAdjustment = gapHorizontal / 2;
    if (hasTopNeighbor) topAdjustment = gapVertical / 2; // Use calculated vertical gap
    if (hasBottomNeighbor) bottomAdjustment = gapVertical / 2; // Use calculated vertical gap
    
    // Calculate final dimensions with adjustments
    const adjustedX_pt = placeholder.x * spreadWidthPt + leftAdjustment;
    const adjustedY_pt = placeholder.y * spreadHeightPt + topAdjustment;
    const adjustedWidth_pt = placeholder.width * spreadWidthPt - leftAdjustment - rightAdjustment;
    const adjustedHeight_pt = placeholder.height * spreadHeightPt - topAdjustment - bottomAdjustment;
    
    adjustedLayout.push({
      id: placeholder.id,
      adjustedX_pt,
      adjustedY_pt,
      adjustedWidth_pt,
      adjustedHeight_pt,
    });
  }
  
  return adjustedLayout;
};