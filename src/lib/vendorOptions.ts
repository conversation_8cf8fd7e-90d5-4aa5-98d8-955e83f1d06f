// Book size configurations for different vendors

export interface BookSize {
  id: string;
  title: string;
  ratio: string;
  dimensions: string;
  bleed: string;
  safetyMargin: string;
}

export interface VendorConfig {
  square: BookSize[];
  horizontal: BookSize[];
  vertical: BookSize[];
}

export const vendorOptions: { [key: string]: VendorConfig } = {
  Graphistudio: {
    square: [
      { id: 'g-square-4x4', title: '4×4', ratio: '1:1', dimensions: '3.62 × 3.70', bleed: '0.12', safetyMargin: '0.3' },
      { id: 'g-square-6x6', title: '6×6', ratio: '1:1', dimensions: '5.845 × 5.94', bleed: '0.12', safetyMargin: '0.40' },
      { id: 'g-square-8x8', title: '8×8', ratio: '1:1', dimensions: '8.385 × 8.54', bleed: '0.12', safetyMargin: '0.40' },
      { id: 'g-square-9.5x9.5', title: '9.5×9.5', ratio: '1:1', dimensions: '10.00 × 10.16', bleed: '0.12', safetyMargin: '0.60' },
      { id: 'g-square-12x12', title: '12×12', ratio: '1:1', dimensions: '11.81 × 12.01', bleed: '0.20', safetyMargin: '0.60' },
      { id: 'g-square-14x14', title: '14×14', ratio: '1:1', dimensions: '13.975 × 14.21', bleed: '0.24', safetyMargin: '0.60' },
      { id: 'g-square-16x16', title: '16×16', ratio: '1:1', dimensions: '15.845 × 16.10', bleed: '0.24', safetyMargin: '0.60' },
    ],
    horizontal: [
      { id: 'g-horizontal-5x4', title: '5×4', ratio: '2.5:2', dimensions: '5.275 × 3.86', bleed: '0.12', safetyMargin: '0.31' },
      { id: 'g-horizontal-8x6', title: '8×6', ratio: '4:3', dimensions: '7.99 × 5.87', bleed: '0.12', safetyMargin: '0.40' },
      { id: 'g-horizontal-12x8', title: '12×8', ratio: '6:4', dimensions: '11.455 × 8.39', bleed: '0.12', safetyMargin: '0.60' },
      { id: 'g-horizontal-13x9.5', title: '13×9.5', ratio: '6.5:4.75', dimensions: '13.72 × 10.04', bleed: '0.20', safetyMargin: '0.60' },
      { id: 'g-horizontal-16x12', title: '16×12', ratio: '8:6', dimensions: '16.14 × 11.81', bleed: '0.24', safetyMargin: '0.60' },
      { id: 'g-horizontal-18x14', title: '18×14', ratio: '9:7', dimensions: '18.565 × 13.58', bleed: '0.24', safetyMargin: '0.60' },
    ],
    vertical: [
      { id: 'g-vertical-4x5', title: '4×5', ratio: '4:5', dimensions: '3.86 × 5.28', bleed: '0.12', safetyMargin: '0.31' },
      { id: 'g-vertical-6x8', title: '6×8', ratio: '6:8', dimensions: '5.845 × 7.99', bleed: '0.12', safetyMargin: '0.40' },
      { id: 'g-vertical-8x12', title: '8×12', ratio: '8:12', dimensions: '8.385 × 11.46', bleed: '0.12', safetyMargin: '0.60' },
      { id: 'g-vertical-9.5x13', title: '9.5×13', ratio: '9.5:13', dimensions: '10.00 × 13.66', bleed: '0.20', safetyMargin: '0.60' },
      { id: 'g-vertical-12x16', title: '12×16', ratio: '12:16', dimensions: '11.81 × 16.14', bleed: '0.24', safetyMargin: '0.60' },
      { id: 'g-vertical-14x18', title: '14×18', ratio: '14:18', dimensions: '13.585 × 18.58', bleed: '0.24', safetyMargin: '0.60' },
      { id: 'g-vertical-16x20', title: '16×20', ratio: '16:20', dimensions: '14.645 × 20', bleed: '0.24', safetyMargin: '0.60' },
    ],
  },
  WHCC: {
    square: [],
    horizontal: [],
    vertical: [],
  }
};