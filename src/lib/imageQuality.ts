/**
 * Utility functions for checking image quality and DPI requirements
 */

import { ImageFile } from '@/components/ImageTray';
import { ImagePlacement } from '@/components/SpreadsTray';

// Target DPI for print quality
export const TARGET_DPI = 300;

// Quality threshold percentage (70% of target DPI is considered acceptable)
export const QUALITY_THRESHOLD = 0.7;

/**
 * Image quality status
 */
export enum ImageQualityStatus {
  GOOD = 'good',       // Image meets or exceeds target DPI
  WARNING = 'warning', // Image is below target DPI but above threshold
  POOR = 'poor'        // Image is below threshold DPI
}

/**
 * Image quality information
 */
export interface ImageQualityInfo {
  status: ImageQualityStatus;
  actualDpi: number;
  message: string;
  placeholderId: string;
  imageId: string;
}

/**
 * Calculate the effective DPI of an image based on its native resolution,
 * the transformation applied, and the physical dimensions of the placeholder
 * 
 * @param image The image file with natural dimensions
 * @param placement The image placement with transform information
 * @param placeholderWidthPt Width of the placeholder in points (1/72 inch)
 * @param placeholderHeightPt Height of the placeholder in points (1/72 inch)
 * @returns The effective DPI of the image or null if calculation isn't possible
 */
export function calculateEffectiveDpi(
  image: ImageFile,
  placement: ImagePlacement,
  placeholderWidthPt: number,
  placeholderHeightPt: number
): number | null {
  if (!image.naturalWidth || !image.naturalHeight || !placement.transform) {
    return null;
  }

  // Convert placeholder dimensions from points to inches (72 points = 1 inch)
  const placeholderWidthInch = placeholderWidthPt / 72;
  const placeholderHeightInch = placeholderHeightPt / 72;
  
  // Get original image dimensions and transform properties
  const imageWidth = image.naturalWidth;
  const imageHeight = image.naturalHeight;
  const { scale, fit } = placement.transform;

  // Calculate the effective image dimensions after applying the user's zoom/scale
  // When scale is larger, the image appears smaller (zoomed out)
  // When scale is smaller, the image appears larger (zoomed in)
  const effectiveImageWidth = imageWidth / scale;
  const effectiveImageHeight = imageHeight / scale;

  // Calculate the image aspect ratio and placeholder aspect ratio
  const imageAspectRatio = effectiveImageWidth / effectiveImageHeight;
  const placeholderAspectRatio = placeholderWidthInch / placeholderHeightInch;

  let effectiveDpi: number;

  if (fit === 'contain') {
    // In contain mode, the entire image is visible within the placeholder
    if (imageAspectRatio > placeholderAspectRatio) {
      // Image is wider than placeholder - width is the limiting factor
      // The image fills the placeholder width and is letterboxed on top/bottom
      effectiveDpi = effectiveImageWidth / placeholderWidthInch;
    } else {
      // Image is taller than placeholder - height is the limiting factor
      // The image fills the placeholder height and is letterboxed on left/right
      effectiveDpi = effectiveImageHeight / placeholderHeightInch;
    }
  } else if (fit === 'cover') {
    // In cover mode, the image fills the entire placeholder, potentially cropping parts
    // We need to use the minimum DPI between width and height calculations
    // because that represents the direction with less resolution
    const effectiveDpiWidth = effectiveImageWidth / placeholderWidthInch;
    const effectiveDpiHeight = effectiveImageHeight / placeholderHeightInch;
    effectiveDpi = Math.min(effectiveDpiWidth, effectiveDpiHeight);
  } else {
    // Should not happen with current fit modes, but handle defensively
    console.warn(`Unsupported fit mode: ${fit}`);
    return null;
  }

  return effectiveDpi;
}

/**
 * Check if an image meets the quality requirements for printing
 * 
 * @param image The image file with natural dimensions
 * @param placement The image placement with transform information
 * @param placeholderWidthPt Width of the placeholder in points (1/72 inch)
 * @param placeholderHeightPt Height of the placeholder in points (1/72 inch)
 * @param dpiWarningThresholdPercent User-defined threshold percentage from slider
 * @returns Image quality information
 */
export function checkImageQuality(
  image: ImageFile,
  placement: ImagePlacement,
  placeholderWidthPt: number,
  placeholderHeightPt: number,
  dpiWarningThresholdPercent: number = 70 // User-defined threshold from slider
): ImageQualityInfo | null {
  // Calculate effective DPI
  const effectiveDpi = calculateEffectiveDpi(image, placement, placeholderWidthPt, placeholderHeightPt);
  if (effectiveDpi === null) {
    return null;
  }

  // Determine quality status
  let status: ImageQualityStatus;
  let message: string;

  // Calculate the warning threshold based on the user's slider setting
  // This is the key change - we now fully respect the user's threshold setting
  const warningThresholdDpi = TARGET_DPI * (dpiWarningThresholdPercent / 100);
  
  // Calculate poor threshold as 50% of the target DPI (fixed at 150 DPI)
  // This is independent of the user's slider setting
  const poorThresholdDpi = TARGET_DPI * 0.5; // 150 DPI

  // Determine status based on thresholds
  if (effectiveDpi >= warningThresholdDpi) {
    // Image meets or exceeds the user-defined warning threshold
    status = ImageQualityStatus.GOOD;
    
    // Customize message based on whether it meets the absolute target
    if (effectiveDpi >= TARGET_DPI) {
      message = `Good quality: ${Math.round(effectiveDpi)} Effective DPI (meets target ${TARGET_DPI} DPI)`;
    } else {
      message = `Acceptable quality: ${Math.round(effectiveDpi)} Effective DPI (meets threshold ${Math.round(warningThresholdDpi)} DPI)`;
    }
  } else if (effectiveDpi >= poorThresholdDpi) {
    // Image is below warning threshold but above poor threshold
    status = ImageQualityStatus.WARNING;
    message = `Warning: ${Math.round(effectiveDpi)} Effective DPI (below threshold ${Math.round(warningThresholdDpi)} DPI)`;
    
  } else {
    // Image is below poor threshold - this is a severe quality issue
    status = ImageQualityStatus.POOR;
    message = `Poor quality: ${Math.round(effectiveDpi)} Effective DPI (below minimum ${Math.round(poorThresholdDpi)} DPI)`;
  }

  return {
    status,
    actualDpi: effectiveDpi,
    message,
    placeholderId: placement.placeholderId,
    imageId: placement.imageId || ''
  };
}

/**
 * Check all images in a spread for quality issues
 * 
 * @param images All available images
 * @param placements Image placements in the spread
 * @param placeholderDimensions Dimensions of each placeholder in points
 * @param dpiWarningThresholdPercent User-defined threshold percentage (0-100)
 * @returns Map of placeholderId to quality information for images with issues
*/
export function checkSpreadImagesQuality(
 images: ImageFile[],
 placements: ImagePlacement[],
 placeholderDimensions: Record<string, { width: number; height: number }>,
 dpiWarningThresholdPercent: number // Added parameter
): Record<string, ImageQualityInfo> {
 const qualityIssues: Record<string, ImageQualityInfo> = {};

 // Check each placement
 placements.forEach(placement => {
   // Skip if no image is assigned
   if (!placement.imageId) return;

   // Find the image
   const image = images.find(img => img.id === placement.imageId);
   if (!image) return;

   // Get placeholder dimensions
   const dimensions = placeholderDimensions[placement.placeholderId];
   if (!dimensions) return;

   // Check quality, passing the threshold
   const qualityInfo = checkImageQuality(image, placement, dimensions.width, dimensions.height, dpiWarningThresholdPercent);
   
   // Add to issues if quality is not good
    if (qualityInfo && qualityInfo.status !== ImageQualityStatus.GOOD) {
      qualityIssues[placement.placeholderId] = qualityInfo;
    }
  });

  return qualityIssues;
}
