import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HashRouter, Routes, Route, useNavigate, useLocation } from "react-router-dom";
import { StatusBarProvider } from "./contexts/StatusBarContext"; // Import the provider
import { ProofingProvider } from "@/components/ProofingComponents"; // Import our new ProofingProvider
// import ContextualStatusBar from "./components/ContextualStatusBar"; // Removed import
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import TemplateEditor from "./components/TemplateEditor";
import { useEffect, createContext, useContext, useState } from "react";

const queryClient = new QueryClient();

// Context for template editor navigation state
interface TemplateEditorContextType {
  currentDimension?: 'square' | 'horizontal' | 'vertical';
  currentTemplateId?: string;
  currentImageCountFilter?: string;
  currentClassificationFilter?: string;
  setCurrentDimension: (dimension: 'square' | 'horizontal' | 'vertical') => void;
  setCurrentTemplateId: (templateId: string) => void;
  setCurrentImageCountFilter: (filter: string) => void;
  setCurrentClassificationFilter: (filter: string) => void;
}

const TemplateEditorContext = createContext<TemplateEditorContextType>({
  setCurrentDimension: () => {},
  setCurrentTemplateId: () => {},
  setCurrentImageCountFilter: () => {},
  setCurrentClassificationFilter: () => {},
});

export const useTemplateEditorContext = () => useContext(TemplateEditorContext);

const TemplateEditorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentDimension, setCurrentDimension] = useState<'square' | 'horizontal' | 'vertical'>('square');
  const [currentTemplateId, setCurrentTemplateId] = useState<string>('');
  const [currentImageCountFilter, setCurrentImageCountFilter] = useState<string>('all');
  const [currentClassificationFilter, setCurrentClassificationFilter] = useState<string>('all');

  return (
    <TemplateEditorContext.Provider
      value={{
        currentDimension,
        currentTemplateId,
        currentImageCountFilter,
        currentClassificationFilter,
        setCurrentDimension,
        setCurrentTemplateId,
        setCurrentImageCountFilter,
        setCurrentClassificationFilter,
      }}
    >
      {children}
    </TemplateEditorContext.Provider>
  );
};

const TemplateEditorWrapper = () => {
  const location = useLocation();
  const context = useTemplateEditorContext();
  
  const state = location.state as {
    initialDimension?: 'square' | 'horizontal' | 'vertical';
    initialTemplateId?: string;
    initialImageCountFilter?: string;
    initialClassificationFilter?: string;
  } | null;

  // Use location state if available, otherwise use context
  const initialDimension = state?.initialDimension || context.currentDimension;
  const initialTemplateId = state?.initialTemplateId || context.currentTemplateId;
  const initialImageCountFilter = state?.initialImageCountFilter || context.currentImageCountFilter;
  const initialClassificationFilter = state?.initialClassificationFilter || context.currentClassificationFilter;

  return (
    <TemplateEditor
      initialDimension={initialDimension}
      initialTemplateId={initialTemplateId}
      initialImageCountFilter={initialImageCountFilter}
      initialClassificationFilter={initialClassificationFilter}
    />
  );
};

const AppContent = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const handleKeydown = (event: KeyboardEvent) => {
      if (event.shiftKey && event.key === 'E') {
        event.preventDefault();
        navigate('/template-editor');
      }
    };

    document.addEventListener('keydown', handleKeydown);
    return () => document.removeEventListener('keydown', handleKeydown);
  }, [navigate]);

  return (
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="/template-editor" element={<TemplateEditorWrapper />} />
      {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <StatusBarProvider> {/* Wrap with the provider */}
      <ProofingProvider> {/* Add our ProofingProvider */}
        <TemplateEditorProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner duration={1750} />
            <HashRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
              <AppContent />
            </HashRouter>
            {/* ContextualStatusBar removed from here */}
          </TooltipProvider>
        </TemplateEditorProvider>
      </ProofingProvider>
    </StatusBarProvider>
  </QueryClientProvider>
);

export default App;
