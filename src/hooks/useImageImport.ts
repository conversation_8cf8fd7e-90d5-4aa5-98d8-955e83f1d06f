import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { ImageFile } from '@/components/ImageTray'; // Import ImageFile type
// Removed: import { useUserSettings } from '@/hooks/useUserSettings';

// Define the structure for import progress
export interface ImportProgress {
  stage: 'starting_import' | 'reading_metadata' | 'generating_thumbnails' | 'generating_previews' | 'previews_on_demand' | 'complete' | 'error' | 'importing'; // Added 'importing' stage
  current: number;
  total: number;
  filename?: string; // Optional filename for more context
  message?: string; // Optional user-friendly message generated by the hook
  error?: string; // Optional error message
}

// Interface for tracking on-demand preview generation
export interface PreviewGenerationProgress {
  inProgress: boolean;
  current: number;
  total: number;
  pendingPaths: string[];
}

// Define the props for the hook
interface UseImageImportProps {
  setImages: React.Dispatch<React.SetStateAction<ImageFile[]>>;
  setIsDirty: React.Dispatch<React.SetStateAction<boolean>>;
}

// Define the return type of the hook
interface UseImageImportReturn {
  handleAddImages: () => Promise<void>;
  importProgress: ImportProgress | null;
  previewProgress: PreviewGenerationProgress;
  trackPreviewRequest: (originalPath: string, resetCounter?: boolean) => void;
  resetPreviewProgress: () => void;
}

export const useImageImport = ({ setImages, setIsDirty }: UseImageImportProps): UseImageImportReturn => {
  const [importProgress, setImportProgress] = useState<ImportProgress | null>(null);
  const [previewProgress, setPreviewProgress] = useState<PreviewGenerationProgress>({ inProgress: false, current: 0, total: 0, pendingPaths: [] });
  // Removed: const { previewGenerationMode } = useUserSettings(); // previewGenerationMode is now a prop

  // --- Internal handler to add unique images to state ---
  const handleImagesAdded = useCallback((newImages: ImageFile[]) => {
    
    // Log the paths of new images for debugging
    newImages.forEach(img => {
    });
    
    setImages(prev => {
      // Check for existing paths to avoid duplicates
      const existingPaths = new Set(prev.map(img => img.originalPath));
      const uniqueNewImages = newImages.filter(img => !existingPaths.has(img.originalPath));
      
      
      // Create a new array with all images
      const updatedImages = [...prev, ...uniqueNewImages];
      
      // Notify the file availability system about the new images
      // This ensures the file availability system won't flag them as missing
      if (uniqueNewImages.length > 0) {
        const pathsUpdatedEvent = new CustomEvent('book-proofs-paths-updated', { 
          detail: { 
            newImages: uniqueNewImages,
            isDirty: true
          } 
        });
        window.dispatchEvent(pathsUpdatedEvent);
      }
      
      return updatedImages;
    });
    
    if (newImages.length > 0) {
      toast.success(`Added ${newImages.length} images to your library`);
    }
  }, [setImages]); // Dependency: setImages

  // --- Function to trigger the image import process ---
  const handleAddImages = useCallback(async () => {
    if (!window.electronAPI) {
      toast.error("Image adding functionality requires the desktop app.");
      console.warn('Electron API not found. Running in browser?');
      return;
    }

    // Set initial progress state for blocking phase
    setImportProgress({ stage: 'starting_import', current: 0, total: 0, message: 'Starting import...' }); // Show 'Starting...' immediately
    let didProceedToProcessing = false; // Flag to track if import proceeds
    try {
      // The API now returns an array of { originalPath, thumbnailUrl, name, rating, naturalWidth, naturalHeight } objects
      // Pass the previewGenerationMode to the main process
      const imageDataArray = await window.electronAPI.openImageDialog({ generatePreviews: false });
      // Check for cancellation immediately
      if (!imageDataArray || imageDataArray.length === 0) {
        setImportProgress(null); // Clear progress on cancellation
        return; // Exit early
      }

      // --- Proceed with processing if files were selected ---
      didProceedToProcessing = true; // Mark that we are proceeding

      // Map backend data to ImageFile structure, generating IDs
      const newImagesWithIds = imageDataArray.map((imgData, index) => {
        const imageId = `image-${Date.now()}-${index}`;
        return {
          id: imageId,
          originalPath: imgData.originalPath,
          thumbnailUrl: imgData.thumbnailUrl,
          // previewUrl is initially undefined, will be added by listener
          name: imgData.name,
          dateAdded: Date.now() + index,
          rating: imgData.rating,
          naturalWidth: imgData.naturalWidth,
          naturalHeight: imgData.naturalHeight,
          dateTaken: imgData.dateTaken,
          dateModified: imgData.dateModified,
        };
      });

      // Filter out images where dimensions couldn't be loaded (width or height is 0)
      // This check might be less necessary if backend guarantees dimensions, but good safeguard
      const validNewImages = newImagesWithIds.filter(img => img.naturalWidth && img.naturalHeight);

      if (validNewImages.length < newImagesWithIds.length) {
        toast.warning("Could not load dimensions for some images. They were not added.");
      }

      if (validNewImages.length > 0) {
        handleImagesAdded(validNewImages); // Add valid images to state
        setIsDirty(true); // Mark as dirty when images are added
      }
      // Note: Progress state (reading metadata, generating thumbnails) is now handled by IPC listeners below
    } catch (error) {
      console.error('Error opening image dialog or processing initial data:', error);
      toast.error("Failed to add images.");
      setImportProgress({ stage: 'error', current: 0, total: 0, error: error.message }); // Set error state
    } finally {
      // Clear the initial "Starting import..." overlay ONLY if we didn't proceed
      // to the background processing stage OR if an error occurred immediately.
      // If didProceedToProcessing is true, the background listener will handle clearing progress later.
      if (!didProceedToProcessing) {
         setImportProgress(null);
      }
    }
  }, [handleImagesAdded, setIsDirty]); // Dependencies updated

  // Function to reset preview progress counter
  const resetPreviewProgress = useCallback(() => {
    setPreviewProgress({
      inProgress: false,
      current: 0,
      total: 0,
      pendingPaths: []
    });
  }, []);

  // Helper function to check if an image already has a preview
  const imageAlreadyHasPreview = useCallback((images: ImageFile[], path: string) => {
    const image = images.find(img => img.originalPath === path);
    return image && image.previewUrl ? true : false;
  }, []);

  // Function to track preview generation requests
  const trackPreviewRequest = useCallback((originalPath: string, resetCounter = false) => {
    // Always check the current images first to avoid unnecessary state updates
    setImages(currentImages => {
      // Skip if this image already has a preview
      if (imageAlreadyHasPreview(currentImages, originalPath)) {
        return currentImages; // Return unchanged images array
      }

      // If we need to reset the counter (new batch), clear everything first
      if (resetCounter) {
        // Reset the preview progress counter completely
        setPreviewProgress({
          inProgress: true,
          current: 0,
          total: 1, // Start with just this one image
          pendingPaths: [originalPath]
        });
      } else {
        // For subsequent images in the same batch, add to existing progress
        setPreviewProgress(prev => {
          // Don't add duplicates
          if (prev.pendingPaths.includes(originalPath)) {
            return prev;
          }
          
          // Add to pending paths
          const updatedPendingPaths = [...prev.pendingPaths, originalPath];
          
          // Calculate the new total based ONLY on the pending paths
          // This ensures the total is always accurate
          return {
            inProgress: true,
            current: prev.current,
            total: prev.current + updatedPendingPaths.length, // Current completed + remaining items
            pendingPaths: updatedPendingPaths
          };
        });
      }
      
      return currentImages; // Return unchanged images array
    });
  }, [setImages, imageAlreadyHasPreview]);
  
  // --- Effect for Import Progress & Preview Listeners ---
  useEffect(() => {
    let removeImportProgressListener: (() => void) | undefined;
    let removeGeneratedListener: (() => void) | undefined;
    let removeProcessingListener: (() => void) | undefined;

    if (window.electronAPI) {
      // Listener for overall import progress updates from main process
      removeImportProgressListener = window.electronAPI.onImportProgress((data) => {
        // Assert the type to match the hook's definition
        const progressData = data as ImportProgress | null; // Can be null on completion

        if (progressData === null) {
          // Handle completion signal from main process
          setImportProgress({ stage: 'complete', current: 0, total: 0, message: 'Import complete!' });
          setTimeout(() => setImportProgress(null), 1000); // Clear after delay
          return;
        }

        // Generate user-friendly message based on stage
        let message = '';
        switch (progressData.stage) {
          case 'starting_import':
            message = 'Starting import...';
            break;
          case 'importing':
            // Single consistent message for the entire import process
            message = `Importing Your Images... (${progressData.current}/${progressData.total})`;
            break;
          case 'previews_on_demand': // Handle new stage
            message = `Import complete. Previews will be generated on demand.`;
            break;
          case 'error':
            message = `Error during import: ${progressData.error || 'Unknown error'}`;
            toast.error(message);
            setTimeout(() => setImportProgress(null), 1500); // Clear after longer delay on error
            break;
          default:
            message = 'Processing...';
        }

        // Update state with received data AND the generated message
        setImportProgress({ ...progressData, message });

        // Handle error state separately for clearing
        if (progressData.stage === 'error') {
           setTimeout(() => setImportProgress(null), 1500);
        }
      });

      // Listener for preview processing status (tracks when processing starts)
      // This helps show progress before the preview is actually completed
      if (window.electronAPI.onPreviewProcessing) {
        removeProcessingListener = window.electronAPI.onPreviewProcessing((data) => {
          if (data.status === 'processing') {
            // Update the processing status for this image
            setPreviewProgress(prev => {
              // Skip if this path is not in our pending paths
              if (!prev.pendingPaths.includes(data.originalPath)) {
                return prev;
              }
              
              // Mark this image as being processed by incrementing the current count
              // but keeping it in the pending paths until it's fully complete
              const newCurrent = prev.current + 0.5; // Use a partial count to show progress
              
              return {
                ...prev,
                current: newCurrent
              };
            });
          }
        });
      }

      // Listener for individual preview completion (updates image state)
      removeGeneratedListener = window.electronAPI.onPreviewGenerated((data) => {
        // Update the preview generation progress
        setPreviewProgress(prev => {
          // Remove this path from pending paths
          const updatedPendingPaths = prev.pendingPaths.filter(path => path !== data.originalPath);
          
          // Calculate new progress based on completed items
          // If this image was already marked as processing (0.5), add another 0.5
          // Otherwise add a full 1.0 to the count
          const isPartiallyProcessed = prev.pendingPaths.includes(data.originalPath) && 
                                     Math.floor(prev.current) !== prev.current;
          
          const completionIncrement = isPartiallyProcessed ? 0.5 : 1.0;
          const newCurrent = Math.floor(prev.current) + completionIncrement;
          const stillInProgress = updatedPendingPaths.length > 0;
          
          // The total is always the sum of completed items and remaining items
          // This ensures we maintain the correct count of the current batch
          const newTotal = Math.ceil(newCurrent) + updatedPendingPaths.length;
          
          // If we've completed all items, reset the progress
          if (!stillInProgress) {
            return {
              inProgress: false,
              current: 0,
              total: 0,
              pendingPaths: []
            };
          }
          
          return {
            inProgress: true,
            current: newCurrent,
            total: newTotal,
            pendingPaths: updatedPendingPaths
          };
        });
        
        // Update the image with the new preview URL
        if (data.previewUrl) {
          setImages(prevImages =>
            prevImages.map(img => {
              if (img.originalPath === data.originalPath) {
                return { ...img, previewUrl: data.previewUrl };
              }
              return img;
            })
          );
        } else {
          console.warn(`[Hook] Preview generation failed for: ${data.originalPath}`);
        }
      });

    } else {
      console.warn("[useImageImport] Electron API not found, cannot set up import listeners.");
    }

    // Cleanup listeners on unmount
    return () => {
      if (removeImportProgressListener) removeImportProgressListener();
      if (removeGeneratedListener) removeGeneratedListener();
      if (removeProcessingListener) removeProcessingListener();
    };
  }, [setImages]); // Dependency: setImages

  // --- Effect for File Update Listener (Round-trip Editing) ---
  useEffect(() => {
    let removeUpdateListener: (() => void) | undefined;
    let removeErrorListener: (() => void) | undefined;

    if (window.electronAPI) {
      // Listener for successful updates
      removeUpdateListener = window.electronAPI.onFileUpdated((data) => {
        const timestamp = Date.now(); // Generate a unique timestamp
        setImages(currentImages => {
          // Check if we need to look for updated paths
          const updatedPathsExist = window.bookProofsApp?.getAllUpdatedPaths && 
            Object.keys(window.bookProofsApp.getAllUpdatedPaths()).length > 0;
          
          return currentImages.map(img => {
            // Check if this is the updated image by comparing original path
            let isMatch = img.originalPath === data.originalPath;
            
            // If not a direct match and we have updated paths, check if the updated path matches
            if (!isMatch && updatedPathsExist && window.bookProofsApp?.getUpdatedFilePath) {
              const updatedPath = window.bookProofsApp.getUpdatedFilePath(img.originalPath);
              if (updatedPath === data.originalPath) {
                isMatch = true;
              }
            }
            
            if (isMatch) {
              return {
                ...img,
                // Append timestamp for cache busting
                thumbnailUrl: data.thumbnailUrl ? `${data.thumbnailUrl}?t=${timestamp}` : img.thumbnailUrl,
                previewUrl: data.previewUrl ? `${data.previewUrl}?t=${timestamp}` : img.previewUrl,
                // Update dateTaken and dateModified if provided
                dateTaken: data.dateTaken !== undefined ? data.dateTaken : img.dateTaken,
                dateModified: data.dateModified !== undefined ? data.dateModified : img.dateModified
              };
            }
            return img;
          });
        });
        toast.success(`Image updated successfully.`);
        // setIsDirty(true); // Optional: Mark dirty on external update?
      });

      // Listener for update errors
      removeErrorListener = window.electronAPI.onFileUpdateError((data) => {
         console.error(`[useImageImport] Error updating file ${data.originalPath}:`, data.error);
         toast.error(`Failed to update image after edit: ${data.error}`);
      });

    } else {
      console.warn("[useImageImport] Electron API not found, cannot set up file update listeners.");
    }

    // Cleanup function
    return () => {
      if (removeUpdateListener) removeUpdateListener();
      if (removeErrorListener) removeErrorListener();
    };
  }, [setImages]); // Dependency: setImages

  return {
    importProgress,
    handleAddImages,
    previewProgress,
    trackPreviewRequest,
    resetPreviewProgress,
  };
};