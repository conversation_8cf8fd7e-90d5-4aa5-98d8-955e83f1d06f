import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { ImageFile } from '@/components/ImageTray';
import { Spread } from '@/components/SpreadsTray';
import { TextOverlay } from '@/components/TextEditor';
import { TemplateImage } from '@/lib/templates/interfaces';

// Define the structure for the project data file (matching BookEditor's)
export interface ProjectData {
  version: number;
  aspectRatio: { id: string; title: string; ratio: string; dimensions: string; widthPt?: number; heightPt?: number; bleed?: string; safetyMargin?: string; };
  imageGap: number;
  spreads: Spread[];
  images: ImageFile[];
  backgroundColor: string; // Required field
  projectBackgroundImage: string | null; // Required field
  projectBackgroundImagePath: string | null; // Required field
  projectBackgroundImageNormalizedUrl?: string | null; // Normalized URL for consistent display
  backgroundImageZoom: number; // Required field with default value of 1
  backgroundImagePanX: number; // Required field with default value of 50
  backgroundImagePanY: number; // Required field with default value of 50
  projectBackgroundImageOpacity?: number; // Optional: Opacity for the project background image
  projectBackgroundImageOriginalDimensions?: { width: number; height: number } | null; // Optional: Original dimensions for DPI calculation
  textOverlays: TextOverlay[]; // Required field
  favoriteTemplateIds?: string[]; // Optional field
  isEditingBackgroundForSpreadId?: string | null; // Optional: Track which spread's background is being edited
  hasCover: boolean; // Renamed from enableProjectCover, will default to false if not in loaded data
  isCoverVisible?: boolean; // Optional: Tracks if the cover is currently visible
  coverImage?: ImageFile | null; // Optional: The image used for the project cover
  coverScale?: number; // Optional: Cover zoom/scale level
  coverTranslateX?: number; // Optional: Cover pan X position
  coverTranslateY?: number; // Optional: Cover pan Y position
  projectBleed?: number; // Optional: Bleed value in inches
  projectSafetyMargin?: number; // Optional: Safety margin value in inches
  imageBorderSize?: number; // Optional: Size of image borders in points
  imageBorderColor?: string; // Optional: Color of image borders
  customTemplates?: Record<string, TemplateImage[]>; // Optional: User-modified templates by spreadId (format: "spreadId")
}

// Define the props the hook needs
interface UseProjectPersistenceProps {
  initialProjectFilePath: string | null;
  initialProjectData?: ProjectData | null; // Add initialProjectData prop
  // Refs to get current state without causing re-renders of the hook itself
  aspectRatioRef: React.RefObject<{ id: string; title: string; ratio: string; dimensions: string; bleed?: string; safetyMargin?: string }>; // Added bleed and safetyMargin to aspectRatioRef type
  imagesRef: React.RefObject<ImageFile[]>;
  spreadsRef: React.RefObject<Spread[]>;
  imageGapRef: React.RefObject<number>;
  backgroundColorRef: React.RefObject<string>; // Added for background color
  projectBackgroundImageRef: React.RefObject<string | null>; // Ref for URL
  projectBackgroundImagePathRef: React.RefObject<string | null>; // Ref for Path
  backgroundImageZoomRef: React.RefObject<number>; // Ref for background image zoom
  backgroundImagePanXRef: React.RefObject<number>; // Ref for background image pan X
  backgroundImagePanYRef: React.RefObject<number>; // Ref for background image pan Y
  projectBackgroundImageOpacityRef: React.RefObject<number>; // Ref for background image opacity
  projectBackgroundImageOriginalDimensionsRef: React.RefObject<{ width: number; height: number } | null>; // Ref for original dimensions
  textOverlaysRef: React.RefObject<any[]>; // Ref for text overlays
  isEditingBackgroundForSpreadIdRef: React.RefObject<string | null>; // Ref for background editing state
  hasCoverRef: React.RefObject<boolean>; // Renamed from enableProjectCoverRef
  isCoverVisibleRef: React.RefObject<boolean>; // ADDED: Ref for isCoverVisible
  coverImageRef?: React.RefObject<ImageFile | null>; // ADDED: Ref for coverImage
  coverScaleRef?: React.RefObject<number>; // ADDED: Ref for cover scale
  coverTranslateXRef?: React.RefObject<number>; // ADDED: Ref for cover translateX
  coverTranslateYRef?: React.RefObject<number>; // ADDED: Ref for cover translateY
  projectBleedRef: React.RefObject<number>; // ADDED: Ref for projectBleed
  projectSafetyMarginRef: React.RefObject<number>; // ADDED: Ref for projectSafetyMargin
  imageBorderSizeRef: React.RefObject<number>; // ADDED: Ref for imageBorderSize
  imageBorderColorRef: React.RefObject<string>; // ADDED: Ref for imageBorderColor
  customTemplatesRef?: React.RefObject<Record<string, TemplateImage[]>>; // ADDED: Ref for custom templates by spreadId
  // Callback to capture cover state properly
  captureCoverState?: () => any; // Function to call captureCurrentCoverState from ProjectCover
  // Callback to apply restored cover state properly
  applyRestoredCoverState?: (coverState: any) => void; // Function to call applyRestoredState from ProjectCover
  // Callbacks to update state in the parent component (BookEditor)
  onLoadAspectRatio: (aspectRatio: { id: string; title: string; ratio: string; dimensions: string; bleed?: string; safetyMargin?: string; }) => void; // Added bleed and safetyMargin to onLoadAspectRatio type
  setImages: React.Dispatch<React.SetStateAction<ImageFile[]>>;
  setSpreads: React.Dispatch<React.SetStateAction<Spread[]>>;
  setImageGap: React.Dispatch<React.SetStateAction<number>>;
  setBackgroundColor: React.Dispatch<React.SetStateAction<string>>; // Added for background color
  setProjectBackgroundImage: React.Dispatch<React.SetStateAction<string | null>>; // Setter for URL
  setProjectBackgroundImagePath: React.Dispatch<React.SetStateAction<string | null>>; // Setter for Path
  setBackgroundImageZoom: React.Dispatch<React.SetStateAction<number>>; // Setter for zoom level
  setBackgroundImagePanX: React.Dispatch<React.SetStateAction<number>>; // Setter for pan X
  setBackgroundImagePanY: React.Dispatch<React.SetStateAction<number>>; // Setter for pan Y
  setProjectBackgroundImageOpacity: React.Dispatch<React.SetStateAction<number>>; // Setter for opacity
  setProjectBackgroundImageOriginalDimensions: React.Dispatch<React.SetStateAction<{ width: number; height: number } | null>>; // Setter for original dimensions
  setTextOverlays: React.Dispatch<React.SetStateAction<any[]>>; // Setter for text overlays
  setIsEditingBackgroundForSpreadId: React.Dispatch<React.SetStateAction<string | null>>; // Setter for background editing state
  setHasCover: React.Dispatch<React.SetStateAction<boolean>>; // Renamed from setEnableProjectCover
  setIsCoverVisible: React.Dispatch<React.SetStateAction<boolean>>; // ADDED: Setter for isCoverVisible
  setCoverImage?: React.Dispatch<React.SetStateAction<ImageFile | null>>; // ADDED: Setter for coverImage
  setCoverScale?: React.Dispatch<React.SetStateAction<number>>; // ADDED: Setter for cover scale
  setCoverTranslateX?: React.Dispatch<React.SetStateAction<number>>; // ADDED: Setter for cover translateX
  setCoverTranslateY?: React.Dispatch<React.SetStateAction<number>>; // ADDED: Setter for cover translateY
  setProjectBleed: React.Dispatch<React.SetStateAction<number>>; // ADDED: Setter for projectBleed
  setProjectSafetyMargin: React.Dispatch<React.SetStateAction<number>>; // ADDED: Setter for projectSafetyMargin
  setImageBorderSize: React.Dispatch<React.SetStateAction<number>>; // ADDED: Setter for imageBorderSize
  setImageBorderColor: React.Dispatch<React.SetStateAction<string>>; // ADDED: Setter for imageBorderColor
  setCustomTemplates?: React.Dispatch<React.SetStateAction<Record<string, TemplateImage[]>>>; // ADDED: Setter for custom templates
  setCurrentSpreadId: React.Dispatch<React.SetStateAction<string>>;
  // Callbacks for parent component actions
  onNewProjectRequest: () => void;
  onProjectSaveAs: (newFilePath: string) => void; // Callback after successful Save As
  resetUndoHistory: () => void; // Added callback to reset undo/redo history
}

// Define the return type of the hook
interface UseProjectPersistenceReturn {
  isDirty: boolean;
  setIsDirty: React.Dispatch<React.SetStateAction<boolean>>;
  currentProjectFilePath: string | null;
  handleSaveProject: () => Promise<boolean>;
  handleSaveProjectAs: () => Promise<boolean>;
  handleLoadProjectRequest: () => Promise<void>;
  handleNewProjectRequest: () => Promise<void>;
}

export const useProjectPersistence = ({
  initialProjectFilePath,
  aspectRatioRef,
  imagesRef,
  spreadsRef,
  imageGapRef,
  backgroundColorRef, // Added
  projectBackgroundImageRef, // Added
  projectBackgroundImagePathRef, // Added path ref
  backgroundImageZoomRef, // Added zoom ref
  backgroundImagePanXRef, // Added pan X ref
  backgroundImagePanYRef, // Added pan Y ref
  projectBackgroundImageOpacityRef, // Added opacity ref
  projectBackgroundImageOriginalDimensionsRef, // Added original dimensions ref
  textOverlaysRef, // Added text overlays ref
  isEditingBackgroundForSpreadIdRef, // Added background editing state ref
  hasCoverRef, // Renamed from enableProjectCoverRef
  isCoverVisibleRef, // ADDED: Destructure isCoverVisibleRef
  coverImageRef, // ADDED: Destructure coverImageRef
  coverScaleRef, // ADDED: Destructure coverScaleRef
  coverTranslateXRef, // ADDED: Destructure coverTranslateXRef
  coverTranslateYRef, // ADDED: Destructure coverTranslateYRef
  projectBleedRef, // ADDED: Destructure projectBleedRef
  projectSafetyMarginRef, // ADDED: Destructure projectSafetyMarginRef
  imageBorderSizeRef, // ADDED: Destructure imageBorderSizeRef
  imageBorderColorRef, // ADDED: Destructure imageBorderColorRef
  customTemplatesRef, // ADDED: Destructure customTemplatesRef
  captureCoverState, // ADDED: Destructure captureCoverState callback
  applyRestoredCoverState, // ADDED: Destructure applyRestoredCoverState callback
  onLoadAspectRatio,
  setImages,
  setSpreads,
  setImageGap,
  setBackgroundColor, // Added
  setProjectBackgroundImage, // Added
  setProjectBackgroundImagePath, // Added path setter
  setBackgroundImageZoom, // Added zoom setter
  setBackgroundImagePanX, // Added pan X setter
  setBackgroundImagePanY, // Added pan Y setter
  setProjectBackgroundImageOpacity, // Added opacity setter
  setProjectBackgroundImageOriginalDimensions, // Added original dimensions setter
  setTextOverlays, // Added text overlays setter
  setIsEditingBackgroundForSpreadId, // Added background editing state setter
  setHasCover, // Renamed from setEnableProjectCover
  setIsCoverVisible, // ADDED: Destructure setIsCoverVisible
  setCoverImage, // ADDED: Destructure setCoverImage
  setCoverScale, // ADDED: Destructure setCoverScale
  setCoverTranslateX, // ADDED: Destructure setCoverTranslateX
  setCoverTranslateY, // ADDED: Destructure setCoverTranslateY
  setProjectBleed, // ADDED: Destructure setProjectBleed
  setProjectSafetyMargin, // ADDED: Destructure setProjectSafetyMargin
  setImageBorderSize, // ADDED: Destructure setImageBorderSize
  setImageBorderColor, // ADDED: Destructure setImageBorderColor
  setCustomTemplates, // ADDED: Destructure setCustomTemplates
  setCurrentSpreadId,
  onNewProjectRequest,
  initialProjectData, // Destructure the new prop
  onProjectSaveAs,
  resetUndoHistory, // Destructure the new prop
}: UseProjectPersistenceProps): UseProjectPersistenceReturn => {

  const [isDirty, setIsDirty] = useState(false);
  const [currentProjectFilePath, setCurrentProjectFilePath] = useState<string | null>(initialProjectFilePath);

  // Effect to update internal file path if the prop changes (e.g., after initial load or Save As)
  useEffect(() => {
    setCurrentProjectFilePath(initialProjectFilePath);
  }, [initialProjectFilePath]);

  // Effect to listen for custom path update events
  useEffect(() => {
    const handlePathsUpdated = (event: CustomEvent) => {
      if (event.detail?.isDirty) {
        setIsDirty(true);
      }
    };

    // Add event listener for our custom event
    window.addEventListener('book-proofs-paths-updated', handlePathsUpdated as EventListener);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('book-proofs-paths-updated', handlePathsUpdated as EventListener);
    };
  }, []);

  // --- Core Save/Load Logic ---

  const handleSaveProjectAs = useCallback(async (): Promise<boolean> => {
    if (!window.electronAPI) {
      toast.error("Saving projects requires the desktop app.");
      return false;
    }

    // 1. Gather current state from refs
    let images = imagesRef.current!;

    // Apply any updated paths to images before saving
    if (window.bookProofsApp?.getAllUpdatedPaths) {
      const updatedPaths = window.bookProofsApp.getAllUpdatedPaths();

      // Manually update image paths
      images = images.map(img => {
        const updatedPath = updatedPaths[img.originalPath];
        if (updatedPath) {
          return { ...img, originalPath: updatedPath };
        }
        return img;
      });
    }

    // Capture cover state properly using the callback if available
    let coverData = {
      coverImage: coverImageRef?.current || null,
      coverScale: coverScaleRef?.current || 1,
      coverTranslateX: coverTranslateXRef?.current || 0,
      coverTranslateY: coverTranslateYRef?.current || 0,
    };

    if (captureCoverState && hasCoverRef.current) {
      try {
        const capturedState = captureCoverState();
        if (capturedState) {
          coverData = {
            coverImage: capturedState.coverImage || coverData.coverImage,
            coverScale: capturedState.coverScale || coverData.coverScale,
            coverTranslateX: capturedState.coverTranslateX || coverData.coverTranslateX,
            coverTranslateY: capturedState.coverTranslateY || coverData.coverTranslateY,
          };
        }
      } catch (error) {
      }
    }

    const projectData: ProjectData = {
      version: 5, // UPDATE: Increment version number for backward compatibility tracking
      aspectRatio: aspectRatioRef.current!,
      imageGap: imageGapRef.current!,
      spreads: spreadsRef.current!,
      images: images,
      backgroundColor: backgroundColorRef.current!, // Add background color
      projectBackgroundImage: projectBackgroundImagePathRef.current, // Save the path, not URL
      projectBackgroundImagePath: projectBackgroundImagePathRef.current, // Include the path specifically (redundant but makes logic clearer)
      backgroundImageZoom: backgroundImageZoomRef?.current || 1, // Include zoom level with default
      backgroundImagePanX: backgroundImagePanXRef?.current || 50, // Include pan X with default
      backgroundImagePanY: backgroundImagePanYRef?.current || 50, // Include pan Y with default
      projectBackgroundImageOpacity: projectBackgroundImageOpacityRef?.current !== undefined ? projectBackgroundImageOpacityRef.current : 1, // Include opacity
      projectBackgroundImageOriginalDimensions: projectBackgroundImageOriginalDimensionsRef?.current || null, // Include original dimensions
      textOverlays: textOverlaysRef.current,
      isEditingBackgroundForSpreadId: isEditingBackgroundForSpreadIdRef?.current || null, // Include background editing state
      hasCover: hasCoverRef.current!, // Renamed
      isCoverVisible: isCoverVisibleRef.current!, // ADDED
      coverImage: coverData.coverImage, // Use captured or fallback data
      coverScale: coverData.coverScale, // Use captured or fallback data
      coverTranslateX: coverData.coverTranslateX, // Use captured or fallback data
      coverTranslateY: coverData.coverTranslateY, // Use captured or fallback data
      projectBleed: projectBleedRef.current!, // ADDED
      projectSafetyMargin: projectSafetyMarginRef.current!, // ADDED
      imageBorderSize: imageBorderSizeRef.current!, // ADDED
      imageBorderColor: imageBorderColorRef.current!, // ADDED
      customTemplates: customTemplatesRef?.current || {}, // ADDED: Custom template modifications by spreadId
    };

    console.log('[ProjectPersistence SaveAs] Complete project data with custom templates:', projectData.customTemplates);

    // 2. Call main process to show 'Save As' dialog and save data
    try {
      const result = await window.electronAPI.saveProjectAs(projectData);

      if (result.success && result.filePath) {
        toast.success("Project saved successfully.");
        setCurrentProjectFilePath(result.filePath); // Update internal path state
        setIsDirty(false); // Reset dirty flag
        onProjectSaveAs(result.filePath); // Notify parent of the new path
        return true;
      } else if (!result.canceled) {
        // Check for empty folder error specifically
        if (result.error?.includes('Folder must be empty')) {
          toast.error("Project requires an empty folder. Please create a new empty folder for your project files.");
          return false;
        }
        // Use result.error if available, otherwise provide a generic message
        const errorMessage = `Failed to save project: ${result.error || 'Unknown error'}`;
        toast.error(errorMessage);
        return false;
      } else {
        toast.info("Save As cancelled.");
        return false; // Indicate cancellation or failure
      }
    } catch (error) {
      toast.error(`An unexpected error occurred during Save As: ${error.message}`);
      return false;
    }
  }, [aspectRatioRef, imagesRef, spreadsRef, imageGapRef, backgroundColorRef, projectBackgroundImagePathRef, backgroundImageZoomRef, backgroundImagePanXRef, backgroundImagePanYRef, projectBackgroundImageOpacityRef, projectSafetyMarginRef, customTemplatesRef, onProjectSaveAs]); // Dependencies: refs and callback

  const handleSaveProject = useCallback(async (): Promise<boolean> => {
    if (!window.electronAPI) {
      toast.error("Saving projects requires the desktop app.");
      return false;
    }

    // If no path exists, trigger Save As instead
    if (!currentProjectFilePath) {
      return handleSaveProjectAs();
    }

    // 1. Gather current state from refs
    let images = imagesRef.current!;

    // Apply any updated paths to images before saving
    if (window.bookProofsApp?.getAllUpdatedPaths) {
      const updatedPaths = window.bookProofsApp.getAllUpdatedPaths();

      // Manually update image paths
      images = images.map(img => {
        const updatedPath = updatedPaths[img.originalPath];
        if (updatedPath) {
          return { ...img, originalPath: updatedPath };
        }
        return img;
      });

      // Note: We don't need to update imagesRef.current as we're using the updated images variable directly
      // in the projectData object below
    }

    // Capture cover state properly using the callback if available
    let coverData = {
      coverImage: coverImageRef?.current || null,
      coverScale: coverScaleRef?.current || 1,
      coverTranslateX: coverTranslateXRef?.current || 0,
      coverTranslateY: coverTranslateYRef?.current || 0,
    };

    if (captureCoverState && hasCoverRef.current) {
      try {
        const capturedState = captureCoverState();
        if (capturedState) {
          coverData = {
            coverImage: capturedState.coverImage || coverData.coverImage,
            coverScale: capturedState.coverScale || coverData.coverScale,
            coverTranslateX: capturedState.coverTranslateX || coverData.coverTranslateX,
            coverTranslateY: capturedState.coverTranslateY || coverData.coverTranslateY,
          };
        }
      } catch (error) {
      }
    }

    const projectData: ProjectData = {
      version: 5, // UPDATE: Increment version number for backward compatibility tracking
      aspectRatio: aspectRatioRef.current!,
      imageGap: imageGapRef.current!,
      spreads: spreadsRef.current!,
      images: images,
      backgroundColor: backgroundColorRef.current!, // Add background color
      projectBackgroundImage: projectBackgroundImagePathRef.current, // Save the path, not URL
      projectBackgroundImagePath: projectBackgroundImagePathRef.current, // Include the path specifically (redundant but makes logic clearer)
      backgroundImageZoom: backgroundImageZoomRef?.current || 1, // Include zoom level with default
      backgroundImagePanX: backgroundImagePanXRef?.current || 50, // Include pan X with default
      backgroundImagePanY: backgroundImagePanYRef?.current || 50, // Include pan Y with default
      projectBackgroundImageOpacity: projectBackgroundImageOpacityRef?.current !== undefined ? projectBackgroundImageOpacityRef.current : 1, // Include opacity
      projectBackgroundImageOriginalDimensions: projectBackgroundImageOriginalDimensionsRef?.current || null, // Include original dimensions
      textOverlays: textOverlaysRef.current,
      isEditingBackgroundForSpreadId: isEditingBackgroundForSpreadIdRef?.current || null, // Include background editing state
      hasCover: hasCoverRef.current!, // Renamed
      isCoverVisible: isCoverVisibleRef.current!, // ADDED
      coverImage: coverData.coverImage, // Use captured or fallback data
      coverScale: coverData.coverScale, // Use captured or fallback data
      coverTranslateX: coverData.coverTranslateX, // Use captured or fallback data
      coverTranslateY: coverData.coverTranslateY, // Use captured or fallback data
      projectBleed: projectBleedRef.current!, // ADDED
      projectSafetyMargin: projectSafetyMarginRef.current!, // ADDED
      imageBorderSize: imageBorderSizeRef.current!, // ADDED
      imageBorderColor: imageBorderColorRef.current!, // ADDED
      customTemplates: customTemplatesRef?.current || {}, // ADDED: Custom template modifications by spreadId
    };

    // 2. Call main process to save data to the existing path
    try {
      // Corrected: Use projectSaveFile for saving to an existing path
      const result = await window.electronAPI.projectSaveFile(projectData); // Pass only data, path is handled internally by main process

      if (result.success) {
        toast.success("Project saved.");
        setIsDirty(false); // Reset dirty flag
        return true;
      } else {
        // Use result.error if available, otherwise provide a generic message
        const errorMessage = `Failed to save project: ${result.error || 'Unknown error'}`;
        toast.error(errorMessage);
        return false;
      }
    } catch (error) {
      toast.error(`An unexpected error occurred during Save: ${error.message}`);
      return false;
    }
  }, [currentProjectFilePath, handleSaveProjectAs, aspectRatioRef, imagesRef, spreadsRef, imageGapRef, backgroundColorRef, projectBackgroundImagePathRef, backgroundImageZoomRef, backgroundImagePanXRef, backgroundImagePanYRef, projectBackgroundImageOpacityRef, projectSafetyMarginRef, customTemplatesRef]); // Dependencies

  // Function to apply loaded data to the parent component's state
  const handleLoadProjectData = useCallback((projectData: any | null, loadedFilePath: string | null) => {
    if (!projectData) {
      toast.error("Failed to apply loaded project data: Data is missing.");
      return;
    }

    // Ensure the project data has all required fields with default values
    const normalizedProjectData: ProjectData = {
      ...projectData,
      version: projectData.version || 5,
      spreads: projectData.spreads || [], // Ensure spreads array exists
      images: projectData.images || [], // Ensure images array exists
      backgroundColor: projectData.backgroundColor || '#FFFFFF',
      projectBackgroundImage: projectData.projectBackgroundImage || null,
      projectBackgroundImagePath: projectData.projectBackgroundImagePath || null,
      backgroundImageZoom: typeof projectData.backgroundImageZoom === 'number' ? projectData.backgroundImageZoom : 1,
      backgroundImagePanX: typeof projectData.backgroundImagePanX === 'number' ? projectData.backgroundImagePanX : 50,
      backgroundImagePanY: typeof projectData.backgroundImagePanY === 'number' ? projectData.backgroundImagePanY : 50,
      projectBackgroundImageOpacity: typeof projectData.projectBackgroundImageOpacity === 'number' ? projectData.projectBackgroundImageOpacity : 1, // Load opacity
      projectBackgroundImageOriginalDimensions: projectData.projectBackgroundImageOriginalDimensions || null, // Load original dimensions
      textOverlays: projectData.textOverlays || [],
      isEditingBackgroundForSpreadId: projectData.isEditingBackgroundForSpreadId || null, // Load background editing state
      hasCover: projectData.hasCover ?? projectData.enableProjectCover ?? false, // Load hasCover, fallback to enableProjectCover for old files
      isCoverVisible: projectData.isCoverVisible ?? (projectData.hasCover ?? projectData.enableProjectCover ?? false ? true : false), // Load isCoverVisible, default based on hasCover
      coverImage: projectData.coverImage || null, // Load cover image with default
      coverScale: projectData.coverScale !== undefined ? projectData.coverScale : 1, // Load cover scale with default
      coverTranslateX: projectData.coverTranslateX !== undefined ? projectData.coverTranslateX : 0, // Load cover translateX with default
      coverTranslateY: projectData.coverTranslateY !== undefined ? projectData.coverTranslateY : 0, // Load cover translateY with default
      projectBleed: projectData.projectBleed !== undefined ? projectData.projectBleed : parseFloat(projectData.aspectRatio?.bleed || "0.125"), // Load projectBleed with default
      projectSafetyMargin: projectData.projectSafetyMargin !== undefined ? projectData.projectSafetyMargin : parseFloat(projectData.aspectRatio?.safetyMargin || "0.6"), // Load projectSafetyMargin with default
      imageBorderSize: projectData.imageBorderSize !== undefined ? projectData.imageBorderSize : 0, // Load imageBorderSize with default
      imageBorderColor: projectData.imageBorderColor || '#000000', // Load imageBorderColor with default
      customTemplates: projectData.customTemplates || {}, // Load custom templates with default
    };


    // Update core state via callbacks
    onLoadAspectRatio(normalizedProjectData.aspectRatio);

    setImages(normalizedProjectData.images);

    // Normalize spreads to include spreadBackgroundData if missing
    // Ensure there's at least one spread - if the project was saved with no spreads, create a default one
    let spreadsToLoad = normalizedProjectData.spreads || [];
    
    if (spreadsToLoad.length === 0) {
      spreadsToLoad = [{ id: 'spread-1', templateId: '__blank__', images: [], spreadBackgroundData: null }];
    }
    
    const normalizedSpreads = spreadsToLoad.map(spread => ({
      ...spread,
      spreadBackgroundData: spread.spreadBackgroundData === undefined ? null : spread.spreadBackgroundData,
    }));
    setSpreads(normalizedSpreads);

    setImageGap(normalizedProjectData.imageGap);

    setBackgroundColor(normalizedProjectData.backgroundColor); // Already has default

    // Convert the path to a safe-file URL for display if it exists and isn't already in that format
    const imagePath = normalizedProjectData.projectBackgroundImagePath;
    let displayUrl = normalizedProjectData.projectBackgroundImage;

    // If we have a path but no display URL, or if we need to ensure the URL is in safe-file format
    if (imagePath && (!displayUrl || !displayUrl.startsWith('safe-file://'))) {
      displayUrl = `safe-file://${imagePath}`;
    }

    setProjectBackgroundImage(displayUrl); // Set URL for display

    setProjectBackgroundImagePath(imagePath); // Set Path for persistence

    // Set background image zoom and pan properties
    setBackgroundImageZoom(normalizedProjectData.backgroundImageZoom);

    setBackgroundImagePanX(normalizedProjectData.backgroundImagePanX);
  
    setBackgroundImagePanY(normalizedProjectData.backgroundImagePanY);
  
    setProjectBackgroundImageOpacity(normalizedProjectData.projectBackgroundImageOpacity !== undefined ? normalizedProjectData.projectBackgroundImageOpacity : 1); // Set opacity
  
    setProjectBackgroundImageOriginalDimensions(normalizedProjectData.projectBackgroundImageOriginalDimensions || null); // Set original dimensions
  
    setTextOverlays(normalizedProjectData.textOverlays); // Set text overlays

    setIsEditingBackgroundForSpreadId(normalizedProjectData.isEditingBackgroundForSpreadId); // Set background editing state
    setHasCover(normalizedProjectData.hasCover); // Call renamed setter
    setIsCoverVisible(normalizedProjectData.isCoverVisible!); // Call new setter, assert non-null as we provide a default
    setProjectBleed(normalizedProjectData.projectBleed!); // Call new setter
    setProjectSafetyMargin(normalizedProjectData.projectSafetyMargin!); // Call new setter
    setImageBorderSize(normalizedProjectData.imageBorderSize!); // Call new setter
    setImageBorderColor(normalizedProjectData.imageBorderColor!); // Call new setter
    
    // Set custom templates if the setter is provided
    if (setCustomTemplates) {
      console.log('[ProjectPersistence Load] Loading custom templates:', normalizedProjectData.customTemplates);
      setCustomTemplates(normalizedProjectData.customTemplates || {});
    }
    
    // Set cover image if the setter is provided
    if (setCoverImage) {
      setCoverImage(normalizedProjectData.coverImage || null);
    }
    
    // Apply cover transform state using the proper callback if available
    if (applyRestoredCoverState && normalizedProjectData.hasCover) {
      const coverStateToRestore = {
        coverImage: normalizedProjectData.coverImage || null,
        scale: normalizedProjectData.coverScale!,
        translateX: normalizedProjectData.coverTranslateX!,
        translateY: normalizedProjectData.coverTranslateY!
      };
      
      // Use setTimeout to ensure the cover component is rendered before applying state
      setTimeout(() => {
        applyRestoredCoverState(coverStateToRestore);
      }, 100);
    } else {
      // Fallback to direct setters if callback not available
      if (setCoverScale) {
        setCoverScale(normalizedProjectData.coverScale!);
      }
      if (setCoverTranslateX) {
        setCoverTranslateX(normalizedProjectData.coverTranslateX!);
      }
      if (setCoverTranslateY) {
        setCoverTranslateY(normalizedProjectData.coverTranslateY!);
      }
    }

    const firstSpreadId = spreadsToLoad[0]?.id; // Use spreadsToLoad instead of normalizedProjectData.spreads
    setCurrentSpreadId(firstSpreadId || 'spread-1'); // Reset to first spread or default

    setCurrentProjectFilePath(loadedFilePath); // Update internal path state

    setIsDirty(false); // Reset dirty flag after successful load

    // Reset undo/redo history (This should ideally be handled by the useUndoRedo hook if it exposed a reset function)
    // For now, we assume the parent component remounts or handles this.
    resetUndoHistory(); // Call the reset function

    toast.success("Project loaded successfully.");

  }, [onLoadAspectRatio, setImages, setSpreads, setImageGap, setBackgroundColor, setProjectBackgroundImage, setProjectBackgroundImagePath, setBackgroundImageZoom, setBackgroundImagePanX, setBackgroundImagePanY, setTextOverlays, setIsEditingBackgroundForSpreadId, setHasCover, setIsCoverVisible, setCoverImage, setCoverScale, setCoverTranslateX, setCoverTranslateY, setProjectBleed, setProjectSafetyMargin, setImageBorderSize, setImageBorderColor, setCurrentSpreadId, setIsDirty, resetUndoHistory]); // Dependencies: callbacks and setters

  // Handler for initiating the load project process
  const handleLoadProjectRequest = useCallback(async () => {
    if (!window.electronAPI) {
      toast.error("Loading projects requires the desktop app.");
      return;
    }

    // 1. Check for unsaved changes
    if (isDirty) {
      const confirmationResult = await window.electronAPI.confirmDialog({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Do you want to save them before loading a new project?',
        buttons: ['Save', "Don't Save", 'Cancel'],
        defaultId: 0,
        cancelId: 2,
      });

      if (confirmationResult === 2) { // Cancel
        toast.info("Load project cancelled.");
        return;
      }
      if (confirmationResult === 0) { // Save
        const saveSuccess = await handleSaveProject();
        if (!saveSuccess) {
          toast.error("Failed to save current project. Load project cancelled.");
          return; // Stop if save failed
        }
      }
      // If "Don't Save" (index 1) or save was successful, proceed...
    }

    // 2. Ask user to select the project file
    try {
      const openResult = await window.electronAPI.openProjectFile();

      if (openResult.success && openResult.filePath) {
        // 3. Load the project data
        const loadResult = await window.electronAPI.loadProject(openResult.filePath);

        if (loadResult.success && loadResult.data && loadResult.filePath) {
          // 4. Apply the loaded data
          handleLoadProjectData(loadResult.data, loadResult.filePath);
        } else if (loadResult.error) {
          toast.error(`Failed to load project: ${loadResult.message}`);
        } else {
          toast.error("Failed to load project data.");
        }
      } else if (!openResult.canceled) {
        toast.error("Failed to select project file.");
      } else {
        toast.info("Load project cancelled.");
      }
    } catch (error) {
      toast.error(`An unexpected error occurred while loading: ${error.message}`);
    }
  }, [isDirty, handleSaveProject, handleLoadProjectData]); // Dependencies

  // Handler for initiating a new project
  const handleNewProjectRequest = useCallback(async () => {
    if (!window.electronAPI) {
      toast.error("This action requires the desktop app.");
      return;
    }

    // Check for unsaved changes
    if (isDirty) {
      const confirmationResult = await window.electronAPI.confirmDialog({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Do you want to save them before starting a new project?',
        buttons: ['Save', "Don't Save", 'Cancel'],
        defaultId: 0,
        cancelId: 2,
      });

      if (confirmationResult === 2) { // Cancel
        toast.info("New project cancelled.");
        return;
      }
      if (confirmationResult === 0) { // Save
        const saveSuccess = await handleSaveProject();
        if (!saveSuccess) {
          toast.error("Failed to save current project. New project cancelled.");
          return; // Stop if save failed
        }
      }
      // If "Don't Save" (index 1) or save was successful, proceed...
    }

    // Trigger parent reset
    onNewProjectRequest();

  }, [isDirty, handleSaveProject, onNewProjectRequest]); // Dependencies

  // --- IPC Listeners ---

  // Effect for Backup State Request Listener
  useEffect(() => {
    let removeListener: (() => void) | undefined;
    // Use the new onBackupStateRequest listener
    if (window.electronAPI?.onBackupStateRequest) {
      const handleStateRequest = () => {
        // Capture cover state properly using the callback if available
        let coverData = {
          coverImage: coverImageRef?.current || null,
          coverScale: coverScaleRef?.current || 1,
          coverTranslateX: coverTranslateXRef?.current || 0,
          coverTranslateY: coverTranslateYRef?.current || 0,
        };

        if (captureCoverState && hasCoverRef.current) {
          try {
            const capturedState = captureCoverState();
            if (capturedState) {
              coverData = {
                coverImage: capturedState.coverImage || coverData.coverImage,
                coverScale: capturedState.coverScale || coverData.coverScale,
                coverTranslateX: capturedState.coverTranslateX || coverData.coverTranslateX,
                coverTranslateY: capturedState.coverTranslateY || coverData.coverTranslateY,
              };
            }
          } catch (error) {
          }
        }

        const currentState: ProjectData = {
          version: 5, // Ensure version is included
          aspectRatio: aspectRatioRef.current!,
          images: imagesRef.current!,
          spreads: spreadsRef.current!,
          imageGap: imageGapRef.current!,
          backgroundColor: backgroundColorRef.current!,
          projectBackgroundImage: projectBackgroundImageRef.current!, // URL
          projectBackgroundImagePath: projectBackgroundImagePathRef.current!, // Path
          backgroundImageZoom: backgroundImageZoomRef?.current || 1, // Include zoom level with default
          backgroundImagePanX: backgroundImagePanXRef?.current || 50, // Include pan X with default
          backgroundImagePanY: backgroundImagePanYRef?.current || 50, // Include pan Y with default
          projectBackgroundImageOpacity: projectBackgroundImageOpacityRef?.current !== undefined ? projectBackgroundImageOpacityRef.current : 1, // Include opacity
          projectBackgroundImageOriginalDimensions: projectBackgroundImageOriginalDimensionsRef?.current || null, // Include original dimensions
          textOverlays: textOverlaysRef.current, // Include textOverlays
          isEditingBackgroundForSpreadId: isEditingBackgroundForSpreadIdRef?.current || null, // Include background editing state
          hasCover: hasCoverRef.current!, // Renamed
          isCoverVisible: isCoverVisibleRef.current!, // ADDED
          coverImage: coverData.coverImage, // Use captured or fallback data
          coverScale: coverData.coverScale, // Use captured or fallback data
          coverTranslateX: coverData.coverTranslateX, // Use captured or fallback data
          coverTranslateY: coverData.coverTranslateY, // Use captured or fallback data
              projectBleed: projectBleedRef.current!, // ADDED
          projectSafetyMargin: projectSafetyMarginRef.current!, // ADDED
          imageBorderSize: imageBorderSizeRef.current!, // ADDED
          imageBorderColor: imageBorderColorRef.current!, // ADDED
        };
        if (window.electronAPI?.sendProjectStateResponse) {
           window.electronAPI.sendProjectStateResponse(currentState);
        } else {
        }
      };
      try {
        // Register listener using the new API
        removeListener = window.electronAPI.onBackupStateRequest(handleStateRequest);
      } catch (error) {
      }
    } else {
    }
    return () => {
      if (typeof removeListener === 'function') {
        try {
          removeListener();
        } catch (error) {
        }
      }
    };
  }, [aspectRatioRef, imagesRef, spreadsRef, imageGapRef, backgroundColorRef, projectBackgroundImageRef, projectBackgroundImagePathRef, backgroundImageZoomRef, backgroundImagePanXRef, backgroundImagePanYRef, projectBackgroundImageOpacityRef, textOverlaysRef, isEditingBackgroundForSpreadIdRef, hasCoverRef, isCoverVisibleRef, coverImageRef, coverScaleRef, coverTranslateXRef, coverTranslateYRef, projectBleedRef, projectSafetyMarginRef, imageBorderSizeRef, imageBorderColorRef]); // Dependencies: Refs

  // Effect for Load Trigger Listener
  useEffect(() => {
    let removeLoadListener: (() => void) | undefined;
    if (window.electronAPI?.onTriggerLoadProject) {
      removeLoadListener = window.electronAPI.onTriggerLoadProject(handleLoadProjectRequest);
    } else {
    }
    return () => {
      if (removeLoadListener) {
        removeLoadListener();
      }
    };
  }, [handleLoadProjectRequest]); // Dependency

  // Effect for New Project Trigger Listener
  useEffect(() => {
    let removeNewProjectListener: (() => void) | undefined;
    if (window.electronAPI?.onTriggerNewProject) {
      removeNewProjectListener = window.electronAPI.onTriggerNewProject(handleNewProjectRequest);
    } else {
    }
    return () => {
      if (removeNewProjectListener) {
        removeNewProjectListener();
      }
    };
  }, [handleNewProjectRequest]); // Dependency

  // Effect for Save Trigger Listener
  useEffect(() => {
    let removeSaveListener: (() => void) | undefined;
    if (window.electronAPI?.onTriggerSave) {
      removeSaveListener = window.electronAPI.onTriggerSave(() => {
        handleSaveProject();
      });
    } else {
    }
    return () => {
      if (removeSaveListener) {
        removeSaveListener();
      }
    };
  }, [handleSaveProject]); // Dependency

  // Removed useEffect for applying initial data - this logic should remain in the component
  // that receives initialProjectData as a prop.

  return {
    isDirty,
    setIsDirty,
    currentProjectFilePath,
    handleSaveProject,
    handleSaveProjectAs,
    handleLoadProjectRequest,
    handleNewProjectRequest,
  };
};