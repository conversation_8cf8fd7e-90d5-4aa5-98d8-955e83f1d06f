import { useState, useCallback, useRef } from 'react';
import { Spread } from '@/components/SpreadsTray'; // Import Spread type
import { ImageFile } from '@/components/ImageTray'; // Added for cover image state

// Define the structure for cover page state within a history entry
export interface CoverState {
  coverImage: ImageFile | null | undefined;
  scale: number;
  translateX: number; // Kept for compatibility during transition, represents pixel offset
  translateY: number; // Kept for compatibility during transition, represents pixel offset
  focalX?: number; // New: For focal point based positioning (0-1)
  focalY?: number; // New: For focal point based positioning (0-1)
  editorMaxTranslateX?: number; // Max possible X translation in editor pixels for current view
  editorMaxTranslateY?: number; // Max possible Y translation in editor pixels for current view
  // Potentially add textOverlays here if they become part of global undo/redo for cover
}

// Define the structure for history entries (can be customized via generic)
export interface HistoryEntry {
  spreads: Spread[];
  currentSpreadId: string;
  backgroundColor: string; // Added for background color
  customTemplates?: Record<string, any[]>; // Added for custom templates by spreadId
  coverState?: CoverState; // Added for cover page undo/redo
  // Add other state pieces if needed in the future
}

// Define the return type of the hook
interface UseUndoRedoReturn<T> {
  undoStack: T[];
  redoStack: T[];
  saveState: (currentState: T) => void;
  undo: (currentState: T) => T | undefined;
  redo: (currentState: T) => T | undefined;
  canUndo: boolean;
  canRedo: boolean;
  isRestoringRef: React.MutableRefObject<boolean>; // Expose ref for external checks/setting
  resetHistory: () => void; // Added reset function
}

export const useUndoRedo = <T,>(): UseUndoRedoReturn<T> => {
  const [undoStack, setUndoStack] = useState<T[]>([]);
  const [redoStack, setRedoStack] = useState<T[]>([]);
  const isRestoringRef = useRef(false); // Renamed to avoid conflict if destructured as 'isRestoring'

  // Function to save the current state before a change
  const saveState = useCallback((currentState: T) => {
    if (isRestoringRef.current) {
      return;
    }
    setUndoStack(prev => {
      const newUndoStack = [...prev, currentState];
      return newUndoStack;
    });
    setRedoStack(prev => {
      if (prev.length > 0) {
      }
      return []; // Clear redo stack on new action
    });
  }, []); // No dependencies needed as it only uses setters

  // Function to perform undo
  const undo = useCallback((currentState: T): T | undefined => {
    if (undoStack.length === 0) {
      return undefined;
    }

    const previousState = undoStack[undoStack.length - 1];

    setUndoStack(prev => {
      const newUndoStack = prev.slice(0, -1);
      return newUndoStack;
    });
    setRedoStack(prev => {
      const newRedoStack = [...prev, currentState];
      return newRedoStack;
    });

    return previousState;
  }, [undoStack]); // Dependency: undoStack

  // Function to perform redo
  const redo = useCallback((currentState: T): T | undefined => {
    if (redoStack.length === 0) {
      return undefined;
    }

    const nextState = redoStack[redoStack.length - 1];
    
    setRedoStack(prev => {
      const newRedoStack = prev.slice(0, -1);
      return newRedoStack;
    });
    setUndoStack(prev => {
      const newUndoStack = [...prev, currentState];
      return newUndoStack;
    });

    return nextState;
  }, [redoStack]); // Dependency: redoStack

  const canUndo = undoStack.length > 0;
  const canRedo = redoStack.length > 0;

  // Function to reset both stacks
  const resetHistory = useCallback(() => {
    setUndoStack([]);
    setRedoStack([]);
  }, []);

  return {
    undoStack,
    redoStack,
    saveState,
    undo,
    redo,
    canUndo,
    canRedo,
    isRestoringRef, // Return the ref
    resetHistory, // Return the new function
  };
};