import { useState, useEffect, useRef } from 'react';


// Create a custom event for settings changes
const SETTINGS_CHANGE_EVENT = 'bookproofsSettingsChange';

// Function to dispatch a settings change event
const dispatchSettingsChangeEvent = (setting: string, value: any) => {
  const event = new CustomEvent(SETTINGS_CHANGE_EVENT, { 
    detail: { setting, value }
  });
  window.dispatchEvent(event);

};

// Helper function to load settings from localStorage
const loadSetting = <T,>(key: string, defaultValue: T, parser: (value: string) => T = JSON.parse): T => {
  const storedValue = localStorage.getItem(key);
  if (storedValue !== null) {
    try {
      return parser(storedValue);
    } catch (e) {
      // console.error(`Failed to parse localStorage key "${key}":`, e); // Keep this commented for less noise
      return defaultValue;
    }
  }
  return defaultValue;
};


// Define the return type of the hook

export interface UserSettings {
  showFilenames: boolean;
  setShowFilenames: React.Dispatch<React.SetStateAction<boolean>>;
  showEndOfFilename: boolean;
  setShowEndOfFilename: React.Dispatch<React.SetStateAction<boolean>>;
  showRatingFilter: boolean;
  setShowRatingFilter: React.Dispatch<React.SetStateAction<boolean>>;
  defaultDropModeIsCover: boolean;
  setDefaultDropModeIsCover: React.Dispatch<React.SetStateAction<boolean>>;
  addSpreadOnArrow: boolean;
  setAddSpreadOnArrow: React.Dispatch<React.SetStateAction<boolean>>;
  autoSwitchTemplateOnRemove: boolean;
  setAutoSwitchTemplateOnRemove: React.Dispatch<React.SetStateAction<boolean>>;
  autosaveOnSpreadTurn: boolean; // New setting
  setAutosaveOnSpreadTurn: React.Dispatch<React.SetStateAction<boolean>>; // New setter
  resetImageTransformsOnTemplateSwitch: boolean; // New setting
  setResetImageTransformsOnTemplateSwitch: React.Dispatch<React.SetStateAction<boolean>>; // New setter
  showBookGutter: boolean; // Setting to show/hide book gutter
  setShowBookGutter: React.Dispatch<React.SetStateAction<boolean>>; // Setter for book gutter
  photoLibraryDisplayMode: string; // Setting for photo library display mode ("unused" or "all")
  setPhotoLibraryDisplayMode: React.Dispatch<React.SetStateAction<string>>; // Setter for photo library display mode
  autoCoverNearEdges: boolean; // Setting to auto-switch contain to cover when edges are close
  setAutoCoverNearEdges: React.Dispatch<React.SetStateAction<boolean>>; // Setter for auto-cover
  showQualityIndicators: boolean; // Setting to show/hide image quality indicators
  setShowQualityIndicators: React.Dispatch<React.SetStateAction<boolean>>; // Setter for quality indicators
  dpiWarningThresholdPercent: number; // DPI threshold percentage (50-70) for warnings
  setDpiWarningThresholdPercent: React.Dispatch<React.SetStateAction<number>>; // Setter for DPI threshold
  projectBackgroundImageOpacity: number; // Opacity for project background image
  setProjectBackgroundImageOpacity: React.Dispatch<React.SetStateAction<number>>; // Setter for opacity
  photoLibrarySortBy: 'modified' | 'name' | 'timeTaken'; // New setting for photo library sort by
  setPhotoLibrarySortBy: React.Dispatch<React.SetStateAction<'modified' | 'name' | 'timeTaken'>>; // New setter
  photoLibrarySortDirection: 'asc' | 'desc'; // New setting for photo library sort direction
  setPhotoLibrarySortDirection: React.Dispatch<React.SetStateAction<'asc' | 'desc'>>; // New setter
  showBleedArea: boolean; // Setting to show/hide the bleed area visualization
  setShowBleedArea: React.Dispatch<React.SetStateAction<boolean>>; // Setter for bleed area visibility
  bleedAreaMode: 'hide' | 'indicate' | 'ignore'; // New setting for 3-way bleed area mode
  setBleedAreaMode: React.Dispatch<React.SetStateAction<'hide' | 'indicate' | 'ignore'>>; // Setter for bleed area mode
  showSafetyMargin: boolean; // Setting to show/hide the safety margin
  setShowSafetyMargin: React.Dispatch<React.SetStateAction<boolean>>; // Setter for safety margin visibility
  showBleedLines: boolean; // Setting to show/hide trim lines
  setShowBleedLines: React.Dispatch<React.SetStateAction<boolean>>; // Setter for trim line visibility
  showOnboardingTooltips: boolean; // Whether to show onboarding tooltips
  setShowOnboardingTooltips: React.Dispatch<React.SetStateAction<boolean>>; // Setter for onboarding tooltips
  theme: 'light' | 'dark'; // Theme setting
  setTheme: React.Dispatch<React.SetStateAction<'light' | 'dark'>>; // Setter for theme
  showVisualDropChoice: boolean; // Setting to show visual drop choice overlay
  setShowVisualDropChoice: React.Dispatch<React.SetStateAction<boolean>>; // Setter for visual drop choice
  showDragIcon: boolean; // Setting to show/hide the visual drag icon and overlay
  setShowDragIcon: React.Dispatch<React.SetStateAction<boolean>>; // Setter for drag icon visibility
  favoriteTemplates: string[];
  setFavoriteTemplates: React.Dispatch<React.SetStateAction<string[]>>;
  imageTrayPosition: 'left' | 'bottom'; // Setting for imageTray position
  setImageTrayPosition: React.Dispatch<React.SetStateAction<'left' | 'bottom'>>; // Setter for imageTray position
}

export const useUserSettings = (): UserSettings => {
  const isInitialMount = useRef(true);

  // --- State Definitions ---
  const [theme, setTheme] = useState<'light' | 'dark'>('light'); // Default to light theme
  const [showFilenames, setShowFilenames] = useState<boolean>(false);
  const [showEndOfFilename, setShowEndOfFilename] = useState<boolean>(false);
  const [showRatingFilter, setShowRatingFilter] = useState<boolean>(true);
  const [defaultDropModeIsCover, setDefaultDropModeIsCover] = useState<boolean>(true); // Always default to cover mode
  const [addSpreadOnArrow, setAddSpreadOnArrow] = useState<boolean>(true);
  const [autoSwitchTemplateOnRemove, setAutoSwitchTemplateOnRemove] = useState<boolean>(true);
  
  // Listen for settings changes from other components
  useEffect(() => {
    const handleSettingsChange = (event: CustomEvent) => {
      const { setting, value } = event.detail;

      
      // Update the appropriate state based on the setting that changed
      // Removed singleImageDropDefaultAction handling
      // Add more settings as needed
    };
    
    // Add event listener with type assertion
    window.addEventListener(SETTINGS_CHANGE_EVENT, handleSettingsChange as EventListener);
    
    return () => {
      // Remove event listener with type assertion
      window.removeEventListener(SETTINGS_CHANGE_EVENT, handleSettingsChange as EventListener);
    };
  }, []);
  const [autosaveOnSpreadTurn, setAutosaveOnSpreadTurn] = useState<boolean>(true); // Default to true
  const [resetImageTransformsOnTemplateSwitch, setResetImageTransformsOnTemplateSwitch] = useState<boolean>(true); // Default to true
  const [showBookGutter, setShowBookGutter] = useState<boolean>(true); // Default to show the book gutter
  const [photoLibraryDisplayMode, setPhotoLibraryDisplayMode] = useState<string>("all"); // Default to "all"
  const [autoCoverNearEdges, setAutoCoverNearEdges] = useState<boolean>(true); // Default to true
  const [showQualityIndicators, setShowQualityIndicators] = useState<boolean>(true); // Default to true
  const [dpiWarningThresholdPercent, setDpiWarningThresholdPercent] = useState<number>(70); // Default to 70%
  const [projectBackgroundImageOpacity, setProjectBackgroundImageOpacity] = useState<number>(1); // Default to 1 (fully opaque)
  const [photoLibrarySortBy, setPhotoLibrarySortBy] = useState<'modified' | 'name' | 'timeTaken'>('modified'); // Default to 'modified'
  const [photoLibrarySortDirection, setPhotoLibrarySortDirection] = useState<'asc' | 'desc'>('desc'); // Default to 'desc'
  const [showBleedArea, setShowBleedArea] = useState<boolean>(false); // Default to false
  const [bleedAreaMode, setBleedAreaMode] = useState<'hide' | 'indicate' | 'ignore'>('hide'); // Default to 'hide'
  const [showSafetyMargin, setShowSafetyMargin] = useState<boolean>(false); // Default to false
  const [showBleedLines, setShowBleedLines] = useState<boolean>(false); // Default to false
  const [showOnboardingTooltips, setShowOnboardingTooltips] = useState<boolean>(true); // Default to true
  const [showVisualDropChoice, setShowVisualDropChoice] = useState<boolean>(true); // Default to true
  const [showDragIcon, setShowDragIcon] = useState<boolean>(true); // Default to true (visible)
  const [favoriteTemplates, setFavoriteTemplates] = useState<string[]>([]);
  const [imageTrayPosition, setImageTrayPosition] = useState<'left' | 'bottom'>(() => {
    // Initialize from localStorage to avoid race condition
    const stored = localStorage.getItem('userSetting_imageTrayPosition');
    return stored === 'left' || stored === 'bottom' ? stored as 'left' | 'bottom' : 'left';
  });

  // --- Load User Settings from localStorage on Mount ---
  useEffect(() => {
    // Load boolean settings
    setShowFilenames(loadSetting('userSetting_showFilenames', false));
    setShowEndOfFilename(loadSetting('userSetting_showEndOfFilename', false));
    setShowRatingFilter(loadSetting('userSetting_showRatingFilter', true));
    // setDefaultDropModeIsCover(loadSetting('userSetting_defaultDropModeIsCover', true)); // Always load as true (cover mode)
    setDefaultDropModeIsCover(true); // Force cover mode - ignore stored setting
    setAddSpreadOnArrow(loadSetting('userSetting_addSpreadOnArrow', true));
    setAutoSwitchTemplateOnRemove(loadSetting('userSetting_autoSwitchTemplateOnRemove', true));
    setAutosaveOnSpreadTurn(loadSetting('userSetting_autosaveOnSpreadTurn', true)); // Load new setting
    setResetImageTransformsOnTemplateSwitch(loadSetting('userSetting_resetImageTransformsOnTemplateSwitch', true)); // Load new setting
    setShowBookGutter(loadSetting('userSetting_showBookGutter', true)); // Load book gutter setting
    setAutoCoverNearEdges(loadSetting('userSetting_autoCoverNearEdges', true)); // Load auto-cover setting
    setShowQualityIndicators(loadSetting('userSetting_showQualityIndicators', true)); // Load quality indicators setting
    setShowBleedArea(loadSetting('userSetting_showBleedArea', false)); // Load bleed area visibility setting
    setBleedAreaMode(loadSetting<'hide' | 'indicate' | 'ignore'>(
      'userSetting_bleedAreaMode',
      'hide',
      (value) => (value === 'hide' || value === 'indicate' || value === 'ignore' ? value as 'hide' | 'indicate' | 'ignore' : 'hide')
    )); // Load bleed area mode setting
    setShowSafetyMargin(loadSetting('userSetting_showSafetyMargin', false)); // Load safety margin visibility
    setShowBleedLines(loadSetting('userSetting_showBleedLines', false)); // Load trim line visibility
    setShowOnboardingTooltips(loadSetting('userSetting_showOnboardingTooltips', true));
    setShowVisualDropChoice(loadSetting('userSetting_showVisualDropChoice', true)); // Load visual drop choice setting
    setShowDragIcon(loadSetting('userSetting_showDragIcon', true)); // Load drag icon visibility setting

    // imageTrayPosition is already loaded in useState initializer to avoid race condition

    // Load theme setting
    setTheme(loadSetting<'light' | 'dark'>(
      'userSetting_theme',
      'light',
      (value) => (value === 'light' || value === 'dark' ? value : 'light')
    ));

    // Load photo library display mode with validation
    setPhotoLibraryDisplayMode(loadSetting<string>(
      'userSetting_photoLibraryDisplayMode',
      'all',
      (value) => (value === 'unused' || value === 'all' ? value : 'all') // Validate mode
    ));


    // Load DPI threshold percentage (number)
    setDpiWarningThresholdPercent(loadSetting<number>(
      'userSetting_dpiWarningThresholdPercent',
      70, // Default value
      (value) => { // Custom parser to ensure number and range
        const num = parseInt(value, 10);
        // Clamp between 50 and 70 (new range)
        return isNaN(num) ? 70 : Math.max(50, Math.min(70, num));
      }
    ));
    
    // Load project background image opacity (number between 0 and 1)
    setProjectBackgroundImageOpacity(loadSetting<number>(
      'userSetting_projectBackgroundImageOpacity',
      1, // Default value (fully opaque)
      (value) => { // Custom parser to ensure number and range
        const num = parseFloat(value);
        return isNaN(num) ? 1 : Math.max(0, Math.min(1, num));
      }
    ));

    // Load photo library sort settings
    setPhotoLibrarySortBy(loadSetting<'modified' | 'name' | 'timeTaken'>(
      'userSetting_photoLibrarySortBy',
      'modified',
      (value) => ['modified', 'name', 'timeTaken'].includes(value) ? value as 'modified' | 'name' | 'timeTaken' : 'modified'
    ));
    setPhotoLibrarySortDirection(loadSetting<'asc' | 'desc'>(
      'userSetting_photoLibrarySortDirection',
      'desc',
      (value) => ['asc', 'desc'].includes(value) ? value as 'asc' | 'desc' : 'desc'
    ));
    setFavoriteTemplates(loadSetting<string[]>(
      'userSetting_favoriteTemplates',
      [],
      (value) => {
        try {
          const parsed = JSON.parse(value);
          return Array.isArray(parsed) && parsed.every(item => typeof item === 'string') ? parsed : [];
        } catch (e) {
          return [];
        }
      }
    ));

  }, []); // Empty dependency array ensures this runs only once on mount

  // --- Save User Settings to localStorage on Change ---
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showFilenames', JSON.stringify(showFilenames));
  }, [showFilenames]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showEndOfFilename', JSON.stringify(showEndOfFilename));
  }, [showEndOfFilename]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showRatingFilter', JSON.stringify(showRatingFilter));
  }, [showRatingFilter]);
  // Commented out: Always force cover mode, don't save to localStorage
  /*
  useEffect(() => {
    if (isInitialMount.current) return;
    try {
      localStorage.setItem('userSetting_defaultDropModeIsCover', JSON.stringify(defaultDropModeIsCover));
    } catch (e) {
      // console.error('[UserSettings] Error saving "userSetting_defaultDropModeIsCover" to localStorage:', e);
    }
  }, [defaultDropModeIsCover]);
  */
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_addSpreadOnArrow', JSON.stringify(addSpreadOnArrow));
  }, [addSpreadOnArrow]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_autoSwitchTemplateOnRemove', JSON.stringify(autoSwitchTemplateOnRemove));
  }, [autoSwitchTemplateOnRemove]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_autosaveOnSpreadTurn', JSON.stringify(autosaveOnSpreadTurn));
  }, [autosaveOnSpreadTurn]); // Save new setting
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_resetImageTransformsOnTemplateSwitch', JSON.stringify(resetImageTransformsOnTemplateSwitch));
  }, [resetImageTransformsOnTemplateSwitch]); // Save new setting
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showBookGutter', JSON.stringify(showBookGutter));
  }, [showBookGutter]); // Save book gutter setting
  useEffect(() => {
    if (isInitialMount.current) return;
    try {
      localStorage.setItem('userSetting_photoLibraryDisplayMode', photoLibraryDisplayMode);
    } catch (e) {
      // console.error('[UserSettings] Error saving "userSetting_photoLibraryDisplayMode" to localStorage:', e);
    }
  }, [photoLibraryDisplayMode]); // Save photo library display mode
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_autoCoverNearEdges', JSON.stringify(autoCoverNearEdges));
  }, [autoCoverNearEdges]); // Save auto-cover setting
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showQualityIndicators', JSON.stringify(showQualityIndicators));
  }, [showQualityIndicators]); // Save quality indicators setting
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_dpiWarningThresholdPercent', JSON.stringify(dpiWarningThresholdPercent));
  }, [dpiWarningThresholdPercent]); // Save DPI threshold setting
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_projectBackgroundImageOpacity', JSON.stringify(projectBackgroundImageOpacity));
  }, [projectBackgroundImageOpacity]); // Save project background image opacity
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_photoLibrarySortBy', photoLibrarySortBy);
  }, [photoLibrarySortBy]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_photoLibrarySortDirection', photoLibrarySortDirection);
  }, [photoLibrarySortDirection]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showBleedArea', JSON.stringify(showBleedArea));
  }, [showBleedArea]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_bleedAreaMode', bleedAreaMode);
  }, [bleedAreaMode]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showSafetyMargin', JSON.stringify(showSafetyMargin));
  }, [showSafetyMargin]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showBleedLines', JSON.stringify(showBleedLines));
  }, [showBleedLines]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showOnboardingTooltips', JSON.stringify(showOnboardingTooltips));
  }, [showOnboardingTooltips]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showVisualDropChoice', JSON.stringify(showVisualDropChoice));
  }, [showVisualDropChoice]);
  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_showDragIcon', JSON.stringify(showDragIcon));
  }, [showDragIcon]);

  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_imageTrayPosition', imageTrayPosition);
  }, [imageTrayPosition]);

  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_theme', theme);
    // Update the OS-level title bar theme when the theme changes
    if (window.electronAPI?.setTitleBarTheme) {
      window.electronAPI.setTitleBarTheme(theme).catch(err => {
        console.error("Failed to set title bar theme:", err);
      });
    }
  }, [theme]);

  useEffect(() => {
    if (isInitialMount.current) return;
    localStorage.setItem('userSetting_favoriteTemplates', JSON.stringify(favoriteTemplates));
  }, [favoriteTemplates]);

  // After all settings are loaded and initial state is set, mark initial mount as false.
  useEffect(() => {
    isInitialMount.current = false;
  }, []);

  // Listen for theme toggle shortcut
  useEffect(() => {
    const cleanup = window.electronAPI.onTriggerToggleTheme(() => {
      setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
    });
    return cleanup; // Cleanup function to remove listener
  }, [setTheme]);

  return {
    theme,
    setTheme,
    showFilenames,
    setShowFilenames,
    showEndOfFilename,
    setShowEndOfFilename,
    showRatingFilter,
    setShowRatingFilter,
    defaultDropModeIsCover,
    setDefaultDropModeIsCover,
    addSpreadOnArrow,
    setAddSpreadOnArrow,
    autoSwitchTemplateOnRemove,
    setAutoSwitchTemplateOnRemove,
    autosaveOnSpreadTurn, // Return new setting
    setAutosaveOnSpreadTurn, // Return new setter
    resetImageTransformsOnTemplateSwitch, // Return new setting
    setResetImageTransformsOnTemplateSwitch, // Return new setter
    showBookGutter, // Return book gutter setting
    setShowBookGutter, // Return book gutter setter
    photoLibraryDisplayMode, // Return photo library display mode
    setPhotoLibraryDisplayMode, // Return photo library display mode setter
    autoCoverNearEdges, // Return auto-cover setting
    setAutoCoverNearEdges, // Return auto-cover setter
    showQualityIndicators, // Return quality indicators setting
    setShowQualityIndicators, // Return quality indicators setter
    dpiWarningThresholdPercent, // Return DPI threshold setting
    setDpiWarningThresholdPercent, // Return DPI threshold setter
    projectBackgroundImageOpacity, // Return project background image opacity
    setProjectBackgroundImageOpacity, // Return project background image opacity setter
    photoLibrarySortBy, // Return new setting
    setPhotoLibrarySortBy, // Return new setter
    photoLibrarySortDirection, // Return new setting
    setPhotoLibrarySortDirection, // Return new setter
    showBleedArea, // Return bleed area visibility setting
    setShowBleedArea, // Return bleed area visibility setter
    bleedAreaMode, // Return bleed area mode setting
    setBleedAreaMode, // Return bleed area mode setter
    showSafetyMargin,
    setShowSafetyMargin,
    showBleedLines,
    setShowBleedLines,
    showOnboardingTooltips,
    setShowOnboardingTooltips,
    showVisualDropChoice,
    setShowVisualDropChoice,
    showDragIcon,
    setShowDragIcon,
    favoriteTemplates,
    setFavoriteTemplates,
    imageTrayPosition,
    setImageTrayPosition,
  };
};