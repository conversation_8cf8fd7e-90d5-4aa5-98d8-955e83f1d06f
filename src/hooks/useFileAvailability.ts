import { useState, useCallback, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { ImageFile } from '@/components/ImageTray';

// TypeScript declarations are now centralized in electron.d.ts

interface FileAvailabilityState {
  checking: boolean;
  missingFiles: ImageFile[];
  totalFiles: number;
  basePath: string | null;
  ignoreAllMissing: boolean; // Added to track if user chose to ignore missing files
}

interface UseFileAvailabilityProps {
  images: ImageFile[];
  setImages: React.Dispatch<React.SetStateAction<ImageFile[]>>;
  setIsDirty: React.Dispatch<React.SetStateAction<boolean>>;
  projectDirectoryPath: string | null;
  setSpreads?: React.Dispatch<React.SetStateAction<any[]>>;
  projectBackgroundImagePath?: string | null; // Added for project background
  setProjectBackgroundImagePath?: React.Dispatch<React.SetStateAction<string | null>>; // Added for project background
  coverImage?: ImageFile | null; // Added for cover image
  setCoverImage?: React.Dispatch<React.SetStateAction<ImageFile | null>>; // Added for cover image
}

interface UseFileAvailabilityReturn {
  fileAvailability: FileAvailabilityState;
  checkFilesAvailability: (silent?: boolean) => Promise<boolean>;
  updateBasePath: (newBasePath: string) => Promise<boolean>;
  updateIndividualPath: (originalPath: string, newPath: string) => Promise<boolean>;
  startPeriodicCheck: (intervalMinutes?: number) => void;
  stopPeriodicCheck: () => void;
  setIgnoreAllMissingFiles: () => void; // Added function to set ignore state
}

/**
 * Hook to check if original image files are still available and provide
 * functionality to update base paths if files have moved.
 */
export const useFileAvailability = ({
  images,
  setImages,
  setIsDirty,
  projectDirectoryPath,
  setSpreads,
  projectBackgroundImagePath, // Destructure new prop
  setProjectBackgroundImagePath, // Destructure new prop
  coverImage, // Destructure new prop
  setCoverImage // Destructure new prop
}: UseFileAvailabilityProps): UseFileAvailabilityReturn => {
  // State to track file availability status
  const [fileAvailability, setFileAvailability] = useState<FileAvailabilityState>({
    checking: false,
    missingFiles: [],
    totalFiles: 0,
    basePath: projectDirectoryPath,
    ignoreAllMissing: false // Initialize new state
  });
  
  // Keep track of updated file paths to avoid false positives
  const [updatedPaths, setUpdatedPaths] = useState<Record<string, string>>({});
  
  // Ref to store the interval ID for periodic checking
  const checkIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Update basePath when projectDirectoryPath changes
  useEffect(() => {
    setFileAvailability(prev => ({
      ...prev,
      basePath: projectDirectoryPath
    }));
  }, [projectDirectoryPath]);
  
  // Reset missing files when images array changes (new images added)
  useEffect(() => {
    // When images array changes, we should reset the missing files list
    // This ensures newly added images aren't immediately flagged as missing
    if (images.length !== fileAvailability.totalFiles) {
      setFileAvailability(prev => ({
        ...prev,
        missingFiles: [],
        totalFiles: images.length
      }));
    }
  }, [images, fileAvailability.totalFiles]);

  /**
   * Check if all image files in the project are accessible
   * @returns Promise resolving to true if all files are available, false otherwise
   */
  const checkFilesAvailability = useCallback(async (silent: boolean = false): Promise<boolean> => {
    // If not a silent check (i.e., user initiated), reset the ignoreAllMissing flag
    if (!silent) {
      setFileAvailability(prev => ({ ...prev, ignoreAllMissing: false }));
    }

    // console.log(`[useFileAvailability] checkFilesAvailability called. Silent: ${silent}, IgnoreAllMissing: ${fileAvailability.ignoreAllMissing}`); // For debugging
    if (!window.electronAPI?.checkFilesExist) {
      if (!silent && !fileAvailability.ignoreAllMissing) toast.error("File checking requires the desktop app.");
      console.warn("[useFileAvailability] checkFilesAvailability: electronAPI.checkFilesExist not found.");
      return true; // Assume files exist if running in browser
    }

    if (images.length === 0) {
      // console.log("[useFileAvailability] checkFilesAvailability: No images to check."); // REMOVED
      return true; // No images to check
    }

    // If already checking, don't start another check
    if (fileAvailability.checking) {
      // console.log("[useFileAvailability] checkFilesAvailability: Already checking, returning."); // REMOVED
      return true;
    }
    
    // console.log("[useFileAvailability] checkFilesAvailability: Setting checking state to true."); // REMOVED
    setFileAvailability(prev => ({ ...prev, checking: true }));

    try {
      // Get all original paths from images
      const imagePaths = images.map(img => {
        // If this path has been updated, use the new path instead
        const updatedPath = updatedPaths[img.originalPath];
        const pathToUse = updatedPath || img.originalPath;
        return pathToUse;
      });
      // console.log(`[useFileAvailability] checkFilesAvailability: Checking ${imagePaths.length} paths.`); // REMOVED
      
      // Check if files exist
      const existResults = await window.electronAPI.checkFilesExist(imagePaths, silent);
      // console.log("[useFileAvailability] checkFilesAvailability: Existence results received:", existResults); // REMOVED
      
      
      // Find missing files
      const missingFiles: ImageFile[] = [];
      const missingPaths: string[] = [];
      let falsePositiveCount = 0;
      
      // Create a map of existence results for faster lookup
      const existenceMap = new Map<string, boolean>();
      imagePaths.forEach((path, index) => {
        existenceMap.set(path, existResults[index]);
      });
      
      images.forEach((image) => {
        // If this path has been updated, use the new path for checking
        const pathToCheck = updatedPaths[image.originalPath] || image.originalPath;
        
        // Check if the file exists using our map
        const exists = existenceMap.get(pathToCheck);
        
        if (exists === undefined) {
          console.warn(`[useFileAvailability] checkFilesAvailability: Could not find path ${pathToCheck} in checked paths`);
          return;
        }
        
        if (!exists) {
          // Double-check if we have an updated path that might be wrong
          if (updatedPaths[image.originalPath] && !silent) {
            falsePositiveCount++;
          }
          missingFiles.push(image);
          missingPaths.push(pathToCheck); // Track which paths are missing
        }
      });
      
      // Log missing paths for debugging (only if not silent and missing files exist)
      if (missingFiles.length > 0 && !silent) {
        // console.log(`[useFileAvailability] checkFilesAvailability: Found ${missingFiles.length} potentially missing files. Paths:`, missingPaths); // REMOVED
      }
      
      // CRITICAL FIX: Completely disable missing files detection if all files exist
      const allFilesExist = existResults.every(exists => exists === true);
      // console.log(`[useFileAvailability] checkFilesAvailability: All files exist according to checkFilesExist? ${allFilesExist}`); // REMOVED
      
      // Double-check missing files against the updated paths cache before showing toast
      const genuinelyMissingFiles = missingFiles.filter(file => {
        const updatedPath = updatedPaths[file.originalPath];
        if (!updatedPath) {
          const originalExists = existenceMap.get(file.originalPath) === true;
          return !originalExists;
        }
        const updatedExists = existenceMap.get(updatedPath) === true;
        return !updatedExists;
      });
      // console.log(`[useFileAvailability] checkFilesAvailability: Genuinely missing files count after filtering: ${genuinelyMissingFiles.length}`); // REMOVED
      
      // Force clear any existing missing files if all files exist
      if (allFilesExist) {
        if (!silent && !fileAvailability.ignoreAllMissing) { // Respect ignoreAllMissing for toasts
          toast.dismiss();
        }
        setFileAvailability(prev => {
          if (prev.missingFiles.length > 0) {
             // console.log("[useFileAvailability] checkFilesAvailability: All files exist now, clearing missingFiles state."); // REMOVED
          }
          const newState = {
            ...prev,
            checking: false,
            missingFiles: [], // Clear missing files as all exist
            totalFiles: images.length
          };
          return newState;
        });
      } else {
        // Only update state with missing files if some files are actually missing
        // AND we are not currently ignoring all missing files (unless it's a manual check)
        if (!fileAvailability.ignoreAllMissing || !silent) {
          setFileAvailability(prev => {
            // console.log(`[useFileAvailability] checkFilesAvailability: Some files missing, updating missingFiles state.`); // REMOVED
            const newState = {
              ...prev,
              checking: false,
              missingFiles: genuinelyMissingFiles,
              totalFiles: images.length
            };
            // console.log(`[useFileAvailability] checkFilesAvailability: Updating state:`, { // REMOVED
            //   prevMissingCount: prev.missingFiles.length,
            //   newMissingCount: newState.missingFiles.length
            // });
            return newState;
          });
        } else {
           // If ignoring and it's a silent check, just update checking status
           setFileAvailability(prev => ({ ...prev, checking: false, totalFiles: images.length }));
        }
      }

      
      // Only show toast if we actually have files that are genuinely missing AND our direct check confirms files are missing
      // AND we are not currently ignoring all missing files (unless it's a manual check, where ignoreAllMissing would be false)
      if (genuinelyMissingFiles.length > 0 && !allFilesExist && !silent && !fileAvailability.ignoreAllMissing) {
        // console.log(`[useFileAvailability] checkFilesAvailability: Showing warning toast for ${genuinelyMissingFiles.length} missing files.`); // REMOVED
        toast.warning(
          `${genuinelyMissingFiles.length} of ${images.length} image files are missing. Use the "Update Folder Location" option to fix.`,
          { duration: 6000 }
        );
        return false;
      } else if (allFilesExist && !silent && !fileAvailability.ignoreAllMissing) {
        toast.dismiss();
        // console.log("[useFileAvailability] checkFilesAvailability: All files exist, dismissing any warning toast."); // REMOVED
        return true;
      } else if (genuinelyMissingFiles.length === 0 && !silent && !fileAvailability.ignoreAllMissing) {
        if (fileAvailability.missingFiles.length > 0) { // Only show success if there were previously missing files
          // console.log("[useFileAvailability] checkFilesAvailability: All missing files now found, showing success toast."); // REMOVED
          toast.success('All image files are now available.');
        }
      }
      
      // console.log(`[useFileAvailability] checkFilesAvailability: Final result - All available? ${genuinelyMissingFiles.length === 0}`); // REMOVED
      return genuinelyMissingFiles.length === 0; // Return true only if genuinely missing files count is 0
    } catch (error: any) {
      console.error("[useFileAvailability] checkFilesAvailability: Error checking file availability:", error);
      if (!silent && !fileAvailability.ignoreAllMissing) toast.error(`Error checking files: ${error.message || 'Unknown error'}`);
      
      setFileAvailability(prev => ({
        ...prev,
        checking: false
      }));
      
      return false;
    }
  }, [images, projectDirectoryPath, updatedPaths, fileAvailability.checking, fileAvailability.missingFiles.length, fileAvailability.ignoreAllMissing]); // Added ignoreAllMissing

  // Listen for custom path update events, especially for newly added images
  useEffect(() => {
    const handlePathsUpdated = (event: CustomEvent) => {
      
      // Handle isDirty flag
      if (event.detail?.isDirty) {
        setIsDirty(true);
      }
      
      // Handle newly added images
      if (event.detail?.newImages && Array.isArray(event.detail.newImages)) {
        
        // Add the paths of new images to the bookProofsApp.updatedPaths cache
        // This ensures they're recognized as valid paths during file availability checks
        if (window.bookProofsApp) {
          const newImagePaths = event.detail.newImages.reduce((acc: Record<string, string>, img: any) => {
            acc[img.originalPath] = img.originalPath; // Map path to itself to mark as valid
            return acc;
          }, {});
          
          // Update the global cache with these new paths
          const currentCache = (window.bookProofsApp as any).updatedPaths || {};
          (window.bookProofsApp as any).updatedPaths = {
            ...currentCache,
            ...newImagePaths
          };
          
        }
        
        // Force a check of file availability to ensure everything is up to date
        // Use setTimeout to ensure this happens after state updates
        setTimeout(() => {
          checkFilesAvailability(true); // Silent check
        }, 500);
      }
    };
    
    // Add event listener for our custom event
    window.addEventListener('book-proofs-paths-updated', handlePathsUpdated as EventListener);
    
    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('book-proofs-paths-updated', handlePathsUpdated as EventListener);
    };
  }, [checkFilesAvailability, setIsDirty]);

  /**
   * Update the base path for all images
   * @param newBasePath New base directory path
   * @returns Promise resolving to true if successful, false otherwise
   */
  const updateBasePath = useCallback(async (newBasePath: string): Promise<boolean> => {
    if (!window.electronAPI?.updateFilePaths) {
      toast.error("Path updating requires the desktop app.");
      return false;
    }

    if (images.length === 0) {
      return true; // No images to update
    }

    console.log(`[updateBasePath] Starting update with new base path: ${newBasePath}`);
    setFileAvailability(prev => ({ ...prev, checking: true }));

    try {
      // Get all original paths from images
      const imagePaths = images.map(img => img.originalPath);
      
      console.log(`[updateBasePath] Updating ${imagePaths.length} images with new base path`);
      
      // Update paths with new base directory
      const pathUpdates = await window.electronAPI.updateFilePaths(imagePaths, newBasePath);
      
      console.log(`[updateBasePath] Update result:`, pathUpdates);
      
      if (!pathUpdates || pathUpdates.length === 0) {
        setFileAvailability(prev => ({ ...prev, checking: false }));
        toast.info("No image paths were updated.");
        return true;
      }
      
      // Create a map of original paths to new paths
      const pathMap: Record<string, string> = {};
      pathUpdates.forEach(update => {
        if (update.originalPath && update.newPath) {
          pathMap[update.originalPath] = update.newPath;
        }
      });
      
      // Update the images with new paths
      setImages(prev => {
        const updatedImages = prev.map(img => {
          const newPath = pathMap[img.originalPath];
          if (newPath) {
            return { ...img, originalPath: newPath };
          }
          return img;
        });
        
        return updatedImages;
      });
      
      // Mark project as dirty since paths have changed
      // Call setIsDirty with a callback to ensure we're working with the latest state
      setIsDirty(() => {
        console.log('[updateBasePath] Setting isDirty to true');
        return true;
      });
      
      // Update the updatedPaths state
      setUpdatedPaths(prev => {
        const newCache = { ...prev };
        pathUpdates.forEach(update => {
          if (update.originalPath && update.newPath) {
            newCache[update.originalPath] = update.newPath;
          }
        });
        
        // Update the global bookProofsApp object with the new cache
        if (window.bookProofsApp && typeof window.bookProofsApp.getAllUpdatedPaths === 'function') {
          // The object is already initialized, so we can safely update the internal cache
          (window.bookProofsApp as any).updatedPaths = newCache;
        }
        
        return newCache;
      });

      // Update project background image path if it was affected
      if (projectBackgroundImagePath && setProjectBackgroundImagePath && pathMap[projectBackgroundImagePath]) {
        console.log(`[updateBasePath] Updating project background image path from ${projectBackgroundImagePath} to ${pathMap[projectBackgroundImagePath]}`);
        setProjectBackgroundImagePath(pathMap[projectBackgroundImagePath]);
      }

      // Update cover image path if it was affected
      if (coverImage && setCoverImage && coverImage.originalPath && pathMap[coverImage.originalPath]) {
        console.log(`[updateBasePath] Updating cover image path from ${coverImage.originalPath} to ${pathMap[coverImage.originalPath]}`);
        setCoverImage(prevCover => prevCover ? { ...prevCover, originalPath: pathMap[coverImage.originalPath]! } : null);
      }
      
      // Update spreads if needed (including spread-specific backgrounds)
      if (setSpreads) {
        setSpreads(prevSpreads => {
          if (Object.keys(pathMap).length === 0) return prevSpreads;

          return prevSpreads.map(spread => {
            let spreadWasUpdated = false;
            const updatedImages = spread.images.map(placement => {
              // Image paths are updated via the main `images` state,
              // so no direct path update needed here for `placement.originalPath`
              // We just need to know if this spread *contained* an image that was updated.
              const imageFile = images.find(img => img.id === placement.imageId);
              if (imageFile && pathMap[imageFile.originalPath]) {
                spreadWasUpdated = true;
              }
              return placement;
            });

            let updatedSpreadBackgroundData = spread.spreadBackgroundData;
            if (spread.spreadBackgroundData?.imagePath && pathMap[spread.spreadBackgroundData.imagePath]) {
              console.log(`[updateBasePath] Updating spread background for spread ${spread.id} from ${spread.spreadBackgroundData.imagePath} to ${pathMap[spread.spreadBackgroundData.imagePath]}`);
              updatedSpreadBackgroundData = {
                ...spread.spreadBackgroundData,
                imagePath: pathMap[spread.spreadBackgroundData.imagePath],
              };
              spreadWasUpdated = true;
            }

            if (spreadWasUpdated) {
              return {
                ...spread,
                images: updatedImages, // This might be redundant if images array is the source of truth
                spreadBackgroundData: updatedSpreadBackgroundData,
              };
            }
            return spread;
          });
        });
      }
      
      // Check if any files are still missing
      const stillMissing = await checkFilesAvailability(true);
      
      if (!stillMissing) {
        toast.success(`Updated paths for ${pathUpdates.length} images. Some files are still missing.`);
      } else {
        toast.success(`Successfully updated paths for ${pathUpdates.length} images.`);
      }
      
      return true;
    } catch (error: any) {
      console.error("[useFileAvailability] Error updating base path:", error);
      setFileAvailability(prev => ({ ...prev, checking: false }));
      toast.error(`An error occurred while updating paths: ${error.message || 'Unknown error'}`);
      return false;
    }
  }, [images, setImages, setFileAvailability, checkFilesAvailability, setSpreads, setUpdatedPaths, setIsDirty, window.electronAPI, projectBackgroundImagePath, setProjectBackgroundImagePath, coverImage, setCoverImage]);

  // Function to start periodic file availability checking
  const startPeriodicCheck = useCallback((intervalMinutes: number = 30) => {
    // First stop any existing interval
    stopPeriodicCheck();
    
    // Convert minutes to milliseconds - ensure it's at least 5 minutes
    const intervalMs = Math.max(intervalMinutes, 5) * 60 * 1000;
    
    // Set up new interval with silent checks to avoid spamming notifications
    checkIntervalRef.current = setInterval(async () => {
      // Only check if there are images to check
      if (images.length > 0) {
        // Use silent mode for periodic checks to avoid notifications
        const allAvailable = await checkFilesAvailability(true);
        
        // Only log if files are missing and limit to once per check
        if (!allAvailable) {
          // Log once per check, not per file
          console.log(`[Periodic check] Found ${fileAvailability.missingFiles.length} missing files`);
        }
      }
    }, intervalMs);
    
    // Log only once when starting the periodic check
    console.log(`Started periodic file availability check every ${Math.max(intervalMinutes, 5)} minutes`);
  }, [images, checkFilesAvailability]);
  
  // Function to stop periodic file availability checking
  const stopPeriodicCheck = useCallback(() => {
    if (checkIntervalRef.current) {
      clearInterval(checkIntervalRef.current);
      checkIntervalRef.current = null;
      console.log('Stopped periodic file availability check');
    }
  }, []);
  
  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      stopPeriodicCheck();
    };
  }, [stopPeriodicCheck]);

  /**
   * Update the path of an individual image file
   * @param originalPath The original path of the file to update
   * @param newPath The new path for the file
   * @returns Promise resolving to true if successful, false otherwise
   */
  const updateIndividualPath = useCallback(async (originalPath: string, newPath: string): Promise<boolean> => {
    console.log(`[useFileAvailability] Updating individual path:
  FROM: ${originalPath}
  TO: ${newPath}`);
    
    if (!originalPath || !newPath) {
    console.error(`[useFileAvailability] Invalid paths provided: originalPath=${originalPath}, newPath=${newPath}`);
    toast.error("Invalid file paths provided.");
    return false;
  }
  
  setFileAvailability(prev => ({ ...prev, checking: true }));
  
  try {
    // Call the main process to update the path
    const result = await window.electronAPI.updateIndividualPath(originalPath, newPath);
    
    if (!result || !result.newPath) {
      console.error(`[useFileAvailability] Failed to update path: ${originalPath}`);
      setFileAvailability(prev => ({ ...prev, checking: false }));
      toast.error("Failed to update file path.");
      return false;
    }
    
    
    // Find the image that needs updating
    const imageToUpdate = images.find(img => img.originalPath === originalPath);
    
    if (!imageToUpdate) {
      console.error(`[useFileAvailability] Could not find image with path: ${originalPath}`);
      setFileAvailability(prev => ({ ...prev, checking: false }));
      toast.error("Could not find the image to update.");
      return false;
    }
    
    
    // Update any references to this image in the spreads data
    // This is important to ensure all parts of the application know about the new path
    let referenceCount = 0;
    
    // Update all references to this image in spread elements if setSpreads is available
    if (setSpreads) {
      setSpreads(prevSpreads => {
      const updatedSpreads = prevSpreads.map(spread => {
        if (!spread.elements) return spread;
        
        const updatedElements = spread.elements.map(element => {
          if (element.type === 'image' && element.originalPath === originalPath) {
            console.log(`[updateIndividualPath] Updating image path in spread ${spread.id}, element ${element.id}`);
            referenceCount++;
            return {
              ...element,
              originalPath: result.newPath
            };
          }
          return element;
        });
        
        return {
          ...spread,
          elements: updatedElements
        };
      });
      
      if (referenceCount > 0) {
        console.log(`[updateIndividualPath] Updated ${referenceCount} references in spreads`);
      }
      
      return updatedSpreads;
      });
    }
    
    // We need to regenerate thumbnails and previews for the updated file
    console.log(`[updateIndividualPath] Regenerating thumbnails and previews for ${result.newPath}`);
    
    try {
      // Use the regenerateThumbnails function to create new thumbnails and previews
      if (!window.electronAPI?.regenerateThumbnails) {
        throw new Error('regenerateThumbnails function not available');
      }
      
      const regeneratedImage = await window.electronAPI.regenerateThumbnails(result.newPath);
      console.log(`[updateIndividualPath] Regenerated image:`, regeneratedImage);
      
      if (!regeneratedImage) {
        throw new Error('Failed to regenerate thumbnails');
      }
      
      // Update the image with the new path and thumbnails
      setImages(prev => {
        // Find the image that needs updating
        const updatedImages = prev.map(img => {
          if (img.originalPath === originalPath) {
            console.log(`[updateIndividualPath] Updating image with regenerated thumbnails`);
            
            // Update with the regenerated image data
            return { 
              ...img,
              originalPath: regeneratedImage.originalPath,
              thumbnailUrl: regeneratedImage.thumbnailUrl,
              previewUrl: regeneratedImage.previewUrl,
              // Keep the original id and other metadata
            };
          }
          return img;
        });
        
        return updatedImages;
      });
    } catch (regenerateError) {
      console.error(`[updateIndividualPath] Error regenerating thumbnails:`, regenerateError);
      
      // Fall back to just updating the path if regeneration fails
      setImages(prev => {
        return prev.map(img => 
          img.originalPath === originalPath 
            ? { ...img, originalPath: result.newPath } 
            : img
        );
      });
    }
    
    // Mark project as dirty since paths have changed
    // Call setIsDirty with a callback to ensure we're working with the latest state
    setIsDirty(() => {
      console.log('[updateIndividualPath] Setting isDirty to true');
      return true;
    });
    
    // Store the updated path in our cache to ensure it's used in future checks
    setUpdatedPaths(prevCache => {
      console.log(`[updateIndividualPath] Updating path cache:
  Original: ${originalPath}
  New: ${result.newPath}`);
        console.log(`[updateIndividualPath] Previous cache:`, prevCache);
        
        const newCache = {
          ...prevCache,
          [originalPath]: result.newPath
        };
        
        console.log(`[updateIndividualPath] New cache:`, newCache);
        
        // Update the global bookProofsApp object with the new cache
        if (window.bookProofsApp) {
          (window.bookProofsApp as any).updatedPaths = { ...newCache }; // Ensure a new object for reactivity if needed elsewhere
          console.log(`[updateIndividualPath] Updated global bookProofsApp.updatedPaths cache`);
          
          const pathsUpdatedEvent = new CustomEvent('book-proofs-paths-updated', {
            detail: {
              updatedPaths: { ...newCache },
              isDirty: true
            }
          });
          window.dispatchEvent(pathsUpdatedEvent);
        }
        return newCache;
      });

      // Update project background image path if it was the one updated
      if (projectBackgroundImagePath && setProjectBackgroundImagePath && originalPath === projectBackgroundImagePath) {
        console.log(`[updateIndividualPath] Updating project background image path from ${originalPath} to ${result.newPath}`);
        setProjectBackgroundImagePath(result.newPath);
      }

      // Update cover image path if it was the one updated
      if (coverImage && setCoverImage && coverImage.originalPath === originalPath) {
        console.log(`[updateIndividualPath] Updating cover image path from ${originalPath} to ${result.newPath}`);
        // Create a new object for the updated cover image to ensure reactivity
        setCoverImage(prevCover => prevCover ? { ...prevCover, originalPath: result.newPath } : null);
      }

      // Update spread-specific background paths if necessary
      if (setSpreads) {
        setSpreads(prevSpreads => {
          return prevSpreads.map(spread => {
            if (spread.spreadBackgroundData?.imagePath === originalPath) {
              console.log(`[updateIndividualPath] Updating spread background for spread ${spread.id} from ${originalPath} to ${result.newPath}`);
              return {
                ...spread,
                spreadBackgroundData: {
                  ...spread.spreadBackgroundData,
                  imagePath: result.newPath,
                },
              };
            }
            return spread;
          });
        });
      }
      
      // Remove this file from missing files list if it was there
      setFileAvailability(prev => {
        const newState = {
          ...prev,
          checking: false,
          missingFiles: prev.missingFiles.filter(file => file.originalPath !== originalPath)
        };
        return newState;
      });
      
      // Add a small delay before showing success message to ensure state updates have propagated
      setTimeout(() => {
        toast.success("File path updated successfully.");
      }, 100);
      
      return true;
    } catch (error: any) {
      console.error("[useFileAvailability] Error updating individual file path:", error);
      
      // Update state to reflect the error
      setFileAvailability(prev => {
        const newState = { ...prev, checking: false };
        return newState;
      });
      
      // Add a small delay before showing error message to ensure state updates have propagated
      setTimeout(() => {
        toast.error(`An error occurred while updating the file path: ${error.message || 'Unknown error'}`);
      }, 100);
      
      return false;
    }
  }, [images, setImages, setIsDirty, setSpreads, setUpdatedPaths, window.electronAPI, projectBackgroundImagePath, setProjectBackgroundImagePath, coverImage, setCoverImage]);

  /**
   * Sets the state to ignore all missing files until the next manual check.
   * Displays a toast message to inform the user.
   */
  const setIgnoreAllMissingFiles = useCallback(() => {
    setFileAvailability(prev => ({
      ...prev,
      ignoreAllMissing: true,
      missingFiles: [] // Clear current missing files as they are being ignored
    }));
    toast.info("Ignoring missing files. Use 'Check for Missing Files' button in settings to relink at a later time.", {
      duration: 6000,
    });
  }, [setFileAvailability]);

  // Expose the updated paths to the rest of the application
  useEffect(() => {
    // Create a global API to access the updated file paths
    if (typeof window !== 'undefined') {
      // Define the bookProofsApp object if it doesn't exist
      if (!window.bookProofsApp) {
        window.bookProofsApp = {};
      }
      
      // Add the getUpdatedFilePath function to the global API
      window.bookProofsApp.getUpdatedFilePath = (originalPath: string) => {
        return updatedPaths[originalPath] || null;
      };
      
      // Add the getAllUpdatedPaths function to the global API
      window.bookProofsApp.getAllUpdatedPaths = () => {
        return { ...updatedPaths };
      };
      
      // Add a function to update paths in images array
      window.bookProofsApp.updateImagePaths = (images: ImageFile[]) => {
        
        // Apply all updated paths to the images array
        const updatedImages = images.map(img => {
          const updatedPath = updatedPaths[img.originalPath];
          if (updatedPath) {
            console.log(`[useFileAvailability] Updating image path:
  FROM: ${img.originalPath}
  TO: ${updatedPath}`);
            return { ...img, originalPath: updatedPath };
          }
          return img;
        });
        
        return updatedImages;
      };
    }
  }, [updatedPaths]);
  
  return {
    fileAvailability,
    checkFilesAvailability,
    updateBasePath,
    updateIndividualPath,
    startPeriodicCheck,
    stopPeriodicCheck,
    setIgnoreAllMissingFiles // Expose the new function
  };
};
