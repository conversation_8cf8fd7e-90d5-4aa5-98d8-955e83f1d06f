// electron/preload.js
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose a controlled API to the Renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // Function the Renderer can call to request opening the image dialog
  // Updated to accept args object
  openImageDialog: (args) => ipcRenderer.invoke('dialog:openImage', args),

  // Function to trigger "Save As..." (always shows dialog)
  saveProjectAs: (data) => ipcRenderer.invoke('dialog:saveProjectAs', data), // Renamed handler

  // Function to trigger "Save" (overwrites current file if path exists)
  projectSaveFile: (data) => ipcRenderer.invoke('project:saveFile', data), // New handler for direct save
  // Function to trigger loading a project file
  // Load project data given a specific file path
  loadProject: (filePath) => ipcRenderer.invoke('dialog:loadProject', filePath), // Returns { success, data?, filePath?, projectDirectoryPath?, error?, message?, canceled? }
  // Function to show confirmation dialog for reusing an image
  confirmImageReuse: (imageName) => ipcRenderer.invoke('dialog:confirmImageReuse', imageName),

  // NEW: Function for generic confirmation dialog
  confirmDialog: (options) => ipcRenderer.invoke('dialog:confirm', options),

  // NEW: Dialog for confirming action when too many images are dropped
  confirmExcessImagesDialog: (droppedCount, availableSlots, templateCapacity) => ipcRenderer.invoke('dialog:confirmExcessImages', droppedCount, availableSlots, templateCapacity),

  // --- Start Page Dialog Handlers ---
  // Show dialog to create a new .bookproof project file
  createNewProjectFile: () => ipcRenderer.invoke('dialog:create-new-project-file'), // Returns { success, filePath?, projectDirectoryPath?, canceled?, error? }
  // Show dialog to select an existing .bookproof file to open
  openProjectFile: () => ipcRenderer.invoke('dialog:open-project-file'), // Returns { success, filePath?, canceled?, error? }
  // Inform main process of the active project directory (after save/load or new project setup)
  setProjectDirectory: (directoryPath) => ipcRenderer.invoke('project:set-directory', directoryPath), // Returns { success, path?, error? }

  // --- Listeners for Main Process events ---
  // Listener for Undo shortcut
  onTriggerUndo: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-undo', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-undo', listener);
    };
  },
  // Listener for Redo shortcut
  onTriggerRedo: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-redo', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-redo', listener);
    };
  }, // Correct comma after onTriggerRedo
  // Listener for Load Project menu item
  onTriggerLoadProject: (callback) => { // Renamed to onTriggerLoadProject for clarity
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-load-project', listener); // Ensure this matches the event name in main.cjs
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-load-project', listener);
    };
  },
  // Listener for New Project menu item
  onTriggerNewProject: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-new-project', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-new-project', listener);
    };
  },
  // Listener for consolidated import progress (metadata, thumbnails, previews)
  onImportProgress: (callback) => {
    const listener = (event, data) => callback(data); // Pass data { stage, current, total } or null
    ipcRenderer.on('import-progress', listener);
    return () => {
      ipcRenderer.removeListener('import-progress', listener);
    };
  },
  // Listener for individual preview completion (still needed to update image state)
  onPreviewGenerated: (callback) => {
    const listener = (event, data) => callback(data); // Pass data { originalPath, previewUrl }
    ipcRenderer.on('preview-generated', listener);
    return () => {
      ipcRenderer.removeListener('preview-generated', listener);
    };
  },
  
  // Listener for preview processing status (to track progress before completion)
  onPreviewProcessing: (callback) => {
    const listener = (event, data) => callback(data); // Pass data { originalPath, status }
    ipcRenderer.on('preview-processing', listener);
    return () => {
      ipcRenderer.removeListener('preview-processing', listener);
    };
  },

  // Functions for native fullscreen control
  setNativeFullScreen: (flag) => ipcRenderer.invoke('window:setFullScreen', flag),
  isNativeFullScreen: () => ipcRenderer.invoke('window:isFullScreen'),
  // Function to load and pre-process image data for PDF export
  loadImageData: (filePath, options) => ipcRenderer.invoke('load-image-data', filePath, options),
  // Function to request window resize
  setWindowSize: (size) => ipcRenderer.invoke('window:setSize', size),
  // Function to reveal a file in the OS file explorer
  showItemInFolder: (filePath) => ipcRenderer.invoke('shell:showItemInFolder', filePath),
  // Function to attempt opening a file specifically in Photoshop
  editInPhotoshop: (filePath) => ipcRenderer.invoke('app:editInPhotoshop', filePath), // Invokes the main process handler
  // Listener for file updates from the main process (after external edit)
  onFileUpdated: (callback) => {
    const listener = (event, data) => callback(data); // data: { originalPath, thumbnailUrl, previewUrl }
    ipcRenderer.on('app:file-updated', listener);
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('app:file-updated', listener);
    };
  },
   // Listener for file update errors from the main process
   onFileUpdateError: (callback) => {
    const listener = (event, data) => callback(data); // data: { originalPath, error }
    ipcRenderer.on('app:file-update-error', listener);
    return () => {
      ipcRenderer.removeListener('app:file-update-error', listener);
    };
  }, // Add comma here
  // Listener for Save shortcut
  onTriggerSave: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-save', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-save', listener);
    };
  },
  onTriggerSaveAs: (callback) => {
   const listener = (event, ...args) => callback(...args);
   ipcRenderer.on('trigger-save-as', listener);
   return () => {
     ipcRenderer.removeListener('trigger-save-as', listener);
   };
  },
  onTriggerImportImages: (callback) => {
   const listener = (event, ...args) => callback(...args);
   ipcRenderer.on('trigger-import-images', listener);
   return () => {
     ipcRenderer.removeListener('trigger-import-images', listener);
   };
  },
  onTriggerExportDialog: (callback) => {
   const listener = (event, ...args) => callback(...args);
   ipcRenderer.on('trigger-export-dialog', listener);
   return () => {
     ipcRenderer.removeListener('trigger-export-dialog', listener);
   };
  },// Add comma here
  // Image processing
  // REMOVED duplicate openImageDialog definition
  processImage: (imagePath) => ipcRenderer.invoke('image:process', imagePath),
  getImageDimensions: (imagePath) => ipcRenderer.invoke('image:get-dimensions', imagePath),
  getExifData: (filePath) => ipcRenderer.invoke('image:get-exif-data', filePath),
  getImageDataUrl: (filePath) => ipcRenderer.invoke('image:get-data-url', filePath), // Ensure this matches the main process handler
  regenerateThumbnails: (filePath) => ipcRenderer.invoke('image:regenerate-thumbnails', filePath),

  // File availability checking
  checkFilesExist: (filePaths, silent) => ipcRenderer.invoke('file:check-exists', filePaths, silent),
  updateFilePaths: (originalPaths, newBasePath) => ipcRenderer.invoke('file:update-paths', originalPaths, newBasePath),
  selectDirectory: (options) => ipcRenderer.invoke('dialog:select-directory', options),
  selectFile: (options) => ipcRenderer.invoke('dialog:select-file', options),
  updateIndividualPath: (originalPath, newPath) => ipcRenderer.invoke('file:update-individual-path', originalPath, newPath),

  // --- PDF Export Related ---
  showSaveDialog: (options) => ipcRenderer.invoke('dialog:showSaveDialog', options), // For PDF save location
  savePdfFile: (filePath, data) => ipcRenderer.invoke('file:savePdf', filePath, data), // To save PDF ArrayBuffer
  openPath: (filePath) => ipcRenderer.invoke('shell:openPath', filePath), // To open the saved PDF
  // NEW: Handler for loading image data + ICC profile for PDF export
  loadImageDataForPdf: (filePath, options) => ipcRenderer.invoke('load-image-data-for-pdf', filePath, options),
  // NEW: Handler for loading image data for Canvas export (similar to PDF)
  loadImageDataForCanvas: (filePath, options) => ipcRenderer.invoke('load-image-data-for-canvas', filePath, options),

  // JPEG Export specific
  showOpenDialog: (options) => ipcRenderer.invoke('dialog:showOpenDialog', options), // For selecting a directory
  saveFile: (filePath, dataUrl) => ipcRenderer.invoke('file:saveFile', filePath, dataUrl), // For saving individual JPEGs
  checkFileExists: (filePath) => ipcRenderer.invoke('file:checkFileExists', filePath), // For checking if a file exists
  // NEW: Handler for generating JPEG with color profile conversion
  convertAndGenerateJpeg: (imageData, options) => ipcRenderer.invoke('image:convertAndGenerateJpeg', imageData, options),

  // --- Backup State Communication ---
  // Listener for main process requesting project state for backup
  onRequestProjectState: () => {
    return new Promise((resolve) => {
      const listener = (event, state) => {
        resolve(state);
        ipcRenderer.removeListener('project-state-response', listener); // Clean up listener
      };
      ipcRenderer.on('project-state-response', listener);
      ipcRenderer.send('request-project-state');
    });
  },
  // Function for renderer to send the state back to main process
  sendProjectStateResponse: (state) => {
    ipcRenderer.send('project-state-response', state);
  },
  // NEW: Listener specifically for backup state requests from main
  onBackupStateRequest: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('request-project-state-for-backup', listener);
    return () => {
      ipcRenderer.removeListener('request-project-state-for-backup', listener);
    };
  },
  // --- End Backup State Communication ---

  // --- Drag and Drop File Processing ---
  processDroppedFiles: (filePaths, options) => ipcRenderer.invoke('dialog:processDroppedFiles', filePaths, options),

  onDroppedFilesProgress: (callback) => {
    const listener = (event, data) => callback(data);
    ipcRenderer.on('dropped-files-progress', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('dropped-files-progress', listener);
    };
  },
  // --- End Drag and Drop File Processing ---
  
  // --- File Association Handling ---
  // Listener for when a .bp file is opened from Finder
  onFileOpened: (callback) => {
    const listener = (event, filePath) => callback(filePath);
    ipcRenderer.on('file-opened', listener);
    return () => {
      ipcRenderer.removeListener('file-opened', listener);
    };
  },
  // --- End File Association Handling ---

  // --- Theme Management ---
  // Function to set the title bar theme
  setTitleBarTheme: (theme) => ipcRenderer.invoke('theme:setTitleBar', theme),
  // Listener for Toggle Theme shortcut
  onTriggerToggleTheme: (callback) => {
    const listener = (event, ...args) => callback(...args);
    ipcRenderer.on('trigger-toggle-theme', listener);
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener('trigger-toggle-theme', listener);
    };
  },
  // --- End Theme Management ---
}); // Correctly closing the exposeInMainWorld object and call