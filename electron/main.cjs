// electron/main.js
const { app, BrowserWindow, ipcMain, dialog, protocol, Menu, shell, session } = require('electron'); // Import Menu, shell, and session
const path = require('node:path');
const fs = require('node:fs');
const fsPromises = require('node:fs').promises; // Use promises for async fs operations
const crypto = require('node:crypto'); // For unique filenames
const { execFile } = require('node:child_process'); // Import execFile
const sharp = require('sharp'); // Import sharp
const exifr = require('exifr'); // Import exifr
const chokidar = require('chokidar'); // Import chokidar for file watching

// Determine if running in development or production
// const isDev = process.env.NODE_ENV !== 'production'; // Use app.isPackaged instead

// Top-level Maps for watcher and debounce management
const fileWatchers = new Map(); // Map to store active chokidar watchers (filePath -> watcher instance)
const debounceTimers = new Map(); // Map to store debounce timers (filePath -> timerId)

// --- Load ICC Profile Buffers and Paths ---
let sRGBProfileBuffer = null;
let adobeRGBProfileBuffer = null;
let srgbProfilePathGlobal = null; // Store path globally
let adobeRGBProfilePathGlobal = null; // Store path globally

try {
  srgbProfilePathGlobal = path.join(__dirname, '..', 'build', 'sRGB_IEC61966-2.1.icc');
  sRGBProfileBuffer = fs.readFileSync(srgbProfilePathGlobal);
  console.log(`[Main Process] Loaded sRGB ICC Profile: ${srgbProfilePathGlobal} (${sRGBProfileBuffer.length} bytes)`);
} catch (err) {
  console.error('[Main Process] Failed to load sRGB ICC Profile:', err);
}

try {
  adobeRGBProfilePathGlobal = path.join(__dirname, '..', 'build', 'AdobeRGB1998.icc');
  adobeRGBProfileBuffer = fs.readFileSync(adobeRGBProfilePathGlobal);
  console.log(`[Main Process] Loaded Adobe RGB ICC Profile: ${adobeRGBProfilePathGlobal} (${adobeRGBProfileBuffer.length} bytes)`);
} catch (err) {
  console.error('[Main Process] Failed to load Adobe RGB ICC Profile:', err);
}
// --- End Load ICC Profile Buffers and Paths ---

// --- Backup Settings ---
const BACKUP_INTERVAL_MS = 5 * 60 * 1000; // 5 minutes
const MAX_BACKUPS = 20;
const BACKUP_FOLDER_NAME = 'autosave backups'; // Visible folder name
let backupIntervalId = null; // To store the backup timer ID
// -----------------------

// Function to stop the backup timer (defined globally)
function stopBackupTimer() {
  if (backupIntervalId) {
    console.log('[Backup] Stopping backup timer.');
    clearInterval(backupIntervalId);
    backupIntervalId = null;
  }
}
function createWindow() {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200, // Adjust width as needed
    height: 800, // Adjust height as needed
    minWidth: 800,
    minHeight: 600,
    maxWidth: 7680,
    maxHeight: 4320,
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'), // Use the preload script
      nodeIntegration: false, // Keep false for security
      contextIsolation: true, // Keep true for security
    },
  });

  // Load the index.html of the app.
  // Use !app.isPackaged to check if running in development
  if (!app.isPackaged) {
    // Load the Vite dev server URL
    // Make sure this port matches your Vite config (default is 5173)
    mainWindow.loadURL('http://localhost:8080'); // Updated port to match Vite server
  } else {
    // Load the built index.html file
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
    // mainWindow.webContents.openDevTools();
  }

  // Optional: Maximize window on start
  // mainWindow.maximize();
  // Always open DevTools for debugging packaged app issues
  // mainWindow.webContents.openDevTools();
  
  // Handle the fileToOpen if it was set by the open-file event before app was ready
  if (fileToOpen) {
    console.log(`[Main Process] Opening file from startup: ${fileToOpen}`);
    // Wait for the window to be ready before sending the file-opened event
    mainWindow.webContents.on('did-finish-load', () => {
      mainWindow.webContents.send('file-opened', fileToOpen);
      fileToOpen = null; // Reset after handling
    });
  }
  
  return mainWindow;
}

// Variable to store file path from open-file event
let fileToOpen = null;

// Handle open-file events (for macOS)
app.on('open-file', (event, filePath) => {
  event.preventDefault();
  console.log(`[Main Process] Received open-file event with path: ${filePath}`);
  
  // If the app is already ready, send the file path to the renderer process
  if (app.isReady() && BrowserWindow.getAllWindows().length > 0) {
    const win = BrowserWindow.getAllWindows()[0];
    if (win && !win.isDestroyed()) {
      win.webContents.send('file-opened', filePath);
    }
  } else {
    // Store the file path to be opened when the app is ready
    fileToOpen = filePath;
  }
});

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Register the custom protocol *after* app is ready, *before* creating window
  protocol.registerFileProtocol('safe-file', (request, callback) => {
    const rawUrl = request.url; // Keep original for logging
    try {
      // Use URL constructor to parse. Base URL is needed but only pathname matters.
      const parsedUrl = new URL(rawUrl);
      const filePath = parsedUrl.pathname; // Gets the path part, e.g., /path/to/cache/hash.webp

      // Decode the pathname specifically
      const decodedPath = decodeURI(filePath);

      // Normalize the decoded path
      const normalizedPath = path.normalize(decodedPath);

      // Check if the file actually exists and is readable *after* normalization
      fs.access(normalizedPath, fs.constants.R_OK, (err) => {
         if (err) {
            console.error(`safe-file: File not found or not readable: ${normalizedPath} (from URL: ${rawUrl})`);
            callback({ error: -6 }); // FILE_NOT_FOUND
         } else {
            callback({ path: normalizedPath }); // Return the valid, normalized path
         }
      });

    } catch (error) {
      console.error(`Failed to process path for safe-file protocol: ${rawUrl}`, error);
      callback({ error: -6 }); // FILE_NOT_FOUND or other processing error
    }
  });

  // --- Set Content Security Policy ---
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    // Define the CSP string
    // NOTE: 'unsafe-inline' for style-src is often needed for UI libraries, but try removing it if possible.
    // NOTE: connect-src needs the Vite dev server URL during development.
    // Define CSP parts conditionally
    const scriptSrc = !app.isPackaged ? "'self' 'unsafe-inline' https://cdn.gpteng.co" : "'self' https://cdn.gpteng.co"; // Allow inline scripts in dev + gpteng script always
    const connectSrc = !app.isPackaged ? "'self' http://localhost:8080 ws://localhost:8080" : "'self'"; // Allow Vite HMR in dev

    const csp = [
      "default-src 'self'",
      `script-src ${scriptSrc}`, // Use the conditional script-src
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com", // Allow Google Fonts stylesheets
      "font-src 'self' https://fonts.gstatic.com", // Allow Google Fonts font files
      "img-src 'self' data: safe-file:", // Allows images from origin, data URLs, and safe-file: protocol
      `connect-src ${connectSrc}`, // Use the conditional connect-src
    ].join('; ');

    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [csp]
      }
    });
  });
  // --- End CSP Setup ---

  let currentProjectPath = null; // Variable to store the active project directory path
  let currentProjectFilePath = null; // Variable to store the full path to the active .bp file
  // These Maps are now declared at the top level (fileWatchers, debounceTimers)

  // Set up IPC handler for opening the image dialog
  // --- Image Processing Logic ---
  const THUMBNAIL_SIZE = 200; // px for list thumbnails
  const PREVIEW_SIZE = 2000; // px for canvas previews (long edge)
  const THUMBNAIL_CACHE_DIR = path.join(app.getPath('userData'), 'thumbnail_cache');
  const PREVIEW_CACHE_DIR = path.join(app.getPath('userData'), 'preview_cache'); // New cache dir

  // Ensure cache directories exist
  if (!fs.existsSync(THUMBNAIL_CACHE_DIR)) {
    fs.mkdirSync(THUMBNAIL_CACHE_DIR, { recursive: true });
  }
  if (!fs.existsSync(PREVIEW_CACHE_DIR)) { // Ensure preview cache dir exists
    fs.mkdirSync(PREVIEW_CACHE_DIR, { recursive: true });
  }

  // IPC Handler for Renderer to set the project path (e.g., after creating new or loading existing)
  ipcMain.handle('project:set-directory', (event, directoryPath) => {
    if (directoryPath && typeof directoryPath === 'string') {
      // Ensure it's a valid directory before setting
      try {
        const stats = fs.statSync(directoryPath);
        if (stats.isDirectory()) {
          currentProjectPath = directoryPath; // Store the actual directory path
          console.log(`[Main Process] Project directory set to: ${currentProjectPath}`);
          return { success: true, path: currentProjectPath };
        } else {
           console.error('[Main Process] Invalid path (not a directory) for project:set-directory:', directoryPath);
           currentProjectPath = null;
           return { success: false, error: 'Path is not a directory.' };
        }
      } catch (error) {
         console.error('[Main Process] Error accessing path for project:set-directory:', directoryPath, error);
         currentProjectPath = null;
         return { success: false, error: `Failed to access path: ${error.message}` };
      }
    } else {
      console.error('[Main Process] Invalid path type received for project:set-directory:', directoryPath);
      currentProjectPath = null; // Reset if invalid path received
      return { success: false, error: 'Invalid directory path provided (must be a string).' };
    }
  });

  async function generateThumbnail(originalPath) {
    try {
      // Create a unique filename based on hash of original path to avoid collisions
      const hash = crypto.createHash('sha256').update(originalPath).digest('hex');
      const thumbnailFilename = `${hash}.webp`;

      if (!currentProjectPath) {
        console.error('Error: Project directory not set. Cannot generate thumbnail.');
        return null; // Or throw an error
      }
      const projectThumbnailCacheDir = path.join(currentProjectPath, 'thumbnail_cache');
      if (!fs.existsSync(projectThumbnailCacheDir)) {
        fs.mkdirSync(projectThumbnailCacheDir, { recursive: true });
      }
      const thumbnailPath = path.join(projectThumbnailCacheDir, thumbnailFilename);

      // Check if thumbnail already exists
      if (!fs.existsSync(thumbnailPath)) {
        console.log(`Generating thumbnail for: ${originalPath}`);
        await sharp(originalPath)
          .resize(THUMBNAIL_SIZE, THUMBNAIL_SIZE, {
            fit: 'inside', // Preserve aspect ratio, fit within bounds
            withoutEnlargement: true, // Don't enlarge small images
          })
          .webp({ quality: 80 }) // Adjust quality as needed
          .toFile(thumbnailPath);
      } else {
         // console.log(`Using cached thumbnail for: ${originalPath}`);
      }
      // Return the path using the custom protocol
      return `safe-file://${thumbnailPath}`;
    } catch (error) {
      console.error(`Failed to generate thumbnail for ${originalPath}:`, error);
      return null; // Indicate failure for this specific image
    }
  }

  // Generate preview into the project-specific preview_cache directory
  async function generatePreview(originalPath) {
    try {
      if (!currentProjectPath) {
        console.error('[Main Process] Error: Project directory not set. Cannot generate project-specific preview.');
        return null;
      }
      // Create a unique filename based on hash of original path to avoid collisions
      const hash = crypto.createHash('sha256').update(originalPath).digest('hex');
      const previewFilename = `${hash}.webp`;

      const projectPreviewCacheDir = path.join(currentProjectPath, 'preview_cache');
      if (!fs.existsSync(projectPreviewCacheDir)) {
        fs.mkdirSync(projectPreviewCacheDir, { recursive: true });
      }
      const previewPath = path.join(projectPreviewCacheDir, previewFilename);

      // Check if preview already exists
      if (!fs.existsSync(previewPath)) {
        console.log(`[Main Process] Generating project-specific preview for: ${path.basename(originalPath)} into ${projectPreviewCacheDir}`);
        await sharp(originalPath)
          .resize(PREVIEW_SIZE, PREVIEW_SIZE, {
            fit: 'inside', // Preserve aspect ratio
            withoutEnlargement: true, // Don't enlarge small images
          })
          .webp({ quality: 85 }) // Slightly higher quality for previews
          .toFile(previewPath);

        // Add extra check after writing
        if (!fs.existsSync(previewPath)) {
          console.error(`[Main Process] Failed to verify preview file existence after write: ${previewPath}`);
          return null; // Indicate failure
        }
      } else {
        // console.log(`Using cached project-specific preview for: ${originalPath}`);
      }
      return `safe-file://${previewPath}`;
    } catch (error) {
      console.error(`Failed to generate project-specific preview for ${originalPath}:`, error);
      return null;
    }
  }

  // --------------------------------
  // --- Preview Generation Logic ---
  // --------------------------------

  // --------------------------------

  ipcMain.handle('dialog:openImage', async (event, args) => { // Added args parameter
    console.log('[Main Process] dialog:openImage invoked. Received args:', JSON.stringify(args)); // Log received args
    const previewGenerationMode = args?.previewGenerationMode || 'onImport'; // Default to 'onImport'
    const generatePreviewsOption = args?.generatePreviews !== undefined ? args.generatePreviews : true; // Default to true if not specified
    console.log(`[Main Process] Determined previewGenerationMode: ${previewGenerationMode}, generatePreviewsOption: ${generatePreviewsOption}`);

    const { canceled, filePaths } = await dialog.showOpenDialog({
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'webp', 'heic', 'avif'] } // Added more types
      ]
    });

    if (canceled || !filePaths || filePaths.length === 0) {
      console.log('[Main Process] Image dialog canceled or no files selected.');
      return [];
    }

    console.log(`[Main Process] Selected file paths: ${JSON.stringify(filePaths)}`); // Log selected paths
    // Send initial progress update
    if (event.sender && !event.sender.isDestroyed()) {
      // Initial message before loop
      event.sender.send('import-progress', { stage: 'starting_import', current: 0, total: filePaths.length });
    }

    // Calculate total operations (1 operation per image - combined metadata and thumbnail)
    const totalOperations = filePaths.length;
    let completedOperations = 0;

    // Process images - read metadata sequentially first (fast operation)
    const imageDataWithMetadata = []; // Array to hold images with metadata
    for (let index = 0; index < filePaths.length; index++) {
      const originalPath = filePaths[index];
      try {
        // --- Metadata Reading ---
        // We'll update progress in the UI with a single 'importing' stage
        if (event.sender && !event.sender.isDestroyed()) {
          event.sender.send('import-progress', { 
            stage: 'importing', 
            current: completedOperations, 
            total: totalOperations, 
            filename: path.basename(originalPath) 
          });
        }
        const imageMetadata = await sharp(originalPath).metadata();
        const naturalWidth = imageMetadata.width;
        const naturalHeight = imageMetadata.height;

        if (!naturalWidth || !naturalHeight) {
          console.error(`[Main Process] Failed to get dimensions for ${path.basename(originalPath)}`);
          continue; // Skip this image if dimensions couldn't be read
        }

        // --- Rating Extraction (Part of Metadata) ---
        let rating = 0; // Default rating
        let dateTaken = null; // Initialize dateTaken
        try {
          const metadata = await exifr.parse(originalPath, {
            xmp: true,
            iptc: true,
            exif: true,
            icc: false,
          });
          rating = metadata?.Rating ?? 0;
          
          // Extract date taken from EXIF if available
          if (metadata?.DateTimeOriginal) {
            dateTaken = metadata.DateTimeOriginal.getTime();
          } else if (metadata?.DateTime) {
            dateTaken = new Date(metadata.DateTime).getTime();
          } else if (metadata?.CreateDate) {
            dateTaken = new Date(metadata.CreateDate).getTime();
          }
        } catch (parseError) {
          console.error(`[Main Process] Error parsing metadata for rating in ${path.basename(originalPath)}:`, parseError);
          // Keep rating as 0 if parsing fails, but don't skip the image
        }

        // Get file stats for dateModified
        let dateModified = null;
        try {
          const fileStats = await fsPromises.stat(originalPath);
          dateModified = fileStats.mtime.getTime();
        } catch (statsError) {
          console.error(`[Main Process] Error getting file stats for ${path.basename(originalPath)}:`, statsError);
        }

        // Store the metadata for parallel processing
        imageDataWithMetadata.push({
          originalPath,
          name: path.basename(originalPath),
          rating,
          naturalWidth,
          naturalHeight,
          dateTaken,
          dateModified,
          index // Keep track of original index for progress reporting
        });
      } catch (error) {
        console.error(`Failed processing metadata for ${originalPath}:`, error);
        // Send an error progress update for this specific file
        if (event.sender && !event.sender.isDestroyed()) {
          event.sender.send('import-progress', {
            stage: 'error',
            current: completedOperations,
            total: totalOperations,
            filename: path.basename(originalPath),
            error: `Failed to process: ${error.message}`
          });
        }
      }
    }

    // Process thumbnails in parallel with a concurrency limit
    const CONCURRENCY_LIMIT = 4; // Adjust based on system capabilities
    const results = [];
    
    // Process thumbnails in batches to control concurrency
    for (let i = 0; i < imageDataWithMetadata.length; i += CONCURRENCY_LIMIT) {
      const batch = imageDataWithMetadata.slice(i, i + CONCURRENCY_LIMIT);
      
      // Process this batch in parallel
      const batchResults = await Promise.all(batch.map(async (imageData) => {
        try {
          // Generate thumbnail
          const thumbnailUrl = await generateThumbnail(imageData.originalPath);
          
          // Update progress - increment completed operations
          completedOperations++;
          if (event.sender && !event.sender.isDestroyed()) {
            event.sender.send('import-progress', { 
              stage: 'importing', 
              current: completedOperations, 
              total: totalOperations,
              filename: imageData.name
            });
          }
          
          if (!thumbnailUrl) {
            console.log(`[Main Process] Thumbnail generation failed for ${imageData.name}`);
            return null; // Skip this image if thumbnail failed
          }
          
          // Return the complete image data with thumbnail
          return {
            originalPath: imageData.originalPath,
            thumbnailUrl: thumbnailUrl,
            name: imageData.name,
            rating: imageData.rating,
            naturalWidth: imageData.naturalWidth,
            naturalHeight: imageData.naturalHeight,
            dateTaken: imageData.dateTaken,
            dateModified: imageData.dateModified
          };
        } catch (error) {
          console.error(`Failed generating thumbnail for ${imageData.originalPath}:`, error);
          if (event.sender && !event.sender.isDestroyed()) {
            event.sender.send('import-progress', {
              stage: 'error',
              current: completedOperations,
              total: totalOperations,
              filename: imageData.name,
              error: `Failed to generate thumbnail: ${error.message}`
            });
          }
          return null; // Skip this image on error
        }
      }));
      
      // Add successful results to the final array
      results.push(...batchResults.filter(result => result !== null));
    }

    const initialResults = results; // Results are already filtered by the loop logic

    // --- Automatic background preview generation removed ---
    // Previews are now generated only on demand via 'image:requestPreviewGeneration'
    console.log('[Main Process] Automatic background preview generation during import is disabled.');
    if (event.sender && !event.sender.isDestroyed()) {
      // Send final completion message for import (thumbnails only)
      event.sender.send('import-progress', null);
    }
    // ---------------------------------------------------------

    return initialResults; // Return initial results (thumbnails, metadata)
  });

  // --- IPC Handler for Processing Dropped Files ---
  ipcMain.handle('dialog:processDroppedFiles', async (event, filePaths, options) => {
    console.log('[Main Process] dialog:processDroppedFiles invoked with paths:', filePaths, 'and options:', options);

    if (!currentProjectPath) {
      console.error('[Main Process] Cannot process dropped files: Project directory not set.');
      // Send an error progress update for the overall operation
      if (event.sender && !event.sender.isDestroyed()) {
        event.sender.send('dropped-files-progress', {
          stage: 'error',
          current: 0,
          total: filePaths?.length || 0,
          error: 'Project directory not set. Please save or load a project first.'
        });
      }
      return null; // Or an empty array, depending on how frontend handles it
    }

    if (!Array.isArray(filePaths) || filePaths.length === 0) {
      console.log('[Main Process] No valid file paths received for dropped files.');
      return [];
    }

    // Validate paths and filter for actual image files (basic check, more robust in loop)
    const validImageFilePaths = filePaths.filter(filePath => {
      if (typeof filePath !== 'string') {
        console.warn(`[Main Process] Invalid file path type received: ${typeof filePath}`);
        return false;
      }
      // Basic extension check - more robust checks (e.g., fs.access) will be done per file
      const lcPath = filePath.toLowerCase();
      const MimeTypes = ['.jpg', '.jpeg', '.png', '.webp', '.heic', '.avif', '.tiff', '.tif'];
      if (!MimeTypes.some(ext => lcPath.endsWith(ext))) {
        console.warn(`[Main Process] Dropped file is not a recognized image type: ${filePath}`);
        return false;
      }
      return true;
    });

    if (validImageFilePaths.length === 0) {
      console.log('[Main Process] No valid image files found in dropped items.');
      if (event.sender && !event.sender.isDestroyed()) {
        event.sender.send('dropped-files-progress', {
          stage: 'error',
          current: 0,
          total: filePaths.length,
          error: 'No valid image files found in the dropped items.'
        });
      }
      return [];
    }

    // Send initial progress update via 'dropped-files-progress'
    if (event.sender && !event.sender.isDestroyed()) {
      event.sender.send('dropped-files-progress', { stage: 'starting_import', current: 0, total: validImageFilePaths.length });
    }

    // Calculate total operations (1 operation per image - combined metadata and thumbnail)
    const totalOperations = validImageFilePaths.length;
    let completedOperations = 0;
    
    // Process images - read metadata sequentially first (fast operation)
    const imageDataWithMetadata = []; // Array to hold images with metadata
    for (let index = 0; index < validImageFilePaths.length; index++) {
      const originalPath = validImageFilePaths[index];
      const filename = path.basename(originalPath);

      try {
        // Security: Verify file existence and readability before processing
        await fsPromises.access(originalPath, fs.constants.R_OK);

        // --- Metadata Reading ---
        // Update progress - we'll count metadata reading as part of the overall progress
        // but we won't update the UI for each metadata read to avoid flickering
        const imageMetadata = await sharp(originalPath).metadata();
        const naturalWidth = imageMetadata.width;
        const naturalHeight = imageMetadata.height;

        if (!naturalWidth || !naturalHeight) {
          console.error(`[Main Process] Failed to get dimensions for ${filename}`);
          if (event.sender && !event.sender.isDestroyed()) {
            event.sender.send('dropped-files-progress', { 
              stage: 'error', 
              current: completedOperations, 
              total: totalOperations, 
              filename, 
              error: 'Could not read image dimensions.' 
            });
          }
          continue;
        }

        // --- Rating Extraction (Part of Metadata) ---
        let rating = 0;
        let dateTaken = null;
        try {
          const exif = await exifr.parse(originalPath, { xmp: true, iptc: true, exif: true, icc: false });
          rating = exif?.Rating ?? 0;
          
          // Extract date taken from EXIF if available
          if (exif?.DateTimeOriginal) {
            dateTaken = exif.DateTimeOriginal.getTime();
          } else if (exif?.DateTime) {
            dateTaken = new Date(exif.DateTime).getTime();
          } else if (exif?.CreateDate) {
            dateTaken = new Date(exif.CreateDate).getTime();
          }
        } catch (parseError) {
          console.error(`[Main Process] Error parsing metadata for rating in ${filename}:`, parseError);
        }
        
        // Get file stats for dateModified
        let dateModified = null;
        try {
          const fileStats = await fsPromises.stat(originalPath);
          dateModified = fileStats.mtime.getTime();
        } catch (statsError) {
          console.error(`[Main Process] Error getting file stats for ${filename}:`, statsError);
        }

        // Store the metadata for parallel processing
        imageDataWithMetadata.push({
          originalPath,
          name: filename,
          rating,
          naturalWidth,
          naturalHeight,
          dateTaken,
          dateModified,
          index // Keep track of original index for progress reporting
        });
      } catch (error) {
        console.error(`[Main Process] Failed processing metadata for ${originalPath}:`, error);
        if (event.sender && !event.sender.isDestroyed()) {
          event.sender.send('dropped-files-progress', {
            stage: 'error',
            current: completedOperations,
            total: totalOperations,
            filename,
            error: `Failed to process: ${error.message}`
          });
        }
      }
    }

    // Send initial progress update for thumbnail generation
    // but keep it as part of the same overall progress bar
    if (event.sender && !event.sender.isDestroyed()) {
      event.sender.send('dropped-files-progress', { 
        stage: 'importing', 
        current: completedOperations, 
        total: totalOperations
      });
    }

    // Process thumbnails in parallel with a concurrency limit
    const CONCURRENCY_LIMIT = 4; // Adjust based on system capabilities
    const results = [];
    
    // Process thumbnails in batches to control concurrency
    for (let i = 0; i < imageDataWithMetadata.length; i += CONCURRENCY_LIMIT) {
      const batch = imageDataWithMetadata.slice(i, i + CONCURRENCY_LIMIT);
      
      // Process this batch in parallel
      const batchResults = await Promise.all(batch.map(async (imageData) => {
        try {
          // Generate thumbnail
          const thumbnailUrl = await generateThumbnail(imageData.originalPath);
          
          // Update progress - increment completed operations
          completedOperations++;
          if (event.sender && !event.sender.isDestroyed()) {
            event.sender.send('dropped-files-progress', { 
              stage: 'importing', 
              current: completedOperations, 
              total: totalOperations,
              filename: imageData.name
            });
          }
          
          if (!thumbnailUrl) {
            console.log(`[Main Process] Thumbnail generation failed for ${imageData.name}`);
            if (event.sender && !event.sender.isDestroyed()) {
              event.sender.send('dropped-files-progress', { 
                stage: 'error', 
                current: completedOperations, 
                total: totalOperations, 
                filename: imageData.name, 
                error: 'Thumbnail generation failed.' 
              });
            }
            return null; // Skip this image if thumbnail failed
          }
          
          // Automatic preview generation is disabled
          let previewUrl = null;
          
          // Return the complete image data with thumbnail
          return {
            id: `img-drop-${Date.now()}-${imageData.index}`, // Unique ID for dropped images
            originalPath: imageData.originalPath,
            thumbnailUrl: thumbnailUrl,
            previewUrl: previewUrl, // Include preview URL (null)
            name: imageData.name,
            rating: imageData.rating,
            naturalWidth: imageData.naturalWidth,
            naturalHeight: imageData.naturalHeight,
            dateAdded: Date.now(), // Add dateAdded timestamp
            dateTaken: imageData.dateTaken,
            dateModified: imageData.dateModified
          };
        } catch (error) {
          console.error(`Failed generating thumbnail for ${imageData.originalPath}:`, error);
          if (event.sender && !event.sender.isDestroyed()) {
            event.sender.send('dropped-files-progress', {
              stage: 'error',
              current: completedOperations,
              total: totalOperations,
              filename: imageData.name,
              error: `Failed to generate thumbnail: ${error.message}`
            });
          }
          return null; // Skip this image on error
        }
      }));
      
      // Add successful results to the final array
      results.push(...batchResults.filter(result => result !== null));
    }

    // Send final completion signal (null) via 'dropped-files-progress'
    if (event.sender && !event.sender.isDestroyed()) {
      event.sender.send('dropped-files-progress', null);
    }

    console.log('[Main Process] Finished processing dropped files. Results:', results.length);
    return results; // Return the array of processed ImageFile objects
  });
  // --- End IPC Handler for Processing Dropped Files ---

  // Track active preview generation tasks to limit parallelism
  const activePreviewTasks = new Set();
  const maxConcurrentPreviews = 4; // Process up to 4 previews in parallel
  const previewQueue = []; // Queue for pending preview requests

  // Function to process the next item in the preview queue
  function processNextPreviewInQueue() {
    // If we're at max capacity or queue is empty, do nothing
    if (activePreviewTasks.size >= maxConcurrentPreviews || previewQueue.length === 0) {
      return;
    }

    // Get the next item from the queue
    const nextItem = previewQueue.shift();
    if (nextItem) {
      const { event, originalPath, resolve, reject } = nextItem;
      // Add to active tasks
      activePreviewTasks.add(originalPath);
      // Process it
      generatePreviewForPath(event, originalPath)
        .then(result => {
          // Remove from active tasks when done
          activePreviewTasks.delete(originalPath);
          // Resolve the promise
          resolve(result);
          // Process next item if available
          processNextPreviewInQueue();
        })
        .catch(error => {
          // Remove from active tasks on error
          activePreviewTasks.delete(originalPath);
          // Reject the promise
          reject(error);
          // Process next item if available
          processNextPreviewInQueue();
        });
    }
  }

  // Core function to generate a preview for a single path
  async function generatePreviewForPath(event, originalPath) {
    if (!originalPath) {
      console.error('[Main Process] Invalid originalPath received for preview generation:', originalPath);
      try {
        event.sender.send('preview-generated', { originalPath, previewUrl: null });
      } catch (error) {
        console.error('[Main Process] Error sending preview-generated event:', error);
      }
      return false;
    }

    console.log(`[Main Process] Processing preview generation for: ${path.basename(originalPath)}`);
    let previewUrl = null;

    try {
      const hash = crypto.createHash('md5').update(originalPath).digest('hex');
      const previewFilename = `${hash}.webp`;

      // Check if project directory is set
      if (!currentProjectPath) {
        console.error('[Main Process] Error: Project directory not set for preview. Cannot save to project-specific cache.');
        try {
          event.sender.send('preview-generated', { originalPath, previewUrl: null });
        } catch (error) {
          console.error('[Main Process] Error sending preview-generated event:', error);
        }
        return false;
      }

      const projectPreviewCacheDir = path.join(currentProjectPath, 'preview_cache');
      if (!fs.existsSync(projectPreviewCacheDir)) {
        fs.mkdirSync(projectPreviewCacheDir, { recursive: true });
      }
      const previewPath = path.join(projectPreviewCacheDir, previewFilename);

      // Send an immediate progress update that we've started processing this image
      try {
        event.sender.send('preview-processing', {
          originalPath,
          status: 'processing'
        });
      } catch (error) {
        console.error('[Main Process] Error sending preview-processing event:', error);
      }

      // Check if preview already exists
      if (!fs.existsSync(previewPath)) {
        console.log(`[Main Process] Generating preview for: ${path.basename(originalPath)}`);
        
        await sharp(originalPath)
          .resize(PREVIEW_SIZE, PREVIEW_SIZE, {
            fit: 'inside',
            withoutEnlargement: true
          })
          .webp({ quality: 85 }) // Slightly higher quality for previews
          .toFile(previewPath);

        // Verify the file was created
        if (!fs.existsSync(previewPath)) {
          console.error(`[Main Process] Failed to verify preview file existence after write: ${previewPath}`);
          throw new Error(`Failed to verify preview file existence: ${previewPath}`);
        }
      } else {
        console.log(`[Main Process] Using cached preview for: ${path.basename(originalPath)}`);
      }

      previewUrl = `safe-file://${previewPath}`;
      try {
        event.sender.send('preview-generated', {
          originalPath,
          previewUrl
        });
        console.log(`[Main Process] Sent preview-generated event for ${path.basename(originalPath)}`);
      } catch (error) {
        console.error('[Main Process] Error sending preview-generated event:', error);
      }
      return true;
    } catch (error) {
      console.error(`[Main Process] Error generating preview for ${originalPath}:`, error);
      try {
        event.sender.send('preview-generated', { originalPath, previewUrl: null });
      } catch (sendError) {
        console.error('[Main Process] Error sending preview-generated error event:', sendError);
      }
      return false;
    }
  }

  // --- IPC Handler for On-Demand Preview Generation with Queue ---
  ipcMain.handle('image:requestPreviewGeneration', async (event, originalPath) => {
    // Return a promise that will be resolved when the preview is generated
    return new Promise((resolve, reject) => {
      // Add this request to the queue
      previewQueue.push({ event, originalPath, resolve, reject });
      // Try to process the next item in the queue
      processNextPreviewInQueue();
    });
  });
  // ---------------------------------------------------------

  // IPC handler for "Save As..." - ALWAYS shows the dialog
  ipcMain.handle('dialog:saveProjectAs', async (event, data) => {
    const mainWindow = BrowserWindow.fromWebContents(event.sender);
    if (!mainWindow) return { success: false, error: 'No active window' };

    const sourceProjectDir = currentProjectPath; // Capture the source project directory

    // Step 1: First show a dialog to select or create a folder
    const { canceled: folderCanceled, filePaths: folderPaths } = await dialog.showOpenDialog(mainWindow, {
      title: 'Select or Create a New Folder for Your Project',
      properties: ['openDirectory', 'createDirectory'],
      buttonLabel: 'Select Folder'
    });

    if (folderCanceled || !folderPaths || folderPaths.length === 0) {
      return { success: false, canceled: true };
    }

    const selectedFolderPath = folderPaths[0];
    
    // Step 2: Check if the folder is empty (or newly created)
    try {
      const folderContents = fs.readdirSync(selectedFolderPath);
      if (folderContents.length > 0) {
        // Show warning dialog that folder must be empty
        const { response } = await dialog.showMessageBox(mainWindow, {
          type: 'warning',
          title: 'Non-Empty Folder Selected',
          message: 'The selected folder is not empty.',
          detail: 'Please create a new empty folder for your project files.',
          buttons: ['OK'],
          defaultId: 0
        });
        return { success: false, error: 'Folder must be empty. Please create a new folder.' };
      }
    } catch (error) {
      console.error('Failed to check folder contents:', error);
      return { success: false, error: `Failed to check folder: ${error.message}` };
    }

    // Step 3: Now show dialog to save the project file within the selected folder
    const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, {
      title: 'Save Project As',
      defaultPath: path.join(selectedFolderPath, currentProjectFilePath ? path.basename(currentProjectFilePath) : 'My Book Project.bp'),
      filters: [
        { name: 'Book Proofs Project', extensions: ['bp'] }
      ]
    });

    if (!canceled && filePath) {
      try {
        const jsonData = JSON.stringify(data, null, 2); // Pretty print JSON
        fs.writeFileSync(filePath, jsonData, 'utf-8');
        currentProjectPath = path.dirname(filePath); // Update directory path
        currentProjectFilePath = filePath; // Update the active file path
        startBackupTimer(); // Start backups after successful Save As
        console.log(`Project saved AS: ${filePath}. Project directory set to: ${currentProjectPath}`);

        // --- Copy Cache Folders ---
        if (sourceProjectDir && sourceProjectDir !== currentProjectPath) { // Only copy if source exists and is different
          const cacheFolderNames = ['thumbnail_cache', 'preview_cache'];
          for (const cacheFolderName of cacheFolderNames) {
            const sourceCachePath = path.join(sourceProjectDir, cacheFolderName);
            const destCachePath = path.join(currentProjectPath, cacheFolderName); // currentProjectPath is now the new path

            try {
              if (fs.existsSync(sourceCachePath)) {
                console.log(`[Main Process] Copying ${cacheFolderName} from ${sourceCachePath} to ${destCachePath}`);
                await fsPromises.cp(sourceCachePath, destCachePath, { recursive: true });
                console.log(`[Main Process] Successfully copied ${cacheFolderName} to new project directory.`);
              } else {
                console.log(`[Main Process] Source cache folder ${sourceCachePath} not found, skipping copy.`);
              }
            } catch (copyError) {
              console.error(`[Main Process] Failed to copy ${cacheFolderName} from ${sourceCachePath} to ${destCachePath}:`, copyError);
              // Non-fatal error, project is saved, but caches aren't copied.
            }
          }
        } else {
          console.log('[Main Process] Skipping cache copy: No valid source project directory or source is same as destination.');
        }
        // --- End Copy Cache Folders ---

        // Return the new file path so the renderer can update its state
        return { success: true, filePath: filePath };
      } catch (error) {
        console.error('Failed to save project (Save As):', error);
        return { success: false, error: error.message };
      }
    }
    // Return success: false if dialog was canceled, but include canceled flag
    return { success: false, canceled: true };
  });

  // IPC handler for "Save" - overwrites the current file if it exists
  ipcMain.handle('project:saveFile', async (event, data) => {
    if (!currentProjectFilePath) {
      console.error('[project:saveFile] Attempted to save without an active file path.');
      // Indicate that "Save As" is needed
      return { success: false, error: 'No active file path. Use Save As.', needsSaveAs: true };
    }

    try {
      const jsonData = JSON.stringify(data, null, 2);
      fs.writeFileSync(currentProjectFilePath, jsonData, 'utf-8');
      console.log(`Project saved (overwrite): ${currentProjectFilePath}`);
      return { success: true, filePath: currentProjectFilePath }; // Return path for consistency
    } catch (error) {
      console.error(`Failed to save project (overwrite) to ${currentProjectFilePath}:`, error);
      return { success: false, error: error.message };
    }
  });

  // --- New Dialog Handlers for StartPage ---

  // Handler to CREATE a NEW project file (.bookproof)
  ipcMain.handle('dialog:create-new-project-file', async () => {
    const mainWindow = BrowserWindow.getFocusedWindow();
    if (!mainWindow) return { success: false, error: 'No active window' };

    // Step 1: First show a dialog to select or create a folder
    const { canceled: folderCanceled, filePaths: folderPaths } = await dialog.showOpenDialog(mainWindow, {
      title: 'Select or Create a New Folder for Your Project',
      properties: ['openDirectory', 'createDirectory'],
      buttonLabel: 'Select Folder'
    });

    if (folderCanceled || !folderPaths || folderPaths.length === 0) {
      return { success: false, canceled: true };
    }

    const selectedFolderPath = folderPaths[0];
    
    // Step 2: Check if the folder is empty (or newly created)
    try {
      const folderContents = fs.readdirSync(selectedFolderPath);
      if (folderContents.length > 0) {
        // Show warning dialog that folder must be empty
        const { response } = await dialog.showMessageBox(mainWindow, {
          type: 'warning',
          title: 'Non-Empty Folder Selected',
          message: 'The selected folder is not empty.',
          detail: 'Please create a new empty folder for your project files.',
          buttons: ['OK'],
          defaultId: 0
        });
        return { success: false, error: 'Folder must be empty. Please create a new folder.' };
      }
    } catch (error) {
      console.error('Failed to check folder contents:', error);
      return { success: false, error: `Failed to check folder: ${error.message}` };
    }

    // Step 3: Now show dialog to save the project file within the selected folder
    // Use the folder name as the default project name
    const folderName = path.basename(selectedFolderPath);
    const defaultProjectName = folderName || 'Untitled Book';
    
    const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, {
      title: 'Create New Project File',
      defaultPath: path.join(selectedFolderPath, `${defaultProjectName}.bp`), // Use folder name as default
      filters: [{ name: 'Book Proofs Project', extensions: ['bp'] }]
    });

    if (canceled || !filePath) {
      return { success: false, canceled: true };
    }

    try {
      // Create a minimal initial project file (can be empty or have basic structure)
      const initialData = JSON.stringify({
        // Add any essential starting fields if needed, otherwise empty is fine
        // aspectRatio: null, // Will be set in setup step
        images: [],
        spreads: [],
        imageGap: 2, // Default gap
      }, null, 2);
      fs.writeFileSync(filePath, initialData, 'utf-8');

      // Set the project path internally
      currentProjectPath = path.dirname(filePath);
      currentProjectFilePath = filePath; // Store the full file path
      console.log(`[Main Process] New project file created: ${filePath}. Project directory set to: ${currentProjectPath}. Active file path set.`);
      startBackupTimer(); // Start backups after creating a new project

      // Return success, the full file path, and the directory path
      return { success: true, filePath: filePath, projectDirectoryPath: currentProjectPath };

    } catch (error) {
      console.error('Failed to create initial project file:', error);
      currentProjectPath = null; // Reset path on failure
      return { success: false, error: `Failed to create project file: ${error.message}` };
    }
  });

  // Handler to select an EXISTING project file (.bookproof)
  ipcMain.handle('dialog:open-project-file', async () => {
    const mainWindow = BrowserWindow.getFocusedWindow();
    if (!mainWindow) return { success: false, error: 'No active window' };

    const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
      title: 'Open Project File',
      properties: ['openFile'],
      filters: [{ name: 'Book Proofs Project', extensions: ['bp'] }]
    });

    if (canceled || !filePaths || filePaths.length === 0) {
      return { success: false, canceled: true };
    }

    const filePath = filePaths[0];
    // Don't load data or set currentProjectPath here. Renderer will call loadProject with this path.
    console.log(`[Main Process] Existing project file selected by user: ${filePath}`);
    return { success: true, filePath: filePath };
  });

  // --- End New Dialog Handlers ---

  // --- File Availability Checking ---
  // Handler for selecting an individual file
  ipcMain.handle('dialog:select-file', async (event, options) => {
    try {
      const { canceled, filePaths } = await dialog.showOpenDialog({
        properties: ['openFile'],
        filters: [{ name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'tif', 'tiff'] }],
        ...options
      });
      
      return canceled ? null : filePaths[0];
    } catch (error) {
      console.error('[Main Process] Error selecting file:', error);
      return null;
    }
  });

  // Handler for regenerating thumbnails and previews for a file
  ipcMain.handle('image:regenerate-thumbnails', async (event, filePath) => {
    console.log(`[Main Process] Regenerating thumbnails and previews for: ${filePath}`);
    
    try {
      // Verify the file exists
      await fsPromises.access(filePath, fs.constants.R_OK);
      
      // Generate thumbnails and previews using the same logic as in the openImageDialog handler
      const fileName = path.basename(filePath);
      const fileStats = await fsPromises.stat(filePath);
      
      // Create thumbnails directory if it doesn't exist (THUMBNAILS REMAIN GLOBAL as per request)
      const thumbnailCacheDir = path.join(app.getPath('userData'), 'thumbnail_cache');
      await fsPromises.mkdir(thumbnailCacheDir, { recursive: true });
      
      // Generate a hash of the file path to use as the cache filename
      const filePathHash = crypto.createHash('sha256').update(filePath).digest('hex');
      const thumbnailPath = path.join(thumbnailCacheDir, `${filePathHash}.webp`);
      
      // Generate thumbnail (remains global)
      await sharp(filePath)
        .resize(300, 300, { fit: 'inside' })
        .webp({ quality: 80 })
        .toFile(thumbnailPath);
      const thumbnailUrl = `safe-file://${thumbnailPath}`; // Thumbnail URL (global)

      let previewUrl = null; // Initialize previewUrl for project-specific preview
      if (currentProjectPath) {
        const projectPreviewCacheDir = path.join(currentProjectPath, 'preview_cache');
        await fsPromises.mkdir(projectPreviewCacheDir, { recursive: true });
        const previewPath = path.join(projectPreviewCacheDir, `${filePathHash}.webp`);
        
        // Generate preview (larger version for image tray) using PREVIEW_SIZE into project cache
        await sharp(filePath)
          .resize(PREVIEW_SIZE, PREVIEW_SIZE, { fit: 'inside' }) // Use PREVIEW_SIZE constant
          .webp({ quality: 85 })
          .toFile(previewPath);
        previewUrl = `safe-file://${previewPath}`; // Preview URL (project-specific)
      } else {
        console.warn(`[Main Process] currentProjectPath not set, skipping project-specific preview generation in image:regenerate-thumbnails for ${filePath}.`);
      }
      
      // Get image dimensions
      const metadata = await sharp(filePath).metadata();
      
      // Get EXIF data if available
      let dateTaken = null;
      try {
        const exifData = await exifr.parse(filePath, ['DateTimeOriginal']);
        if (exifData && exifData.DateTimeOriginal) {
          dateTaken = exifData.DateTimeOriginal.getTime();
        }
      } catch (exifError) {
        console.warn(`[Main Process] Error reading EXIF data: ${exifError.message}`);
      }
      
      // Create safe file URLs (thumbnailUrl is global, previewUrl is project-specific or null)
      
      // <<< ADDED: Send preview-generated event back to renderer >>>
      if (event.sender && !event.sender.isDestroyed()) {
        event.sender.send('preview-generated', {
          originalPath: filePath,
          previewUrl: previewUrl // Send the generated preview URL
        });
        console.log(`[Main Process] Sent preview-generated event for regenerated file: ${filePath}`);
      }
      // <<< END ADDED >>>
      
      return {
        originalPath: filePath,
        thumbnailUrl,
        previewUrl,
        name: fileName,
        dateAdded: Date.now(),
        dateTaken,
        dateModified: fileStats.mtime.getTime(),
        naturalWidth: metadata.width,
        naturalHeight: metadata.height
      };
    } catch (error) {
      console.error(`[Main Process] Error regenerating thumbnails: ${error.message}`);
      // <<< ADDED: Send preview-generated event on error >>>
      if (event.sender && !event.sender.isDestroyed()) {
        event.sender.send('preview-generated', {
          originalPath: filePath,
          previewUrl: null // Indicate failure
        });
      }
      // <<< END ADDED >>>
      return null;
    }
  });
  
  // Handler for updating an individual file path
  ipcMain.handle('file:update-individual-path', async (event, originalPath, newPath) => {
    console.log(`[Main Process] Updating individual file path:
  FROM: ${originalPath}
  TO: ${newPath}`);
    
    if (!originalPath || !newPath) {
      console.error(`[Main Process] Invalid paths provided: originalPath=${originalPath}, newPath=${newPath}`);
      return { originalPath, newPath: null };
    }
    
    // Check if the file extension is the same
    const originalExt = path.extname(originalPath).toLowerCase();
    const newExt = path.extname(newPath).toLowerCase();
    if (originalExt !== newExt) {
      console.warn(`[Main Process] File extension changed from ${originalExt} to ${newExt}`);
    }
    
    try {
      // Verify the new path exists
      await fsPromises.access(newPath, fs.constants.R_OK);
      console.log(`[Main Process] File exists and is readable: ${newPath}`);
      
      // Get file stats to verify it's a file (not a directory)
      const stats = await fsPromises.stat(newPath);
      if (!stats.isFile()) {
        console.error(`[Main Process] Path is not a file: ${newPath}`);
        return { originalPath, newPath: null };
      }
      
      // Read a small part of the file to verify it's accessible
      try {
        const fileHandle = await fsPromises.open(newPath, 'r');
        const buffer = Buffer.alloc(12);
        await fileHandle.read(buffer, 0, 12, 0);
        await fileHandle.close();
        
        console.log(`[Main Process] Successfully verified file access for ${newPath}`);
        
        // Generate thumbnails directly here to ensure they're ready
        try {
          // Generate thumbnails and previews using the same logic as in the regenerate-thumbnails handler
          const fileName = path.basename(newPath);
          const fileStats = await fsPromises.stat(newPath);
          
          // Create thumbnails directory if it doesn't exist
          const thumbnailCacheDir = path.join(app.getPath('userData'), 'thumbnail_cache');
          const previewCacheDir = path.join(app.getPath('userData'), 'preview_cache');
          await fsPromises.mkdir(thumbnailCacheDir, { recursive: true });
          await fsPromises.mkdir(previewCacheDir, { recursive: true });
          
          // Generate a hash of the file path to use as the cache filename
          const filePathHash = crypto.createHash('sha256').update(newPath).digest('hex');
          const thumbnailPath = path.join(thumbnailCacheDir, `${filePathHash}.webp`);
          const previewPath = path.join(previewCacheDir, `${filePathHash}.webp`);
          
          // Generate thumbnail
          await sharp(newPath)
            .resize(300, 300, { fit: 'inside' })
            .webp({ quality: 80 })
            .toFile(thumbnailPath);
          
          // Generate preview (larger version for image tray) using PREVIEW_SIZE
          await sharp(newPath)
            .resize(PREVIEW_SIZE, PREVIEW_SIZE, { fit: 'inside' }) // Use PREVIEW_SIZE constant
            .webp({ quality: 85 })
            .toFile(previewPath);
          
          console.log(`[Main Process] Successfully generated thumbnails for ${newPath}`);
        } catch (thumbnailError) {
          console.error(`[Main Process] Error generating thumbnails: ${thumbnailError.message}`);
          // Continue even if thumbnail generation fails
        }
        
        console.log(`[Main Process] Returning updated path mapping: ${originalPath} -> ${newPath}`);
        return { originalPath, newPath };
      } catch (readError) {
        console.error(`[Main Process] Error reading file ${newPath}:`, readError);
        return { originalPath, newPath: null };
      }
    } catch (error) {
      console.error(`[Main Process] Error accessing new path ${newPath}:`, error);
      return { originalPath, newPath: null };
    }
  });
  
  // Handler for checking if files exist
  ipcMain.handle('file:check-exists', async (event, filePaths, isSilentCheck = false) => {
    // Only log on explicit checks, not periodic silent checks
    if (!isSilentCheck) {
      console.log(`[Main Process] Checking existence of ${filePaths.length} files`);
    }
    
    if (!Array.isArray(filePaths)) {
      console.error('[Main Process] file:check-exists received non-array input');
      return [];
    }
    
    // Filter out any empty paths or invalid paths
    const validPaths = filePaths.filter(path => {
      if (!path || typeof path !== 'string') {
        console.error(`[Main Process] Invalid path in check-exists: ${path}`);
        return false;
      }
      return true;
    });
    
    if (!isSilentCheck) {
      console.log(`[Main Process] Checking ${validPaths.length} valid paths`);
    }
    
    try {
      // Batch file checks to reduce overhead
      // Process files in batches of 10 to avoid overwhelming the file system
      const batchSize = 10;
      const results = [];
      
      // Create a results array matching the original filePaths length
      // This ensures the results align with the input paths
      const fullResults = new Array(filePaths.length).fill(false);
      
      for (let i = 0; i < validPaths.length; i += batchSize) {
        const batch = validPaths.slice(i, i + batchSize);
        const batchResults = await Promise.all(batch.map(async (filePath) => {
          try {
            await fsPromises.access(filePath, fs.constants.R_OK);
            return { path: filePath, exists: true }; // File exists and is readable
          } catch (error) {
            // Only log inaccessible files for non-silent checks
            if (!isSilentCheck) {
              console.log(`[Main Process] File not accessible: ${filePath}`);
            }
            return { path: filePath, exists: false }; // File doesn't exist or isn't readable
          }
        }));
        
        // Add batch results to the results array
        results.push(...batchResults);
      }
      
      // Map the results back to the original file paths
      filePaths.forEach((path, index) => {
        if (!path) return; // Skip invalid paths
        
        // Find the corresponding result for this path
        const resultEntry = results.find(r => r.path === path);
        if (resultEntry) {
          fullResults[index] = resultEntry.exists;
        }
      });
      
      if (!isSilentCheck) {
        console.log(`[Main Process] File check results: ${fullResults.filter(r => r).length} of ${fullResults.length} files exist`);
      }
      
      return fullResults;
    } catch (error) {
      console.error('[Main Process] Error checking file existence:', error);
      return filePaths.map(() => false); // Return all false on error
    }
  });
  
  // Handler for updating file paths with a new base directory
  ipcMain.handle('file:update-paths', async (event, originalPaths, newBasePath) => {
    console.log(`[Main Process] Updating paths with new base: ${newBasePath}`);
    
    if (!Array.isArray(originalPaths) || !newBasePath) {
      console.error('[Main Process] Invalid parameters for file:update-paths');
      return [];
    }
    
    try {
      // Extract common base path from original paths
      const results = await Promise.all(originalPaths.map(async (originalPath) => {
        // Get the filename and any subdirectories from the original path
        const fileName = path.basename(originalPath);
        
        // Try different possible locations for the file
        const possiblePaths = [
          // Direct match with same filename in new base directory
          path.join(newBasePath, fileName),
          // Try to find in subdirectories (up to 2 levels deep)
          ...await findFileInSubdirectories(newBasePath, fileName, 2)
        ];
        
        // Check if any of the possible paths exist
        for (const possiblePath of possiblePaths) {
          try {
            await fsPromises.access(possiblePath, fs.constants.R_OK);
            console.log(`[Main Process] Found file at new location: ${possiblePath}`);
            return {
              originalPath,
              newPath: possiblePath
            };
          } catch (error) {
            // File not found at this location, continue checking
          }
        }
        
        // If we get here, file wasn't found in any of the possible locations
        console.log(`[Main Process] Could not find file in new location: ${fileName}`);
        return {
          originalPath,
          newPath: null
        };
      }));
      
      return results;
    } catch (error) {
      console.error('[Main Process] Error updating file paths:', error);
      return originalPaths.map(originalPath => ({
        originalPath,
        newPath: null
      }));
    }
  });
  
  // Helper function to find a file in subdirectories (recursive)
  async function findFileInSubdirectories(baseDir, fileName, maxDepth = 2, currentDepth = 0) {
    if (currentDepth >= maxDepth) {
      return [];
    }
    
    try {
      const entries = await fsPromises.readdir(baseDir, { withFileTypes: true });
      let results = [];
      
      for (const entry of entries) {
        const fullPath = path.join(baseDir, entry.name);
        
        if (entry.isDirectory()) {
          // Recursively search subdirectories
          const subResults = await findFileInSubdirectories(
            fullPath, 
            fileName, 
            maxDepth, 
            currentDepth + 1
          );
          results = results.concat(subResults);
        } else if (entry.name === fileName) {
          // Found a matching file
          results.push(fullPath);
        }
      }
      
      return results;
    } catch (error) {
      console.error(`[Main Process] Error reading directory ${baseDir}:`, error);
      return [];
    }
  }
  
  // Handler for selecting a directory
  ipcMain.handle('dialog:select-directory', async (event, options = {}) => {
    console.log('[Main Process] Opening directory selection dialog');
    
    try {
      const { canceled, filePaths } = await dialog.showOpenDialog({
        title: options.title || 'Select Directory',
        defaultPath: options.defaultPath || app.getPath('documents'),
        properties: ['openDirectory']
      });
      
      if (canceled || filePaths.length === 0) {
        console.log('[Main Process] Directory selection canceled');
        return null;
      }
      
      console.log(`[Main Process] Selected directory: ${filePaths[0]}`);
      return filePaths[0];
    } catch (error) {
      console.error('[Main Process] Error selecting directory:', error);
      return null;
    }
  });
  // --- End File Availability Checking ---

  // IPC handler for loading the project
  // IPC handler for loading the project DATA (requires filePath)
  ipcMain.handle('dialog:loadProject', async (event, filePath) => { // filePath argument is required
    // Removed internal dialog.showOpenDialog call

    if (!filePath || typeof filePath !== 'string') {
        console.error('[Main Process] loadProject called without a valid filePath.');
        return { success: false, error: true, message: 'No project file path provided.' };
    }

    // Proceed with loading using the provided filePath
    try {
        const jsonData = fs.readFileSync(filePath, 'utf-8');
        const data = JSON.parse(jsonData);
        currentProjectPath = path.dirname(filePath); // Set project directory path
        currentProjectFilePath = filePath; // Set the active file path
        console.log(`Project loaded from: ${filePath}. Project directory set to: ${currentProjectPath}. Active file path set.`);
        startBackupTimer(); // Start backups after loading a project
        // Return data, file path, and the derived directory path
        return { success: true, data: data, filePath: filePath, projectDirectoryPath: currentProjectPath };
      } catch (error) {
        console.error('Failed to load or parse project:', error);
        // Return a structured error object
        currentProjectPath = null;
        return { success: false, error: true, message: error.message };
      }
    // Catch block remains the same (handles read/parse errors)
  }); // End of dialog:loadProject

  // IPC handler to get image data as Data URL for preview modal
  ipcMain.handle('image:get-data-url', async (event, filePath) => {
    console.log(`[Main Process] Received request for data URL: ${filePath}`); // Log request
    if (!filePath || typeof filePath !== 'string') {
      return { success: false, error: 'Invalid file path provided.' };
    }

    try {
      // Basic Security Check: Ensure file exists and is readable before proceeding.
      // WARNING: In a real application, add more robust checks here to ensure
      // only expected/allowed files are accessed (e.g., check if path is within
      // the project directory or known cache locations). Accessing arbitrary paths
      // passed from the renderer can be a security risk.
      await fsPromises.access(filePath, fs.constants.R_OK);

      const fileBuffer = await fsPromises.readFile(filePath);
      const base64Data = fileBuffer.toString('base64');

      // Determine MIME type from file extension (simple approach)
      const ext = path.extname(filePath).toLowerCase();
      let mimeType = 'application/octet-stream'; // Default
      if (ext === '.jpg' || ext === '.jpeg') {
        mimeType = 'image/jpeg';
      } else if (ext === '.png') {
        mimeType = 'image/png';
      } else if (ext === '.webp') {
        mimeType = 'image/webp';
      } else if (ext === '.gif') {
        mimeType = 'image/gif';
      } else if (ext === '.heic') {
        mimeType = 'image/heic'; // Common MIME type for HEIC
      } else if (ext === '.avif') {
         mimeType = 'image/avif';
      }
      // Add more types as needed

      const dataUrl = `data:${mimeType};base64,${base64Data}`;
      const filename = path.basename(filePath); // Extract filename
      console.log(`[Main Process] Successfully generated data URL for: ${filePath} (MIME: ${mimeType})`); // Log success
      return { success: true, dataUrl: dataUrl, filename: filename }; // Add filename

    } catch (error) {
      console.error(`[Main Process] Failed to get data URL for ${filePath}:`, error);
      // Check for specific file not found error
      if (error.code === 'ENOENT') {
         return { success: false, error: `File not found: ${filePath}` };
      }
      return { success: false, error: `Failed to read file or generate data URL: ${error.message}` };
    }
  });

  // Removed incorrect project:request-location handler

  // IPC handler for confirming image reuse
  ipcMain.handle('dialog:confirmImageReuse', async (event, imageName) => {
    const mainWindow = BrowserWindow.getFocusedWindow(); // Get the window to make dialog modal
    if (!mainWindow) return false; // Should not happen if called from renderer

    const { response } = await dialog.showMessageBox(mainWindow, { // Make modal to window
      type: 'question',
      buttons: ['Cancel', 'Use Image'],
      defaultId: 1, // Index of the default button ('Use Image')
      title: 'Confirm Image Reuse',
      message: `The image "${imageName}" has already been used in this project.`,
      detail: 'Are you sure you want to use it again?',
      cancelId: 0 // Index of the cancel button
    });
    return response === 1; // Return true if 'Use Image' was clicked
  });

  // NEW: IPC handler for generic confirmation dialog
  ipcMain.handle('dialog:confirm', async (event, options) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (!window) {
      console.error("Could not find browser window for confirm dialog.");
      return options.cancelId ?? 0; // Return cancel index if window not found
    }
    try {
      const { response } = await dialog.showMessageBox(window, {
        type: options.type ?? 'question', // Default to question type
        buttons: options.buttons ?? ['Cancel', 'OK'], // Default buttons
        title: options.title ?? 'Confirm', // Default title
        message: options.message, // Required
        detail: options.detail,
        defaultId: options.defaultId,
        cancelId: options.cancelId,
        noLink: true // Often desirable for confirmation dialogs
      });
      return response; // Return the index of the clicked button
    } catch (error) {
      console.error("Error showing confirmation dialog:", error);
      return options.cancelId ?? 0; // Return cancel index on error
    }
  });

  // --- NEW: IPC Handler for Confirming Excess Image Drop ---
  ipcMain.handle('dialog:confirmExcessImages', async (event, droppedCount, availableSlots, templateCapacity) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (!window) {
      console.error("Could not find browser window for confirmExcessImagesDialog.");
      return 'error_or_closed'; // Indicate error or closure
    }

    const message = `You dropped ${droppedCount} images, but the current template only has ${availableSlots > 0 ? availableSlots : 'no'} available slot(s).`;
    const detail = `The template has a total capacity of ${templateCapacity} images.\n\nChoose 'Auto-build' to automatically create new spreads for all dropped images, or 'Cancel' to stop.`;

    try {
      const { response } = await dialog.showMessageBox(window, {
        type: 'question',
        buttons: ['Cancel', 'Auto-build'], // Index 0: Cancel, Index 1: Auto-build
        defaultId: 1, // Default to 'Auto-build'
        cancelId: 0, // 'Cancel' button index
        title: 'Too Many Images Dropped',
        message: message,
        detail: detail,
        noLink: true
      });

      if (response === 1) {
        return 'autobuild';
      } else { // response === 0 (Cancel) or dialog closed
        return 'cancel';
      }
    } catch (error) {
      console.error("Error showing confirmExcessImagesDialog:", error);
      return 'error_or_closed'; // Indicate error
    }
  });
  // --- END NEW IPC Handler ---


  // --- Backup Logic ---

  // IPC Handler for main process to request current state from renderer
  // THIS IS NOT USED DIRECTLY BY PRELOAD. Main process sends 'request-project-state-for-backup'
  // and listens for 'project-state-response'.
  // ipcMain.handle('app:get-project-state', async (event) => { ... }); // Removed flawed handler


  async function rotateBackups() {
    if (!currentProjectPath || !currentProjectFilePath) return; // Need project path and file path for prefix

    const backupDir = path.join(currentProjectPath, BACKUP_FOLDER_NAME);
    // Use currentProjectFilePath to get the base name for the prefix
    const backupFilePrefix = path.basename(currentProjectFilePath).replace(/\.bp$/, '') + '.backup.';
    const backupFileSuffix = '.bp';

    try {
      if (!fs.existsSync(backupDir)) return; // No directory, nothing to rotate

      const allFiles = await fsPromises.readdir(backupDir);
      const backupFiles = allFiles
        .filter(file => file.startsWith(backupFilePrefix) && file.endsWith(backupFileSuffix))
        .map(file => {
          // Extract timestamp (YYYY-MM-DDTHH-MM-SS format)
          const timestampStr = file.substring(backupFilePrefix.length, file.length - backupFileSuffix.length);
          // Basic validation of timestamp format
          if (!/^\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}$/.test(timestampStr)) {
             console.warn(`[Backup Rotation] Skipping file with unexpected format: ${file}`);
             return null;
          }
          // Convert to sortable time (handle potential timezone issues if needed, but ISO string is usually fine for sorting)
          const time = new Date(timestampStr.replace(/-/g, '/').replace('T', ' ')).getTime(); // Use / for broader date parsing compatibility
          return { name: file, time: time };
        })
        .filter(fileInfo => fileInfo && !isNaN(fileInfo.time)) // Filter out nulls or invalid dates
        .sort((a, b) => a.time - b.time); // Sort oldest first

      if (backupFiles.length > MAX_BACKUPS) {
        const filesToDelete = backupFiles.slice(0, backupFiles.length - MAX_BACKUPS);
        console.log(`[Backup Rotation] Found ${backupFiles.length} backups. Deleting ${filesToDelete.length} oldest...`);
        for (const fileInfo of filesToDelete) {
          try {
            const filePathToDelete = path.join(backupDir, fileInfo.name);
            await fsPromises.unlink(filePathToDelete);
            console.log(`[Backup Rotation] Deleted: ${fileInfo.name}`);
          } catch (deleteError) {
            console.error(`[Backup Rotation] Failed to delete ${fileInfo.name}:`, deleteError);
          }
        }
      } else {
         // console.log(`[Backup Rotation] Found ${backupFiles.length} backups. No rotation needed.`);
      }
    } catch (error) {
      console.error('[Backup Rotation] Error rotating backups:', error);
    }
  }

  async function performBackup() {
    if (!currentProjectFilePath || !currentProjectPath) {
      console.warn('[Backup] Skipping backup: No active project file path or directory.');
      return;
    }

    console.log('[Backup] Starting backup process...');
    const mainWindow = BrowserWindow.getAllWindows()[0]; // Assume single window
    if (!mainWindow || mainWindow.isDestroyed()) {
      console.error('[Backup] Cannot perform backup: Main window not found or destroyed.');
      return;
    }

    try {
      // --- Revised State Request Logic ---
      const state = await new Promise((resolve, reject) => {
        // Listener for the response
        const responseListener = (event, receivedState) => {
          // Ensure the response is from the correct window
          if (event.sender === mainWindow.webContents) {
            console.log('[Backup] Received project state response.');
            // Clean up listener immediately *before* resolving
            ipcMain.removeListener('project-state-response', responseListener);
            resolve(receivedState);
          }
        };
        // Use once for automatic cleanup if response received
        ipcMain.once('project-state-response', responseListener);

        // Send the request
        console.log('[Backup] Sending request-project-state-for-backup to renderer...');
        mainWindow.webContents.send('request-project-state-for-backup');

        // Timeout if renderer doesn't respond
        const timeoutId = setTimeout(() => {
          // Check if listener is still attached before removing and rejecting
          if (ipcMain.listeners('project-state-response').includes(responseListener)) {
             ipcMain.removeListener('project-state-response', responseListener); // Clean up listener
             console.error('[Backup] Timeout waiting for project state response from renderer.');
             reject(new Error('Timeout waiting for project state response from renderer.'));
          }
        }, 5000); // 5 second timeout

        // Clear timeout if response received before timeout
         ipcMain.once('project-state-response', () => clearTimeout(timeoutId));

      });
      // --- End Revised State Request Logic ---


      if (!state) {
        // This case should ideally be handled by the reject in the promise now
        console.error('[Backup] Failed to get project state from renderer (state is null/undefined).');
        return;
      }

      // 2. Prepare backup path and filename
      const timestamp = new Date().toISOString().replace(/:/g, '-').split('.')[0]; // Format: YYYY-MM-DDTHH-MM-SS
      const projectName = path.basename(currentProjectFilePath).replace(/\.bp$/, '');
      const backupFilename = `${projectName}.backup.${timestamp}.bp`;
      const backupDir = path.join(currentProjectPath, BACKUP_FOLDER_NAME);
      const backupFilePath = path.join(backupDir, backupFilename);

      // 3. Ensure backup directory exists
      await fsPromises.mkdir(backupDir, { recursive: true });

      // 4. Write backup file asynchronously
      const jsonData = JSON.stringify(state, null, 2); // Pretty print
      await fsPromises.writeFile(backupFilePath, jsonData, 'utf-8');
      console.log(`[Backup] Successfully saved backup: ${backupFilename}`);

      // 5. Rotate old backups
      await rotateBackups();

    } catch (error) {
      console.error('[Backup] Error during backup process:', error);
      // Optionally notify the user non-intrusively if backup fails?
    }
  }

  function startBackupTimer() {
    stopBackupTimer(); // Clear any existing timer first
    if (currentProjectFilePath) {
      console.log(`[Backup] Starting backup timer (${BACKUP_INTERVAL_MS / 1000 / 60} min interval) for: ${currentProjectFilePath}`);
      // Run once immediately? Optional. Let's not for now to avoid immediate save on load.
      // performBackup();
      backupIntervalId = setInterval(performBackup, BACKUP_INTERVAL_MS);
    } else {
       console.log('[Backup] Not starting backup timer: No project file path set.');
    }
  }

  // --- stopBackupTimer definition moved to top level ---

  // --- End Backup Logic ---

  // --- Window Control Handlers ---
  ipcMain.handle('window:setFullScreen', (event, flag) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (window) {
      window.setFullScreen(flag);
    }
  });

  ipcMain.handle('window:isFullScreen', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    return window ? window.isFullScreen() : false;
  });

  // --- Window Resizing Handler ---
  const defaultWindowSize = { width: 1200, height: 800 };
  const largeWindowSize = { width: 1600, height: 1000 }; // Define the larger size

  ipcMain.handle('window:setSize', (event, size) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (window) {
      let targetSize;
      if (size === 'large') {
        targetSize = largeWindowSize;
        console.log(`[Main Process] Resizing window to large: ${targetSize.width}x${targetSize.height}`);
      } else { // Default to 'default' size
        targetSize = defaultWindowSize;
        console.log(`[Main Process] Resizing window to default: ${targetSize.width}x${targetSize.height}`);
      }
      // Use setBounds for potentially better control, or setSize
      // setSize(width, height, animate)
      window.setSize(targetSize.width, targetSize.height, true); // Animate the resize
    } else {
      console.error("[Main Process] Could not find window to resize.");
    }
  });
  // -----------------------------

// Helper function for calculating sharp extraction region based on fit/scale/pan
function calculateExtractionRegion(options) {
  const {
    targetWidthPt: containerW,
    targetHeightPt: containerH,
    transform,
    naturalWidth: imgW,
    naturalHeight: imgH,
  } = options;
  const { scale, offsetX, offsetY, fit, rotation = 0 } = transform;

  if (!imgW || !imgH || !containerW || !containerH) {
    console.warn('Invalid dimensions for extraction calculation:', options);
    return null; // Cannot calculate
  }

  // Log scale factor to debug zooming issues
  console.log(`[calculateExtractionRegion] Processing ${fit} mode image with scale: ${scale}`);

  const imgRatio = imgW / imgH;
  const containerRatio = containerW / containerH;

  let interimW, interimH;

  // Calculate intermediate dimensions based on fit mode (before user scaling)
  if (fit === 'cover') {
    if (imgRatio > containerRatio) {
      interimH = containerH;
      interimW = interimH * imgRatio;
    } else {
      interimW = containerW;
      interimH = interimW / imgRatio;
    }
  } else { // 'contain'
    if (imgRatio > containerRatio) {
      interimW = containerW;
      interimH = interimW / imgRatio;
    } else {
      interimH = containerH;
      interimW = interimH * imgRatio;
    }
  }

  // Apply user scaling
  const scaledW = interimW * scale;
  const scaledH = interimH * scale;
  
  console.log(`[calculateExtractionRegion] Original image: ${imgW}x${imgH}, Container: ${containerW}x${containerH}`);
  console.log(`[calculateExtractionRegion] Interim dimensions: ${interimW}x${interimH}, Scaled: ${scaledW}x${scaledH}`);

  // Simple centering offset
  const centerShiftX = offsetX;
  const centerShiftY = offsetY;

  // Top-left corner of the scaled image relative to the container's top-left
  const scaledImgRelX = (containerW - scaledW) / 2 + centerShiftX;
  const scaledImgRelY = (containerH - scaledH) / 2 + centerShiftY;

  // Calculate the visible portion of the scaled image within the container (intersection)
  // Coordinates relative to the container's top-left
  const visibleX_relContainer = Math.max(0, scaledImgRelX);
  const visibleY_relContainer = Math.max(0, scaledImgRelY);
  const visibleEndX_relContainer = Math.min(containerW, scaledImgRelX + scaledW);
  const visibleEndY_relContainer = Math.min(containerH, scaledImgRelY + scaledH);

  const visibleW_inContainer = visibleEndX_relContainer - visibleX_relContainer;
  const visibleH_inContainer = visibleEndY_relContainer - visibleY_relContainer;

  if (visibleW_inContainer <= 0 || visibleH_inContainer <= 0) {
    console.warn('Image is entirely outside the visible container area.');
    return null; // Nothing to extract
  }

  // Map the visible portion back to the *original* image coordinates for sharp.extract
  // Top-left corner of the visible area *within the scaled image*
  const visibleX_inScaledImg = visibleX_relContainer - scaledImgRelX;
  const visibleY_inScaledImg = visibleY_relContainer - scaledImgRelY;

  // Convert this top-left corner back to original image coordinates
  const extractX = Math.round(visibleX_inScaledImg * (imgW / scaledW));
  const extractY = Math.round(visibleY_inScaledImg * (imgH / scaledH));

  // Convert the visible dimensions back to original image coordinates
  const extractW = Math.round(visibleW_inContainer * (imgW / scaledW));
  const extractH = Math.round(visibleH_inContainer * (imgH / scaledH));

  // Clamp values to ensure they are within the original image bounds
  const finalExtractX = Math.max(0, extractX);
  const finalExtractY = Math.max(0, extractY);
  const finalExtractW = Math.max(1, Math.min(imgW - finalExtractX, extractW)); // Ensure at least 1px
  const finalExtractH = Math.max(1, Math.min(imgH - finalExtractY, extractH)); // Ensure at least 1px

  if (finalExtractW <= 0 || finalExtractH <= 0) {
     console.warn('Calculated extraction width or height is zero or negative.');
     return null;
  }

  return {
    left: finalExtractX,
    top: finalExtractY,
    width: finalExtractW,
    height: finalExtractH,
  };
}


// --- PDF Export Image Loading Handler ---
ipcMain.handle('load-image-data', async (event, filePath, options) => {
  try {
    const cleanPath = filePath.startsWith('safe-file://')
      ? path.normalize(decodeURI(filePath.substring(12)))
      : path.normalize(decodeURI(filePath));

    if (!cleanPath || typeof cleanPath !== 'string') {
       throw new Error('Invalid or unsafe file path provided');
    }

    console.log(`[Main Process] Processing image for PDF: ${cleanPath} with options:`, options);
    console.log(`[Main Process] Image transform:`, options.transform);

    // Calculate the region to extract from the original image
    const region = calculateExtractionRegion(options);

    if (!region) {
      throw new Error('Failed to calculate valid extraction region.');
    }

    const pointsPerInch = 72;
    let processedImageBuffer;
    let actualWidthPt;
    let actualHeightPt;

    // Check the desired resolution
    if (options.resolution === 'native') {
      // Native Resolution Export - works for both contain and cover modes
      console.log(`[Main Process] Exporting native resolution for: ${cleanPath}`);

      // Extract the calculated region without resizing
      let nativeSharp = sharp(cleanPath).extract(region);
      const rotation = options.transform?.rotation || 0;
      if (rotation !== 0) {
        console.log(`[Main Process] Applying rotation: ${rotation} degrees (native)`);
        nativeSharp = nativeSharp.rotate(rotation, { background: { r: 255, g: 255, b: 255, alpha: 1 } });
      }
      const { data, info } = await nativeSharp.jpeg({ quality: 98 }).toBuffer({ resolveWithObject: true });

      processedImageBuffer = data;
      
      // Use consistent calculation for both contain and cover
      const extractedRatio = region.width / region.height;
      const containerRatio = options.targetWidthPt / options.targetHeightPt;
      
      // Apply zoom scaling factor from the transform
      const scale = options.transform.scale || 1;
      console.log(`[Main Process] Applying scale factor: ${scale} to extracted region`);
      
      if (options.transform.fit === 'cover') {
        // For cover mode, calculate dimensions that fill the container while maintaining aspect ratio
        if (extractedRatio > containerRatio) {
          // Wider image - fit to height
          actualHeightPt = options.targetHeightPt * scale;
          actualWidthPt = actualHeightPt * extractedRatio;
        } else {
          // Taller image - fit to width
          actualWidthPt = options.targetWidthPt * scale;
          actualHeightPt = actualWidthPt / extractedRatio;
        }
      } else {
        // For contain mode, calculate dimensions that fit within container while maintaining aspect ratio
        if (extractedRatio > containerRatio) {
          // Wider image - fit to width
          actualWidthPt = options.targetWidthPt * scale;
          actualHeightPt = actualWidthPt / extractedRatio;
        } else {
          // Taller image - fit to height
          actualHeightPt = options.targetHeightPt * scale;
          actualWidthPt = actualHeightPt * extractedRatio;
        }
      }
      
      console.log(`[Main Process] Final dimensions: ${actualWidthPt}x${actualHeightPt} points`);

    } else {
      // 300 DPI Resizing Logic
      console.log(`[Main Process] Exporting 300 DPI resolution for: ${cleanPath}`);
      const DPI = 300;
      const scaleFactor = DPI / pointsPerInch;

      // Apply zoom scaling from transform to target dimensions
      const scale = options.transform.scale || 1;
      const scaledTargetWidthPt = options.targetWidthPt * scale;
      const scaledTargetHeightPt = options.targetHeightPt * scale;
      
      console.log(`[Main Process] Applying scale factor: ${scale} to target dimensions`);
      console.log(`[Main Process] Scaled target dimensions: ${scaledTargetWidthPt}x${scaledTargetHeightPt} points`);

      const finalPixelWidth = Math.round(scaledTargetWidthPt * scaleFactor);
      const finalPixelHeight = Math.round(scaledTargetHeightPt * scaleFactor);

      if (finalPixelWidth <= 0 || finalPixelHeight <= 0) {
          throw new Error(`Invalid target dimensions for resize: ${finalPixelWidth}x${finalPixelHeight}`);
      }

      let resizeSharp = sharp(cleanPath).extract(region);
      const rotation = options.transform?.rotation || 0;
      if (rotation !== 0) {
        console.log(`[Main Process] Applying rotation: ${rotation} degrees (300dpi)`);
        resizeSharp = resizeSharp.rotate(rotation, { background: { r: 255, g: 255, b: 255, alpha: 1 } });
      }
      const { data, info } = await resizeSharp
        .resize({
          width: finalPixelWidth,
          height: finalPixelHeight,
          fit: options.transform.fit === 'contain' ? sharp.fit.inside : sharp.fit.cover,
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .jpeg()
        .toBuffer({ resolveWithObject: true });

      processedImageBuffer = data;
      actualWidthPt = info.width / scaleFactor;
      actualHeightPt = info.height / scaleFactor;
      
      console.log(`[Main Process] Final dimensions: ${actualWidthPt}x${actualHeightPt} points`);
    }

    // Return base64 string and the calculated final dimensions in points
    return {
        base64Data: processedImageBuffer.toString('base64'),
        finalWidthPt: actualWidthPt,
        finalHeightPt: actualHeightPt
    };

  } catch (error) {
    console.error(`[Main Process] Error processing image data for PDF (${filePath}):`, error);
    return null; // Indicate failure
  }
});
// ------------------------------------
// --- NEW: PDF Export Image Loading Handler with Color Profile (v3 - Handles PNG/JPEG) ---
ipcMain.handle('load-image-data-for-pdf', async (event, filePath, options) => {
  try {
    const cleanPath = filePath.startsWith('safe-file://')
      ? path.normalize(decodeURI(filePath.substring(12)))
      : path.normalize(decodeURI(filePath));

    if (!cleanPath || typeof cleanPath !== 'string') {
       throw new Error('Invalid or unsafe file path provided');
    }

    console.log(`[Main Process PDF v3] Processing image for PDF: ${path.basename(cleanPath)} with options:`, options);

    // 1. Read original file buffer to detect type and extract PNG ICCP if needed
    const originalFileBuffer = await fsPromises.readFile(cleanPath);
    const metadata = await sharp(originalFileBuffer).metadata(); // Use buffer for metadata

    const naturalWidth = metadata.width;
    const naturalHeight = metadata.height;
    const imageFormat = metadata.format; // 'jpeg', 'png', 'webp', etc.

    if (!naturalWidth || !naturalHeight || !imageFormat) {
      throw new Error(`Could not read dimensions/format for image: ${path.basename(cleanPath)}`);
    }

    console.log(`[Main Process PDF v3] Detected format: ${imageFormat}`);

    // 2. Add natural dimensions to options for region calculation
    const calculationOptions = {
        ...options,
        naturalWidth: naturalWidth,
        naturalHeight: naturalHeight
    };

    // 3. Calculate Extraction Region
    const region = calculateExtractionRegion(calculationOptions);
    if (!region) {
      throw new Error(`Failed to calculate valid extraction region for ${path.basename(cleanPath)}.`);
    }
    console.log(`[Main Process PDF v3] Calculated extraction region:`, region);

    // 4. Extract Region using sharp
    let sharpInstance = sharp(originalFileBuffer).extract(region); // Start with original buffer
    
    // Apply rotation if specified
    const rotation = options.transform?.rotation || 0;
    if (rotation !== 0) {
      console.log(`[Main Process PDF v3] Applying rotation: ${rotation} degrees`);
      // Use rotate with background: transparent and don't expand canvas
      sharpInstance = sharpInstance.rotate(rotation, { 
        background: { r: 0, g: 0, b: 0, alpha: 0 } // Transparent background
      });
    }

    let finalImageBuffer;
    let finalIccProfileBuffer = null; // Default to null
    // Default to original type, or jpeg if not png. This might be overridden by color space conversion.
    let finalImageType = metadata.format === 'png' ? 'png' : 'jpeg';
    let finalInfo;

    // --- Detailed Logging for Debugging Specific Image Types ---
    if (path.basename(cleanPath).toLowerCase().includes('adobe98') && path.basename(cleanPath).toLowerCase().endsWith('.jpg')) {
      console.log(`[DEBUG Adobe98 JPG] File: ${cleanPath}`);
      console.log(`[DEBUG Adobe98 JPG] metadata.format: ${metadata.format}`);
      console.log(`[DEBUG Adobe98 JPG] metadata.space: ${metadata.space}`);
      console.log(`[DEBUG Adobe98 JPG] metadata.hasProfile: ${metadata.hasProfile}`);
      console.log(`[DEBUG Adobe98 JPG] metadata.icc exists: ${!!metadata.icc}`);
      if (metadata.icc) {
        console.log(`[DEBUG Adobe98 JPG] metadata.icc size: ${metadata.icc.length}`);
      }
    }
    // --- End Detailed Logging ---

    // --- Color Space Conversion ---
    if (options.colorSpace === 'srgb') {
      console.log(`[Main Process PDF v3] SRGB_CONVERT_ALL: Converting ${path.basename(cleanPath)} to target sRGB. Original space: ${metadata.space}, format: ${imageFormat}.`);
      
      sharpInstance = sharpInstance.toColorspace('srgb'); // Convert to generic sRGB space first
      
      const srgbProfilePath = path.join(__dirname, '..', 'build', 'sRGB_IEC61966-2.1.icc');
      try {
        await fsPromises.access(srgbProfilePath);
        finalIccProfileBuffer = await fsPromises.readFile(srgbProfilePath); // This is our target profile
        console.log(`[Main Process PDF v3] SRGB_CONVERT_ALL: Loaded standard sRGB profile for embedding: ${srgbProfilePath}`);
      } catch (srgbProfileError) {
        console.error(`[Main Process PDF v3] SRGB_CONVERT_ALL: FAILED to load standard sRGB profile from ${srgbProfilePath}: ${srgbProfileError.message}. Image will be sRGB but may lack explicit profile in PDF.`);
        finalIccProfileBuffer = null;
      }
      
      finalImageType = 'jpeg'; // Standardize output to JPEG for sRGB converted images
      console.log(`[Main Process PDF v3] SRGB_CONVERT_ALL: Outputting as JPEG, with explicit target sRGB profile intended.`);
      const { data, info } = await sharpInstance.jpeg({ quality: 92, mozjpeg: true }).toBuffer({ resolveWithObject: true });
      finalImageBuffer = data;
      finalInfo = info;

    } else if (options.colorSpace === 'adobergb') {
      console.log(`[Main Process PDF v3] ADOBERGB_CONVERT_ALL: Attempting to convert ${path.basename(cleanPath)} to Adobe RGB (1998). Original space: ${metadata.space}, format: ${imageFormat}.`);
      const adobeRGBProfilePath = path.join(__dirname, '..', 'build', 'AdobeRGB1998.icc'); // Updated path to build folder
      try {
        await fsPromises.access(adobeRGBProfilePath);
        sharpInstance = sharpInstance.toColorspace(adobeRGBProfilePath, { intent: 'relative' }); // Added intent: 'relative'
        finalIccProfileBuffer = await fsPromises.readFile(adobeRGBProfilePath);
        finalImageType = 'jpeg';
        console.log(`[Main Process PDF v3] ADOBERGB_CONVERT_ALL: Outputting as JPEG after Adobe RGB conversion. Profile loaded: ${adobeRGBProfilePath}`);
        const { data, info } = await sharpInstance.jpeg({ quality: 92, mozjpeg: true }).toBuffer({ resolveWithObject: true });
        finalImageBuffer = data;
        finalInfo = info;
      } catch (profileError) {
        console.error(`[Main Process PDF v3] ADOBERGB_CONVERT_ALL: Adobe RGB profile error (${adobeRGBProfilePath}): ${profileError.message}. Falling back to passthrough for ${path.basename(cleanPath)}.`);
        options.colorSpace = 'passthrough';
        finalImageBuffer = null;
      }
    }
    // --- End Color Space Conversion ---

    // If colorSpace is 'passthrough' (either originally, or due to AdobeRGB fallback)
    // OR if a conversion path was taken but finalImageBuffer wasn't set (e.g. sRGB/AdobeRGB conversion failed before buffer creation)
    if (options.colorSpace === 'passthrough' || !finalImageBuffer) { // Simplified condition: if it's passthrough OR if a conversion didn't yield a buffer, do passthrough.
      if (options.colorSpace === 'passthrough' && !finalImageBuffer && imageFormat !== 'png') {
         // This case implies AdobeRGB conversion failed and it's not a PNG, so it needs to be processed by sharpInstance to get a buffer.
         console.log(`[Main Process PDF v3] PASSTHROUGH_FALLBACK (from failed AdobeRGB): Processing ${path.basename(cleanPath)} as JPEG.`);
         finalImageType = 'jpeg';
         finalIccProfileBuffer = metadata.icc; // Attempt to use original ICC
         const jpegOptions = { quality: 92, mozjpeg: true };
         const { data, info } = await sharpInstance.jpeg(jpegOptions).toBuffer({ resolveWithObject: true });
         finalImageBuffer = data;
         finalInfo = info;
      } else if (options.colorSpace === 'passthrough') { // Genuine passthrough request
         console.log(`[Main Process PDF v3] PASSTHROUGH: Processing ${path.basename(cleanPath)} with original color space (${imageFormat}).`);
      } else { // This case means sRGB conversion failed to produce a buffer (should be rare)
         console.warn(`[Main Process PDF v3] PASSTHROUGH_FALLBACK: finalImageBuffer not set after attempted sRGB conversion on ${path.basename(cleanPath)}, processing as passthrough.`);
      }

      // If finalImageBuffer is still not set, it means it's a genuine passthrough for an image format
      // that wasn't JPEG after a failed AdobeRGB conversion.
      if (!finalImageBuffer) {
        if (imageFormat === 'png') {
          finalImageType = 'png';
          finalIccProfileBuffer = extractCompressedIccProfileFromPng(originalFileBuffer);
          if (finalIccProfileBuffer) {
            console.log(`[Main Process PDF v3] PASSTHROUGH: Extracted compressed iCCP profile (${finalIccProfileBuffer.length} bytes) for PNG.`);
          } else {
            console.log(`[Main Process PDF v3] PASSTHROUGH: No compressed iCCP profile found in PNG.`);
          }
          const pngOptions = { quality: 90, compressionLevel: 6, force: true };
          let passthroughSharp = sharp(originalFileBuffer).extract(region);
          if (rotation !== 0) passthroughSharp = passthroughSharp.rotate(rotation, { background: { r: 0, g: 0, b: 0, alpha: 0 } });
          const { data, info } = await passthroughSharp.png(pngOptions).toBuffer({ resolveWithObject: true }); // Re-init sharpInstance for passthrough PNG
          finalImageBuffer = data;
          finalInfo = info;
          console.log(`[Main Process PDF v3] PASSTHROUGH: PNG processed. Buffer: ${finalImageBuffer.length}, Dims: ${info.width}x${info.height}px`);
        } else { // Assume JPEG or convert other types to JPEG for passthrough
          finalImageType = 'jpeg';
          finalIccProfileBuffer = metadata.icc;
          if (finalIccProfileBuffer) {
            console.log(`[Main Process PDF v3] PASSTHROUGH: Using raw ICC profile from metadata (${finalIccProfileBuffer.length} bytes) for JPEG.`);
          } else {
            console.log(`[Main Process PDF v3] PASSTHROUGH: No raw ICC profile found in metadata for JPEG.`);
          }
          const jpegOptions = { quality: 92, mozjpeg: true };
          let passthroughSharp = sharp(originalFileBuffer).extract(region);
          if (rotation !== 0) passthroughSharp = passthroughSharp.rotate(rotation, { background: { r: 255, g: 255, b: 255, alpha: 1 } });
          const { data, info } = await passthroughSharp.jpeg(jpegOptions).toBuffer({ resolveWithObject: true }); // Re-init sharpInstance for passthrough JPEG
          finalImageBuffer = data;
          finalInfo = info;
          console.log(`[Main Process PDF v3] PASSTHROUGH: JPEG processed. Buffer: ${finalImageBuffer.length}, Dims: ${info.width}x${info.height}px`);
        }
      }
    }

    // 6. Return results (ensure finalInfo is valid)
    if (!finalInfo || typeof finalInfo.width === 'undefined' || typeof finalInfo.height === 'undefined') {
        // This might happen if an image processing path failed to set finalInfo
        console.error(`[Main Process PDF v3] finalInfo is undefined or incomplete for ${path.basename(cleanPath)}. Attempting to get metadata from finalImageBuffer.`);
        const fallbackMeta = await sharp(finalImageBuffer).metadata();
        finalInfo = { width: fallbackMeta.width, height: fallbackMeta.height, ...fallbackMeta }; // Spread to include other potential info
        if (!finalInfo.width || !finalInfo.height) {
            throw new Error(`Failed to determine final dimensions for ${path.basename(cleanPath)} even after fallback.`);
        }
    }
    return {
        success: true,
        imageType: finalImageType, // 'png' or 'jpeg'
        imageBuffer: finalImageBuffer, // Send the raw buffer of the extracted region
        // Send COMPRESSED for PNG, RAW for JPEG (or null if none)
        iccProfileBuffer: finalIccProfileBuffer,
        width: finalInfo.width, // Pixel dimensions of the *extracted region* buffer
        height: finalInfo.height
    };

  } catch (error) {
    console.error(`[Main Process PDF v3] Error processing image data for PDF (${filePath}):`, error);
    // Return a structured error
    return {
        success: false,
        error: error.message || 'An unknown error occurred during image processing for PDF.'
    };
  }
});
// --- End NEW PDF Handler (v3) ---

// --- Canvas Export Image Loading Handler (similar to PDF v3) ---
ipcMain.handle('load-image-data-for-canvas', async (event, filePath, options) => {
  try {
    const cleanPath = filePath.startsWith('safe-file://')
      ? path.normalize(decodeURI(filePath.substring(12)))
      : path.normalize(decodeURI(filePath));

    if (!cleanPath || typeof cleanPath !== 'string') {
       throw new Error('Invalid or unsafe file path provided for canvas data');
    }

    console.log(`[Main Process Canvas] Processing image for Canvas: ${path.basename(cleanPath)} with options:`, options);

    const originalFileBuffer = await fsPromises.readFile(cleanPath);
    const metadata = await sharp(originalFileBuffer).metadata();

    const naturalWidth = metadata.width;
    const naturalHeight = metadata.height;
    const imageFormat = metadata.format;

    if (!naturalWidth || !naturalHeight || !imageFormat) {
      throw new Error(`Could not read dimensions/format for image: ${path.basename(cleanPath)}`);
    }

    console.log(`[Main Process Canvas] Detected format: ${imageFormat}`);

    const calculationOptions = {
        ...options,
        naturalWidth: naturalWidth,
        naturalHeight: naturalHeight
    };

    const region = calculateExtractionRegion(calculationOptions);
    if (!region) {
      throw new Error(`Failed to calculate valid extraction region for ${path.basename(cleanPath)} (canvas).`);
    }
    console.log(`[Main Process Canvas] Calculated extraction region:`, region);

    let sharpInstance = sharp(originalFileBuffer).extract(region);
    
    // Apply rotation if specified
    const rotation = options.transform?.rotation || 0;
    if (rotation !== 0) {
      console.log(`[Main Process Canvas] Applying rotation: ${rotation} degrees`);
      sharpInstance = sharpInstance.rotate(rotation, { background: { r: 0, g: 0, b: 0, alpha: 0 } });
    }

    let finalImageBuffer;
    let finalIccProfileBuffer = null;
    let finalImageType = metadata.format === 'png' ? 'png' : 'jpeg'; // Default to JPEG if not PNG
    let finalInfo;

    // Color Space Conversion (similar to PDF logic)
    if (options.colorSpace === 'srgb') {
      console.log(`[Main Process Canvas] SRGB_CONVERT_ALL: Converting ${path.basename(cleanPath)} to target sRGB. Original space: ${metadata.space}, format: ${imageFormat}.`);
      sharpInstance = sharpInstance.toColorspace('srgb');
      const srgbProfilePath = path.join(__dirname, '..', 'build', 'sRGB_IEC61966-2.1.icc');
      try {
        await fsPromises.access(srgbProfilePath);
        finalIccProfileBuffer = await fsPromises.readFile(srgbProfilePath);
      } catch (srgbProfileError) {
        console.error(`[Main Process Canvas] SRGB_CONVERT_ALL: FAILED to load standard sRGB profile from ${srgbProfilePath}: ${srgbProfileError.message}.`);
        finalIccProfileBuffer = null;
      }
      finalImageType = 'jpeg'; // Output as JPEG for sRGB
      const { data, info } = await sharpInstance.jpeg({ quality: 92, mozjpeg: true }).toBuffer({ resolveWithObject: true });
      finalImageBuffer = data;
      finalInfo = info;
    } else if (options.colorSpace === 'adobergb') {
      console.log(`[Main Process Canvas] ADOBERGB_CONVERT_ALL: Attempting to convert ${path.basename(cleanPath)} to Adobe RGB (1998).`);
      const adobeRGBProfilePath = path.join(__dirname, '..', 'build', 'AdobeRGB1998.icc');
      try {
        await fsPromises.access(adobeRGBProfilePath);
        sharpInstance = sharpInstance.toColorspace(adobeRGBProfilePath, { intent: 'relative' });
        finalIccProfileBuffer = await fsPromises.readFile(adobeRGBProfilePath);
        finalImageType = 'jpeg';
        const { data, info } = await sharpInstance.jpeg({ quality: 92, mozjpeg: true }).toBuffer({ resolveWithObject: true });
        finalImageBuffer = data;
        finalInfo = info;
      } catch (profileError) {
        console.error(`[Main Process Canvas] ADOBERGB_CONVERT_ALL: Adobe RGB profile error (${adobeRGBProfilePath}): ${profileError.message}. Falling back to passthrough.`);
        options.colorSpace = 'passthrough'; // Fallback
        finalImageBuffer = null; // Ensure passthrough logic runs
      }
    }

    if (options.colorSpace === 'passthrough' || !finalImageBuffer) {
      console.log(`[Main Process Canvas] PASSTHROUGH: Processing ${path.basename(cleanPath)} with original color space (${imageFormat}).`);
      if (imageFormat === 'png') {
        finalImageType = 'png';
        finalIccProfileBuffer = extractCompressedIccProfileFromPng(originalFileBuffer);
        // For canvas, we might prefer to send PNG data directly if it's already PNG.
        // The renderer can then decide if it needs to convert to JPEG for the final export.
        // Let's output PNG if original was PNG and passthrough is selected.
        let passthroughSharp = sharp(originalFileBuffer).extract(region);
        if (rotation !== 0) passthroughSharp = passthroughSharp.rotate(rotation, { background: { r: 0, g: 0, b: 0, alpha: 0 } });
        const { data, info } = await passthroughSharp.png({ quality: 90, compressionLevel: 6, force: true }).toBuffer({ resolveWithObject: true });
        finalImageBuffer = data;
        finalInfo = info;
      } else { // Assume JPEG or convert other types to JPEG for passthrough
        finalImageType = 'jpeg';
        finalIccProfileBuffer = metadata.icc;
        let passthroughSharp = sharp(originalFileBuffer).extract(region);
        if (rotation !== 0) passthroughSharp = passthroughSharp.rotate(rotation, { background: { r: 255, g: 255, b: 255, alpha: 1 } });
        const { data, info } = await passthroughSharp.jpeg({ quality: 92, mozjpeg: true }).toBuffer({ resolveWithObject: true });
        finalImageBuffer = data;
        finalInfo = info;
      }
    }

    if (!finalInfo || typeof finalInfo.width === 'undefined' || typeof finalInfo.height === 'undefined') {
        const fallbackMeta = await sharp(finalImageBuffer).metadata();
        finalInfo = { width: fallbackMeta.width, height: fallbackMeta.height, ...fallbackMeta };
        if (!finalInfo.width || !finalInfo.height) {
            throw new Error(`Failed to determine final dimensions for ${path.basename(cleanPath)} (canvas) even after fallback.`);
        }
    }

    // For canvas, we might want to return the buffer directly without base64 encoding,
    // as ArrayBuffer can be used to create a Blob and then an object URL.
    // However, consistency with the PDF handler (which might be adapted by renderer) suggests keeping similar structure.
    // The key is that the renderer gets the image data and its type.
    return {
        success: true,
        imageType: finalImageType,    // 'png' or 'jpeg'
        imageBuffer: finalImageBuffer,  // This will be an ArrayBuffer in the renderer via contextBridge
        iccProfileBuffer: finalIccProfileBuffer, // Also ArrayBuffer
        width: finalInfo.width,       // Pixel dimensions of the processed image buffer
        height: finalInfo.height
    };

  } catch (error) {
    console.error(`[Main Process Canvas] Error processing image data for Canvas (${filePath}):`, error);
    return {
        success: false,
        error: error.message || 'An unknown error occurred during image processing for Canvas.'
    };
  }
});
// --- End Canvas Export Image Loading Handler ---
 

// --- Helper: Find Latest Photoshop on Windows ---
async function findLatestPhotoshopWindows() {
  const baseDirs = [
    process.env.ProgramFiles, // C:\Program Files
    process.env['ProgramFiles(x86)'] // C:\Program Files (x86)
  ].filter(Boolean); // Filter out undefined/null paths

  let latestYear = -1;
  let latestPhotoshopExePath = null;

  for (const baseDir of baseDirs) {
    const adobeDir = path.join(baseDir, 'Adobe');
    try {
      // Check if Adobe directory exists
      await fsPromises.access(adobeDir, fs.constants.F_OK);
      const entries = await fsPromises.readdir(adobeDir, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.isDirectory()) {
          const match = entry.name.match(/^Adobe Photoshop (\d{4})$/); // Match "Adobe Photoshop YYYY"
          if (match) {
            const year = parseInt(match[1], 10);
            if (year > latestYear) {
              const potentialExePath = path.join(adobeDir, entry.name, 'Photoshop.exe');
              try {
                // Verify the executable exists
                await fsPromises.access(potentialExePath, fs.constants.X_OK); // Check execute permission
                latestYear = year;
                latestPhotoshopExePath = potentialExePath;
                console.log(`[Main Process] Found potential Photoshop: ${potentialExePath}`);
              } catch (exeAccessError) {
                // Photoshop.exe not found or not executable in this version folder
                console.warn(`[Main Process] Photoshop.exe not found or accessible in ${entry.name}`);
              }
            }
          }
        }
      }
    } catch (error) {
      // Adobe directory doesn't exist or couldn't be read in this baseDir
      if (error.code !== 'ENOENT') {
         console.warn(`[Main Process] Error reading Adobe directory ${adobeDir}:`, error);
      }
    }
  }

  if (latestPhotoshopExePath) {
     console.log(`[Main Process] Determined latest Photoshop path: ${latestPhotoshopExePath}`);
  } else {
     console.warn('[Main Process] Could not find any verified Photoshop installation path on Windows.');
  }
  return latestPhotoshopExePath;
}

// --- File Change Handler (for watched files) ---
async function handleFileChange(filePath) {
  console.log(`[Main Process] Change detected for: ${filePath}`);

  // Clear existing debounce timer for this file
  if (debounceTimers.has(filePath)) {
      clearTimeout(debounceTimers.get(filePath));
  }

  // Set a new debounce timer (e.g., 1 second)
  const timerId = setTimeout(async () => {
      console.log(`[Main Process] Debounced processing for: ${filePath}`);
      try {
          // Generate a hash of the file path to use as the cache filename
          const hash = crypto.createHash('sha256').update(filePath).digest('hex');
          
          // Thumbnail cache remains project-specific (as per original logic here)
          const thumbnailCacheDir = currentProjectPath ? path.join(currentProjectPath, 'thumbnail_cache') : null;
          
          // Clean up old thumbnail if it exists (project-specific)
          if (thumbnailCacheDir) {
             const thumbnailFilename = `${hash}.webp`;
             const oldThumbnailPath = path.join(thumbnailCacheDir, thumbnailFilename);
             try {
                if (fs.existsSync(oldThumbnailPath)) {
                   await fsPromises.unlink(oldThumbnailPath);
                   console.log(`[Main Process] Deleted old project-specific thumbnail: ${oldThumbnailPath}`);
                }
             } catch (deleteError) {
                console.error(`[Main Process] Error deleting cached project-specific thumbnail for ${filePath}:`, deleteError);
             }
          } else {
             console.warn('[Main Process] Project path not set, cannot delete cached project-specific thumbnail.');
          }

          let newThumbnailUrl = null;
          if (thumbnailCacheDir) {
             await fsPromises.mkdir(thumbnailCacheDir, { recursive: true }); // Ensure dir exists
             const thumbnailPath = path.join(thumbnailCacheDir, `${hash}.webp`);
             await sharp(filePath)
               .resize(THUMBNAIL_SIZE, THUMBNAIL_SIZE, { fit: 'inside' })
               .webp({ quality: 80 })
               .toFile(thumbnailPath);
             newThumbnailUrl = `safe-file://${thumbnailPath}`;
          }
          
          // Preview cache is now project-specific - Force regeneration by deleting cache first
          let newPreviewUrl = null;
          if (currentProjectPath) {
            const projectPreviewCacheDir = path.join(currentProjectPath, 'preview_cache');
            const previewFilename = `${hash}.webp`;
            const oldPreviewPathProject = path.join(projectPreviewCacheDir, previewFilename);

            // Clean up old preview if it exists (project-specific) - FORCE deletion for roundtrip
            try {
               if (fs.existsSync(oldPreviewPathProject)) {
                  await fsPromises.unlink(oldPreviewPathProject);
                  console.log(`[Main Process] Deleted old project-specific preview for roundtrip: ${oldPreviewPathProject}`);
               }
            } catch (deleteError) {
               console.error(`[Main Process] Error deleting cached project-specific preview for ${filePath}:`, deleteError);
               // Continue regeneration even if deletion failed
            }

            await fsPromises.mkdir(projectPreviewCacheDir, { recursive: true }); // Ensure dir exists
            
            // Force regeneration of preview from updated file
            const previewPathProject = path.join(projectPreviewCacheDir, previewFilename);
            await sharp(filePath)
              .resize(PREVIEW_SIZE, PREVIEW_SIZE, { fit: 'inside' })
              .webp({ quality: 85 })
              .toFile(previewPathProject);
            newPreviewUrl = `safe-file://${previewPathProject}`;
          } else {
            console.warn(`[Main Process] currentProjectPath not set, skipping project-specific preview management in handleFileChange for ${filePath}.`);
          }
          
          // Extract EXIF data for dateTaken and get file stats for dateModified
          let dateTaken = null;
          let dateModified = null;
          
          try {
            // Get file stats for dateModified
            const fileStats = await fsPromises.stat(filePath);
            dateModified = fileStats.mtime.getTime();
            
            // Extract date taken from EXIF if available
            const exif = await exifr.parse(filePath, { xmp: true, iptc: true, exif: true, icc: false });
            if (exif?.DateTimeOriginal) {
              dateTaken = exif.DateTimeOriginal.getTime();
            } else if (exif?.DateTime) {
              dateTaken = new Date(exif.DateTime).getTime();
            } else if (exif?.CreateDate) {
              dateTaken = new Date(exif.CreateDate).getTime();
            }
          } catch (metadataError) {
            console.error(`[Main Process] Error extracting metadata in handleFileChange for ${filePath}:`, metadataError);
          }
          
          // Notify the renderer - Check if regeneration was successful before sending update
          if ((thumbnailCacheDir && newThumbnailUrl) || !thumbnailCacheDir) {
            const mainWindow = BrowserWindow.getAllWindows()[0];
            if (mainWindow && !mainWindow.isDestroyed()) {
                 console.log(`[Main Process] Sending app:file-updated for ${filePath}`);
                 mainWindow.webContents.send('app:file-updated', {
                     originalPath: filePath,
                     thumbnailUrl: newThumbnailUrl,
                     previewUrl: newPreviewUrl,
                     dateTaken: dateTaken,
                     dateModified: dateModified
                 });
                 
                 // Also send preview-generated event for consistency with regenerateThumbnails
                 mainWindow.webContents.send('preview-generated', {
                     originalPath: filePath,
                     previewUrl: newPreviewUrl
                 });
            }
          } else {
             // Regeneration failed
             console.error(`[Main Process] Failed to regenerate cache for ${filePath}. Thumbnail: ${!!newThumbnailUrl}, Preview: ${!!newPreviewUrl}`);
             throw new Error('Failed to regenerate cached images after update.');
          }
      } catch (error) {
          console.error(`[Main Process] Error processing changed file ${filePath}:`, error); // This will now catch regeneration errors too
          // Optionally notify renderer of the error
          const mainWindow = BrowserWindow.getAllWindows()[0];
           if (mainWindow && !mainWindow.isDestroyed()) {
               mainWindow.webContents.send('app:file-update-error', {
                   originalPath: filePath,
                   error: error.message || 'Failed to update image after external edit.'
               });
           }
      } finally {
          // Clean up the timer reference after execution
          debounceTimers.delete(filePath);
      }
  }, 1000); // 1 second debounce period

  debounceTimers.set(filePath, timerId);
}
// ---------------------------------------------
// ---------------------------------------------

// --- Edit in Photoshop Handler ---
ipcMain.handle('app:editInPhotoshop', async (event, filesToEdit) => {
  const platform = process.platform;

  if (!filesToEdit || (typeof filesToEdit !== 'string' && !Array.isArray(filesToEdit))) {
    console.error('[Main Process] Invalid argument received for editInPhotoshop. Expected a file path string or an array of strings.');
    throw new Error('Invalid file path(s) provided.');
  }

  // Ensure filesToEdit is an array for consistent processing
  const filePathsArray = Array.isArray(filesToEdit) ? filesToEdit : [filesToEdit];

  if (filePathsArray.length === 0) {
    console.warn('[Main Process] No file paths provided to editInPhotoshop.');
    return; // Or throw an error if appropriate
  }

  console.log(`[Main Process] Received request to edit in Photoshop for: ${filePathsArray.join(', ')}`);

  try {
    if (platform === 'darwin') { // macOS
      await new Promise((resolve, reject) => {
        // For macOS, 'open -b com.adobe.Photoshop /path1 /path2 ...' handles multiple files
        const openArgs = ['-b', 'com.adobe.Photoshop', ...filePathsArray];
        execFile('open', openArgs, (error, stdout, stderr) => {
          if (error) {
            console.error(`[Main Process] Error opening with Photoshop bundle ID: ${stderr || error.message}`);
            reject(new Error('Failed to launch Photoshop. Is it installed?'));
          } else {
            console.log(`[Main Process] Launched Photoshop for ${filePathsArray.length} file(s) via bundle ID.`);
            resolve();
          }
        });
      });
    } else if (platform === 'win32') { // Windows
      const photoshopPath = await findLatestPhotoshopWindows();
      if (!photoshopPath) {
        throw new Error('Adobe Photoshop installation not found.');
      }
      // For Windows, execFile Photoshop.exe with all file paths as arguments
      // We assume Photoshop.exe can handle multiple file paths as arguments.
      // If not, a loop might be needed here for Windows, but the primary issue is macOS.
      await new Promise((resolve, reject) => {
        execFile(photoshopPath, filePathsArray, (error, stdout, stderr) => {
          if (error) {
            console.error(`[Main Process] Error launching Photoshop executable (${photoshopPath}): ${stderr || error.message}`);
            reject(new Error('Failed to launch Photoshop.'));
          } else {
            console.log(`[Main Process] Launched ${photoshopPath} for ${filePathsArray.length} file(s).`);
            resolve();
          }
        });
      });
    } else {
      console.warn(`[Main Process] Edit in Photoshop not supported on platform: ${platform}`);
      throw new Error(`Editing in Photoshop is not supported on this operating system (${platform}).`);
    }

    // After successfully launching Photoshop, set up watchers for all files
    for (const filePath of filePathsArray) {
      if (!fileWatchers.has(filePath)) {
        console.log(`[Main Process] Starting to watch: ${filePath}`);
        try {
          const watcher = chokidar.watch(filePath, {
            persistent: true,
            ignoreInitial: true,
            awaitWriteFinish: {
              stabilityThreshold: 2000,
              pollInterval: 100
            },
            usePolling: platform === 'win32'
          });

          watcher.on('change', (path) => handleFileChange(path));
          watcher.on('error', (watchError) => {
            console.error(`[Main Process] Watcher error for ${filePath}:`, watchError);
            const faultyWatcher = fileWatchers.get(filePath);
            if (faultyWatcher) {
              faultyWatcher.close();
              fileWatchers.delete(filePath);
              console.log(`[Main Process] Closed faulty watcher for ${filePath}`);
            }
          });
          fileWatchers.set(filePath, watcher);
        } catch (watchError) {
          console.error(`[Main Process] Failed to start watcher for ${filePath}:`, watchError);
          // Continue trying to watch other files even if one fails
        }
      }
    }

  } catch (error) {
    console.error('[Main Process] Error in editInPhotoshop handler:', error);
    throw error instanceof Error ? error : new Error('An unexpected error occurred while trying to open Photoshop.');
  }
});
// -----------------------------

// --- PDF Export Handlers ---
ipcMain.handle('dialog:showSaveDialog', async (event, options) => {
  const window = BrowserWindow.fromWebContents(event.sender);
  if (!window) {
    console.error("Could not find browser window for showSaveDialog.");
    return null; // Indicate cancellation or error
  }
  try {
    const result = await dialog.showSaveDialog(window, options);
    return result.canceled ? null : result.filePath;
  } catch (error) {
    console.error("Error showing save dialog:", error);
    return null; // Indicate error
  }
});

ipcMain.handle('file:savePdf', async (event, filePath, data) => {
  if (!filePath || !data) {
    console.error("Invalid arguments received for file:savePdf");
    return false; // Indicate failure
  }
  try {
    // Convert ArrayBuffer (received as plain object) back to Buffer
    const buffer = Buffer.from(data);
    await fsPromises.writeFile(filePath, buffer);
    console.log(`[Main Process] PDF saved successfully to: ${filePath}`);
    return true; // Indicate success
  } catch (error) {
    console.error(`[Main Process] Failed to save PDF file to ${filePath}:`, error);
    return false; // Indicate failure
  }
});

ipcMain.handle('shell:openPath', async (event, filePath) => {
  if (!filePath || typeof filePath !== 'string') {
    console.error("Invalid file path received for shell:openPath");
    return;
  }
  try {
    await shell.openPath(filePath);
    console.log(`[Main Process] Opened path: ${filePath}`);
  } catch (error) {
    console.error(`[Main Process] Failed to open path ${filePath}:`, error);
  }
});
ipcMain.handle('shell:showItemInFolder', async (event, filePath) => {
  if (!filePath || typeof filePath !== 'string') {
    console.error("[Main Process] Invalid file path received for shell:showItemInFolder");
    return { success: false, error: "Invalid file path provided." };
  }
  try {
    shell.showItemInFolder(filePath); // This is synchronous
    console.log(`[Main Process] Attempted to show item in folder: ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error(`[Main Process] Failed to show item in folder ${filePath}:`, error);
    return { success: false, error: error.message };
  }
});
// --- End PDF Export Handlers ---

// --- JPEG Export Specific Handlers ---
ipcMain.handle('dialog:showOpenDialog', async (event, options) => {
  const window = BrowserWindow.fromWebContents(event.sender);
  if (!window) {
    console.error("Could not find browser window for showOpenDialog.");
    return { canceled: true, filePaths: [] };
  }
  try {
    const result = await dialog.showOpenDialog(window, options);
    return result; // { canceled: boolean, filePaths: string[], bookmark?: string }
  } catch (error) {
    console.error("Error showing open dialog:", error);
    return { canceled: true, filePaths: [] }; // Indicate error/cancellation
  }
});

ipcMain.handle('file:saveFile', async (event, filePath, dataUrl) => {
  if (!filePath || !dataUrl) {
    console.error("Invalid arguments received for file:saveFile");
    return false;
  }
  try {
    // Convert data URL to Buffer
    // Example: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
    const base64Data = dataUrl.split(';base64,').pop();
    if (!base64Data) {
      throw new Error('Invalid data URL format');
    }
    const buffer = Buffer.from(base64Data, 'base64');
    await fsPromises.writeFile(filePath, buffer);
    console.log(`[Main Process] File saved successfully to: ${filePath}`);
    return true;
  } catch (error) {
    console.error(`[Main Process] Failed to save file to ${filePath}:`, error);
    return false;
  }
});

ipcMain.handle('file:checkFileExists', async (event, filePath) => {
  if (!filePath || typeof filePath !== 'string') {
    console.error('[Main Process] Invalid file path received for file:checkFileExists');
    return false;
  }
  try {
    await fsPromises.access(filePath, fs.constants.F_OK); // Check for existence
    return true;
  } catch (error) {
    // ENOENT means file does not exist, other errors might indicate permission issues etc.
    // For this simple check, any error means we treat it as "not existing" or "not accessible as expected".
    return false;
  }
});
// --- End JPEG Export Specific Handlers ---

// --- NEW: IPC Handler for JPEG Conversion with Color Profile ---
ipcMain.handle('image:convertAndGenerateJpeg', async (event, imageData, options) => {
  console.log('[Main Process JPEG Convert] Received request:', { imageData: { width: imageData.width, height: imageData.height, dataLength: imageData.data.length }, options });

  if (!imageData || !imageData.data || !imageData.width || !imageData.height) {
    return { success: false, error: 'Invalid imageData provided.' };
  }
  if (!options || typeof options.quality !== 'number' || !options.colorSpace) {
    return { success: false, error: 'Invalid options provided.' };
  }

  try {
    const rawPixelBuffer = Buffer.from(imageData.data);
    let sharpInstance = sharp(rawPixelBuffer, {
      raw: {
        width: imageData.width,
        height: imageData.height,
        channels: 4 // Assuming RGBA from canvas getImageData
      }
    });

    if (options.colorSpace === 'adobergb') {
      if (!adobeRGBProfilePathGlobal || !adobeRGBProfileBuffer) { // Check path and buffer
        console.error('[Main Process JPEG Convert] Adobe RGB profile path or buffer not loaded. Cannot convert.');
        return { success: false, error: 'Adobe RGB profile not available for conversion.' };
      }
      console.log('[Main Process JPEG Convert] Converting to Adobe RGB using profile path and embedding profile.');
      sharpInstance = sharpInstance.toColourspace('srgb'); // Ensure input is sRGB before converting to a specific profile space
      sharpInstance = sharpInstance.toColourspace(adobeRGBProfilePathGlobal); // Convert to Adobe RGB using the profile path
      // Explicitly embed the Adobe RGB profile for the output, even if toColourspace is supposed to do it.
      sharpInstance = sharpInstance.withMetadata({ icc: adobeRGBProfilePathGlobal });
    } else { // srgb or passthrough (treat passthrough from canvas as sRGB for JPEG)
      if (!srgbProfilePathGlobal || !sRGBProfileBuffer) { // Check path and buffer
        console.warn('[Main Process JPEG Convert] sRGB profile path or buffer not loaded. JPEG will be sRGB but may lack explicit profile.');
      } else {
        console.log('[Main Process JPEG Convert] Ensuring sRGB and embedding sRGB profile using path.');
        sharpInstance = sharpInstance.toColourspace('srgb'); // Ensure data is in sRGB space
        sharpInstance = sharpInstance.withMetadata({ icc: srgbProfilePathGlobal }); // Embed sRGB profile using its path
      }
    }

    const jpegBuffer = await sharpInstance
      .jpeg({
        quality: options.quality,
        mozjpeg: true
      })
      .toBuffer();

    const dataUrl = `data:image/jpeg;base64,${jpegBuffer.toString('base64')}`;
    console.log('[Main Process JPEG Convert] Successfully generated JPEG data URL.');
    return { success: true, dataUrl };

  } catch (error) {
    console.error('[Main Process JPEG Convert] Error:', error);
    return { success: false, error: error.message || 'Failed to convert and generate JPEG.' };
  }
});
// --- End NEW JPEG Handler ---

// -----------------------------

  // --- Create Application Menu ---
  const menuTemplate = [
    // { role: 'appMenu' } // On macOS
    ...(process.platform === 'darwin' ? [{
      label: app.name,
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    }] : []),
    // { role: 'fileMenu' }
    {
      label: 'File',
      submenu: [
        {
          label: 'New Project',
          accelerator: 'CmdOrCtrl+N', // Standard accelerator for New
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              // Send message to renderer to initiate the new project process
              browserWindow.webContents.send('trigger-new-project');
            }
          }
        },
        { type: 'separator' }, // Separator
        {
          label: 'Load Project...',
          accelerator: 'CmdOrCtrl+O', // Standard open shortcut
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              // Send a message to the renderer to trigger the load project action
              browserWindow.webContents.send('trigger-load-project');
            }
          }
        },
        {
          label: 'Save',
          accelerator: 'CmdOrCtrl+S', // Standard save shortcut
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              // Send a message to the renderer to trigger the save action
              browserWindow.webContents.send('trigger-save');
            }
          }
        },
        {
          label: 'Save As...',
          accelerator: 'CmdOrCtrl+Shift+S',
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              browserWindow.webContents.send('trigger-save-as');
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Import Images...',
          accelerator: 'CmdOrCtrl+I',
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              // This message will be handled by the renderer to call 'dialog:openImage'
              browserWindow.webContents.send('trigger-import-images');
            }
          }
        },
        {
          label: 'Export Project...',
          accelerator: 'CmdOrCtrl+E',
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              // This message will be handled by the renderer to show the export dialog
              browserWindow.webContents.send('trigger-export-dialog');
            }
          }
        },
        { type: 'separator' }, // Separator before close/quit
        process.platform === 'darwin' ? { role: 'close' } : { role: 'quit' }
      ]
    },
    // { role: 'editMenu' }
    {
      label: 'Edit',
      submenu: [
        {
          label: 'Undo',
          accelerator: 'CmdOrCtrl+Z',
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              browserWindow.webContents.send('trigger-undo');
            }
          }
        },
        {
          label: 'Redo',
          // Common accelerators for Redo
          accelerator: process.platform === 'darwin' ? 'Cmd+Shift+Z' : 'CmdOrCtrl+Y',
           click: (menuItem, browserWindow) => {
            if (browserWindow) {
              browserWindow.webContents.send('trigger-redo');
            }
          }
        },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        ...(process.platform === 'darwin' ? [
          { type: 'separator' },
          { role: 'selectAll' },
          { type: 'separator' },
          {
            label: 'Speech',
            submenu: [
              { role: 'startSpeaking' },
              { role: 'stopSpeaking' }
            ]
          }
        ] : [
          { type: 'separator' },
          { role: 'selectAll' }
        ])
      ]
    },
    // { role: 'viewMenu' }
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'togglefullscreen' },
        { type: 'separator' },
        {
          label: 'Toggle Theme',
          accelerator: 'CmdOrCtrl+T',
          click: (menuItem, browserWindow) => {
            if (browserWindow) {
              browserWindow.webContents.send('trigger-toggle-theme');
            }
          }
        }
      ]
    },
    // { role: 'windowMenu' }
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'zoom' },
        ...(process.platform === 'darwin' ? [
          { type: 'separator' },
          { role: 'front' },
          { type: 'separator' },
          { role: 'window' }
        ] : [
          { role: 'close' }
        ])
      ]
    },
    {
      role: 'help',
      submenu: [
        // Add help links later
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(menu);
  // --------------------------

  createWindow();
  // createWindow(); // Removed duplicate call

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
}); // This closes app.whenReady().then()

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') {
    stopBackupTimer(); // Ensure timer is stopped before quitting
    app.quit();
  }
});

// App cleanup before quitting
app.on('will-quit', () => {
  console.log('[Main Process] Closing all file watchers...');
  fileWatchers.forEach((watcher, filePath) => {
      try {
        watcher.close();
        console.log(`[Main Process] Closed watcher for: ${filePath}`);
      } catch (closeError) {
         console.error(`[Main Process] Error closing watcher for ${filePath}:`, closeError);
      }
  });
  fileWatchers.clear();

  console.log('[Main Process] Clearing all debounce timers...');
  debounceTimers.forEach(timerId => clearTimeout(timerId));
  debounceTimers.clear();

  // Stop backup timer on quit
  stopBackupTimer();
});


// In this file, you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.

// --- NEW: Get Image EXIF Data ---
ipcMain.handle('image:get-exif-data', async (event, filePath) => {
  try {
    const cleanPath = filePath.startsWith('safe-file://')
      ? path.normalize(decodeURI(filePath.substring(12)))
      : path.normalize(decodeURI(filePath));

    if (!cleanPath || typeof cleanPath !== 'string') {
       throw new Error('Invalid or unsafe file path provided');
    }

    console.log(`[Main Process] Getting EXIF data for: ${path.basename(cleanPath)}`);

    // Get metadata with sharp for dimensions and color profile information
    const imageMetadata = await sharp(cleanPath).metadata();
    
    // Log metadata for debugging
    console.log(`[Main Process] Image metadata for ${path.basename(cleanPath)}:`, {
      hasICC: !!imageMetadata.icc,
      iccLength: imageMetadata.icc ? imageMetadata.icc.length : 0,
      space: imageMetadata.space,
      channels: imageMetadata.channels,
      depth: imageMetadata.depth
    });
    
    // Get EXIF data with exifr - use more options to extract all possible tags
    const exifData = await exifr.parse(cleanPath, {
      xmp: true,     // XMP data for rating
      iptc: true,    // IPTC data
      exif: true,    // EXIF data for capture time
      icc: true,     // ICC profile info
      tiff: true,    // TIFF headers may contain profile info
      jfif: true,    // JPEG headers
      ihdr: true,    // PNG info headers
      userComment: true, // Sometimes contains color info
      makerNote: true,   // Sometimes contains color info
      translateValues: true, // Translate numeric values to strings when possible
      reviveValues: true,    // Convert special values to JS types
      mergeOutput: false     // Keep all output separate to see what each parser found
    });

    // Get the raw ICC profile data using a different approach
    let iccProfile = null;
    try {
      // Try to get raw ICC profile data using a different method
      iccProfile = await exifr.gps(cleanPath, { icc: true });
    } catch (e) {
      console.log(`[Main Process] Error getting raw ICC profile: ${e.message}`);
    }
    
    // Log all results for debugging
    console.log(`[Main Process] Complete EXIF data for ${path.basename(cleanPath)}:`, exifData);
    console.log(`[Main Process] Raw ICC profile data:`, iccProfile);
    
    // Merge all exif data for easier access
    const mergedExifData = { ...(exifData.icc || {}), ...(exifData.exif || {}), ...(exifData.xmp || {}) };
    
    // Log EXIF data for debugging
    console.log(`[Main Process] Merged EXIF data for ${path.basename(cleanPath)}:`, {
      hasICC: !!mergedExifData.ICC,
      ColorSpace: mergedExifData.ColorSpace,
      ICCProfileName: mergedExifData.ICCProfileName,
      ProfileDescription: mergedExifData.ProfileDescription,
      DeviceModelDesc: mergedExifData.DeviceModelDesc,
      iccKeys: Object.keys(mergedExifData).filter(key => key.includes('ICC') || key.includes('Profile') || key.includes('Color'))
    });

    // Format the capture time nicely if it exists - Use mergedExifData!
    let formattedCaptureTime = null;
    // Check the most specific EXIF tag first
    if (mergedExifData?.DateTimeOriginal) {
      try {
        const captureDate = new Date(mergedExifData.DateTimeOriginal);
        formattedCaptureTime = captureDate.toLocaleString();
      } catch (e) { console.error("Error parsing DateTimeOriginal:", e); }
    } 
    // Fallback to DateTime tag
    else if (mergedExifData?.DateTime) {
      try {
        const captureDate = new Date(mergedExifData.DateTime);
        formattedCaptureTime = captureDate.toLocaleString();
      } catch (e) { console.error("Error parsing DateTime:", e); }
    } 
    // Fallback to CreateDate tag
    else if (mergedExifData?.CreateDate) {
      try {
        const captureDate = new Date(mergedExifData.CreateDate);
        formattedCaptureTime = captureDate.toLocaleString();
      } catch (e) { console.error("Error parsing CreateDate:", e); }
    }
    
    // Log the determined capture time
    console.log(`[Main Process] Determined capture time for ${path.basename(cleanPath)}: ${formattedCaptureTime}`);

    // Get color profile information - enhanced with more fallbacks
    let colorProfile = null;
    
    // Try to extract the most accurate profile name possible from various places
    
    // First check for common Photoshop/Lightroom colorspace identifiers
    if (mergedExifData.ICCProfileName) {
      colorProfile = mergedExifData.ICCProfileName;
      console.log(`[Main Process] Found ICCProfileName: ${colorProfile}`);
    }
    // Check the direct profile name
    else if (mergedExifData.ProfileName) {
      colorProfile = mergedExifData.ProfileName;
      console.log(`[Main Process] Found ProfileName: ${colorProfile}`);
    }
    // Check for ICC specific profile description
    else if (mergedExifData.ProfileDescription) {
      colorProfile = mergedExifData.ProfileDescription;
      console.log(`[Main Process] Found ProfileDescription: ${colorProfile}`);
    }
    // Check ICC profile description from a different source
    else if (exifData?.icc?.ProfileDescription) {
      colorProfile = exifData.icc.ProfileDescription;
      console.log(`[Main Process] Found ICC ProfileDescription: ${colorProfile}`);
    } 
    // Try reading the profile description from the ICC block
    else if (exifData?.icc?.Description) {
      colorProfile = exifData.icc.Description;
      console.log(`[Main Process] Found ICC Description: ${colorProfile}`);
    }
    // Check device model description
    else if (mergedExifData.DeviceModelDesc) {
      colorProfile = mergedExifData.DeviceModelDesc;
      console.log(`[Main Process] Found DeviceModelDesc: ${colorProfile}`);
    }
    // Check manufacturer description
    else if (mergedExifData.DeviceMfgDesc) {
      colorProfile = `${mergedExifData.DeviceMfgDesc} Profile`;
      console.log(`[Main Process] Using DeviceMfgDesc: ${colorProfile}`);
    }
    // For Adobe RGB or ProPhoto RGB, check if the ProfileID contains known values
    else if (mergedExifData.ProfileID) {
      const profileId = mergedExifData.ProfileID.toLowerCase();
      if (profileId.includes('adobe')) {
        colorProfile = 'Adobe RGB';
        console.log(`[Main Process] Found Adobe RGB in ProfileID: ${mergedExifData.ProfileID}`);
      } else if (profileId.includes('prophoto') || profileId.includes('pro photo')) {
        colorProfile = 'ProPhoto RGB';
        console.log(`[Main Process] Found ProPhoto RGB in ProfileID: ${mergedExifData.ProfileID}`);
      } else if (profileId.includes('srgb')) {
        colorProfile = 'sRGB';
        console.log(`[Main Process] Found sRGB in ProfileID: ${mergedExifData.ProfileID}`);
      } else {
        colorProfile = `Profile ID: ${mergedExifData.ProfileID}`;
        console.log(`[Main Process] Using ProfileID: ${mergedExifData.ProfileID}`);
      }
    }
    // Check camera model for known camera color profiles
    else if (mergedExifData.Model) {
      const cameraModel = mergedExifData.Model.toLowerCase();
      if (cameraModel.includes('canon')) {
        colorProfile = `Canon ${mergedExifData.Model} Default`;
      } else if (cameraModel.includes('nikon')) {
        colorProfile = `Nikon ${mergedExifData.Model} Default`;
      } else if (cameraModel.includes('sony')) {
        colorProfile = `Sony ${mergedExifData.Model} Default`;
      } else {
        colorProfile = `${mergedExifData.Model} Default`;
      }
      console.log(`[Main Process] Using camera model for profile: ${colorProfile}`);
    }
    // Try to use the profile class if available
    else if (mergedExifData.ProfileClass) {
      // Use the profile class as a fallback
      let profileClass = mergedExifData.ProfileClass;
      // Remove 'Class' suffix if present
      profileClass = profileClass.replace(/\s*[Cc]lass$/, '');
      colorProfile = `${profileClass} ICC Profile`;
      console.log(`[Main Process] Using ProfileClass: ${colorProfile}`);
    }
    // Check if we have an ICC profile buffer but no name
    else if (imageMetadata.icc) {
      // Try to extract profile info directly from the buffer as a last resort
      const iccHeaderInfo = tryReadICCProfileHeader(imageMetadata.icc);
      if (iccHeaderInfo && iccHeaderInfo.profileName) {
        colorProfile = iccHeaderInfo.profileName;
        console.log(`[Main Process] Found profile name in ICC header: ${colorProfile}`);
      } else {
        // For ICC data, try to get some info on the color space from the profile length
        const iccLength = imageMetadata.icc.length;
        if (iccLength > 0) {
          // Let's try to make an educated guess based on common ICC profile sizes
          if (iccLength > 500000) {
            colorProfile = "Large ICC Profile";
          } else if (iccLength > 100000) {
            colorProfile = "Medium ICC Profile";
          } else {
            colorProfile = "ICC Profile";
          }
          colorProfile += ` (${Math.round(iccLength / 1024)}KB)`;
        } else {
          colorProfile = "ICC Profile (Unknown)";
        }
      }
      console.log(`[Main Process] Found ICC profile buffer: ${colorProfile}`);
    } 
    // Check if sharp detected a standard color space 
    else if (imageMetadata.space) {
      colorProfile = imageMetadata.space;
      console.log(`[Main Process] Using color space from Sharp: ${colorProfile}`);
    } 
    // Last resort fallback
    else {
      colorProfile = "sRGB (default)";
      console.log(`[Main Process] No profile info found, defaulting to: ${colorProfile}`);
    }
    
    // Check for standard color spaces in EXIF when we couldn't get a specific profile name
    if ((colorProfile === "ICC Profile (Unknown)" || !colorProfile) && mergedExifData.ColorSpace) {
      // Check numeric ColorSpace values (standard EXIF field)
      if (mergedExifData.ColorSpace === 1 || mergedExifData.ColorSpace === '1') {
        colorProfile = "sRGB";
        console.log(`[Main Process] Found ColorSpace=1 (sRGB) in EXIF`);
      } else if (mergedExifData.ColorSpace === 2 || mergedExifData.ColorSpace === '2') {
        colorProfile = "Adobe RGB";
        console.log(`[Main Process] Found ColorSpace=2 (Adobe RGB) in EXIF`);
      } else if (typeof mergedExifData.ColorSpace === 'string') {
        // If ColorSpace is a string, use it directly
        colorProfile = mergedExifData.ColorSpace;
        console.log(`[Main Process] Using ColorSpace string: ${colorProfile}`);
      }
    }
    
    // Check for ProfileCopyright as another potential source
    if ((colorProfile === "ICC Profile (Unknown)" || !colorProfile) && mergedExifData.ProfileCopyright) {
      colorProfile = `Profile by ${mergedExifData.ProfileCopyright}`;
      console.log(`[Main Process] Using ProfileCopyright as fallback: ${colorProfile}`);
    }

    console.log(`[Main Process] Final color profile for ${path.basename(cleanPath)}: ${colorProfile}`);
    
    // Add additional metadata for UI display if we have ICC profile or EXIF data
    // Store color space data if available
    let colorSpaceData = null;
    
    // Try to get color components from ICC data
    if (mergedExifData.NumComponents || (exifData?.icc?.NumComponents)) {
      const components = mergedExifData.NumComponents || exifData?.icc?.NumComponents;
      
      // Add interpretation of common color spaces
      if (components === 1) {
        colorSpaceData = "Grayscale";
      } else if (components === 3) {
        colorSpaceData = "RGB";
      } else if (components === 4) {
        colorSpaceData = "CMYK";
      } else {
        colorSpaceData = `${components} components`;
      }
      
      // Add color depth if available
      if (imageMetadata.depth) {
        colorSpaceData += ` ${imageMetadata.depth}-bit`;
      }
    }
    // Try to infer from the number of channels in the image if no ICC info
    else if (imageMetadata.channels) {
      const channels = imageMetadata.channels;
      if (channels === 1) {
        colorSpaceData = "Grayscale";
      } else if (channels === 3) {
        colorSpaceData = "RGB";
      } else if (channels === 4) {
        colorSpaceData = "CMYK";
      } else {
        colorSpaceData = `${channels} channels`;
      }
      
      // Add color depth if available
      if (imageMetadata.depth) {
        colorSpaceData += ` ${imageMetadata.depth}-bit`;
      }
    }
    
    // Store the color space data for UI display
    if (colorSpaceData) {
      if (!exifData.icc) exifData.icc = {};
      exifData.icc.ColorSpaceData = colorSpaceData;
    }
    
    // Add creation date information if available
    if (mergedExifData.ProfileDateTime || mergedExifData.CalibrationDateTime) {
      const dateStr = mergedExifData.ProfileDateTime || mergedExifData.CalibrationDateTime;
      try {
        const date = new Date(dateStr);
        if (!exifData.icc) exifData.icc = {};
        exifData.icc.FormattedDateTime = date.toLocaleDateString();
      } catch (e) {
        // Ignore date parsing errors
      }
    }

    return {
      success: true,
      exifData: {
        dimensions: {
          width: imageMetadata.width,
          height: imageMetadata.height
        },
        colorProfile: colorProfile,
        // Use mergedExifData for rating to ensure we get it from XMP if possible
        rating: mergedExifData?.Rating || 0, 
        captureTime: formattedCaptureTime,
        // Include the full raw exif data for debugging or advanced usage
        raw: exifData
      }
    };
  } catch (error) {
    console.error(`Failed to get EXIF data for ${filePath}:`, error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Helper function to determine MIME type from filename extension
function getMimeTypeFromFilename(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.webp': 'image/webp',
    '.gif': 'image/gif',
    '.avif': 'image/avif',
    '.heic': 'image/heic'
  };
  return mimeTypes[ext] || 'application/octet-stream';
}

// Add a helper function to try reading ICC profile header information
function tryReadICCProfileHeader(buffer) {
  if (!buffer || buffer.length < 128) {
    return null; // Not enough data for ICC header
  }
  
  try {
    // Check ICC profile signature "acsp" at position 36
    const signature = buffer.toString('ascii', 36, 40);
    if (signature !== 'acsp') {
      return null; // Not a valid ICC profile
    }
    
    // Try to read the profile description tag
    // This is a simplified approach - a real implementation would parse the ICC tag table
    
    // Check for common profile description strings in the buffer
    const fullStr = buffer.toString('ascii');
    
    // Common ICC profile names to look for
    const profilePatterns = [
      'sRGB', 'Adobe RGB', 'ProPhoto', 'Display P3', 'CMYK', 
      'Coated', 'Uncoated', 'Newspaper', 'Web Coated',
      'Apple', 'Canon', 'Nikon', 'Sony', 'Epson', 'HP'
    ];
    
    for (const pattern of profilePatterns) {
      if (fullStr.includes(pattern)) {
        // Find the surrounding text to get the full name
        const index = fullStr.indexOf(pattern);
        const start = Math.max(0, index - 15);
        const end = Math.min(fullStr.length, index + pattern.length + 15);
        const fullName = fullStr.substring(start, end).replace(/[^\x20-\x7E]/g, '').trim();
        
        if (fullName) {
          return {
            profileName: fullName,
            match: pattern
          };
        }
      }
    }
    
    // If we couldn't find any patterns, at least return the size
    return {
      profileName: `ICC Profile (${Math.round(buffer.length / 1024)}KB)`,
      size: buffer.length
    };
  } catch (e) {
    console.error('Error reading ICC header:', e);
    return null;
  }
}
// --- Helper Function to Extract Compressed ICC Profile from PNG ---
// Based on PNG specification: http://www.libpng.org/pub/png/spec/1.2/PNG-Chunks.html
function extractCompressedIccProfileFromPng(pngBuffer) {
  if (!pngBuffer || pngBuffer.length < 8) {
    console.warn('[PNG ICC] Buffer too small or invalid.');
    return null;
  }

  // Check PNG signature
  const signature = pngBuffer.slice(0, 8);
  if (signature.toString('hex') !== '89504e470d0a1a0a') {
    console.warn('[PNG ICC] Invalid PNG signature.');
    return null;
  }

  let offset = 8; // Start after signature
  while (offset < pngBuffer.length) {
    if (offset + 8 > pngBuffer.length) {
      console.warn('[PNG ICC] Reached end of buffer unexpectedly while reading chunk header.');
      break; // Not enough data for chunk length and type
    }

    const chunkLength = pngBuffer.readUInt32BE(offset);
    const chunkType = pngBuffer.toString('ascii', offset + 4, offset + 8);

    // Check for buffer overflow before reading chunk data and CRC
    if (offset + 8 + chunkLength + 4 > pngBuffer.length) {
       console.warn(`[PNG ICC] Chunk ${chunkType} length (${chunkLength}) exceeds buffer bounds.`);
       break;
    }

    if (chunkType === 'iCCP') {
      console.log(`[PNG ICC] Found iCCP chunk at offset ${offset}, length ${chunkLength}.`);
      // iCCP Data format:
      // Profile name: 1-79 bytes (null-terminated string)
      // Compression method: 1 byte (0 for zlib/deflate)
      // Compressed profile: n bytes

      const dataOffset = offset + 8;
      let nameEnd = dataOffset;
      while (nameEnd < dataOffset + chunkLength && pngBuffer[nameEnd] !== 0) {
        nameEnd++;
      }

      if (nameEnd >= dataOffset + chunkLength) {
        console.warn('[PNG ICC] iCCP chunk missing null terminator for profile name.');
        return null; // Malformed chunk
      }

      const profileName = pngBuffer.toString('utf8', dataOffset, nameEnd);
      const compressionMethodOffset = nameEnd + 1;

      if (compressionMethodOffset >= dataOffset + chunkLength) {
         console.warn('[PNG ICC] iCCP chunk too short for compression method.');
         return null; // Malformed chunk
      }

      const compressionMethod = pngBuffer.readUInt8(compressionMethodOffset);
      console.log(`[PNG ICC] Profile Name: ${profileName}, Compression Method: ${compressionMethod}`);

      if (compressionMethod === 0) { // 0 indicates zlib/deflate
        const compressedProfileOffset = compressionMethodOffset + 1;
        const compressedProfileLength = chunkLength - (compressedProfileOffset - dataOffset);

        if (compressedProfileLength <= 0) {
           console.warn('[PNG ICC] iCCP chunk has zero length for compressed profile.');
           return null;
        }

        const compressedProfile = pngBuffer.slice(compressedProfileOffset, compressedProfileOffset + compressedProfileLength);
        console.log(`[PNG ICC] Extracted compressed profile (${compressedProfile.length} bytes).`);
        return compressedProfile; // Return the compressed data buffer
      } else {
        console.warn(`[PNG ICC] Unsupported iCCP compression method: ${compressionMethod}`);
        return null; // Unsupported compression
      }
    } else if (chunkType === 'IEND') {
      // Reached the end chunk, stop searching
      console.log('[PNG ICC] Reached IEND chunk, no iCCP found.');
      break;
    }

    // Move to the next chunk (Chunk Data + CRC)
    offset += 8 + chunkLength + 4;
  }

  console.log('[PNG ICC] No iCCP chunk found after scanning.');
  return null; // iCCP chunk not found
}
// --- End Helper Function ---

// --- Theme Management ---
// Handler to set the title bar theme
ipcMain.handle('theme:setTitleBar', async (event, theme) => {
  try {
    const { nativeTheme } = require('electron');
    
    console.log(`[Main Process] Setting title bar theme to: ${theme}`);
    
    // Set the native theme source based on the theme
    if (theme === 'dark') {
      nativeTheme.themeSource = 'dark';
    } else if (theme === 'light') {
      nativeTheme.themeSource = 'light';
    } else {
      // For 'system' or any other value, use system default
      nativeTheme.themeSource = 'system';
    }
    
    console.log(`[Main Process] Native theme source set to: ${nativeTheme.themeSource}`);
    return { success: true };
  } catch (error) {
    console.error('[Main Process] Error setting title bar theme:', error);
    return { success: false, error: error.message };
  }
});
// --- End Theme Management ---