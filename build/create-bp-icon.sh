#!/bin/bash

# <PERSON>ript to create .bp file icon for BookProofs application
# This script requires ImageMagick to be installed

# Check if ImageMagick is installed
if ! command -v magick &> /dev/null; then
    echo "Error: ImageMagick is required but not installed. Please install it using 'brew install imagemagick'."
    exit 1
 fi

# Set paths
BUILD_DIR="$(dirname "$0")"
LOGO_PNG="$BUILD_DIR/logo.png"
BP_ICON_PNG="$BUILD_DIR/bp-file-icon.png"
BP_ICON_ICNS="$BUILD_DIR/bp-file-icon.icns"

# Check if logo.png exists
if [ ! -f "$LOGO_PNG" ]; then
    echo "Error: $LOGO_PNG not found"
    exit 1
fi

# Create a simple icon using just the logo
# Resize the logo to appropriate size for the icon
magick "$LOGO_PNG" -resize 512x512 -background transparent "$BP_ICON_PNG"

# No BP text as requested

# Create .icns file for macOS
if command -v iconutil &> /dev/null; then
    # Create temporary iconset directory
    TEMP_ICONSET="$BUILD_DIR/bp-file.iconset"
    mkdir -p "$TEMP_ICONSET"
    
    # Generate various sizes
    magick "$BP_ICON_PNG" -resize 16x16 "$TEMP_ICONSET/icon_16x16.png"
    magick "$BP_ICON_PNG" -resize 32x32 "$TEMP_ICONSET/<EMAIL>"
    magick "$BP_ICON_PNG" -resize 32x32 "$TEMP_ICONSET/icon_32x32.png"
    magick "$BP_ICON_PNG" -resize 64x64 "$TEMP_ICONSET/<EMAIL>"
    magick "$BP_ICON_PNG" -resize 128x128 "$TEMP_ICONSET/icon_128x128.png"
    magick "$BP_ICON_PNG" -resize 256x256 "$TEMP_ICONSET/<EMAIL>"
    magick "$BP_ICON_PNG" -resize 256x256 "$TEMP_ICONSET/icon_256x256.png"
    magick "$BP_ICON_PNG" -resize 512x512 "$TEMP_ICONSET/<EMAIL>"
    magick "$BP_ICON_PNG" -resize 512x512 "$TEMP_ICONSET/icon_512x512.png"
    magick "$BP_ICON_PNG" -resize 1024x1024 "$TEMP_ICONSET/<EMAIL>"
    
    # Create .icns file
    iconutil -c icns "$TEMP_ICONSET" -o "$BP_ICON_ICNS"
    
    # Clean up
    rm -rf "$TEMP_ICONSET"
    
    echo "Created $BP_ICON_ICNS successfully"
else
    echo "Warning: iconutil not found. .icns file not created."
fi

echo "BP file icon created successfully at $BP_ICON_PNG"
